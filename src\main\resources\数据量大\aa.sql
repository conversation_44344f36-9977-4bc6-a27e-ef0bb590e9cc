
CREATE TABLE `E10ListModuleDocuments_sr_primary_bak` (
                                                     `eid` varchar(65533) NOT NULL COMMENT "",
                                                     `source_db_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_account_set` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_module` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_category` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_no` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_date` date NOT NULL COMMENT "",
                                                     `e10_doc_code` varchar(65533) NULL COMMENT "",
                                                     `e10_doc_name` varchar(65533) NULL COMMENT "",
                                                     `e10_create_date` date NULL COMMENT "",
                                                     `e10_approve_date` date NULL COMMENT "",
                                                     `e10_approve_status` varchar(65533) NULL COMMENT "",
                                                     `e10_approve_period` int(11) NULL COMMENT "",
                                                     `e10_is_make_up_order` boolean NULL COMMENT "",
                                                     `e10_is_not_timely_approve` boolean NULL COMMENT "",
                                                     `e10_max_last_modify_date` varchar(65533) NULL COMMENT "",
                                                     `e10_model_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_name` varchar(65533) NULL COMMENT "",
                                                     `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`, `source_db_id`, `e10_account_set`, `e10_module`, `e10_doc_category`, `e10_doc_id`, `e10_doc_no`, `e10_doc_date`)
COMMENT "OLAP"
PARTITION BY RANGE(`e10_doc_date`)
(PARTITION p202301 VALUES [("2023-01-01"), ("2023-02-01")),
PARTITION p202302 VALUES [("2023-02-01"), ("2023-03-01")),
PARTITION p202303 VALUES [("2023-03-01"), ("2023-04-01")),
PARTITION p202304 VALUES [("2023-04-01"), ("2023-05-01")),
PARTITION p202305 VALUES [("2023-05-01"), ("2023-06-01")),
PARTITION p202306 VALUES [("2023-06-01"), ("2023-07-01")),
PARTITION p202307 VALUES [("2023-07-01"), ("2023-08-01")),
PARTITION p202308 VALUES [("2023-08-01"), ("2023-09-01")),
PARTITION p202309 VALUES [("2023-09-01"), ("2023-10-01")),
PARTITION p202310 VALUES [("2023-10-01"), ("2023-11-01")),
PARTITION p202311 VALUES [("2023-11-01"), ("2023-12-01")),
PARTITION p202312 VALUES [("2023-12-01"), ("2024-01-01")),
PARTITION p202401 VALUES [("2024-01-01"), ("2024-02-01")),
PARTITION p202402 VALUES [("2024-02-01"), ("2024-03-01")),
PARTITION p202403 VALUES [("2024-03-01"), ("2024-04-01")),
PARTITION p202404 VALUES [("2024-04-01"), ("2024-05-01")),
PARTITION p202405 VALUES [("2024-05-01"), ("2024-06-01")),
PARTITION p202406 VALUES [("2024-06-01"), ("2024-07-01")),
PARTITION p202407 VALUES [("2024-07-01"), ("2024-08-01")),
PARTITION p202408 VALUES [("2024-08-01"), ("2024-09-01")),
PARTITION p202409 VALUES [("2024-09-01"), ("2024-10-01")),
PARTITION p202410 VALUES [("2024-10-01"), ("2024-11-01")),
PARTITION p202411 VALUES [("2024-11-01"), ("2024-12-01")),
PARTITION p202412 VALUES [("2024-12-01"), ("2025-01-01")))
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "MONTH",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-12",
"dynamic_partition.end" = "2",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"dynamic_partition.start_day_of_month" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-11-01'
  AND e10_doc_date < '2024-12-01';

select count(1) from (
  SELECT *
  FROM E10ListModuleDocuments_sr_primary
  WHERE e10_doc_date >= '2024-11-01'
    AND e10_doc_date < '2024-12-01'
) as aa;


select count(1) from (
  SELECT *
  FROM E10ListModuleDocuments_sr_primary
  WHERE e10_doc_date >= '2023-12-01'
) as aa;
select count(1) from (
  SELECT *
  FROM E10ListModuleDocuments_sr_primary_bak
  WHERE e10_doc_date >= '2023-12-01'
) as aa;

alter table E10ListModuleDocuments_sr_primary rename  E10ListModuleDocuments_sr_primary_bak_20241101;
alter table E10ListModuleDocuments_sr_primary_bak rename  E10ListModuleDocuments_sr_primary;
CREATE TABLE `T100DocumentDeductionListCollected_sr_primary_bak` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `t100_ent` int(11) NOT NULL COMMENT "",
  `t100_site` varchar(65533) NOT NULL COMMENT "",
  `t100_doc_no` varchar(65533) NOT NULL COMMENT "",
  `t100_item` int(11) NOT NULL COMMENT "",
  `t100_item_order` int(11) NOT NULL COMMENT "",
  `t100_access_code` int(11) NOT NULL COMMENT "",
  `t100_max_last_modify_date` varchar(65533) NOT NULL COMMENT "",
  `deviceId` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `collectConfigId` varchar(65533) NULL COMMENT "",
  `uploadDataModelCode` varchar(65533) NULL COMMENT "",
  `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
  `t100_reality_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_doc_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_yyyy` varchar(65533) NULL COMMENT "",
  `t100_mm` varchar(65533) NULL COMMENT "",
  `t100_module_code` varchar(65533) NULL COMMENT "",
  `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(`eid`, `source_db_id`, `t100_ent`, `t100_site`, `t100_doc_no`, `t100_item`, `t100_item_order`, `t100_access_code`, `t100_max_last_modify_date`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-11-01 00:00:00' AND collectedTime < '2024-12-01 00:00:00';


alter table T100DocumentDeductionListCollected_sr_primary rename  T100DocumentDeductionListCollected_sr_primary_bak_20241101;
alter table T100DocumentDeductionListCollected_sr_primary_bak rename  T100DocumentDeductionListCollected_sr_primary;

select count(1) from (
  SELECT *
  FROM T100DocumentDeductionListCollected_sr_primary
  WHERE collectedTime >= '2024-01-01 00:00:00' AND collectedTime < '2024-02-01 00:00:00'
) as aa;


PRIMARY KEY(eid,source_db_id,e10_account_set,e10_module,e10_doc_category,e10_doc_id,e10_doc_no,e10_doc_date)  PARTITION BY RANGE(e10_doc_date)(  START ("2019-01-01") END ("2023-03-23") EVERY (INTERVAL 1 DAY)  )  DISTRIBUTED BY HASH(source_db_id) BUCKETS 10  PROPERTIES(      "replication_num" = "1",      "dynamic_partition.enable" = "true",      "dynamic_partition.time_unit" = "DAY",      "dynamic_partition.start" = "-1096",      "dynamic_partition.end" = "30",      "dynamic_partition.prefix" = "p",      "dynamic_partition.buckets" = "10"  );


-- 测试环境
CREATE TABLE `E10ListModuleDocuments_sr_primary` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `e10_account_set` varchar(65533) NOT NULL COMMENT "",
  `e10_module` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_category` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_id` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_no` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_date` date NOT NULL COMMENT "",
  `e10_doc_code` varchar(65533) NULL COMMENT "",
  `e10_doc_name` varchar(65533) NULL COMMENT "",
  `e10_create_date` date NULL COMMENT "",
  `e10_approve_date` date NULL COMMENT "",
  `e10_approve_status` varchar(65533) NULL COMMENT "",
  `e10_approve_period` int(11) NULL COMMENT "",
  `e10_is_make_up_order` boolean NULL COMMENT "",
  `e10_is_not_timely_approve` boolean NULL COMMENT "",
  `e10_max_last_modify_date` varchar(65533) NULL COMMENT "",
  `e10_model_code` varchar(65533) NULL COMMENT "",
  `e10_company_code` varchar(65533) NULL COMMENT "",
  `e10_company_name` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(`eid`, `source_db_id`, `e10_account_set`, `e10_module`, `e10_doc_category`, `e10_doc_id`, `e10_doc_no`, `e10_doc_date`)
COMMENT "OLAP"
PARTITION BY (`e10_doc_date`)
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "false",
"fast_schema_evolution" = "true",
"replicated_storage" = "false",
"replication_num" = "1"
);

CREATE TABLE `E10ListModuleDocuments_sr_primary_t` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `e10_account_set` varchar(65533) NOT NULL COMMENT "",
  `e10_module` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_category` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_id` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_no` varchar(65533) NOT NULL COMMENT "",
  `e10_doc_date` date NOT NULL COMMENT "",
  `e10_doc_code` varchar(65533) NULL COMMENT "",
  `e10_doc_name` varchar(65533) NULL COMMENT "",
  `e10_create_date` date NULL COMMENT "",
  `e10_approve_date` date NULL COMMENT "",
  `e10_approve_status` varchar(65533) NULL COMMENT "",
  `e10_approve_period` int(11) NULL COMMENT "",
  `e10_is_make_up_order` boolean NULL COMMENT "",
  `e10_is_not_timely_approve` boolean NULL COMMENT "",
  `e10_max_last_modify_date` varchar(65533) NULL COMMENT "",
  `e10_model_code` varchar(65533) NULL COMMENT "",
  `e10_company_code` varchar(65533) NULL COMMENT "",
  `e10_company_name` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(eid,source_db_id,e10_account_set,e10_module,e10_doc_category,e10_doc_no,e10_doc_date,e10_doc_code)
PARTITION BY RANGE(`e10_doc_date`)(
START ("2023-01-01") END ("2024-03-23") EVERY (INTERVAL 1 MONTH)
)
DISTRIBUTED BY HASH(source_db_id) BUCKETS 10
PROPERTIES(
    "replication_num" = "1",
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-12",
    "dynamic_partition.end" = "2",
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "10"
);

alter table E10ListModuleDocuments_sr_primary rename  E10ListModuleDocuments_sr_primary_bak_20241104;
insert into E10ListModuleDocuments_sr_primary select 
  `eid`,
  `source_db_id`,
  `e10_account_set`,
  `e10_module`, 
  `e10_doc_category`,
  `e10_doc_no`,
  `e10_doc_date`,
  `e10_doc_code`,
    `e10_doc_id`,
  `e10_doc_name`,
  `e10_create_date`,
  `e10_approve_date`, 
  `e10_approve_status`,
  `e10_approve_period`,
  `e10_is_make_up_order`,
  `e10_is_not_timely_approve`,
  `e10_max_last_modify_date`,
  `e10_model_code`,
  `e10_company_code`,
  `e10_company_name`,
  `aiId`,
  `aiopsItem`
from E10ListModuleDocuments_sr_primary_bak_20241104;
