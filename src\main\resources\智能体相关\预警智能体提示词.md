# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定时间范围且问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件（如预警级别、设备类型等）。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": {datetime}
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "最近有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
2. 过滤条件映射
- 设备类型：`devicetype`（主机 对应 HOST，终端 对应 CLIENT，SQL Server数据库 对应 MSSQL，ORACLE数据库 对应 ORACLE，MYSQL数据库 对应 MYSQL，LINUX 对应 LINUX，MAC 对应 MAC，NAS 对应 NAS，RAID 对应 RAID，防火墙 对应 FIREWALL，ESXI 对应 ESXI，交换器 对应 SWITCH，不断电系统 对应 UPS，温湿度 对应 TMP_RH，iLO 对应 iLO，iDRAC 对应 iDRAC，iMM 对应 iMM，XCC 对应 XCC ）
- 告警状态：`status`（unsolved 对应 未解决/solved 对应 解决）
- 预警级别：`warningLevel`（FATAL 对应 严重 / ERROR 对应 错误 / WARNING 对应 警告 / INFO 对应 一般）
- 是否紧急：`urgency`（1 对应紧急/0 对应不紧急/null）
- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘损坏→"磁盘损坏"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 是否需要解决建议：当用户输入包含“如何解决”、“怎么处理”、“怎么解决”等词时，请直接根据上下文对话总结预警过滤条件映射的关键词，重新查询即可，因为我们的预警数据里就有解决方案）
- 多条件处理：用户输入可能包含多个条件（如时间范围、预警级别、设备类型等），请确保提取所有提到的条件，并将它们包含在JSON查询中。
- 规则：
- 只要有紧急字样，设置 `"urgency": 1`；不紧急字样设置 `"urgency": 0`
- 只要有 严重、警告、错误、一般 字样，设置 `预警级别`（`warningLevel`）为对应级别，例如 严重 设置 "warningLevel": "FATAL"， 错误 设置 "warningLevel": "ERROR"， 警告 设置 "warningLevel": "WARNING"， 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或`解决`字样，设置 `告警状态`（`status`）为对应状态，例如 `未解决` 设置为 "status": "unsolved"
- 示例：
- "主机未处理的严重紧急告警" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1
- "最近有磁盘损坏相关的预警吗" ➔ "warningItemName": "磁盘损坏"
- "最近有严重的预警吗" ➔ "warningLevel": "FATAL"
- "有哪些必须马上处理的预警，我该如何解决" ➔ "warningLevel": "FATAL","urgency": 1,"includeSolution": true
- "严重紧急预警" ➔ "warningLevel": "FATAL","urgency": 1
- "严重预警" ➔ "warningLevel": "FATAL"
- "紧急预警" ➔ "urgency": "1"
- "未解决预警" ➔ "status": "unsolved"
- 只要有紧急字样，设置 `"urgency": 1` 不紧急字样设置 `"urgency": 0`
- 只要有 严重、错误、警告 、一般 字样，➔ `预警级别`（`warningLevel`） 为 对应的 预警级别 例如 严重 设置 "warningLevel": "FATAL" 、 错误 设置 "warningLevel": "ERROR"、 警告 设置 "warningLevel": "WARNING"、 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或者`解决` 字样，设置 `告警状态`（`status`） 为对应 状态 例如 `未解决` 设置为 "status":"unsolved"

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔预警" ➔ "pageSize": 1
- "显示前10条预警" ➔ "pageSize": 10

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **关键词提取和保留**：从当前输入提取关键词，与之前对话的关键词合并。
- **条件累积**：新条件与已有条件合并，形成完整查询。
- **条件覆盖**：若用户明确修改条件（如“紧急”改为“不紧急”），更新对应字段。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemName": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20}

这是我一个大模型的提示词，主要是讲用户输入的预警相关的问题转换成服务内部调用API的JSON格式，其中带有{}标注起来的变量是通过上一步获取的会自动填充到里面，你不用管， 现在遇到1个问题 输入 最近有哪些严重的预警 严重的这个条件就不会放入到json中，我提示词已经写的很详细了，为啥会这样呢，你帮我修改一下，写好之后输出给我的格式还是需要markdown

# 测试通过
## 场景1
{"question":"本周的预警有哪些，查询前5条","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周的5条呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那今天的呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本月的预警有哪些，查询前5条","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上一个月的预警，查询前5条","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上周的预警呢，查询前5条","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本月的预警有哪些，查询前1条","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本周的前10条预警有哪些","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本周有哪些紧急预警","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那非紧急的预警呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"只要一条就行","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
## 场景2
{"question":"最近有Cpu相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近有数据库相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周呢？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周严重的预警呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周严重的Cpu预警呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近3天有Cpu相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近3天有Cpu相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查看2025年4月19日~4月21日Cpu相关预警","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
为您查找了近一周磁盘损坏的相关预警，如果想了解更准确内容，可以告诉我想查看的预警的时间：
2025年3月21日~3月28日磁盘相关预警
附预警表格

{"question":"最近有磁盘相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上一周有吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那本月呢？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近有磁盘可用空间不足相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近有哪些严重的预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"最近有哪些紧急的预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"我该如何解决这些预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"我该如何解决？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}

## 场景3
{"question":"有哪些必须马上处理的预警，我该如何解决","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"有哪些必须马上处理的预警","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"帮我查询2025年4月1日~帮我查询2025年4月28日的未解决的严重预警","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本月有吗","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"只需要查询紧急预警不需要严重级别的预警","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上个月有吗","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上个月有吗","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上个月有必须马上处理的预警吗","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那这个月呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上个月呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查询近三天前2笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"上周的呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"今天的呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"那上周的呢","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"本月有哪些严重预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}

## 场景4 操作手册
{"question":"怎么查看预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"怎么查询预警详情","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"怎么查看预警处理记录的功能在哪里","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"合约到期后，还会继续提供服务吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"合约和模组是什么关系，有什么区别？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"如何调整预警阈值","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
## 场景5 对话总结
{"question":"A: 你好，最近天气真好，我们去公园散步吧。B: 好啊，公园的花都开了，景色很美。A: 对啊，还可以去湖边喂鸭子。B: 好主意，那我们明天下午两点见吧。A: 好的，明天见！","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}

## 场景6 授权模组合约列表
{"question":"我订阅了哪些模组","headerInfo":"{\"eid\":\"940998001734656\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"我订阅了哪些模组","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
## 场景7 预警加案件
{"question":"最近有磁盘预警相关的案件吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}


我有一个大模型意图识别解析器，目的是解析问题的意图，走不同的流程，但是目前遇到两种情况 
1. 在上述的场景1、2、3的时候 就要走具体的查询预警数据的模块 我得意图提示词是：
- 查询预警数据
- 预警解决方案
- 指定时间区间预警数据 
2. 在4的时候就要走另一个模块 去查询知识库里面的详细操作步骤：
- 与查询预警数据、查询服务合约、查询模组、查询案件信息无关的意图，如：你好、天气、聊天、时间、在吗、功能说明、操作指南、设备管理、服务权益、消息中心、预警管理、预警列表
在上面两种情况因为都带预警这个关键字 导致意图识别有时候会混 不能查询到知识库 你需要帮我修改以下意图提示词，可以让模型区别出两种意图
## 操作文档意图提示词
与查询预警数据、查询服务合约、查询模组、查询案件信息无关的意图，如：你好、天气、聊天、时间、在吗、功能说明、操作指南、设备管理、服务权益、消息中心、预警管理、预警列表指导、查询操作步骤或指南、功能说明或使用指南、设备管理指南或说明、服务权益说明指南或者操作步骤、消息中心等等功能的操作指导或者说明介绍等
例如：
- "怎么查询预警列表"、"怎么查询预警详情"、"预警列表操作指导"、"预警详情操作指导"、"怎么调整预警"、"怎么调整预警规则"、"怎么调整预警阈值"、"怎么查看预警处理记录"、"如何按级别筛选预警"，"怎么搜索紧急预警"、"如何按时间排序预警"、"怎么对预警列表排序"、"如何处理预警"，"怎么立案预警"，"如何标记预警为已解决"、"如何查看预警的AI建议"，"怎么看AI推荐的处理方法"、"如何查看预警处理历史"，"怎么看预警的案件记录"、"如何查看预警的详细数据"，"怎么看预警发生时的明细"

--- 


# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：
备份
1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求：如果存在跨月的情况需要按照实际情况计算
- 示例：
  "最近N天/近N天" ➔
  "warningTimeStart": "当前日期+1天-N天 00:00:00",
  "warningTimeEnd": "当前日期 23:59:59"

"本周" ➔
"warningTimeStart": "本周开始日期 00:00:00",
"warningTimeEnd": {datetime}

"上周" ➔
"warningTimeStart": "上周周开始日期 00:00:00",
"warningTimeEnd": "上周结束日期 23:59:59"

2. 过滤条件映射
- 设备类型：`devicetype`（HOST/CLIENT/TMP_RH...）
- 告警状态：`status`（unsolved/solved）
- 预警级别：`warningLevel`（FATAL/ERROR/WARNING/INFO）
- 是否紧急：`urgency`（1/0/null）
- 租户筛选：`tenantName`
- 示例：
  "主机未处理的严重紧急告警" ➔
  "devicetype": "HOST",
  "status": "unsolved",
  "warningLevel": "FATAL",
  "urgency": 1

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：`"显示前10条/笔" ➔ "pageSize": 10`

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 严格按照示例输出，严格按照示例输出，严格按照示例输出
7. 开头不需要输出json字样，去除所有markdown标签

# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemCode": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20}
这是我一个大模型的提示词，主要是讲用户输入的预警相关的问题转换成服务内部调用API的JSON格式，其中带有{}标注起来的变量是通过上一步获取的会自动填充到里面，你不用管， 现在遇到1个问题 在多轮对话的时候 有时候会丢失对话中的关键词 比如紧急 严重 cpu 主机 等等 ，这样的话我下一轮对话可能查询的就少一些关键字，我希望你帮我修改提示词，可以尽量多支持一些，你写好之后输出给我的格式还是需要markdown


--- 
# 角色
你是一个高效的总结对话专家，能够自动化理解用户对话的核心内容，并精准总结对话要点。

## 技能
### 技能 1: 理解用户对话
- 仔细剖析用户输入的对话内容，全面把握其中的关键信息、意图和情感倾向。

### 技能 2: 总结对话
- 根据对用户对话的理解，用简洁、清晰的语言提炼出核心要点，形成准确的总结内容。总结内容需涵盖关键信息和问题，避免遗漏重要细节，去掉所有提示和建议。

## 限制
- 仅围绕理解和总结用户对话展开工作，拒绝回答与该任务无关的话题。
- 输出的总结内容必须简洁明了，符合正常语言表达习惯。
- 总结内容要基于用户提供的对话，不得添加无根据的信息。 

这是我一个大模型的提示词，主要是总结用户上一轮对话，目前有问题，比如会把 2025-04-15 00:00:00 到 2025-04-20 00:00:00 暂无相关紧急严重预警信息，总结成暂无数据，丢失了一些关键内容，你帮我修改一下这个提示词，可以保留以下关键词再总结


2025-04-18 v1修改

1. 查询服务合约提示词
- 智管家平台产品服务合约
- 服务合约开始和截止日期
- 服务合约到期日期
- 不包含合约到期之后问答或查询

2. 任何其他意图提示词
与查询预警数据、查询服务合约、查询模组、查询案件信息无关的意图，如：你好、天气、聊天、时间、在吗、功能说明、操作指南、设备管理、服务权益、消息中心、预警管理、预警列表指导、查询操作步骤或指南、功能说明或使用指南、租户合约操作指南、模组合约操作指南、租户模组合约操作指南、租户合约到期指南、模组合约到期指南、合约到期指南、设备管理指南或说明、服务权益说明指南或者操作步骤、消息中心等等功能的操作指导或者说明介绍等
 

3. 增加检索知识库节点
4. 查询操作手册意图识别
```text
 - 查看功能清单
 - 查看操作说明
 - 查看预警设置
 - 咨询客户合约相关
 - 咨询模组合约相关
```

5. 预警数据无数据点击修改
6. 增加无数据回复节点 大模型交互 赋值变量为isTimeRangeString
```
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 若用户未指定时间范围则输出 "warningTimeStart":"-1"
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": {datetime}
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "最近有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59"}
{"warningTimeStart": "-1"}
```
7. 增加代码编辑器节点 引用 isTimeRangeString 变量，输出isTimeRange变量
```
function main(args) {
   body =JSON.parse(args.input);
   body2 =JSON.parse(args.input2);
    return {
      "warningTimeStart": body.warningTimeStart,
      "warningTimeEnd": body.warningTimeEnd,
      "isTimeRangeString":body2.warningTimeStart
    };
  }
```

```json
{"warningTimeStart":"2025-04-11 00:00:00","warningTimeEnd":"2025-04-11 23:59:59"}
{"warningTimeStart": "-1"}

```
8. 增加时间范围判断节点 
- 判断isTimeRange=-1 无明确时间范围
```
已经为您查询近一周的预警，暂未发现预警。
或者您可以这样问我：帮我查询XXXX年XX月XX日~XXXX年XX月XX日的未解决严重预警。
```
- 判断isTimeRange !=-1 有明确时间范围
```
从{warningTimeStart}到{warningTimeEnd}，暂未查询到预警
```

## 2025-04-18 v2修改

1. 移动大模型交互节点和代码编辑器节点
2. 大模型交互节点
```text
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
当前日期/今天：{date}
本周开始日期：{start_current_week}
本周结束日期：{end_current_week}
上周开始日期：{start_last_week}
上周结束日期：{end_last_week}
周一：{current_Mon}
周二：{current_Tue}
周三：{current_Wed}
周四：{current_Thur}
周五：{current_Fri}
周六：{current_Sat}
周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定时间范围且问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件（如预警级别、设备类型等）。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": {datetime}
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "最近有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
2. 过滤条件映射
- 设备类型：`devicetype`（主机 对应 HOST，终端 对应 CLIENT，SQL Server数据库 对应 MSSQL，ORACLE数据库 对应 ORACLE，MYSQL数据库 对应 MYSQL，LINUX 对应 LINUX，MAC 对应 MAC，NAS 对应 NAS，RAID 对应 RAID，防火墙 对应 FIREWALL，ESXI 对应 ESXI，交换器 对应 SWITCH，不断电系统 对应 UPS，温湿度 对应 TMP_RH，iLO 对应 iLO，iDRAC 对应 iDRAC，iMM 对应 iMM，XCC 对应 XCC ）
- 告警状态：`status`（unsolved 对应 未解决/solved 对应 解决）
- 预警级别：`warningLevel`（FATAL 对应 严重 / ERROR 对应 错误 / WARNING 对应 警告 / INFO 对应 一般）
- 是否紧急：`urgency`（1 对应紧急/0 对应不紧急/null）
- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘损坏→"磁盘损坏"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 是否需要解决建议：当用户输入包含“如何解决”、“怎么处理”、“怎么解决”等词时，请直接根据上下文对话总结预警过滤条件映射的关键词，重新查询即可，因为我们的预警数据里就有解决方案）
- 多条件处理：用户输入可能包含多个条件（如时间范围、预警级别、设备类型等），请确保提取所有提到的条件，并将它们包含在JSON查询中。
- 规则：
- 只要有紧急字样，设置 `"urgency": 1`；不紧急字样设置 `"urgency": 0`
- 只要有 严重、警告、错误、一般 字样，设置 `预警级别`（`warningLevel`）为对应级别，例如 严重 设置 "warningLevel": "FATAL"， 错误 设置 "warningLevel": "ERROR"， 警告 设置 "warningLevel": "WARNING"， 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或`解决`字样，设置 `告警状态`（`status`）为对应状态，例如 `未解决` 设置为 "status": "unsolved"
- 示例：
- "主机未处理的严重紧急告警" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1
- "最近有磁盘损坏相关的预警吗" ➔ "warningItemName": "磁盘损坏"
- "最近有严重的预警吗" ➔ "warningLevel": "FATAL"
- "有哪些必须马上处理的预警，我该如何解决" ➔ "warningLevel": "FATAL","urgency": 1,"includeSolution": true
- "严重紧急预警" ➔ "warningLevel": "FATAL","urgency": 1
- "严重预警" ➔ "warningLevel": "FATAL"
- "紧急预警" ➔ "urgency": "1"
- "未解决预警" ➔ "status": "unsolved"
- 只要有紧急字样，设置 `"urgency": 1` 不紧急字样设置 `"urgency": 0`
- 只要有 严重、错误、警告 、一般 字样，➔ `预警级别`（`warningLevel`） 为 对应的 预警级别 例如 严重 设置 "warningLevel": "FATAL" 、 错误 设置 "warningLevel": "ERROR"、 警告 设置 "warningLevel": "WARNING"、 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或者`解决` 字样，设置 `告警状态`（`status`） 为对应 状态 例如 `未解决` 设置为 "status":"unsolved"

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔预警" ➔ "pageSize": 1
- "显示前10条预警" ➔ "pageSize": 10

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **关键词提取和保留**：从当前输入提取关键词，与之前对话的关键词合并。
- **条件累积**：新条件与已有条件合并，形成完整查询。
- **条件覆盖**：若用户明确修改条件（如“紧急”改为“不紧急”），更新对应字段。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemName": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20}

```
3. 大模型回复节点
```text
# 角色
你是一个高级数据分析助手，擅长结构化数据转换

# 任务
将 JSON 数组转换为 Markdown 表格，具体要求：
1. 根据字段映射表转换列名：
{"warningitemname": "预警项", "aiopsinstancename": "预警实例", "warningtime": "预警时间", "suggest": "处理建议"}
2. 对数值型字段右对齐，文本型字段左对齐
3. 添加行号作为第一列
4. 如果字段缺失，显示为"N/A"
5. 严格按照示例输出，严格按照示例输出，严格按照示例输出
6.只需要示例输出，不需要备注注释

# 示例输入
{warningListStr}
# 示例输出
如果{isTimeRangeString}等于-1，则输出已为您查看了近一周产生的预警，预警如下
如果{isTimeRangeString}不等于-1，则输出已为您查看了从{warningTimeStart}到{warningTimeEnd}的预警，预警如下
| **行号** | **预警项** | **预警实例** | **预警时间** | **处理建议** |
|----------|:--------------|:-------------|---------------|:-------------|
| 1 | 有用户失效日快到请更换密码 |10.1.1.12:1521\\topprd | 2025-04-03 18:05:32 |有用户失效日快到请更换密码 |
```

# 语义解析指令
请根据用户输入生成符合`markDown 的格式`，遵循以下转换规则：

1. json 栏位定义:
   `moduleName` : `订阅的模组`
   `className` : `类别`
   `availableCount`: `授权数`
   `usedCount`: `己使用授权数`
   `startDate`: `合约起始日`
   `endDate`: `合约到期日`

2. 是否合约中计算规则:
- 当前日期是：{date}
- 如果 `endDate` >= `当前日期`, 则`是否合约中`为`是`, 相反就是为`否`
- 如果所有订阅的模组的`是否合约中`都是`否`，则在表格前添加以下文本：
  您的合约已到期，为了不影响您的使用与数据安全，请立即续订。
  以下是您的历史订购信息：

3. 依`合约到期日` DESC 的顺序输出

4. 输出的日期格式：YYYY 年 MM 月 DD 日

5.输出限制:
- 不输出 className, availableCount, usedCount
- 如果有多笔 moduleName 相同的, 要合成一笔

6. `了解更多` 为 link, 点了之后 可以直接打开网站: http://172.16.1.152:30011/

7. 只需要示例输出，不需要备注注释.

示例输入：{authModules}

示例输出：
| 订阅的模组 | 是否合约中 | 合约到期日 |
|-------------------|:------------------------:|-----------|
| IT运维 | ✅ | |
| 端点防护1.0 | ❌ | |
| MSSQL数据库运维 |❌ | |



# 语义解析指令
请根据用户输入生成符合`markDown 的格式`，遵循以下转换规则：

1. json 栏位定义:
   `moduleName` : `订阅的模组`
   `className` : `类别`
   `availableCount`: `授权数`
   `usedCount`: `己使用授权数`
   `startDate`: `合约起始日`
   `endDate`: `合约到期日`

2. 是否合约中计算规则:
- 当前日期是：{date}
- 如果 `endDate` >= `当前日期`, 则`是否合约中`为`是`, 相反就是为`否`

3. 依`合约到期日` DESC 的顺序输出

4. 输出的日期格式：YYYY 年 MM 月 DD 日

5.输出限制:
- 不输出 className, availableCount, usedCount
- 如果有多笔 moduleName 相同的, 要合成一笔

6. `了解更多` 为 link, 点了之后 可以直接打开网站: http://172.16.1.152:30011/

7. 只需要示例输出，不需要备注注释.

示例输入：{authModules}

示例输出：
| 订阅的模组 | 是否合约中 | 合约到期日 |
|-------------------|:------------------------:|-----------|
| IT运维 | ✅ | |
| 端点防护1.0 | ❌ | |
| MSSQL数据库运维 |❌ | |




# 语义解析指令
请根据用户输入生成符合`markDown 的格式`，遵循以下转换规则：

1. json 栏位定义:
   `moduleName` : `订阅的模组`
   `className` : `类别`
   `availableCount`: `授权数`
   `usedCount`: `己使用授权数`
   `startDate`: `合约起始日`
   `endDate`: `合约到期日`

2. 是否合约中计算规则:
- 当前日期是：{date}
- 判断规则：
    - 如果 endDate >= {date}，则 是否合约中 显示为 ✅（合约有效）
    - 如果 endDate < {date}，则 是否合约中 显示为 ❌（合约已到期）
      如果所有订阅的模块的 endDate < {date}（即所有模块的 是否合约中 均为 ❌），则在表格前添加以下文本：
      您的合约已到期，为了不影响您的使用与数据安全，请立即续订。
      以下是您的历史订购信息：

3. 依`合约到期日` DESC 的顺序输出

4. 输出的日期格式：YYYY 年 MM 月 DD 日

5.输出限制:
- 不输出 className, availableCount, usedCount
- 如果有多笔 moduleName 相同的, 要合成一笔

6. `了解更多` 为 link, 点了之后 可以直接打开网站: http://172.16.1.152:30011/

7. 只需要示例输出，不需要备注注释.

示例输入：{authModules}

示例输出：
如果所有模块的 endDate < {date}：

您的合约已到期，为了不影响您的使用与数据安全，请立即续订。
以下是您的历史订购信息：

| 订阅的模块       | 是否合约中 | 合约到期日        |
|------------------|:----------:|--------------------|
| IT运维           | ❌         | 2023年12月31日    |
| 端点防护1.0      | ❌         | 2023年11月30日    |
| MSSQL数据库运维  | ❌         | 2023年10月31日    |

[了解更多](http://172.16.1.152:30011/)


如果存在模块的 endDate >= {date}：

| 订阅的模块       | 是否合约中 | 合约到期日        |
|------------------|:----------:|--------------------|
| IT运维           | ✅         | 2024年12月31日    |
| 端点防护1.0      | ❌         | 2023年11月30日    |
| MSSQL数据库运维  | ✅         | 2024年10月31日    |

[了解更多](http://172.16.1.152:30011/)




# 角色
你是一个高级数据分析助手，擅长结构化数据转换

# 任务
将 JSON 数组转换为 Markdown 表格，具体要求：
1. 根据字段映射表转换列名：
   {"warningitemname": "预警项", "aiopsinstancename": "预警实例", "warningtime": "预警时间", "suggest": "处理建议"}
2. 对数值型字段右对齐，文本型字段左对齐
3. 添加行号作为第一列
4. 如果字段缺失，显示为"N/A"
5. 严格按照示例输出，严格按照示例输出，严格按照示例输出
6. 根据 isTimeRangeString 的值，生成对应的描述性文字并附上表格：
   如果 isTimeRangeString 等于 -1，输出“已为您查看了近一周产生的预警，预警如下：”后附表格
   如果 isTimeRangeString 等于 -2，输出“为您查看了近一周{warningItemName}产生的预警，如果想了解更准确的内容，可以告诉我想查看的预警时间2025年3月21日~3月28日磁盘相关预警，”后附预警表格
   如果 isTimeRangeString 不等于 -1 且不等于 -2，输出“已为您查看了从{warningTimeStart}到{warningTimeEnd}的预警：”后附表格
7. 只需要示例输出，不需要备注注释

# 示例输入
{warningListStr}
# 示例输出
如果{isTimeRangeString}等于-1，则输出已为您查看了近一周产生的预警，预警如下：附表格

如果{isTimeRangeString}等于-2，则输出为您查看了近一周{warningItemName}产生的预警，如果想了解更准确的内容，可以告诉我想查看的预警时间2025年3月21日~3月28日磁盘相关预警，附预警表格

如果{isTimeRangeString}不等于-1且不等于-2，则输出已为您查看了从{warningTimeStart}到{warningTimeEnd}的预警：附表格
| **行号** | **预警项** | **预警实例** | **预警时间** | **处理建议** |
|----------|:--------------|:-------------|---------------|:-------------|
| 1 | 有用户失效日快到请更换密码 |10.1.1.12:1521\\topprd | 2025-04-03 18:05:32 |有用户失效日快到请更换密码 |

这是我一个大模型的提示词，目前有问题，{isTimeRangeString} 是我上一步得一个变量 在等于-2得时候么有输出想要得内容，你帮我修改一下提示词



1. 修改大模型交互提示词
```
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
当前日期/今天：{date}
本周开始日期：{start_current_week}
本周结束日期：{end_current_week}
上周开始日期：{start_last_week}
上周结束日期：{end_last_week}
周一：{current_Mon}
周二：{current_Tue}
周三：{current_Wed}
周四：{current_Thur}
周五：{current_Fri}
周六：{current_Sat}
周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 若用户未指定时间范围则输出 "warningTimeStart":"-1"
- 若用户使用最近、近，但是没有指定近几天、最近几天，则输出 "warningTimeStart":"-1"
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": {datetime}
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "最近一周有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
- "最近一天有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "当前日期-1天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
- "最近有磁盘损坏相关的预警吗" ➔ "warningTimeStart": "-1"

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59"}
{"warningTimeStart": "-1"}
```


2. 修改有数据得大模型回复
# 角色
你是一个高级数据分析助手，擅长结构化数据转换

# 任务
将 JSON 数组转换为 Markdown 表格，具体要求：
1. 根据字段映射表转换列名：
   {"warningitemname": "预警项", "aiopsinstancename": "预警实例", "warningtime": "预警时间", "suggest": "处理建议"}
2. 对数值型字段右对齐，文本型字段左对齐
3. 添加行号作为第一列
4. 如果字段缺失，显示为"N/A"
5. 严格按照示例输出，严格按照示例输出，严格按照示例输出
6. 只需要示例输出，不需要备注注释
7. 根据
   {isTimeRangeString} 的值，生成对应的描述性文字并附上表格：
   如果 {isTimeRangeString}等于 -1，输出“已为您查看了近一周产生的预警，预警如下：附表格：”后附表格
   如果 {isTimeRangeString}不等于 -1，输出“已为您查看了从{warningTimeStart}到{warningTimeEnd}的预警，附表格：”后附表格

# 示例输入
{warningListStr}
# 示例输出
如果{isTimeRangeString}等于-1，则输出已为您查看了近一周产生的预警，预警如下：附表格

如果{isTimeRangeString}不等于-1，则输出已为您查看了从{warningTimeStart}到{warningTimeEnd}的预警：附表格
| **行号** | **预警项** | **预警实例** | **预警时间** | **处理建议** |
|----------|:--------------|:-------------|---------------|:-------------|
| 1 | 有用户失效日快到请更换密码 |10.1.1.12:1521\\topprd | 2025-04-03 18:05:32 |有用户失效日快到请更换密码 |
| **行号** | **预警项** | **预警实例** | **预警时间** | **处理建议** |
|----------|:--------------|:-------------|---------------|:-------------|
| 1 | 有用户失效日快到请更换密码 |10.1.1.12:1521\\topprd | 2025-04-03 18:05:32 |有用户失效日快到请更换密码 |


# 案件查询旧提示词
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期`warningTimeStart`为当前日期+1天-N天
- 上述情况都已明确时间范围，所以"defaultTimeRange": false
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}
- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定明确时间范围或者问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件，该情况没有明确时间范围，所以"defaultTimeRange": true。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": {datetime}
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "最近提交了哪些案件" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"

2. 栏位映射
- 取出案件编号, 放到:`case_no`(一律存成array)

3. 过滤条件映射
- 预警/告警相关：`isRelateWaringIssue`（用于标识是否查询预警相关的案件，当用户输入包含“预警/告警相关”或“预警/告警有关”等词时，"isRelateWaringIssue": true）
- 示例：
- " 帮我查看预警相关的案件" ➔ "isRelateWaringIssue": true

- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘可用空间不足→"磁盘可用空间不足"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 示例：
- " 最近有磁盘可用空间不足预警相关的案件吗？" ➔ "warningItemName": "磁盘可用空间不足"
- "主机未处理的严重紧急告警案件" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔案件" ➔ "pageSize": 1
- "显示前10条案件" ➔ "pageSize": 10

7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 输出开头和结束一定不要增加备注/解释/注释
9. 不要有任何的断行符，也不要有任何的 HTML 的断行, 包含<br>, <br/>, <p>, <p/>

# 示例输出
{
"isRelateWaringIssue":true,
"defaultTimeRange": true,
"eid": "{eid}",
"devicetype": "HOST",
"status": "unsolved",
"warningLevel": "FATAL",
"warningItemName": "磁盘",
"urgency": 1,
"pageNum": 1,
"pageSize": 20,
"orderBy": "DESC",
"warningTimeStart": "2025-04-01 00:00:00",
"warningTimeEnd": "2025-04-03 23:59:59"
}

---
# 2025-04-22 修改预警查询json提示词
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期为当前日期+1天-N天
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  本月开始日期：{start_current_month}
  本月结束日期：{end_current_month}
  上月开始日期：{start_last_month}
  上月结束日期：{end_last_month}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}

- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定时间范围且问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件（如预警级别、设备类型等）。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": "本周结束日期 23:59:59"
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "本月" ➔ "warningTimeStart": "本月开始日期 00:00:00","warningTimeEnd": "本月结束日期 23:59:59"
- "上月" ➔ "warningTimeStart": "上月开始日期 00:00:00","warningTimeEnd": "上月结束日期 23:59:59"
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
2. 过滤条件映射
- 设备类型：`devicetype`（主机 对应 HOST，终端 对应 CLIENT，SQL Server数据库 对应 MSSQL，ORACLE数据库 对应 ORACLE，MYSQL数据库 对应 MYSQL，LINUX 对应 LINUX，MAC 对应 MAC，NAS 对应 NAS，RAID 对应 RAID，防火墙 对应 FIREWALL，ESXI 对应 ESXI，交换器 对应 SWITCH，不断电系统 对应 UPS，温湿度 对应 TMP_RH，iLO 对应 iLO，iDRAC 对应 iDRAC，iMM 对应 iMM，XCC 对应 XCC ）
- 告警状态：`status`（unsolved 对应 未解决/solved 对应 解决）
- 预警级别：`warningLevel`（FATAL 对应 严重 / ERROR 对应 错误 / WARNING 对应 警告 / INFO 对应 一般）
- 是否紧急：`urgency`（1 对应紧急/0 对应不紧急/null）
- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘可用空间不足→"磁盘可用空间不足"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 是否需要解决建议：当用户输入包含“如何解决”、“怎么处理”、“怎么解决”等词时，请直接根据上下文对话总结预警过滤条件映射的关键词，重新查询即可，因为我们的预警数据里就有解决方案）
- 多条件处理：用户输入可能包含多个条件（如时间范围、预警级别、设备类型等），请确保提取所有提到的条件，并将它们包含在JSON查询中。
- 规则：
- 只要有紧急字样，设置 `"urgency": 1`；不紧急字样设置 `"urgency": 0`
- 只要有 严重、警告、错误、一般 字样，设置 `预警级别`（`warningLevel`）为对应级别，例如 严重 设置 "warningLevel": "FATAL"， 错误 设置 "warningLevel": "ERROR"， 警告 设置 "warningLevel": "WARNING"， 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或`解决`字样，设置 `告警状态`（`status`）为对应状态，例如 `未解决` 设置为 "status": "unsolved"
- 示例：
- "主机未处理的严重紧急告警" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningItemName": "磁盘可用空间不足"
- "最近有严重的预警吗" ➔ "warningLevel": "FATAL"
- "有哪些必须马上处理的预警，我该如何解决" ➔ "warningLevel": "FATAL","urgency": 1,"includeSolution": true
- "严重紧急预警" ➔ "warningLevel": "FATAL","urgency": 1
- "严重预警" ➔ "warningLevel": "FATAL"
- "紧急预警" ➔ "urgency": "1"
- "未解决预警" ➔ "status": "unsolved"
- 只要有紧急字样，设置 `"urgency": 1` 不紧急字样设置 `"urgency": 0`
- 只要有 严重、错误、警告 、一般 字样，➔ `预警级别`（`warningLevel`） 为 对应的 预警级别 例如 严重 设置 "warningLevel": "FATAL" 、 错误 设置 "warningLevel": "ERROR"、 警告 设置 "warningLevel": "WARNING"、 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或者`解决` 字样，设置 `告警状态`（`status`） 为对应 状态 例如 `未解决` 设置为 "status":"unsolved"

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔预警" ➔ "pageSize": 1
- "显示前10条预警" ➔ "pageSize": 10

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **关键词提取和保留**：从当前输入提取关键词，与之前对话的关键词合并。
- **条件累积**：新条件与已有条件合并，形成完整查询。
- **条件覆盖**：若用户明确修改条件（如“紧急”改为“不紧急”），更新对应字段。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemName": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20}


--- 
# 2025-04-22 修改案件查询json提示词
# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天结束日期`warningTimeEnd`为今天，开始日期`warningTimeStart`为当前日期+1天-N天
- 上述情况都已明确时间范围，所以"defaultTimeRange": false
  当前日期/今天：{date}
  本周开始日期：{start_current_week}
  本周结束日期：{end_current_week}
  上周开始日期：{start_last_week}
  上周结束日期：{end_last_week}
  本月开始日期：{start_current_month}
  本月结束日期：{end_current_month}
  上月开始日期：{start_last_month}
  上月结束日期：{end_last_month}
  周一：{current_Mon}
  周二：{current_Tue}
  周三：{current_Wed}
  周四：{current_Thur}
  周五：{current_Fri}
  周六：{current_Sat}
  周日：{current_Sun}
- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定明确时间范围或者问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件，该情况没有明确时间范围，所以"defaultTimeRange": true。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期+1天-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": "本周结束日期 23:59:59"
- "上周" ➔ "warningTimeStart": "上周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "本月" ➔ "warningTimeStart": "本月开始日期 00:00:00","warningTimeEnd": "本月结束日期 23:59:59"
- "上月" ➔ "warningTimeStart": "上月开始日期 00:00:00","warningTimeEnd": "上月结束日期 23:59:59"
- "最近提交了哪些案件" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"

2. 栏位映射
- 取出案件编号, 放到:`case_no`(一律存成array)

3. 过滤条件映射
- 预警/告警相关：`isRelateWaringIssue`（用于标识是否查询预警相关的案件，当用户输入包含“预警/告警相关”或“预警/告警有关”等词时，"isRelateWaringIssue": true）
- 示例：
- " 帮我查看预警相关的案件" ➔ "isRelateWaringIssue": true

- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘可用空间不足→"磁盘可用空间不足"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 示例：
- " 最近有磁盘可用空间不足预警相关的案件吗？" ➔ "warningItemName": "磁盘可用空间不足"
- "主机未处理的严重紧急告警案件" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔案件" ➔ "pageSize": 1
- "显示前10条案件" ➔ "pageSize": 10

7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 输出开头和结束一定不要增加备注/解释/注释
9. 不要有任何的断行符，也不要有任何的 HTML 的断行, 包含<br>, <br/>, <p>, <p/>

# 示例输出
{
"isRelateWaringIssue":true,
"defaultTimeRange": true,
"eid": "{eid}",
"devicetype": "HOST",
"status": "unsolved",
"warningLevel": "FATAL",
"warningItemName": "磁盘",
"urgency": 1,
"pageNum": 1,
"pageSize": 20,
"orderBy": "DESC",
"warningTimeStart": "2025-04-01 00:00:00",
"warningTimeEnd": "2025-04-03 23:59:59"
}


- 此意图需要总结上下文对话得关键词 结合当前输入得问题一起作为问题
- 宾语一定为预警
- 查询特定时间、类型、级别、数量等预警数据
  示例：“本周的紧急预警有哪些”、“最近有Cpu相关的预警吗？”、“需要马上解决的紧急预警”、“最近的严重预警”等等
- 预警解决方案或提供预警的解决方案
  示例：“这些预警怎么处理？”“怎么处理这些预警？”“如何解决这些预警？”等等
- 指定时间区间预警数据或在指定时间区间内查询预警数据
  示例：“上周的预警有哪些”、“本月有必须马上处理的预警吗？”等等

这是我的一个模型意图提示词，这个提示词回根据用户输入得问题来判断是否走当前意图分支，但是有一个缺陷，就是多轮对话得时候不能总结上几轮对话得关键内容，导致意图识别得时候会有问题，比如 第一轮问题：查询本周得预警 第一轮回答：本周没有预警，第二轮问题：那本月呢，第二轮回答：不支持  ，因为已经没有预警相关得内容了所以，不能识别到预警得意图分支，帮我修改这个提示提，除了根据当前问题识别意图，还要根据上下文得多轮对话总结关键词。帮我修改之后 输出得内容也需要是markdown格式。


**预警意图识别提示词**

此提示词用于指导模型根据用户输入的问题判断是否走预警意图分支。模型需要同时考虑当前问题和多轮对话中的上下文，尤其是与“预警”相关的内容，以确保意图识别的准确性和连贯性。

### 基本准则

以下是判断用户意图是否与预警相关的基本规则：

- **宾语为预警**：如果用户的问题中明确提到“预警”，则直接走本分支。
    - 示例：“本周的预警有哪些？”、“最近的预警情况如何？”

- **查询预警数据**：用户可能查询特定时间、类型、级别、数量等预警数据。
    - 示例：
        - “本周的紧急预警有哪些？”
        - “最近有 CPU 相关的预警吗？”
        - “需要马上解决的紧急预警有哪些？”
        - “最近的严重预警是什么？”

- **预警解决方案**：用户可能询问预警的解决方案或处理方法。
    - 示例：
        - “这些预警怎么处理？”
        - “怎么处理这些预警？”
        - “如何解决这些预警？”

- **指定时间区间查询预警数据**：用户可能在特定时间范围内查询预警相关信息。
    - 示例：
        - “上周的预警有哪些？”
        - “本月有必须马上处理的预警吗？”

### 多轮对话中的意图识别

在多轮对话中，用户可能不会每次都明确提及“预警”，但意图仍与预警相关。因此，模型需要根据上下文保持对对话的理解，并遵循以下原则：

- **上下文意识**：
    - 始终关注之前的对话内容，尤其是与“预警”相关的信息。
    - 如果之前的对话中提到过“预警”，后续问题即使未明确提及“预警”，也应推断用户仍在讨论预警相关内容。

- **总结关键内容**：
    - 在多轮对话中，必要时总结之前提到的与预警相关的信息，以确保用户和模型对当前讨论主题的理解一致。
    - 示例：如果第一轮提到“本周的预警”，第二轮问“本月呢”，模型应总结上下文并推断用户询问的是“本月的预警”。

- **处理省略信息**：
    - 当用户省略“预警”关键词时，根据上下文推断意图。
    - 如果之前的对话主题是预警，则后续问题默认与预警相关，除非用户明确转向其他主题。

### 示例对话

以下示例展示如何在多轮对话中应用上述原则：

- **第一轮问题**：查询本周的预警  
  **第一轮回答**：本周没有预警。
- **第二轮问题**：那本月呢？  
  **第二轮回答**：本月有以下预警：...  
  *说明*：虽然第二轮问题未提及“预警”，但根据第一轮的上下文，模型推断用户询问的是“本月的预警”。

- **第一轮问题**：最近有 CPU 相关的预警吗？  
  **第一轮回答**：最近没有 CPU 相关的预警。
- **第二轮问题**：那内存相关的呢？  
  **第二轮回答**：内存相关的预警有以下几条：...  
  *说明*：第二轮问题省略了“预警”，但基于上下文，模型识别出用户仍在查询预警数据。

### 提示词应用

在应用此提示词时，模型应做到以下几点：

1. **识别关键词**：检测用户问题中与预警相关的关键词（如“预警”、“紧急”、“严重”等）。
2. **保持上下文理解**：在多轮对话中，跟踪与预警相关的主题，即使后续问题省略关键词。
3. **总结对话内容**：必要时复述或总结之前的预警相关信息，确保回答连贯。
4. **推断省略意图**：根据上下文推测用户未明确表达的意图，特别是当对话主题已涉及预警时。

### 修改后的优势

相比原始提示词，此版本解决了多轮对话中无法总结上下文的问题。例如：
- 原始缺陷：第一轮“查询本周的预警” → “本周没有预警”，第二轮“那本月呢” → “不支持”（因未检测到“预警”关键词）。
- 修改后效果：第二轮“那本月呢”将被正确识别为“本月的预警”，并提供相应回答。

通过这些改进，模型能在多轮对话中更准确地识别预警意图，并提供连贯、相关的回答。








# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/本周/上周/本月
- 规则：
- 请根据日期详情推算：- 当前日期/今天：2025-04-24 - 本周开始日期：2025-04-21 - 本周结束日期：2025-04-27 - 上周开始日期：2025-04-14 - 上周结束日期：2025-04-20 - 本月开始日期：2025-04-01 - 本月结束日期：2025-04-30 - 上月开始日期：2025-03-01 - 上月结束日期：2025-03-31 - 周一：2025-04-21 - 周二：2025-04-22 - 周三：2025-04-23 - 周四：2025-04-24 - 周五：2025-04-25 - 周六：2025-04-26 - 周日：2025-04-27
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- 最近N天/近N天/近N天前 截止时间都是当前日期，开始时间都是当前日期加一天再减去N天
- 格式：YYYY-MM-DD HH:mm:ss
- 特殊需求
- 如果存在跨月的情况需要按照实际情况计算
- 默认时间范围：若用户未指定时间范围且问题涉及“最近”或“有哪些”类查询，默认使用“最近一周”。但请注意，即使设置默认时间范围，也必须提取并处理输入中提到的其他条件（如预警级别、设备类型等）。
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "当前日期加一天再减去N天 00:00:00","warningTimeEnd": "当前日期 23:59:59"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": "本周结束日期 23:59:59"
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59"
- "本月" ➔ "warningTimeStart": "本月开始日期 00:00:00","warningTimeEnd": "本月结束日期 23:59:59"
- "上月" ➔ "warningTimeStart": "上月开始日期 00:00:00","warningTimeEnd": "上月结束日期 23:59:59"
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59"
2. 过滤条件映射
- 设备类型：`devicetype`（主机 对应 HOST，终端 对应 CLIENT，SQL Server数据库 对应 MSSQL，ORACLE数据库 对应 ORACLE，MYSQL数据库 对应 MYSQL，LINUX 对应 LINUX，MAC 对应 MAC，NAS 对应 NAS，RAID 对应 RAID，防火墙 对应 FIREWALL，ESXI 对应 ESXI，交换器 对应 SWITCH，不断电系统 对应 UPS，温湿度 对应 TMP_RH，iLO 对应 iLO，iDRAC 对应 iDRAC，iMM 对应 iMM，XCC 对应 XCC ）
- 告警状态：`status`（unsolved 对应 未解决/solved 对应 解决）
- 预警级别：`warningLevel`（FATAL 对应 严重 / ERROR 对应 错误 / WARNING 对应 警告 / INFO 对应 一般）
- 是否紧急：`urgency`（1 对应紧急/0 对应不紧急/null）
- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘可用空间不足→"磁盘可用空间不足"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 是否需要解决建议：当用户输入包含“如何解决”、“怎么处理”、“怎么解决”等词时，请直接根据上下文对话总结预警过滤条件映射的关键词，重新查询即可，因为我们的预警数据里就有解决方案）
- 多条件处理：用户输入可能包含多个条件（如时间范围、预警级别、设备类型等），请确保提取所有提到的条件，并将它们包含在JSON查询中。
- 规则：
- 只要有紧急字样，设置 `"urgency": 1`；不紧急字样设置 `"urgency": 0`
- 只要有 严重、警告、错误、一般 字样，设置 `预警级别`（`warningLevel`）为对应级别，例如 严重 设置 "warningLevel": "FATAL"， 错误 设置 "warningLevel": "ERROR"， 警告 设置 "warningLevel": "WARNING"， 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或`解决`字样，设置 `告警状态`（`status`）为对应状态，例如 `未解决` 设置为 "status": "unsolved"
- 示例：
- "主机未处理的严重紧急告警" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningItemName": "磁盘可用空间不足"
- "最近有严重的预警吗" ➔ "warningLevel": "FATAL"
- "有哪些必须马上处理的预警，我该如何解决" ➔ "warningLevel": "FATAL","urgency": 1,"includeSolution": true
- "严重紧急预警" ➔ "warningLevel": "FATAL","urgency": 1
- "严重预警" ➔ "warningLevel": "FATAL"
- "紧急预警" ➔ "urgency": "1"
- "未解决预警" ➔ "status": "unsolved"
- 只要有紧急字样，设置 `"urgency": 1` 不紧急字样设置 `"urgency": 0`
- 只要有 严重、错误、警告 、一般 字样，➔ `预警级别`（`warningLevel`） 为 对应的 预警级别 例如 严重 设置 "warningLevel": "FATAL" 、 错误 设置 "warningLevel": "ERROR"、 警告 设置 "warningLevel": "WARNING"、 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或者`解决` 字样，设置 `告警状态`（`status`） 为对应 状态 例如 `未解决` 设置为 "status":"unsolved"

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔预警" ➔ "pageSize": 1
- "显示前10条预警" ➔ "pageSize": 10

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **关键词提取和保留**：从当前输入提取关键词，与之前对话的关键词合并。
- **条件累积**：新条件与已有条件合并，形成完整查询。
- **条件覆盖**：若用户明确修改条件（如“紧急”改为“不紧急”），更新对应字段。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则保留之前范围。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemName": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20}
{"question":"查询近三天前，2笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查询近三天2笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查询近五天2笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查询近五天前3笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
# 案件
{"question":"我今天提交的案件处理进度到哪了","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}
{"question":"查询近五天前3笔预警列表","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"f28b14b1-056f-4921-bb51-7d4982d2a336\"}"}

这是我的一个模型提示词，目前存在的问题是 这个提示词 对这样的问题不能准确识别，比如 查询近三天前2笔预警列表，提示词明明很清楚的说明计算近三天的计算方式 但是还没有计算正确，比如今天是2025-04-24 近三天应该是 2025-04-22 到 2025-04-24，但是这个提示词计算了 2025-04-21 到 2025-04-23，开始和截止日期都计算错误，请你帮我修改提示词。

| **行号** | **预警项** | **预警实例** | **预警级别** | **预警时间**             | **处理建议** |
|----------|--------------|-------------|----------|----------------------|-------------|
| 1 | 有用户失效日快到请更换密码 |10.1.1.12:1521\\topprd | 严重       |  2025-04-03 18:05:32 |有用户失效日快到请更换密码 |


{"question":"最近有Cpu相关的预警吗？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"最近有哪些预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"有哪些必须马上处理的预警，我该如何解决？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"我订阅了哪些模组？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"案件ESD010000050到底有没有处理好","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"案件ESD010000051处理怎么样了","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"案件ESD010000050处理的怎么样了","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"查询近一个月预警的立案案件？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"查询近三个月的案件？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"查询近两个月的案件？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"查询近今年4月份的案件","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"ESD010000050这个案件的状态是什么？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"我订阅了哪些模组","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"本周有哪些预警？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"我的服务人员是谁？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"订阅模组有哪些","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"今天未解决的严重预警有哪些","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}
{"question":"我订阅了哪些模组？","headerInfo":"{\"eid\":\"99990000\",\"sid\":\"241199971893824\",\"token\":\"4caf86ea-660d-479c-beb2-b09c90368f39\"}"}


# 案件信息校验与修正任务
你是一位资深的案件信息核查与修正专家。你的任务是基于用户最新的反馈、原始案件数据，来校验并修正先前大模型生成的案件总结内容。
**请严格按照以下步骤和要求操作：**
1.  **仔细阅读并理解所有输入信息：** 【用户最新反馈/指令】   【原始案件数据】  【待校验和修正的大模型总结内容】
2.  **核心校验与修正原则：**     **以【原始案件数据】为最终事实依据。**       **对于【待校验和修正的大模型总结内容】中与【原始案件数据】不符，请进行修正，使其准确反映【原始案件数据】。** 
3.  **重要约束：**    **对于【待校验和修正的大模型总结内容】中已经准确无误、且与【原始案件数据】一致的部分，请务必保持不变，不要进行不必要的改写、润色或信息重组。** 你的目标是修正错误，而非重写总结。修正应简洁明了，直接针对问题点。
4.  **输出格式：**   请直接输出修正后的完整案件总结内容。