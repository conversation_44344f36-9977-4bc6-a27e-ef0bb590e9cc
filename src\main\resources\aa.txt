偉勝 2023/11/7 14:59:29
腳本邏輯
1.透過深信服地址({{sxfAddress}})取的公鑰
2.透過地端ras實用接口，取得密碼 <-需開發地端
3.透過深信服地址({{sxfAddress}})及用戶名({{sxfUserName}})及第2步取得的密碼，獲取Token({{sxfToken}})
發送器邏輯
4.透過發送器儲存Token({{sxfToken}})到文件參數
5.刷新地端執行參數緩存

偉勝 2023/11/7 14:59:45
{{sxfAddress}}:深信服地址
{{sxfUserName}}:用戶名
{{sxfKey}}:金鑰

偉勝 2023/11/7 15:03:26
參考576373215154752(T100BasicApplicaDiagnosisLastModificaTime        T100基础应用诊断最后修改时间)收集項
的sendersContent:
[{"file_partition":"0","file_send_path":"{{file_params_dir}}/basicApplicaDiaLastModTime.conf","sender_type":"file_v2","file_encrypt_result":"true","file_is_append":"false","file_write_raw":"false"},{"http_sender_csv_split":",","http_sender_use_proxy":"false","http_sender_protocol":"body_json","http_sender_package_template":"","http_sender_upload_data_model_code":"EmptyCollected","http_sender_collect_config_id":"576373215154752","http_sender_timeout":"60s","http_sender_fixed_sending_data":"","ft_retry_count_before_discard":"6","http_sender_other_headers":"","ft_memory_channel":"false","ft_strategy":"backup_only","max_size_per_file":"104857600","http_sender_quote_package_data":"false","http_sender_gzip":"false","sender_type":"http_v2","runner_name":"T100BasicApplicaDiagnosisLastModificaTime.577352480256577","http_sender_parent_package_template":"","http_sender_data_batch_send_size":"300","http_sender_send_data_to_string":"false","http_sender_url":"{{aiopskit_local_url}}/aiopskit/refresh/basicApplicaDiaLastModTime.conf/cache","ft_discard_failed_data":"false","ft_long_data_discard":"false","http_sender_encrypt_self_data":"false","max_disk_used_bytes":"524288000","http_sender_escape_html":"false"}]


2023-12-18 中午(12:00) 申请发版到台湾正式环境 发版请求已发出
涉及项目：aioitms
涉及分支：Release_aiops_3.6.6_1.29
涉及环境：台湾正式区
修改内容：1.修复生成报告报错问题
影响范围：体检中心-报告生成
测试人员：莹莹


6340186688] by path[C:\ProgramData\digiwin\aiopskit\.aiopskitconfs\TestCC2.671486340186688.conf]



1.首先小明需要明确自己想要提供哪些数据，这些数据如何被查询及使用，以及谁可以访问这些API。
2.利用IPaaS平台的工具设计API，定义请求和响应的格式。这通常包括HTTP方法、路径（URL）、参数、以及返回的数据结构。
3.在IPaaS平台上配置数据库连接，将API与后端的SQL数据库整合起来。
4.在开发过程中，进行API的测试，确保查询结果正确，并且处理好错误情况。


INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (729636528575040, 241199971893824, 99990000, 'AIEOM', 'tenant_CSO_model_100', 'CSO报价速立得高潜客户', 578475709559360, 728903607669312, '模型1:CSO报价速立得高潜客户:选配商品型号复杂
且 报价周期长
且 主推行业 =裝備
且 直销模式
且 业务人员多', 'STRING', 'AUTO', 1, 'DAY', '23:56:57', '2024-06-16 23:57:00', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'NO_NEED', 1051, 76052544950848, '林文絹', '2024-06-03 19:24:33', '2024-05-23 17:38:38', '2024-06-16 23:57:00', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (729646154027584, 241199971893824, 99990000, 'AIEOM', 'tenant_CSO_model_200', 'CSO订货易点通高潜客户', 578475709559360, 728903607669312, '模型:CSO订货易点通高潜客户 (標準商品型号复杂且 下单耗时长且 主推行业且 直销模式且 业务人员多)', 'STRING', 'AUTO', 1, 'DAY', '23:57:27', '2024-06-16 23:57:30', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'NO_NEED', 1055, 76052544950848, '林文絹', '2024-06-03 19:31:04', '2024-05-23 18:17:48', '2024-06-16 23:57:30', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (731432248885824, 241199971893824, 99990000, 'AIEOM', 'tenant_CSO_model_100_01', 'CSO报价速立得痛点分布', 578475709559360, 728903607669312, '模型1:CSO报价速立得高潜客户:选配商品型号复杂
且 报价周期长
且 主推行业 不包含半導體
且 直销模式
且 业务人员多', 'STRING', 'AUTO', 1, 'DAY', '23:40:36', '2024-06-16 23:40:40', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 1056, 186747464151616, '张慧娟', '2024-06-03 19:39:04', '2024-05-28 19:25:26', '2024-06-16 23:40:41', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (731433121329728, 241199971893824, 99990000, 'AIEOM', 'tenant_CSO_model_200_01', 'CSO订货易点通痛点分布', 578475709559360, 728903607669312, '模型:CSO订货易点通高潜客户 (標準商品型号复杂且 下单耗时长且 主推行业((ALL)且 直销模式且 业务人员多)', 'STRING', 'AUTO', 1, 'DAY', '23:50:14', '2024-06-16 23:50:18', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 1057, 186747464151616, '张慧娟', '2024-06-03 19:39:07', '2024-05-28 19:28:59', '2024-06-16 23:50:18', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (729520260567616, 241199971893824, 99990000, 'AIEOM', 'tenant_CHO_model_001', 'CHO人资领域高潜客户', 578475709559360, 728903781265984, '模型:CHO人资领域高潜客户', 'STRING', 'AUTO', 1, 'DAY', '23:55:12', '2024-06-16 23:55:18', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'NO_NEED', 1065, 76052544950848, '林文絹', '2024-05-29 10:22:46', '2024-05-23 09:45:32', '2024-06-16 23:55:18', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (731434398249536, 241199971893824, 99990000, 'AIEOM', 'tenant_CHO_model_001_001', 'CHO人资领域痛点分布', 578475709559360, 728903781265984, '', 'STRING', 'AUTO', 1, 'DAY', '23:40:59', '2024-06-16 23:41:03', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 1064, 186747464151616, '张慧娟', '2024-05-28 19:34:22', '2024-05-28 19:34:11', '2024-06-16 23:41:03', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (729270513226304, 241199971893824, 99990000, 'AIEOM', 'tenant_model_gphb_hpc', 'CFO集团合报痛点分布', 578475709559360, 728903420838464, 'CFO模型:【合报需求频次高】且【合报产出速度慢 或 合报合规披露严】', 'STRING', 'AUTO', 1, 'DAY', '23:40:38', '2024-06-16 23:40:42', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 1021, 76052544950848, '林文絹', '2024-05-28 19:42:55', '2024-05-22 16:49:18', '2024-06-16 23:40:42', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (731424753066560, 241199971893824, 99990000, 'AIEOM', 'tenant_model_gphb_hpc0', 'CFO集团合报高潜客户', 578475709559360, 728903420838464, 'CFO模型:【合报需求频次高】且【合报产出速度慢 或 合报合规披露严】', 'STRING', 'AUTO', 1, 'DAY', '23:58:48', '2024-06-16 23:58:51', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'NO_NEED', 1045, 186747464151616, '张慧娟', '2024-05-28 19:44:35', '2024-05-28 18:54:56', '2024-06-16 23:58:51', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (732017592111680, 241199971893824, 99990000, 'AIEOM', 'CXO_model_001', '快筛CXO高潜客户群痛点分布', 578475709559360, 728903420838464, '可看 CHO人资领域高潜客户+CFO集团合报领域高潜客户+CSO报价速立得高潜客户+CSO订货易点通高潜客户  及CXO(四個條件都符合)快篩客群分佈', 'STRING', 'AUTO', 1, 'DAY', '23:41:59', '2024-06-16 23:42:03', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 1074, 76052544950848, '林文絹', '2024-06-04 15:11:53', '2024-05-30 11:07:12', '2024-06-16 23:42:03', 0, 0, 0);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero) VALUES (732102633046592, 241199971893824, 99990000, 'AIEOM', 'CXO_model_002', '快筛CXO高潜客户群', 578475709559360, 728903267775040, '客戶具備符合CHO人资领域高潜客户+CFO集团合报领域高潜客户+CSO报价速立得高潜客户+CSO订货易点通高潜客户 快篩客群', 'STRING', 'AUTO', 1, 'DAY', '23:40:49', '2024-06-16 23:40:52', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'NO_NEED', 1077, 76052544950848, '林文絹', '2024-06-04 15:11:48', '2024-05-30 16:53:14', '2024-06-16 23:40:52', 0, 0, 0);





