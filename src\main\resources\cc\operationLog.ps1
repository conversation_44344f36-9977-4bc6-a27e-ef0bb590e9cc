function IpAndPort{
#    return "{{http_service_address}}"
        return "https://************:4435"
}






function QueryAzs {
    # 调用函数并获取返回值
#    $token = '{{sxfToken}}'
    $token = 'abcfe49a5b994b969d2f8d9a1384f558'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $startDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd 00:00:00')
    $endDate=(Get-Date).AddDays(0).ToString('yyyy-MM-dd 00:00:00')
    $myArray = New-Object System.Collections.ArrayList
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/tasks?page_num=$next_page_num&page_size=1000&sort=begin_time&order_by=DESC&begin_time=$startDate&end_time=$endDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            foreach ($item in $jsonObject.data.data) {
                $item | Add-Member -MemberType NoteProperty -Name "sxf_log_status" -Value $item.status -Force
            }
            $myArray.AddRange($jsonObject.data.data)
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}

$aa=QueryAzs
$result = Write-Output $aa | ConvertTo-Json
Write-Output $result



function IpAndPort{
    return "{{http_service_address}}"
    #        return "https://************:4435"
}

function UserNameGet{
    #        return "admin"
    return "{{http_basic_login_id}}"
}

function PassGet{
    #        return "#1TXUo6aK6"
    return "{{http_basic_login_pwd}}"
}

function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function QueryAzs {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    #    $token = 'ff336f7941df4d4385ed509eb06bef8a'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $startDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd 00:00:00')
    $endDate=(Get-Date).AddDays(0).ToString('yyyy-MM-dd 00:00:00')
    $myArray = New-Object System.Collections.ArrayList
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/tasks?page_num=$next_page_num&page_size=1000&sort=begin_time&order_by=DESC&begin_time=$startDate&end_time=$endDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            foreach ($item in $jsonObject.data.data) {
                $item | Add-Member -MemberType NoteProperty -Name "sxf_log_status" -Value $item.status -Force
            }
            $myArray.AddRange($jsonObject.data.data)
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}

$aa=QueryAzs
$result = Write-Output $aa | ConvertTo-Json
$jsonBytes = [System.Text.Encoding]::UTF8.GetBytes($result)
$jsonUTF8 = [System.Text.Encoding]::UTF8.GetString($jsonBytes)
Write-Output $jsonUTF8