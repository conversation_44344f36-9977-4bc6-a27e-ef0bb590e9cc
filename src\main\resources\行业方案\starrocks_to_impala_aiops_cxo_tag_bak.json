{"job": {"content": [{"writer": {"parameter": {"partitionEnabled": false, "writeMode": "truncate", "fieldDelimiter": "\t", "column": [{"type": "STRING", "name": "eid"}, {"type": "TIMESTAMP", "name": "caldatetime"}, {"type": "STRING", "name": "cxotype"}, {"type": "STRING", "name": "cxotag"}, {"type": "STRING", "name": "cxochildtag"}, {"type": "INT", "name": "cxoscore"}], "path": "/user/hive/warehouse/tbb.db/aiops_cxo_tag", "fileType": "text", "defaultFS": "hdfs://ddp1:8020", "fileName": "aiops_cxo_tag"}, "name": "hdfswriter"}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , '' as cxochildtag, chotag01score * 0.5 + managertag01score * 0.3 + hrtag01score * 0.2 as cxoscore from (select eid , ifnull(f_highEmploymentRisk,0) * 100 as chotag01score, ifnull(f_contractRiskControlDifficult,0) * 20 + ifnull(f_multipleInspectionInstitutionsLowPassRate,0) * 60 +ifnull(f_overseasCompRisk,0) * 20 as managertag01score, ifnull(f_paperDocCtrlDiff,0) * 100 + ifnull(f_factoryAuditLow,0) * 60 + ifnull(f_paySlipConfidLow,0) * 5 as hrtag01score from servicecloud.CHORoleSurvey ) as tag001 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype,'人工成本高' as cxotag , '' as cxochildtag, chotag02score * 0.5 + managertag02score * 0.3 + hrtag02score * 0.2 as cxoscore from (select eid , ifnull(f_highLaborCost,0) * 100 as chotag02score, ifnull(f_staffControlDiff,0) * 30 + ifnull(f_budgetControlDiff,0) * 30 + ifnull(f_OTHoursVerifyDiff,0) * 40 as managertag02score, ifnull(f_leaveCtrlDiff ,0)* 50 + ifnull(f_OTHoursVerifyDiff ,0)* 50 as hrtag02score from servicecloud.CHORoleSurvey ) as tag002 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , '' as cxochildtag, chotag03score * 0.5 + managertag03score * 0.3 + hrtag03score * 0.2 as cxoscore from (select eid , ifnull(f_lowEfficiencyHR,0) * 100 as chotag03score, ifnull(f_payrollPeriodLong ,0)* 50 + ifnull(f_HRSalaryCycleLong,0) * 50 as managertag03score, ifnull(f_HRInfoCollectDiff ,0)* 10 + ifnull(f_HRChangeProcComplex,0) * 5 + ifnull(f_reportDiff,0) * 5 + + ifnull(f_attDataCollectDiff ,0) * 10 + ifnull(f_shiftChangeFreq,0) * 15 + ifnull(f_attDataCollectDiff,0) * 40 +ifnull( f_excelPRLinkDiff,0) * 10 + ifnull(f_pieceworkOutputDiff,0) * 10 + ifnull(f_taxCalcDiff,0) * 10 + ifnull(f_salaryCycleLong,0) * 15 + ifnull(f_partTimeCostAllocDiff,0) * 5 as hrtag03score from servicecloud.CHORoleSurvey ) as tag003 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741929574449728 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人工成本高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741932005069376 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741933931708992 limit 1000"], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}