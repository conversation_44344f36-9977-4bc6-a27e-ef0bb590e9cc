2024-10-12T17:25:36.001+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : selectE10 count:221
2024-10-12T17:25:36.464+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322659148352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e0b32b83f3c6c4258f3b49d668b3af5e&deviceId=475203782385349699
2024-10-12T17:25:36.583+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e3890898cbe89a12e43043a14545be27\\\",\\\"dbIdValue\\\":\\\"e3890898cbe89a12e43043a14545be27\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475203782385349699
2024-10-12T17:25:38.699+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318695760448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=06feb9d01d2cd947c86a09ac48e31c06&deviceId=475062791342928963
2024-10-12T17:25:38.699+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d9f97ac06a717e4fcda49a811146aa65\\\",\\\"dbIdValue\\\":\\\"d9f97ac06a717e4fcda49a811146aa65\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TEST0423\\\",\\\"targetValue\\\":\\\"TEST0423\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475062791342928963
2024-10-12T17:25:40.197+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321355862592 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e4691e0e2e04977066bb710aa8b149e9&deviceId=475052141954741298
2024-10-12T17:25:40.197+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cd316daca8534b142c1020eb65a6cdec\\\",\\\"dbIdValue\\\":\\\"cd316daca8534b142c1020eb65a6cdec\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"xinglian\\\",\\\"targetValue\\\":\\\"xinglian\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475052141954741298
2024-10-12T17:25:41.686+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:407909687452224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7e1b4fa3fe75dd438314c488cca768b0&deviceId=475042155014928178
2024-10-12T17:25:41.686+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3910cf2d4ce25f5c1ec265d1d099861c\\\",\\\"dbIdValue\\\":\\\"3910cf2d4ce25f5c1ec265d1d099861c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ARK_GCX\\\",\\\"targetValue\\\":\\\"ARK_GCX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850340557e0a042c77dca4c66fd2170044bfc71840096b5069822a71926ba6cb05066a849aebfa785de196112825fdb53eefc6cae1c657fcd27940df24c47d09e7bfa2b99707e81ae30f9a4c3f73821f7128b520900505b8f03458042f7cc2c9c64eb1bae6ee4b0e9f8c70e2dccbc808f188a862a51565cefd980b75ae4e27947fa755968937dc8863fd5aaba79b15e31e9be468978b6c09e9bd3a318a126eaccb1948bbefd57d632ab9ad081c5a25cf914a892a96bfb55b52b671438690043e9d350c7c3e5cd6327bbbaa6efe5b5abb29b059c98d898aa5d4c03f581f1701ed63ab9b287e1b4895348527cc99fe5bfde1a9500765100d4d04d7","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761430223233602,"id":779886362235456,"adcId":632233822220864,"execParamsVersion":"68291557576c893c40352b16bf09098b","aiId":632248155124288,"isEnable":1},"paramsMap":{"deviceId":"475042155014928178","eid":407909687452224,"aiId":632248155124288,"execParamsDbAiId":632247934997056,"execParamsDbId":"3910cf2d4ce25f5c1ec265d1d099861c"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779886362235456 acc incomplete","batchId":779886368404032,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   475042155014928178
2024-10-12T17:25:44.445+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318314345024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=296c2bbf2e5c858c229385d5e1a2c2db&deviceId=475040805724112185
2024-10-12T17:25:44.446+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"791f782e432a26e028e1a7503f4ab698\\\",\\\"dbIdValue\\\":\\\"791f782e432a26e028e1a7503f4ab698\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JHL1901\\\",\\\"targetValue\\\":\\\"JHL1901\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475040805724112185
2024-10-12T17:25:46.102+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324388213312 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=30ee76201492fa744d74f52724f923ae&deviceId=434741637629887288
2024-10-12T17:25:46.103+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"33df74e8c437ebc74db3acc0ac300a34\\\",\\\"dbIdValue\\\":\\\"33df74e8c437ebc74db3acc0ac300a34\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GNZMdata\\\",\\\"targetValue\\\":\\\"GNZMdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   434741637629887288
2024-10-12T17:25:47.523+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:387302979891776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=adeb2da70e16ac93e9adef4795693467&deviceId=474481020574385713
2024-10-12T17:25:47.524+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f28e8c05fb245405f3bbce4ce62dda8c\\\",\\\"dbIdValue\\\":\\\"f28e8c05fb245405f3bbce4ce62dda8c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RB\\\",\\\"targetValue\\\":\\\"RB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474481020574385713
2024-10-12T17:25:49.000+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323340292672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d1078f1cbdf2b71be97b0a618a517983&deviceId=474460945729467956
2024-10-12T17:25:49.001+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a628ca5e8978b693cba6a2970fded620\\\",\\\"dbIdValue\\\":\\\"a628ca5e8978b693cba6a2970fded620\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JXQC\\\",\\\"targetValue\\\":\\\"JXQC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474460945729467956
2024-10-12T17:25:50.408+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321707291200 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=df06b2d13422d7e52aba78746b43c043&deviceId=424619881275600965
2024-10-12T17:25:50.408+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"29e757938bcc562df7db2b48dbe29847\\\",\\\"dbIdValue\\\":\\\"29e757938bcc562df7db2b48dbe29847\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XZZN\\\",\\\"targetValue\\\":\\\"XZZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   424619881275600965
2024-10-12T17:25:51.913+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:134653318820416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fd01235d39eaf103e87e9be697563524&deviceId=474459430864303684
2024-10-12T17:25:51.913+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4649c2807c9e335d8a44afe43b172543\\\",\\\"dbIdValue\\\":\\\"4649c2807c9e335d8a44afe43b172543\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DATA\\\",\\\"targetValue\\\":\\\"DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474459430864303684
2024-10-12T17:25:53.468+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:69973831074368 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a93cb9bfda85ca7b7c3ea7ca67ff5b35&deviceId=474456369928353845
2024-10-12T17:25:53.469+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ba6da3e51ca2515b898ff313b7058517\\\",\\\"dbIdValue\\\":\\\"ba6da3e51ca2515b898ff313b7058517\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"kqj_e10\\\",\\\"targetValue\\\":\\\"kqj_e10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474456369928353845
2024-10-12T17:25:55.206+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318586892864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4ec7610295a91ed992040f54597ea775&deviceId=474448190297354804
2024-10-12T17:25:55.206+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"542bd06affdd49cef2f2acb9ecde1551\\\",\\\"dbIdValue\\\":\\\"542bd06affdd49cef2f2acb9ecde1551\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10DATA2023\\\",\\\"targetValue\\\":\\\"E10DATA2023\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474448190297354804
2024-10-12T17:25:56.654+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318834537024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61ba02a1da1c7ff4405144d43fff3457&deviceId=474446081736848945
2024-10-12T17:25:56.654+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"03a044f66b6b64061207ecd1af2adde5\\\",\\\"dbIdValue\\\":\\\"03a044f66b6b64061207ecd1af2adde5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Zdata\\\",\\\"targetValue\\\":\\\"Zdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474446081736848945
2024-10-12T17:25:58.082+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:88110815724096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f720ef9cde0690cf8ae9ab6f010c4f48&deviceId=474346771556873283
2024-10-12T17:25:58.083+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"640e49d1d96187905f91681ca50995a5\\\",\\\"dbIdValue\\\":\\\"640e49d1d96187905f91681ca50995a5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Hy\\\",\\\"targetValue\\\":\\\"E10_Hy\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474346771556873283
2024-10-12T17:25:59.819+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:348505635279424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d2d4c93c807d166f533548cb46d211b2&deviceId=474330452929294391
2024-10-12T17:25:59.820+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fe2e54a75d937e4d324ef9bcde2247b6\\\",\\\"dbIdValue\\\":\\\"fe2e54a75d937e4d324ef9bcde2247b6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JW2021\\\",\\\"targetValue\\\":\\\"JW2021\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474330452929294391
2024-10-12T17:26:01.220+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:389588521386560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d6866b3f11f0496ed4eed5de95743f42&deviceId=474327498159567939
2024-10-12T17:26:01.220+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7a8dae8ffaba1a14c3d94449bb665eab\\\",\\\"dbIdValue\\\":\\\"7a8dae8ffaba1a14c3d94449bb665eab\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HTJD\\\",\\\"targetValue\\\":\\\"HTJD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474327498159567939
2024-10-12T17:26:02.653+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321345724992 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bfc40df5c75d5f0ffe8615b177d5da9b&deviceId=440100375932977714
2024-10-12T17:26:02.654+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ed2409923400b68d963ebcdccf5fce25\\\",\\\"dbIdValue\\\":\\\"ed2409923400b68d963ebcdccf5fce25\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RONGTA\\\",\\\"targetValue\\\":\\\"RONGTA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   440100375932977714
2024-10-12T17:26:04.419+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320819741248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=59230dfb5391612c44d97033d0329b3d&deviceId=474197002423386418
2024-10-12T17:26:04.420+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"963839e00d9761418e0e47398b4a343d\\\",\\\"dbIdValue\\\":\\\"963839e00d9761418e0e47398b4a343d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Bdata\\\",\\\"targetValue\\\":\\\"Bdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474197002423386418
2024-10-12T17:26:06.460+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:261927914103360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eb8e75fb2c6c396da4871aebbc76075b&deviceId=474190285128090689
2024-10-12T17:26:06.460+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"894b325891875cc7f4968f21f4ad7e61\\\",\\\"dbIdValue\\\":\\\"894b325891875cc7f4968f21f4ad7e61\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TYDATA\\\",\\\"targetValue\\\":\\\"TYDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474190285128090689
2024-10-12T17:26:08.093+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321210356288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ea68dc08408df90c6645ef1fc46ba1fd&deviceId=474184985792743995
2024-10-12T17:26:08.094+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"db3a018ca302909fc7791fa0b7a45c29\\\",\\\"dbIdValue\\\":\\\"db3a018ca302909fc7791fa0b7a45c29\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Wdata\\\",\\\"targetValue\\\":\\\"Wdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474184985792743995
2024-10-12T17:26:09.418+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:179273588060736 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=39b927e32a4de30451ce7142b4a83b85&deviceId=474167337822466096
2024-10-12T17:26:09.419+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c35ef9a880fa3eab398cd7fa8d6f5775\\\",\\\"dbIdValue\\\":\\\"c35ef9a880fa3eab398cd7fa8d6f5775\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FQDATA\\\",\\\"targetValue\\\":\\\"FQDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474167337822466096
2024-10-12T17:26:10.767+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321988407872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1478e773cc4c1e6553a7fcfcb8769567&deviceId=474158615096210499
2024-10-12T17:26:10.768+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e7d57f11482e5bba905ec05f71926ddd\\\",\\\"dbIdValue\\\":\\\"e7d57f11482e5bba905ec05f71926ddd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XData\\\",\\\"targetValue\\\":\\\"XData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474158615096210499
2024-10-12T17:26:12.107+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323746730560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dd870cf9ec42b555b5ed508d2c825c08&deviceId=474050649969932342
2024-10-12T17:26:12.108+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"060d78b757f5a720ed8b9929320c3891\\\",\\\"dbIdValue\\\":\\\"060d78b757f5a720ed8b9929320c3891\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_QIFENGDB\\\",\\\"targetValue\\\":\\\"E10_QIFENGDB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474050649969932342
2024-10-12T17:26:13.460+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:68204371202624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=da9d257b19ad003b138fb27fbbde34bf&deviceId=474025426985961030
2024-10-12T17:26:13.460+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"97e1c44b9f28d684bb2a8af6a8255b0c\\\",\\\"dbIdValue\\\":\\\"97e1c44b9f28d684bb2a8af6a8255b0c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"QFZN\\\",\\\"targetValue\\\":\\\"QFZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503ec0d37d6c9918b51d891244bd1be0540cbf15b7235d550c8ffd95d218db1f933eee58dd4e2cef1336bed373d3840ac586561055e3cc5aee43320c08756c3bedfb79833b6a93f30cc2ecea774a29bf91d7d9a9d542cae27b371168bec26260789935781f363a85303d95c3118f4777ad448d2014ab1ecd51762f8747afb35f6058b28d14550d58c6c731d1fa26b45280b7343e90cc5ebb20ce0086aea51f3afc79e8d6954b3afb1acc55ca31d944ac318ea1435e86b1f28b7adcb11a56cf6e76ccd465b39e7fc443fe745abc14f6243ece439e5f2773378a8abbcdc0d34216b16b161cbb4a6bbbe4766a34a5e9d46af12","collectName":"E10附件数据采集","accId":779544910123584,"adimId":629770719744576,"id":779886492402240,"adcId":629756070724160,"execParamsVersion":"68968015bb05d0cb1abf71714b9c37a7","aiId":629770719691330,"isEnable":1},"paramsMap":{"deviceId":"474025426985961030","eid":68204371202624,"aiId":629770719691330,"execParamsDbAiId":629755828290112,"execParamsDbId":"97e1c44b9f28d684bb2a8af6a8255b0c"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779886492402240 acc incomplete","batchId":779886493479488,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   474025426985961030
2024-10-12T17:26:14.856+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321768223296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1fcfbad52b9df6745517f95e40f220e5&deviceId=474013838392439619
2024-10-12T17:26:14.857+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"56826c237af58b937b43b119106f7af7\\\",\\\"dbIdValue\\\":\\\"56826c237af58b937b43b119106f7af7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SZJR_2023\\\",\\\"targetValue\\\":\\\"SZJR_2023\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   474013838392439619
2024-10-12T17:26:16.304+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318218551872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b8751768ccb8daca142abc9bcd83d633&deviceId=473911304034206788
2024-10-12T17:26:16.304+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a5e3a9dad12bd4475e36a7f013ab52ec\\\",\\\"dbIdValue\\\":\\\"a5e3a9dad12bd4475e36a7f013ab52ec\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LRDATA\\\",\\\"targetValue\\\":\\\"LRDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473911304034206788
2024-10-12T17:26:17.639+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:130184840688192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=afc086700717c4bfe759d413c9e7b3a2&deviceId=473879242958975554
2024-10-12T17:26:17.639+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"febb9b120d7a4a90586163073170b5b2\\\",\\\"dbIdValue\\\":\\\"febb9b120d7a4a90586163073170b5b2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSH\\\",\\\"targetValue\\\":\\\"XSH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473879242958975554
2024-10-12T17:26:18.961+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:194337225056832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aa554e5eae9c537c709835df20a6c6b7&deviceId=473868791575491651
2024-10-12T17:26:18.962+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8cb0f6fdc83e8a12891967625a59c80a\\\",\\\"dbIdValue\\\":\\\"8cb0f6fdc83e8a12891967625a59c80a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JHMJ\\\",\\\"targetValue\\\":\\\"JHMJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473868791575491651
2024-10-12T17:26:20.296+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320559440448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9c1a3dcf024f9cff7a918dbdba161f3a&deviceId=473296918290641987
2024-10-12T17:26:20.296+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ef0abd0473daba54b88f7e38a1ffc26a\\\",\\\"dbIdValue\\\":\\\"ef0abd0473daba54b88f7e38a1ffc26a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GuiRuiHeng\\\",\\\"targetValue\\\":\\\"GuiRuiHeng\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473296918290641987
2024-10-12T17:26:21.607+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:501263597011520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bf0ee5460c3f205ffd37679236087aaa&deviceId=473152592222897459
2024-10-12T17:26:21.608+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"756227b722e3e80d371f62036a1abddb\\\",\\\"dbIdValue\\\":\\\"756227b722e3e80d371f62036a1abddb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DWSWERP\\\",\\\"targetValue\\\":\\\"DWSWERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473152592222897459
2024-10-12T17:26:22.948+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:473756949385792 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2710f0a0ca2615992498bbfcccf22ee1&deviceId=473142455512872003
2024-10-12T17:26:22.949+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"030f68156436722ac622e1f8ece417a0\\\",\\\"dbIdValue\\\":\\\"030f68156436722ac622e1f8ece417a0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DKXT20230613\\\",\\\"targetValue\\\":\\\"DKXT20230613\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473142455512872003
2024-10-12T17:26:24.287+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:295280734032448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f92aa578db6a7cbc00a58f934febb185&deviceId=473044730360379457
2024-10-12T17:26:24.288+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4b3f29325f59d2e04e2560e061334430\\\",\\\"dbIdValue\\\":\\\"4b3f29325f59d2e04e2560e061334430\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_DB2021_S3\\\",\\\"targetValue\\\":\\\"E10_DB2021_S3\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473044730360379457
2024-10-12T17:26:25.614+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:133679980597824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=66be62c1873420b55fb84943e0bfe4c6&deviceId=473032836958401603
2024-10-12T17:26:25.614+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7a232146527b98c368ac6deb9540e884\\\",\\\"dbIdValue\\\":\\\"7a232146527b98c368ac6deb9540e884\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YKSL\\\",\\\"targetValue\\\":\\\"YKSL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473032836958401603
2024-10-12T17:26:26.939+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:182606070161984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8f45a138c4d06a7b823240e11ca9b0cc&deviceId=473028661964977219
2024-10-12T17:26:26.940+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"02382cb4081f3ed26fc307829d8a1d65\\\",\\\"dbIdValue\\\":\\\"02382cb4081f3ed26fc307829d8a1d65\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_XZY\\\",\\\"targetValue\\\":\\\"E10_XZY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473028661964977219
2024-10-12T17:26:28.281+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322517631552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=08405a3b35f32f908ed49f26105a3b98&deviceId=473025439783207491
2024-10-12T17:26:28.281+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5496835f4f975bf5de8f58c974d51909\\\",\\\"dbIdValue\\\":\\\"5496835f4f975bf5de8f58c974d51909\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DRAGON01\\\",\\\"targetValue\\\":\\\"DRAGON01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473025439783207491
2024-10-12T17:26:29.619+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233421890384448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=703a7739bb581ea6ff72f9048de30d09&deviceId=473022663053292611
2024-10-12T17:26:29.619+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f70f969e2906a9c9d3ba58023e9edf4d\\\",\\\"dbIdValue\\\":\\\"f70f969e2906a9c9d3ba58023e9edf4d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HAXN\\\",\\\"targetValue\\\":\\\"HAXN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473022663053292611
2024-10-12T17:26:30.937+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324173070912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a3c4390a0c5903d7b620d9a6e70dca4c&deviceId=473009164558283843
2024-10-12T17:26:30.937+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ccd7f4b431c6efef7d1374a786148ce7\\\",\\\"dbIdValue\\\":\\\"ccd7f4b431c6efef7d1374a786148ce7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BD\\\",\\\"targetValue\\\":\\\"BD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473009164558283843
2024-10-12T17:26:32.260+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322880807488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=26a902a67fcd9685ecc27466cd21d8b5&deviceId=473005130963235654
2024-10-12T17:26:32.261+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"98cdb7a2cd01b65b58c6e4d358de2680\\\",\\\"dbIdValue\\\":\\\"98cdb7a2cd01b65b58c6e4d358de2680\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6003_GQ\\\",\\\"targetValue\\\":\\\"E10_6003_GQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473005130963235654
2024-10-12T17:26:33.646+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322788934208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0d7ecab6aed2fd04b53dca458377362f&deviceId=472890954525717317
2024-10-12T17:26:33.647+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e39e6d4064269dc1c86212037e40ec3a\\\",\\\"dbIdValue\\\":\\\"e39e6d4064269dc1c86212037e40ec3a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JWJX\\\",\\\"targetValue\\\":\\\"JWJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472890954525717317
2024-10-12T17:26:35.002+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323380552256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9cbfc646a8a909ebd14812daee178991&deviceId=472457409118352451
2024-10-12T17:26:35.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7d69df9ba0e470588425aa878879e615\\\",\\\"dbIdValue\\\":\\\"7d69df9ba0e470588425aa878879e615\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XZCJM00\\\",\\\"targetValue\\\":\\\"XZCJM00\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472457409118352451
2024-10-12T17:26:36.343+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233427120550464 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ce5817cda42c9ead5f8da3b7690c27d0&deviceId=472449630999689017
2024-10-12T17:26:36.343+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9e20a47626bedcbe8c5df4bb56759244\\\",\\\"dbIdValue\\\":\\\"9e20a47626bedcbe8c5df4bb56759244\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472449630999689017
2024-10-12T17:26:37.804+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233424411116096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=af1a3f0d5051f31993437e1163de124a&deviceId=472444964266066742
2024-10-12T17:26:37.805+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b5ec26208bb2da73ae5c21db20f1981e\\\",\\\"dbIdValue\\\":\\\"b5ec26208bb2da73ae5c21db20f1981e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JiaYuGroup\\\",\\\"targetValue\\\":\\\"JiaYuGroup\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472444964266066742
2024-10-12T17:26:39.123+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:203729368351296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=44e7428654f1de9de8fd86c7265727c8&deviceId=472312929287419459
2024-10-12T17:26:39.123+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8b0ac140add730d8bd5dc5988e8b0467\\\",\\\"dbIdValue\\\":\\\"8b0ac140add730d8bd5dc5988e8b0467\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ahsor\\\",\\\"targetValue\\\":\\\"ahsor\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472312929287419459
2024-10-12T17:26:40.447+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:74530223714880 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=976b018d2c119446b294b13a557f0393&deviceId=472298839496209968
2024-10-12T17:26:40.447+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f13fb69458550ed4e70f25e683f285a9\\\",\\\"dbIdValue\\\":\\\"f13fb69458550ed4e70f25e683f285a9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ROPERS\\\",\\\"targetValue\\\":\\\"ROPERS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472298839496209968
2024-10-12T17:26:41.800+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320807998016 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=09f6812124b0100787c72ab10e2f0826&deviceId=472299007838794819
2024-10-12T17:26:41.801+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"492d9e3803b97b65e0621e5f0d495a14\\\",\\\"dbIdValue\\\":\\\"492d9e3803b97b65e0621e5f0d495a14\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSSL\\\",\\\"targetValue\\\":\\\"HSSL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472299007838794819
2024-10-12T17:26:43.243+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:211529701704256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b9ab1be001cd6b3cb2fa3cb8f30b138b&deviceId=472159326224528965
2024-10-12T17:26:43.243+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"671fb003c9e8bc54963caca6c50265fe\\\",\\\"dbIdValue\\\":\\\"671fb003c9e8bc54963caca6c50265fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_JH02\\\",\\\"targetValue\\\":\\\"E10_JH02\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472159326224528965
2024-10-12T17:26:44.589+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320186651200 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f8c44639bea16aaac37ca07363bfe80c&deviceId=396051271791948598
2024-10-12T17:26:44.590+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2b1372895a9ded513b03fedc37ad0509\\\",\\\"dbIdValue\\\":\\\"2b1372895a9ded513b03fedc37ad0509\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XYK2017\\\",\\\"targetValue\\\":\\\"XYK2017\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   396051271791948598
2024-10-12T17:26:45.942+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:485892572471872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=25662dddcb95561759305aa60f88e4a8&deviceId=471985996813055043
2024-10-12T17:26:45.943+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"363fab8df06fe9d98c98cb30e0db4d20\\\",\\\"dbIdValue\\\":\\\"363fab8df06fe9d98c98cb30e0db4d20\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_YBZC0601\\\",\\\"targetValue\\\":\\\"E10_YBZC0601\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   471985996813055043
2024-10-12T17:26:47.361+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:216278042899008 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=63da966d7593af34eeb19b42590f2e20&deviceId=471264718225421379
2024-10-12T17:26:47.361+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b41c3aef143351d4fbbec006565911de\\\",\\\"dbIdValue\\\":\\\"b41c3aef143351d4fbbec006565911de\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"yuyuan\\\",\\\"targetValue\\\":\\\"yuyuan\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   471264718225421379
2024-10-12T17:26:48.719+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322276119104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b3e502914914e984171049669b786e16&deviceId=421279876587210054
2024-10-12T17:26:48.720+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d9e456dd6efd886cd3497c7e60e37d20\\\",\\\"dbIdValue\\\":\\\"d9e456dd6efd886cd3497c7e60e37d20\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.1\\\",\\\"targetValue\\\":\\\"E10_6.0.1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   421279876587210054
2024-10-12T17:26:50.074+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318731952704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=942f2d30205aae307771e7db05ac8cf0&deviceId=471141185503639105
2024-10-12T17:26:50.075+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c040b1cd0967f9e57f9ade42eb38c88f\\\",\\\"dbIdValue\\\":\\\"c040b1cd0967f9e57f9ade42eb38c88f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E50\\\",\\\"targetValue\\\":\\\"E50\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   471141185503639105
2024-10-12T17:26:51.415+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:482737593594432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aa35a2c5be4fbd81d3ccb88bcff7a028&deviceId=470996703961429829
2024-10-12T17:26:51.416+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1c31898c7293a08894fd2bc397254fdb\\\",\\\"dbIdValue\\\":\\\"1c31898c7293a08894fd2bc397254fdb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JM_DATA\\\",\\\"targetValue\\\":\\\"JM_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470996703961429829
2024-10-12T17:26:52.751+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:462033709179456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=204c173d23f1fd30c36debc3576948b5&deviceId=470862261200630329
2024-10-12T17:26:52.752+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7f68c9d4a1cdcd2a91a328541ee0a6bb\\\",\\\"dbIdValue\\\":\\\"7f68c9d4a1cdcd2a91a328541ee0a6bb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ACWZS\\\",\\\"targetValue\\\":\\\"ACWZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470862261200630329
2024-10-12T17:26:54.059+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322039116352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=37b153c6e8aa1ec5bacf9ebbce7dba63&deviceId=470277792236319811
2024-10-12T17:26:54.059+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9e072f337c16be20ebcccee7952809e3\\\",\\\"dbIdValue\\\":\\\"9e072f337c16be20ebcccee7952809e3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_LFE\\\",\\\"targetValue\\\":\\\"E10_LFE\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470277792236319811
2024-10-12T17:26:55.403+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:382790875468352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=84e7f9b5f88d49d2a8fa2b90d5c45ee1&deviceId=470246314739774531
2024-10-12T17:26:55.403+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3c8b84bc5f1f4d079d0b65a4b006fe44\\\",\\\"dbIdValue\\\":\\\"3c8b84bc5f1f4d079d0b65a4b006fe44\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AOLI\\\",\\\"targetValue\\\":\\\"AOLI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470246314739774531
2024-10-12T17:26:56.770+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322027614784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=89cd2b52ade3e8af8eb9ff34597280e0&deviceId=470106627236705347
2024-10-12T17:26:56.771+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cd34cce8a2b8be5528070a0d662a7918\\\",\\\"dbIdValue\\\":\\\"cd34cce8a2b8be5528070a0d662a7918\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WZSY\\\",\\\"targetValue\\\":\\\"WZSY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470106627236705347
2024-10-12T17:26:58.135+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:497602377597504 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0cabe347ad7272a2d2fc13bd1a78e00a&deviceId=470103376718214211
2024-10-12T17:26:58.136+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8cecad6f23a43107be62d04e3cca7b9d\\\",\\\"dbIdValue\\\":\\\"8cecad6f23a43107be62d04e3cca7b9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GZJX\\\",\\\"targetValue\\\":\\\"GZJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   470103376718214211
2024-10-12T17:26:59.458+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:298215400993344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=63bb49457d4a8cc97360e374d7c9997d&deviceId=469961079653417521
2024-10-12T17:26:59.459+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"18fb848bc4dd73dc6a56cdc93f75b94c\\\",\\\"dbIdValue\\\":\\\"18fb848bc4dd73dc6a56cdc93f75b94c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"chuantu_prd\\\",\\\"targetValue\\\":\\\"chuantu_prd\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469961079653417521
2024-10-12T17:27:00.813+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:295988743856704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a28681f4438ce44db6346b098db23f14&deviceId=469846284774163766
2024-10-12T17:27:00.814+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"410c512e2d04b6d7269c6aa1d385fb9b\\\",\\\"dbIdValue\\\":\\\"410c512e2d04b6d7269c6aa1d385fb9b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_data\\t\\\",\\\"targetValue\\\":\\\"E10_data\\t\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469846284774163766
2024-10-12T17:27:02.150+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:518705264656960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cfb16075ca9ca4f97606db57ee72b49c&deviceId=469841132390465843
2024-10-12T17:27:02.151+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"218eb28a4314c28911a71e4d2d34554f\\\",\\\"dbIdValue\\\":\\\"218eb28a4314c28911a71e4d2d34554f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YDKJ\\\",\\\"targetValue\\\":\\\"YDKJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469841132390465843
2024-10-12T17:27:03.564+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233422551814720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=90e34abadb291839121565e386254940&deviceId=469820653533414210
2024-10-12T17:27:03.564+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e4c32749a75c1aa5030f5fbfd1e79032\\\",\\\"dbIdValue\\\":\\\"e4c32749a75c1aa5030f5fbfd1e79032\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XJ_DATE\\\",\\\"targetValue\\\":\\\"XJ_DATE\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469820653533414210
2024-10-12T17:27:04.892+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321749434944 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=28b4c671311b39d324d5a98be6d7692e&deviceId=469810678555751491
2024-10-12T17:27:04.893+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4ca0fb3ecfd7a06bdab4d22159683c1a\\\",\\\"dbIdValue\\\":\\\"4ca0fb3ecfd7a06bdab4d22159683c1a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Wangxun\\\",\\\"targetValue\\\":\\\"E10_Wangxun\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469810678555751491
2024-10-12T17:27:06.233+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321448698432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=64836ba87d69135d5255a2cdfb7b25db&deviceId=469404145841877049
2024-10-12T17:27:06.235+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"129dda05de0e883e2a789c73b069e8f1\\\",\\\"dbIdValue\\\":\\\"129dda05de0e883e2a789c73b069e8f1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AILE\\\",\\\"targetValue\\\":\\\"AILE\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469404145841877049
2024-10-12T17:27:07.786+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:185151402283584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1f3d6ec4e2fdbbb0c3d9d73166edbc5f&deviceId=469377426649003075
2024-10-12T17:27:07.786+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f10337fb4291214744576b09244b8081\\\",\\\"dbIdValue\\\":\\\"f10337fb4291214744576b09244b8081\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LSSY\\\",\\\"targetValue\\\":\\\"LSSY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469377426649003075
2024-10-12T17:27:09.129+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319713583680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e1cb409ddb50523eebcf80ad2b740b4e&deviceId=434923135012188471
2024-10-12T17:27:09.130+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"86adc1e9393c2807c6e1ba946367ed46\\\",\\\"dbIdValue\\\":\\\"86adc1e9393c2807c6e1ba946367ed46\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSSG\\\",\\\"targetValue\\\":\\\"HSSG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   434923135012188471
2024-10-12T17:27:10.465+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:302720957129280 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dc6b1b64cf4bbc826d79efa61a9454ed&deviceId=469256851062536248
2024-10-12T17:27:10.466+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e988ef043f382528542e11e3ba461429\\\",\\\"dbIdValue\\\":\\\"e988ef043f382528542e11e3ba461429\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XCtech\\\",\\\"targetValue\\\":\\\"XCtech\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469256851062536248
2024-10-12T17:27:11.867+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:387479924552256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c43e60f3e1623be17f55aa442f53acb6&deviceId=469256648125329730
2024-10-12T17:27:11.868+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b2ad3d56c9026812a32a1e632c458a55\\\",\\\"dbIdValue\\\":\\\"b2ad3d56c9026812a32a1e632c458a55\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AY\\\",\\\"targetValue\\\":\\\"AY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469256648125329730
2024-10-12T17:27:13.221+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322718769728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=091fc89d7b09384b6eca2bc708446c56&deviceId=469124003429103683
2024-10-12T17:27:13.221+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"313b991263fbcad43910a47766d1275a\\\",\\\"dbIdValue\\\":\\\"313b991263fbcad43910a47766d1275a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XLH\\\",\\\"targetValue\\\":\\\"XLH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469124003429103683
2024-10-12T17:27:14.603+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319858643520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=72116e61a527ed51e8d16002343a0bca&deviceId=469082670895219777
2024-10-12T17:27:14.604+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2b61a358df10ba649c2ef8b9f67e1aa5\\\",\\\"dbIdValue\\\":\\\"2b61a358df10ba649c2ef8b9f67e1aa5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"PRX-zs\\\",\\\"targetValue\\\":\\\"PRX-zs\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469082670895219777
2024-10-12T17:27:15.931+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320002822720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0777ef1a1202f6f0d6091006a64597d8&deviceId=467808158157387065
2024-10-12T17:27:15.932+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4fa568791a4ec95ed60cf98b08ecf47a\\\",\\\"dbIdValue\\\":\\\"4fa568791a4ec95ed60cf98b08ecf47a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"chingcheng\\\",\\\"targetValue\\\":\\\"chingcheng\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   467808158157387065
2024-10-12T17:27:17.298+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320645079616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8947cf0f95c405115699e70d4e0c5d95&deviceId=468978436568790083
2024-10-12T17:27:17.299+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"48fd1d8b13d6986b37486fd36c5d48ed\\\",\\\"dbIdValue\\\":\\\"48fd1d8b13d6986b37486fd36c5d48ed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LY\\\",\\\"targetValue\\\":\\\"LY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468978436568790083
2024-10-12T17:27:18.700+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:154589481226816 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aee10febbdc17a9e33f3b6afff8e9561&deviceId=468973442360685635
2024-10-12T17:27:18.701+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"188695837867ddcd255745d75d28ab1e\\\",\\\"dbIdValue\\\":\\\"188695837867ddcd255745d75d28ab1e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"STDJ\\\",\\\"targetValue\\\":\\\"STDJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468973442360685635
2024-10-12T17:27:20.062+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:172578934809152 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5cd9c001f8fc19bb22fd2f95517a0943&deviceId=468794953586193202
2024-10-12T17:27:20.063+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ee058676460b73d08192da4ebc33cf68\\\",\\\"dbIdValue\\\":\\\"ee058676460b73d08192da4ebc33cf68\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YESON\\\",\\\"targetValue\\\":\\\"YESON\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468794953586193202
2024-10-12T17:27:21.402+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320358371904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d1ddc93ebde44a243e44e161ec17febe&deviceId=468806764159055929
2024-10-12T17:27:21.403+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ec01f109baac2a3ad2e433a4cda2f8c1\\\",\\\"dbIdValue\\\":\\\"ec01f109baac2a3ad2e433a4cda2f8c1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHIFA\\\",\\\"targetValue\\\":\\\"SHIFA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468806764159055929
2024-10-12T17:27:22.801+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324913455680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2aa6a3aa31ed104eb7531b397107ab45&deviceId=468398152513238083
2024-10-12T17:27:22.801+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"12d407f9f56498602977e98fb2c0dc38\\\",\\\"dbIdValue\\\":\\\"12d407f9f56498602977e98fb2c0dc38\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJKL\\\",\\\"targetValue\\\":\\\"ZJKL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468398152513238083
2024-10-12T17:27:24.154+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:352267779834432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8dfc6f563783449b33ef902d6ab07ea5&deviceId=432292048800855107
2024-10-12T17:27:24.154+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6fb5dfcad9e9fbb429e15831c267f67d\\\",\\\"dbIdValue\\\":\\\"6fb5dfcad9e9fbb429e15831c267f67d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JN\\\",\\\"targetValue\\\":\\\"JN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   432292048800855107
2024-10-12T17:27:25.462+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:291401699627584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=33bbc4f8b2a69a70e313900fc5b4364f&deviceId=468114871435670853
2024-10-12T17:27:25.463+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"84c853c8578ee999ad5a537791155c8b\\\",\\\"dbIdValue\\\":\\\"84c853c8578ee999ad5a537791155c8b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SSD\\\",\\\"targetValue\\\":\\\"SSD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468114871435670853
2024-10-12T17:27:26.790+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318011171392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8a8dbc5449bf61323b000af7a93bccf6&deviceId=468102509295060273
2024-10-12T17:27:26.790+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1c5621fb9a846235afb01c79e5a344ed\\\",\\\"dbIdValue\\\":\\\"1c5621fb9a846235afb01c79e5a344ed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Trd\\\",\\\"targetValue\\\":\\\"Trd\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   468102509295060273
2024-10-12T17:27:28.135+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319676920384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=90acf685f35288bd15dc7d8418dea64f&deviceId=467961577308440132
2024-10-12T17:27:28.136+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"891e10734e314b22c98994cbaa91826e\\\",\\\"dbIdValue\\\":\\\"891e10734e314b22c98994cbaa91826e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FXSY\\\",\\\"targetValue\\\":\\\"FXSY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   467961577308440132
2024-10-12T17:27:29.506+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322679337536 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dff04ab95d19fcaf0130877f26bcdecc&deviceId=467956564930607426
2024-10-12T17:27:29.506+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"eaaff3b1db3aa48937b4d1bf6596fd66\\\",\\\"dbIdValue\\\":\\\"eaaff3b1db3aa48937b4d1bf6596fd66\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XC2\\\",\\\"targetValue\\\":\\\"XC2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   467956564930607426
2024-10-12T17:27:30.828+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:434890409058880 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=089f55d81bae874340ad5cbbc7f14507&deviceId=464201839827498041
2024-10-12T17:27:30.829+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4bea61590fa99734201592892033d0c7\\\",\\\"dbIdValue\\\":\\\"4bea61590fa99734201592892033d0c7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JT_E10_F\\\",\\\"targetValue\\\":\\\"JT_E10_F\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464201839827498041
2024-10-12T17:27:32.153+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317884629568 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a9cc913bc93a9ffc6d28102ea62df468&deviceId=467934635666457913
2024-10-12T17:27:32.153+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0e17f673a0757fa7d865a6832931258f\\\",\\\"dbIdValue\\\":\\\"0e17f673a0757fa7d865a6832931258f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JLZKCS\\\",\\\"targetValue\\\":\\\"JLZKCS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   467934635666457913
2024-10-12T17:27:33.553+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322027614784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=72899591444e7bf4dea7a161f211aee9&deviceId=467350461242946627
2024-10-12T17:27:33.553+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cd34cce8a2b8be5528070a0d662a7918\\\",\\\"dbIdValue\\\":\\\"cd34cce8a2b8be5528070a0d662a7918\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WZSY\\\",\\\"targetValue\\\":\\\"WZSY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   467350461242946627
2024-10-12T17:27:34.889+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:201252057252416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dcdabc1297a9c5b459257b004b891296&deviceId=466793013780165699
2024-10-12T17:27:34.889+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3a3799c2bba9dd87736da23fbd495f6a\\\",\\\"dbIdValue\\\":\\\"3a3799c2bba9dd87736da23fbd495f6a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_CASTEC\\\",\\\"targetValue\\\":\\\"E10_CASTEC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   466793013780165699
2024-10-12T17:27:36.226+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:372085685215808 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=97ff905c64b477b2eaa90629e45f1c12&deviceId=466665977656915011
2024-10-12T17:27:36.227+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a59d0554afdc3e2b213223253b907fc4\\\",\\\"dbIdValue\\\":\\\"a59d0554afdc3e2b213223253b907fc4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SLDATA\\\",\\\"targetValue\\\":\\\"SLDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   466665977656915011
2024-10-12T17:27:37.569+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324748821056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0bb3651e56b40c124533f7210426c466&deviceId=466628426338415683
2024-10-12T17:27:37.570+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1c5c60468a41525bb0341e4147190e0b\\\",\\\"dbIdValue\\\":\\\"1c5c60468a41525bb0341e4147190e0b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"infy\\\",\\\"targetValue\\\":\\\"infy\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   466628426338415683
2024-10-12T17:27:38.952+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:443645629964864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2ec5746db49684c2b1e7e3e13a7bf91d&deviceId=466618914378363971
2024-10-12T17:27:38.952+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"02b2c9a0dcbb64f22da73fb7ae868649\\\",\\\"dbIdValue\\\":\\\"02b2c9a0dcbb64f22da73fb7ae868649\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KSData\\\",\\\"targetValue\\\":\\\"KSData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   466618914378363971
2024-10-12T17:27:40.276+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:393126973198912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=77a11b91f4e301d20488c49197586a73&deviceId=399253820497998644
2024-10-12T17:27:40.276+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7643fd4dde02553e2b5a6ab2060f7ce8\\\",\\\"dbIdValue\\\":\\\"7643fd4dde02553e2b5a6ab2060f7ce8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"V6data\\\",\\\"targetValue\\\":\\\"V6data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   399253820497998644
2024-10-12T17:27:41.587+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320388624960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0df631020a8a500b08cdbab25ef451c4&deviceId=465763606437311028
2024-10-12T17:27:41.588+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"91f3562074a308af4c48878ee1a097fe\\\",\\\"dbIdValue\\\":\\\"91f3562074a308af4c48878ee1a097fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSJX_E10\\\",\\\"targetValue\\\":\\\"HSJX_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465763606437311028
2024-10-12T17:27:42.927+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323255009856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3324d69904fcedad604d68f218e419fc&deviceId=465755298628383801
2024-10-12T17:27:42.927+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3ad97d021bc2e10b4dcc70c6e61df1d0\\\",\\\"dbIdValue\\\":\\\"3ad97d021bc2e10b4dcc70c6e61df1d0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Dain\\\",\\\"targetValue\\\":\\\"E10_Dain\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465755298628383801
2024-10-12T17:27:44.242+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:489108120646208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=57300e9abd3ab19e930cd9bd29fd311b&deviceId=465345370289877047
2024-10-12T17:27:44.243+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"06127ddbc2472e8ba389f8c99a8e19d9\\\",\\\"dbIdValue\\\":\\\"06127ddbc2472e8ba389f8c99a8e19d9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DCDATA\\\",\\\"targetValue\\\":\\\"DCDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465345370289877047
2024-10-12T17:27:45.554+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:424984904491584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6a8af60118199c9b1f5a8d34572cf6c4&deviceId=465341799125235014
2024-10-12T17:27:45.555+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3b1319aaf6ea4d87cd546a01815ea463\\\",\\\"dbIdValue\\\":\\\"3b1319aaf6ea4d87cd546a01815ea463\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LDFC\\\",\\\"targetValue\\\":\\\"LDFC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465341799125235014
2024-10-12T17:27:46.915+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:379251966255680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5a539f8bfe4d33f42e007849afafeefd&deviceId=465338614557979715
2024-10-12T17:27:46.916+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1a5410f08855eeae129ed415327ab8ef\\\",\\\"dbIdValue\\\":\\\"1a5410f08855eeae129ed415327ab8ef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465338614557979715
2024-10-12T17:27:48.289+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321564721728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=062b3f4d251ba95e9f79606e5365cf60&deviceId=465317050332492080
2024-10-12T17:27:48.289+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"40d24917922fb07f16ea185b868978df\\\",\\\"dbIdValue\\\":\\\"40d24917922fb07f16ea185b868978df\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YB2023\\\",\\\"targetValue\\\":\\\"YB2023\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465317050332492080
2024-10-12T17:27:49.615+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:400211168641600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=961fab62f541d187a8778481fb68b4b9&deviceId=437651800129025091
2024-10-12T17:27:49.615+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9926ab009fa6f4fdc26d103446abf330\\\",\\\"dbIdValue\\\":\\\"9926ab009fa6f4fdc26d103446abf330\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_KH\\\",\\\"targetValue\\\":\\\"E10_KH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   437651800129025091
2024-10-12T17:27:50.923+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321181426240 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c97473ac15d44e4c365c43288211d4e6&deviceId=465198001070028853
2024-10-12T17:27:50.923+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1a13e74cf892b1d4cc64d8638d859199\\\",\\\"dbIdValue\\\":\\\"1a13e74cf892b1d4cc64d8638d859199\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CASP\\\",\\\"targetValue\\\":\\\"CASP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465198001070028853
2024-10-12T17:27:52.308+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:125584258277952 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=813e71dbb3d212ea1572c156edd565c5&deviceId=465178163001963332
2024-10-12T17:27:52.308+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"458c06aca7ee448374e42a6d498f9abe\\\",\\\"dbIdValue\\\":\\\"458c06aca7ee448374e42a6d498f9abe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GSLDATABASE\\\",\\\"targetValue\\\":\\\"GSLDATABASE\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465178163001963332
2024-10-12T17:27:53.651+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:477782751806016 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c1bdd58216619789254f5dc75ea3ed5a&deviceId=465175169325872195
2024-10-12T17:27:53.652+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5aed12a2875fdcc4f1ea81bad8351b7c\\\",\\\"dbIdValue\\\":\\\"5aed12a2875fdcc4f1ea81bad8351b7c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RSHC\\\",\\\"targetValue\\\":\\\"RSHC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465175169325872195
2024-10-12T17:27:54.950+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319590519360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=df53af24f21274c94e315e660b0dcc45&deviceId=465066933381771574
2024-10-12T17:27:54.951+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9d65100ce86018f955ab2683b3033b12\\\",\\\"dbIdValue\\\":\\\"9d65100ce86018f955ab2683b3033b12\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TYdata\\\",\\\"targetValue\\\":\\\"TYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465066933381771574
2024-10-12T17:27:56.288+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:132706661843520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d6ff1613811f0a5c863ff83447c783fd&deviceId=465056117244834883
2024-10-12T17:27:56.288+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"357f81765d46d33780c603427dd83aa6\\\",\\\"dbIdValue\\\":\\\"357f81765d46d33780c603427dd83aa6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XWdata\\\",\\\"targetValue\\\":\\\"XWdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465056117244834883
2024-10-12T17:27:57.628+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324995273280 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=441b6c110a3900aaa962d32cf7d17154&deviceId=464911243350651971
2024-10-12T17:27:57.628+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c8b6119b9308fafe2bf6484a1873eafa\\\",\\\"dbIdValue\\\":\\\"c8b6119b9308fafe2bf6484a1873eafa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YLdata\\\",\\\"targetValue\\\":\\\"YLdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464911243350651971
2024-10-12T17:27:58.985+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322626425408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=914e362789325d63a516ca1003b1be5c&deviceId=464908671436993603
2024-10-12T17:27:58.986+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"15fa99cdc46692acb7cf38d685ae0502\\\",\\\"dbIdValue\\\":\\\"15fa99cdc46692acb7cf38d685ae0502\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XTDATA\\\",\\\"targetValue\\\":\\\"XTDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464908671436993603
2024-10-12T17:28:00.307+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:499725619659328 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a56da85468abd4048ce16830364673b3&deviceId=464893664653489219
2024-10-12T17:28:00.307+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b8a0aaf300d959a12eea8413ae7668e2\\\",\\\"dbIdValue\\\":\\\"b8a0aaf300d959a12eea8413ae7668e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GXXL\\\",\\\"targetValue\\\":\\\"GXXL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464893664653489219
2024-10-12T17:28:01.784+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:53024056496704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=efb6bc6727b7af67126c9c986472d8b0&deviceId=464886528884094019
2024-10-12T17:28:01.785+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bf6ef48459a620fb483d44ac87ced574\\\",\\\"dbIdValue\\\":\\\"bf6ef48459a620fb483d44ac87ced574\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KSJT\\\",\\\"targetValue\\\":\\\"KSJT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464886528884094019
2024-10-12T17:28:03.125+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:327507379536448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=953c89a3512a663d10f51e5a772594e3&deviceId=464883337320739907
2024-10-12T17:28:03.125+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3e109defe53fb9e62129ebe0e71de89a\\\",\\\"dbIdValue\\\":\\\"3e109defe53fb9e62129ebe0e71de89a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LANBK\\\",\\\"targetValue\\\":\\\"LANBK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464883337320739907
2024-10-12T17:28:04.466+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317469078080 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6d1825cb9a08cbe02ccc2a54f420f139&deviceId=464746511742288692
2024-10-12T17:28:04.466+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"db2bdafb6d8fa5d73a565e091e1a3206\\\",\\\"dbIdValue\\\":\\\"db2bdafb6d8fa5d73a565e091e1a3206\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"wonder\\\",\\\"targetValue\\\":\\\"wonder\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464746511742288692
2024-10-12T17:28:05.806+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318785737280 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=62243f07bc6b94a5aab8d2fbe66bc715&deviceId=464338914681829687
2024-10-12T17:28:05.806+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4b486735bcf4464b231be3a89ea24659\\\",\\\"dbIdValue\\\":\\\"4b486735bcf4464b231be3a89ea24659\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JL\\\",\\\"targetValue\\\":\\\"JL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464338914681829687
2024-10-12T17:28:07.160+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322365366848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=51e822914a8574b5f74367ff9df9b655&deviceId=464332858358514241
2024-10-12T17:28:07.160+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9abf891325a57a4a1efd52a0ae63b394\\\",\\\"dbIdValue\\\":\\\"9abf891325a57a4a1efd52a0ae63b394\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"nbxl\\\",\\\"targetValue\\\":\\\"nbxl\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464332858358514241
2024-10-12T17:28:08.498+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:444686950593088 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7c0466c90525d0a036873921c473a3c3&deviceId=464194314289824821
2024-10-12T17:28:08.499+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ad3f15ec660080767032185392293506\\\",\\\"dbIdValue\\\":\\\"ad3f15ec660080767032185392293506\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Taiei\\\",\\\"targetValue\\\":\\\"Taiei\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464194314289824821
2024-10-12T17:28:09.990+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320588763712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7e639508d94d7c500e3d9a3a484c8245&deviceId=463892416408072756
2024-10-12T17:28:09.991+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2afa3384b568595960e400042d51da18\\\",\\\"dbIdValue\\\":\\\"2afa3384b568595960e400042d51da18\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YJSH\\\",\\\"targetValue\\\":\\\"YJSH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463892416408072756
2024-10-12T17:28:11.362+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322381623872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9fa25d54361f25911d5f833036c5cde8&deviceId=463758910285689923
2024-10-12T17:28:11.362+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b33cf9d1454a9f20a7e61a39304bc51d\\\",\\\"dbIdValue\\\":\\\"b33cf9d1454a9f20a7e61a39304bc51d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JBDZ\\\",\\\"targetValue\\\":\\\"JBDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463758910285689923
2024-10-12T17:28:12.685+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323312575040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=55a40d48e1b69797bf6fe28932845e2c&deviceId=463734806761451568
2024-10-12T17:28:12.686+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cb340627e0238b8bba25650444eb31c0\\\",\\\"dbIdValue\\\":\\\"cb340627e0238b8bba25650444eb31c0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SAGEZS\\\",\\\"targetValue\\\":\\\"SAGEZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463734806761451568
2024-10-12T17:28:13.994+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:501264037851712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=214b8bb204a0ad6b53ffcb9a9d1bd1c2&deviceId=463025207011980355
2024-10-12T17:28:13.994+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"66c996f25efd266fb944971ceec500a7\\\",\\\"dbIdValue\\\":\\\"66c996f25efd266fb944971ceec500a7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HLJGMN\\\",\\\"targetValue\\\":\\\"HLJGMN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463025207011980355
2024-10-12T17:28:15.321+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:412952614519360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a21ccfad2400fbae1e8542261529410c&deviceId=435479882227529538
2024-10-12T17:28:15.322+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f0c83d76ae39651134d5f7e99aef595d\\\",\\\"dbIdValue\\\":\\\"f0c83d76ae39651134d5f7e99aef595d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NPData\\\",\\\"targetValue\\\":\\\"NPData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   435479882227529538
2024-10-12T17:28:16.657+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:451107336372800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3c4376aab59126dc2523460d0b6caa44&deviceId=463151602128138288
2024-10-12T17:28:16.657+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"34b352fa553571993c9542ba848317e2\\\",\\\"dbIdValue\\\":\\\"34b352fa553571993c9542ba848317e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85032c2520a28d3081bb098083b7ffaa4fc6c3b92454c28d9db9c49b2eb3155a6ce9a3abfe8b8c0d116466f287d64dacab7957cd11a3061100c96782cdb27c61295c128f30138455311b3c12754a5ee7a18315c4a5dbd111aae59293aa76b0c3171dbc2a6f5eb14a4ed25300f6c3f37bd145f8d203ecdf3b58b781b34b9f74281e5102aaad11bfcb7d4ca9d4484929b2e0b229922e826ce8f4f5b38301f4edcadba2133074f922c6b75805dd3408574b7219143604e121075eee2c5336cc04c6391d43646783f067a5d43398d626f58af18debe294568917c3dd4d9eac9e11a2b3844cab580b23483ae765a9d0e6f25b473205cf2e8df7b8e6bf","collectName":"E10附件数据采集","accId":779544910123584,"adimId":717518472565313,"id":779886997021248,"adcId":603204787839552,"execParamsVersion":"97a75039788c74b48507c8d88b4f4d17","aiId":603204915262016,"isEnable":1},"paramsMap":{"deviceId":"463151602128138288","eid":451107336372800,"aiId":603204915262016,"execParamsDbAiId":603199616729664,"execParamsDbId":"34b352fa553571993c9542ba848317e2"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779886997021248 acc incomplete","batchId":779886997987904,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   463151602128138288
2024-10-12T17:28:18.018+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:132382187176512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c30bb75fef1d3fc157c57c3d92df5c5b&deviceId=463142253007747137
2024-10-12T17:28:18.019+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"23999f8f292d1af6e1545f5b7e1b5a9d\\\",\\\"dbIdValue\\\":\\\"23999f8f292d1af6e1545f5b7e1b5a9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_XFKJ\\\",\\\"targetValue\\\":\\\"E10_XFKJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463142253007747137
2024-10-12T17:28:19.390+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:295354470220352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4fa8044a7142f9004f04b85d1c3c91de&deviceId=431832336808555570
2024-10-12T17:28:19.391+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1e45583baecdc338045633f89f7e6ddb\\\",\\\"dbIdValue\\\":\\\"1e45583baecdc338045633f89f7e6ddb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ALZS\\\",\\\"targetValue\\\":\\\"ALZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   431832336808555570
2024-10-12T17:28:20.730+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:72097211044416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ba2a74b1a10599ad19aaf79adf7fa428&deviceId=462898555758065473
2024-10-12T17:28:20.730+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3fa2ae489da3ac4d953e1ac1c381147f\\\",\\\"dbIdValue\\\":\\\"3fa2ae489da3ac4d953e1ac1c381147f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HDdata\\\",\\\"targetValue\\\":\\\"HDdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   462898555758065473
2024-10-12T17:28:22.070+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:360403414884928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=90a855a29b16e44e181dee52213a2b7e&deviceId=462859431911109699
2024-10-12T17:28:22.070+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3dd9edf9e408b246c8bddfa3aa84e977\\\",\\\"dbIdValue\\\":\\\"3dd9edf9e408b246c8bddfa3aa84e977\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LIDA\\\",\\\"targetValue\\\":\\\"LIDA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   462859431911109699
2024-10-12T17:28:23.427+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:230554899939904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3c2ba5fac8831611dd8f7a94604c7d70&deviceId=462855865662189623
2024-10-12T17:28:23.427+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b2891d0e2dcc7375457f1bb3f880a7fe\\\",\\\"dbIdValue\\\":\\\"b2891d0e2dcc7375457f1bb3f880a7fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_YX\\\",\\\"targetValue\\\":\\\"E10_YX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   462855865662189623
2024-10-12T17:28:24.750+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321409221184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5003284fd37785f3d0511a424081963c&deviceId=451573999500013878
2024-10-12T17:28:24.751+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f4f4cea890fd2daf821ba83cf6496b5b\\\",\\\"dbIdValue\\\":\\\"f4f4cea890fd2daf821ba83cf6496b5b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451573999500013878
2024-10-12T17:28:26.085+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323811078720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c4176e7edaa5a77602445ee8bf2dc244&deviceId=462297770967974470
2024-10-12T17:28:26.085+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"\\\",\\\"dbIdValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   462297770967974470
2024-10-12T17:28:27.429+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319683080768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6f9a192539fbfd9d8adb733f58aaaeba&deviceId=461875906529473603
2024-10-12T17:28:27.429+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ef3fccf1ef9c73a75f8f9498f42b5c53\\\",\\\"dbIdValue\\\":\\\"ef3fccf1ef9c73a75f8f9498f42b5c53\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MLK\\\",\\\"targetValue\\\":\\\"MLK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461875906529473603
2024-10-12T17:28:28.787+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:506407357104704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=697b7d2d680330bf219a312e9db83bba&deviceId=461721480510583875
2024-10-12T17:28:28.788+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0231a11ec8a85fe32609d3a8d43a64dd\\\",\\\"dbIdValue\\\":\\\"0231a11ec8a85fe32609d3a8d43a64dd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TKSW\\\",\\\"targetValue\\\":\\\"TKSW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461721480510583875
2024-10-12T17:28:30.186+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320091927104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9d86e905926c0573cb1a48d11f800a2c&deviceId=461703602222675011
2024-10-12T17:28:30.187+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"92a128c53dc98f1a5c2f5f46e304db03\\\",\\\"dbIdValue\\\":\\\"92a128c53dc98f1a5c2f5f46e304db03\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RZDATA\\\",\\\"targetValue\\\":\\\"RZDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461703602222675011
2024-10-12T17:28:31.497+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317599695424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b6b516c260d7845bcfa2c7910da5d43d&deviceId=461699186912408386
2024-10-12T17:28:31.497+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a6a7fb89f010581c6d0bcb484f24bedf\\\",\\\"dbIdValue\\\":\\\"a6a7fb89f010581c6d0bcb484f24bedf\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJYFJX\\\",\\\"targetValue\\\":\\\"ZJYFJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461699186912408386
2024-10-12T17:28:32.899+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:331141344461376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3ef7b727c0195a894b1db36cef7cb7ac&deviceId=461691477160899651
2024-10-12T17:28:32.899+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c7d3dbbdd1d037120fe52a11568ec49f\\\",\\\"dbIdValue\\\":\\\"c7d3dbbdd1d037120fe52a11568ec49f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TDtest\\\",\\\"targetValue\\\":\\\"TDtest\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461691477160899651
2024-10-12T17:28:34.250+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:254857038070336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=04235a355d0355434f82527b83c2b981&deviceId=461260476035118147
2024-10-12T17:28:34.250+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"335bd747f827d82e27fb381e3e0fbbaa\\\",\\\"dbIdValue\\\":\\\"335bd747f827d82e27fb381e3e0fbbaa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461260476035118147
2024-10-12T17:28:35.574+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318789538368 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ba53dafaddd4de63334ac89b0f087c84&deviceId=461120118315824449
2024-10-12T17:28:35.574+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ce0acee85784cf37eb6eeb8ad080a01b\\\",\\\"dbIdValue\\\":\\\"ce0acee85784cf37eb6eeb8ad080a01b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HHDT\\\",\\\"targetValue\\\":\\\"HHDT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   461120118315824449
2024-10-12T17:28:36.925+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:315201539281472 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ffca8775b490c0d33a87f30d6d039aae&deviceId=460973531820209465
2024-10-12T17:28:36.925+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"056f6cb5d885fefe31c7708eeb6bafb0\\\",\\\"dbIdValue\\\":\\\"056f6cb5d885fefe31c7708eeb6bafb0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   460973531820209465
2024-10-12T17:28:38.280+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:332468446376512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c9856de260d4c7ee4bf952db73c485b6&deviceId=460864055435867203
2024-10-12T17:28:38.281+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"66d99ce8febfeae60ccbdc73f23509b4\\\",\\\"dbIdValue\\\":\\\"66d99ce8febfeae60ccbdc73f23509b4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   460864055435867203
2024-10-12T17:28:39.606+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:430201355084352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8ed40faaac2cf9f75245ece65412a126&deviceId=460855145844980038
2024-10-12T17:28:39.606+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e479765467fecfa23a7cc94f1bd0b132\\\",\\\"dbIdValue\\\":\\\"e479765467fecfa23a7cc94f1bd0b132\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   460855145844980038
2024-10-12T17:28:40.945+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:412952614392384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=170d626cabafafe973f49ce560557e40&deviceId=449697624086294834
2024-10-12T17:28:40.945+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"73b6a8c746a192190e12c1e63bbc249b\\\",\\\"dbIdValue\\\":\\\"73b6a8c746a192190e12c1e63bbc249b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449697624086294834
2024-10-12T17:28:42.268+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:367485118304832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bb1c6bed8fb94dd66e5ed3dbe3c4fbae&deviceId=450999743800161347
2024-10-12T17:28:42.269+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b6ea7a16c977afe1cf0e5b22f3d75919\\\",\\\"dbIdValue\\\":\\\"b6ea7a16c977afe1cf0e5b22f3d75919\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450999743800161347
2024-10-12T17:28:43.588+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:293614494167616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cfb61f2f2d86d776ab8d0eb6b66b0721&deviceId=450705649806881585
2024-10-12T17:28:43.588+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e91a0ba2f31dcd139d329b621da5ece2\\\",\\\"dbIdValue\\\":\\\"e91a0ba2f31dcd139d329b621da5ece2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450705649806881585
2024-10-12T17:28:44.944+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:291210039718464 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=12911c3be5ce6cc08656b48168bab62b&deviceId=449991898149434435
2024-10-12T17:28:44.944+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"688a177b8c9fa5a2d9fd71387543fa46\\\",\\\"dbIdValue\\\":\\\"688a177b8c9fa5a2d9fd71387543fa46\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"szml2022\\\",\\\"targetValue\\\":\\\"szml2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449991898149434435
2024-10-12T17:28:46.302+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:209568561267264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=35da90545c9d6f8d1b456643acfae381&deviceId=450098317423035460
2024-10-12T17:28:46.302+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e82faaca07495767e46da9731031ce4c\\\",\\\"dbIdValue\\\":\\\"e82faaca07495767e46da9731031ce4c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450098317423035460
2024-10-12T17:28:47.657+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:179273588060736 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=318981e3784cd452a0853088ce67e01b&deviceId=405038473695736377
2024-10-12T17:28:47.658+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c35ef9a880fa3eab398cd7fa8d6f5775\\\",\\\"dbIdValue\\\":\\\"c35ef9a880fa3eab398cd7fa8d6f5775\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FQDATA\\\",\\\"targetValue\\\":\\\"FQDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   405038473695736377
2024-10-12T17:28:48.967+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:175763947459136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5fdc3f98b4b542e5bb51b2975f0e0454&deviceId=429800008414410566
2024-10-12T17:28:48.967+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c82eea34c9968f99e171b15cfba7bdfd\\\",\\\"dbIdValue\\\":\\\"c82eea34c9968f99e171b15cfba7bdfd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   429800008414410566
2024-10-12T17:28:50.309+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:173729077674560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1a4e0b06cea0ced4719dac4e8895f53a&deviceId=451006382930408755
2024-10-12T17:28:50.309+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f0992c8c8ec1956b44f9607e0e008752\\\",\\\"dbIdValue\\\":\\\"f0992c8c8ec1956b44f9607e0e008752\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451006382930408755
2024-10-12T17:28:51.649+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:122753130824256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=900637e4427cd8a7a30097f492f74732&deviceId=450132715547673410
2024-10-12T17:28:51.649+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1832198daeec0aa4d0f7b57856c61c7d\\\",\\\"dbIdValue\\\":\\\"1832198daeec0aa4d0f7b57856c61c7d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450132715547673410
2024-10-12T17:28:53.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:84483431494208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=09f99b108a8462ea806e85c19a57599c&deviceId=449693335058134066
2024-10-12T17:28:53.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0e222b60382df8e6a80801351b9a5fd7\\\",\\\"dbIdValue\\\":\\\"0e222b60382df8e6a80801351b9a5fd7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449693335058134066
2024-10-12T17:28:54.339+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:65373234971200 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b9cbbc5c5f1d02cf042a77550ad1abc5&deviceId=449553448862495541
2024-10-12T17:28:54.340+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4fb7006c68d9e9e4623726c88d491e6f\\\",\\\"dbIdValue\\\":\\\"4fb7006c68d9e9e4623726c88d491e6f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HHCK\\\",\\\"targetValue\\\":\\\"HHCK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449553448862495541
2024-10-12T17:28:55.751+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233430182216256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e19c0c5f1ee4cb37b428e0105ef40dd4&deviceId=385627501172373045
2024-10-12T17:28:55.751+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"88a1ebedec72bb7ba02742e0c841e2e2\\\",\\\"dbIdValue\\\":\\\"88a1ebedec72bb7ba02742e0c841e2e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   385627501172373045
2024-10-12T17:28:57.092+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321792459328 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4970f72b704fe3ee663e81bc6f5642f9&deviceId=450975154877510470
2024-10-12T17:28:57.092+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c374c9a7d704958a8a2eb4eb2e1d7ddb\\\",\\\"dbIdValue\\\":\\\"c374c9a7d704958a8a2eb4eb2e1d7ddb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JHJC\\\",\\\"targetValue\\\":\\\"JHJC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450975154877510470
2024-10-12T17:28:58.421+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320212140608 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aa91340ac3535b4120d34d9f34dfedfb&deviceId=430848485294618692
2024-10-12T17:28:58.421+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e10a8c2dbc9efb808d2f9158c48bac93\\\",\\\"dbIdValue\\\":\\\"e10a8c2dbc9efb808d2f9158c48bac93\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SDJM\\\",\\\"targetValue\\\":\\\"SDJM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   430848485294618692
2024-10-12T17:28:59.790+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320672498240 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=22fe7b73ac07b29e4e110565b337302d&deviceId=421583480711819320
2024-10-12T17:28:59.791+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"856f872c5bea1e593dcd174c0e8b9d7c\\\",\\\"dbIdValue\\\":\\\"856f872c5bea1e593dcd174c0e8b9d7c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   421583480711819320
2024-10-12T17:29:01.170+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41325075677760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4f8640586ee143e6517bbc7b74a34e15&deviceId=448831002916176450
2024-10-12T17:29:01.170+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"df9c8c74bfbf6c4fbaf3d376aa95cc5f\\\",\\\"dbIdValue\\\":\\\"df9c8c74bfbf6c4fbaf3d376aa95cc5f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448831002916176450
2024-10-12T17:29:02.478+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323741962816 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1ff51babd17f22da072b129abcc8f451&deviceId=448648599765530936
2024-10-12T17:29:02.478+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a413431b31abdf89d1672eb3b24be415\\\",\\\"dbIdValue\\\":\\\"a413431b31abdf89d1672eb3b24be415\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448648599765530936
2024-10-12T17:29:03.830+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323544035904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0242063e381de3aca9617f8c8f22d01f&deviceId=447629864275554371
2024-10-12T17:29:03.830+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"65bcb89fed711df5e0bd49ace30f845b\\\",\\\"dbIdValue\\\":\\\"65bcb89fed711df5e0bd49ace30f845b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   447629864275554371
2024-10-12T17:29:05.197+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323654804032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=926ecbb4171c633bd262bef231eb9bae&deviceId=459950992717263681
2024-10-12T17:29:05.198+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f2d41ad6d31f9c8d8e936cd393f6a79f\\\",\\\"dbIdValue\\\":\\\"f2d41ad6d31f9c8d8e936cd393f6a79f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   459950992717263681
2024-10-12T17:29:06.526+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323764019776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a1b3c2753af5002c7250b672f71c28d3&deviceId=459689414042334275
2024-10-12T17:29:06.526+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a5befb7a183cb3d1da7546f2f76a6cb2\\\",\\\"dbIdValue\\\":\\\"a5befb7a183cb3d1da7546f2f76a6cb2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   459689414042334275
2024-10-12T17:29:08.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:185136800076352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8b0848d5e55fee258895c4e98bddaf56&deviceId=457953039513302083
2024-10-12T17:29:08.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"efe5eaede2924ee8624c6664ca79a5a1\\\",\\\"dbIdValue\\\":\\\"efe5eaede2924ee8624c6664ca79a5a1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LJ\\\",\\\"targetValue\\\":\\\"LJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457953039513302083
2024-10-12T17:29:09.345+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320452276800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2ce1957b0248141007bc23a1b774e305&deviceId=457944780156974903
2024-10-12T17:29:09.345+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6fcc6e61dbdc37c72f1f989a48e13fca\\\",\\\"dbIdValue\\\":\\\"6fcc6e61dbdc37c72f1f989a48e13fca\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JJQC\\\",\\\"targetValue\\\":\\\"JJQC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457944780156974903
2024-10-12T17:29:10.680+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:397727998583360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6b5d5bc8aa5971ee8eded70a2d9719d0&deviceId=457934657321711670
2024-10-12T17:29:10.680+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6cf566c983a64db561458e8b7215146e\\\",\\\"dbIdValue\\\":\\\"6cf566c983a64db561458e8b7215146e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CZDZ\\\",\\\"targetValue\\\":\\\"CZDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457934657321711670
2024-10-12T17:29:12.004+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323477541440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2d1d6690c30fe5da2e5abc2b887179a8&deviceId=431732625183360581
2024-10-12T17:29:12.004+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"006307b238bfd1a1ae48daeaffffb4af\\\",\\\"dbIdValue\\\":\\\"006307b238bfd1a1ae48daeaffffb4af\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   431732625183360581
2024-10-12T17:29:13.345+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:45583119381056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=942fbe25ecd422b643a925e507d2b7fd&deviceId=457641298002719792
2024-10-12T17:29:13.345+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"aa759dc84c138aca9a549d61c0543d1d\\\",\\\"dbIdValue\\\":\\\"aa759dc84c138aca9a549d61c0543d1d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457641298002719792
2024-10-12T17:29:14.685+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324122468928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cea348d8727b501bea65616f86dcecb0&deviceId=449082269659838515
2024-10-12T17:29:14.685+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d029e2cb1f98a7c8dbb95d2c83678669\\\",\\\"dbIdValue\\\":\\\"d029e2cb1f98a7c8dbb95d2c83678669\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449082269659838515
2024-10-12T17:29:16.034+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:159214592426560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dc7b0ab2b4ad575cccc38d0a22cbd351&deviceId=457202982061093444
2024-10-12T17:29:16.034+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6d79bc41c1d2c74637099c2bf6b7b9ab\\\",\\\"dbIdValue\\\":\\\"6d79bc41c1d2c74637099c2bf6b7b9ab\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSL\\\",\\\"targetValue\\\":\\\"HSL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457202982061093444
2024-10-12T17:29:17.372+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324496147008 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c19b0894f9394f24a9d64ab48374cd4c&deviceId=448968263964050737
2024-10-12T17:29:17.373+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"553adbd1cac48bbdeb576aa564bb7d39\\\",\\\"dbIdValue\\\":\\\"553adbd1cac48bbdeb576aa564bb7d39\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448968263964050737
2024-10-12T17:29:18.693+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322175803968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=28d82342d07be2b2c5bfae18759f61a9&deviceId=448950308752602179
2024-10-12T17:29:18.693+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7bfd971d7171090ec26a66af07400a22\\\",\\\"dbIdValue\\\":\\\"7bfd971d7171090ec26a66af07400a22\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448950308752602179
2024-10-12T17:29:20.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317888270912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4f3befae221c2046321b7c3785aa8e4b&deviceId=448948806050919480
2024-10-12T17:29:20.003+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d9c89bee00454bb63125cc639aafc003\\\",\\\"dbIdValue\\\":\\\"d9c89bee00454bb63125cc639aafc003\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448948806050919480
2024-10-12T17:29:21.375+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318761796160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=445fd6e25474e5829b0d2ca9bfeedd4b&deviceId=448788730707195954
2024-10-12T17:29:21.375+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bc2485a3b72f51da856772f31115d822\\\",\\\"dbIdValue\\\":\\\"bc2485a3b72f51da856772f31115d822\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448788730707195954
2024-10-12T17:29:22.761+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:365005571555904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ed12e244569af0a34757792655662347&deviceId=448814031302575666
2024-10-12T17:29:22.761+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4f7396282066a589deac9774d18c6e7d\\\",\\\"dbIdValue\\\":\\\"4f7396282066a589deac9774d18c6e7d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448814031302575666
2024-10-12T17:29:24.117+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320121475648 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=744f3db48f741f70fe7f2e41f99b6370&deviceId=448658403665918262
2024-10-12T17:29:24.117+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f6f3d07129513f2a1db1dd0f289b3093\\\",\\\"dbIdValue\\\":\\\"f6f3d07129513f2a1db1dd0f289b3093\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448658403665918262
2024-10-12T17:29:25.500+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:330330196791872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9b39501229a67ec11e039da94cf5a7d0&deviceId=448668501620568880
2024-10-12T17:29:25.500+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f67097ff03d39ee7445522f6b9cc6460\\\",\\\"dbIdValue\\\":\\\"f67097ff03d39ee7445522f6b9cc6460\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TSZS\\\",\\\"targetValue\\\":\\\"TSZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   448668501620568880
2024-10-12T17:29:26.807+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320209658432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f5285b7ed13de8b2316af5e2981ce691&deviceId=447512815628988980
2024-10-12T17:29:26.807+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cbbaabe25bb2df182a783091163b574b\\\",\\\"dbIdValue\\\":\\\"cbbaabe25bb2df182a783091163b574b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HBbill\\\",\\\"targetValue\\\":\\\"HBbill\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   447512815628988980
2024-10-12T17:29:28.127+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:247988360983104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=df596407f9b6db821ec304c051eaa6a5&deviceId=457194514247857976
2024-10-12T17:29:28.128+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ae75922eb420bed512d4f39727c5e970\\\",\\\"dbIdValue\\\":\\\"ae75922eb420bed512d4f39727c5e970\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457194514247857976
2024-10-12T17:29:29.495+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:379871274742336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c226672986e1701b73b0bbf7b85952fc&deviceId=457083271507882039
2024-10-12T17:29:29.495+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"446d1390f8ee75aedfc5a1d82d6474c9\\\",\\\"dbIdValue\\\":\\\"446d1390f8ee75aedfc5a1d82d6474c9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   457083271507882039
2024-10-12T17:29:30.831+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317478605376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fd2c158a682fd1d561d2f1b3ff65c40d&deviceId=456932285908795961
2024-10-12T17:29:30.831+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d3911b321d8c788da1fe3ec0fbcdda66\\\",\\\"dbIdValue\\\":\\\"d3911b321d8c788da1fe3ec0fbcdda66\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456932285908795961
2024-10-12T17:29:32.158+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:177312440308288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6569c0e52f855992f60d5cbfbf305a62&deviceId=456917541185406018
2024-10-12T17:29:32.159+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9d9d70af58bd1d50ed5211acb8d9fcd9\\\",\\\"dbIdValue\\\":\\\"9d9d70af58bd1d50ed5211acb8d9fcd9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KLFTZS\\\",\\\"targetValue\\\":\\\"KLFTZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456917541185406018
2024-10-12T17:29:33.559+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233423735304768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=66a0bf13e4e55059def9075ab68b6ac4&deviceId=456799204434523203
2024-10-12T17:29:33.559+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e52e1eb557b25723391f2e09445b60ff\\\",\\\"dbIdValue\\\":\\\"e52e1eb557b25723391f2e09445b60ff\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HBSdata\\\",\\\"targetValue\\\":\\\"HBSdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456799204434523203
2024-10-12T17:29:34.882+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:323068879934016 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a6075f796f0aa7ecb0acca28da5a4e17&deviceId=456791594104796998
2024-10-12T17:29:34.882+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f45003f82aa6cf1e245b93f04bd40b6b\\\",\\\"dbIdValue\\\":\\\"f45003f82aa6cf1e245b93f04bd40b6b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456791594104796998
2024-10-12T17:29:36.206+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323386290752 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dc6fc400993c1bb5c58a3b149e7d33ae&deviceId=456207384969229379
2024-10-12T17:29:36.206+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"60311a993dcc7cd32ba2e88b1cbe7050\\\",\\\"dbIdValue\\\":\\\"60311a993dcc7cd32ba2e88b1cbe7050\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YANJAN\\\",\\\"targetValue\\\":\\\"YANJAN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456207384969229379
2024-10-12T17:29:37.576+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:425265043907136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c6f675e9738f8516540bc8547bc7c57e&deviceId=456184590621488195
2024-10-12T17:29:37.577+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"620c66b8a6600d4da132802dd1b4aaca\\\",\\\"dbIdValue\\\":\\\"620c66b8a6600d4da132802dd1b4aaca\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"[E10_XK]\\\",\\\"targetValue\\\":\\\"[E10_XK]\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456184590621488195
2024-10-12T17:29:38.927+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324213752384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=de521edc404c8194cc5356135d1815c0&deviceId=456044179718420016
2024-10-12T17:29:38.927+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"eb07066c57ba046c4d82867bb5e3cd1c\\\",\\\"dbIdValue\\\":\\\"eb07066c57ba046c4d82867bb5e3cd1c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456044179718420016
2024-10-12T17:29:40.255+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319638934080 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f5d17cb9b6e02b13df62d1ab7cdb450a&deviceId=455921677621015620
2024-10-12T17:29:40.255+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"828e777b59ee269ba700eb0818fba936\\\",\\\"dbIdValue\\\":\\\"828e777b59ee269ba700eb0818fba936\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"colors\\\",\\\"targetValue\\\":\\\"colors\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455921677621015620
2024-10-12T17:29:41.668+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:464201288077888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=403562561ed4dde1adb96525d2edd303&deviceId=455915183546582326
2024-10-12T17:29:41.669+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6976b50b0444a374e8dd692983a69e8b\\\",\\\"dbIdValue\\\":\\\"6976b50b0444a374e8dd692983a69e8b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455915183546582326
2024-10-12T17:29:43.177+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:437692042146368 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2e10fb9a2118b03add1afdde13ebe5cd&deviceId=455791644868883523
2024-10-12T17:29:43.177+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5b6251a1bfa77ec05d3ae0738a6aa508\\\",\\\"dbIdValue\\\":\\\"5b6251a1bfa77ec05d3ae0738a6aa508\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455791644868883523
2024-10-12T17:29:44.486+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324861739584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9fbdc8cf45ce9197df62c9bfe675ff5c&deviceId=455792820414858552
2024-10-12T17:29:44.486+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"880534a8a1e754edaa14ad7ef884cf5c\\\",\\\"dbIdValue\\\":\\\"880534a8a1e754edaa14ad7ef884cf5c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"EVERJET\\\",\\\"targetValue\\\":\\\"EVERJET\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455792820414858552
2024-10-12T17:29:45.845+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321556935232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2b91889b5170226ebb1e8a896bfdcfe5&deviceId=455780901763168577
2024-10-12T17:29:45.845+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9608fd02927ec3bddb84fdd177de6c70\\\",\\\"dbIdValue\\\":\\\"9608fd02927ec3bddb84fdd177de6c70\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455780901763168577
2024-10-12T17:29:47.184+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:494063198863936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=059fa2b2a69fdbfd0a7604a9a10ae94b&deviceId=455755585481093443
2024-10-12T17:29:47.185+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2d29c878afb29cef468b2533d5040b42\\\",\\\"dbIdValue\\\":\\\"2d29c878afb29cef468b2533d5040b42\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455755585481093443
2024-10-12T17:29:48.581+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:433769757155904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6f1efb9945b391d9d688de1f755d8c6a&deviceId=455750660848108610
2024-10-12T17:29:48.581+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8eaae47e52e106c14259a5b24b3d8581\\\",\\\"dbIdValue\\\":\\\"8eaae47e52e106c14259a5b24b3d8581\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455750660848108610
2024-10-12T17:29:49.954+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:216292631134784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8df480cd47c109297349c36488d67452&deviceId=455746214583877956
2024-10-12T17:29:49.954+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5e643e831a2a5dd7a3ad85c12ebe569b\\\",\\\"dbIdValue\\\":\\\"5e643e831a2a5dd7a3ad85c12ebe569b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455746214583877956
2024-10-12T17:29:51.295+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:489770684957248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f201fdfedccccfbaa77ecdb2855c7cc1&deviceId=455035416957958449
2024-10-12T17:29:51.295+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2729bc27de00196673684dfdb398d284\\\",\\\"dbIdValue\\\":\\\"2729bc27de00196673684dfdb398d284\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   455035416957958449
2024-10-12T17:29:52.607+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:367131227071040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4f0a606adb63796a3a2a65f7fa50338a&deviceId=454763203876893493
2024-10-12T17:29:52.607+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"30ead5c66dd0dd392f81b2ee96c30843\\\",\\\"dbIdValue\\\":\\\"30ead5c66dd0dd392f81b2ee96c30843\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454763203876893493
2024-10-12T17:29:53.950+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318257660480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bdb9dec108c87d04459c873d34397947&deviceId=454595298489021766
2024-10-12T17:29:53.950+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1811db0db1cb8d1757e7e5a4beb446b1\\\",\\\"dbIdValue\\\":\\\"1811db0db1cb8d1757e7e5a4beb446b1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454595298489021766
2024-10-12T17:29:55.314+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:347313279197760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=32b7bd2fa909198504df329d419c7b26&deviceId=454161330446808131
2024-10-12T17:29:55.314+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"038f3a268a45a4a6b231a868c06a10a6\\\",\\\"dbIdValue\\\":\\\"038f3a268a45a4a6b231a868c06a10a6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454161330446808131
2024-10-12T17:29:56.654+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318745928256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e1725248d9f3f8b9ddbdbdd3ed26a470&deviceId=454038149828457780
2024-10-12T17:29:56.654+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d6b3d60572d58f889151b6217d2417ef\\\",\\\"dbIdValue\\\":\\\"d6b3d60572d58f889151b6217d2417ef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454038149828457780
2024-10-12T17:29:58.069+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324279341632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3d3db307b08433f5bc6ec9fcf89b3679&deviceId=454015059429963073
2024-10-12T17:29:58.069+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"228d56d6dbe63ce8d520017eb6af5fd1\\\",\\\"dbIdValue\\\":\\\"228d56d6dbe63ce8d520017eb6af5fd1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"OT01\\\",\\\"targetValue\\\":\\\"OT01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454015059429963073
2024-10-12T17:30:02.341+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:177327185748544 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f7d4b1cd21f1be74e5001274bf843ea8&deviceId=384025625507153459
2024-10-12T17:30:02.341+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"54f3045a74f23d9cf44cef4e7637f7ff\\\",\\\"dbIdValue\\\":\\\"54f3045a74f23d9cf44cef4e7637f7ff\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   384025625507153459
2024-10-12T17:30:15.626+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319279120960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fd1774a147a8eeebe73ddfa6761daa64&deviceId=451146238776062000
2024-10-12T17:30:15.626+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0aa38b99707903c77e54c45e6cc0d3c9\\\",\\\"dbIdValue\\\":\\\"0aa38b99707903c77e54c45e6cc0d3c9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HEATWELL\\\",\\\"targetValue\\\":\\\"HEATWELL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451146238776062000
2024-10-12T17:30:17.059+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318030893632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d4d39120d3a16c0068907a85fa782bcd&deviceId=453746928681759032
2024-10-12T17:30:17.059+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"619642c5e676d617142f4046fcad382c\\\",\\\"dbIdValue\\\":\\\"619642c5e676d617142f4046fcad382c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   453746928681759032
2024-10-12T17:30:18.535+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:354739886670400 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7d29787a06ff922e3b47281c0a712c27&deviceId=453718829780976435
2024-10-12T17:30:18.535+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d2adeeca48e9f8fcc04c815e516a3949\\\",\\\"dbIdValue\\\":\\\"d2adeeca48e9f8fcc04c815e516a3949\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   453718829780976435
2024-10-12T17:30:19.890+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:380298921230912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=341b2c87771926afd96b4e7c661e3f7a&deviceId=453580719436870723
2024-10-12T17:30:19.890+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28e004857c78ad82f51ad5d54df0e3f5\\\",\\\"dbIdValue\\\":\\\"28e004857c78ad82f51ad5d54df0e3f5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DH\\\",\\\"targetValue\\\":\\\"DH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   453580719436870723
2024-10-12T17:30:21.276+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:392419644908096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f06119be73a866e2fe610c1ab5983f5c&deviceId=452892555818844995
2024-10-12T17:30:21.276+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e2fa3673592b42a4c6a6ff064740a2a4\\\",\\\"dbIdValue\\\":\\\"e2fa3673592b42a4c6a6ff064740a2a4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RLJT6003\\\",\\\"targetValue\\\":\\\"RLJT6003\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452892555818844995
2024-10-12T17:30:22.585+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:126557462741568 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=092aaf175f63fea4a020b2a3548f9068&deviceId=452876965104333891
2024-10-12T17:30:22.585+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"71409e8a33eebccf38a3190d5c548479\\\",\\\"dbIdValue\\\":\\\"71409e8a33eebccf38a3190d5c548479\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452876965104333891
2024-10-12T17:30:24.014+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:212237481988672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=34d2f3a1269faea410d075220a07d81b&deviceId=452857525646144579
2024-10-12T17:30:24.014+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1910b44d803c0ad5cfbc9a010b7c9f81\\\",\\\"dbIdValue\\\":\\\"1910b44d803c0ad5cfbc9a010b7c9f81\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452857525646144579
2024-10-12T17:30:25.356+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318029238848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=64d9fa55100dc408b30c30b9f71560b6&deviceId=452743872422884921
2024-10-12T17:30:25.356+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f9c6c3bb2fe0894bf3750b9699e86de\\\",\\\"dbIdValue\\\":\\\"1f9c6c3bb2fe0894bf3750b9699e86de\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452743872422884921
2024-10-12T17:30:26.708+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:329725743227456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=44d71fda35ab795a9fd1c327a0ede6c1&deviceId=452739352271795267
2024-10-12T17:30:26.708+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"12684a8f222dd6878c242d61ec351dbd\\\",\\\"dbIdValue\\\":\\\"12684a8f222dd6878c242d61ec351dbd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BASE60031\\\",\\\"targetValue\\\":\\\"BASE60031\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452739352271795267
2024-10-12T17:30:28.065+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:389912946221632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c361497f21e3ed89a4efe60fb7f6f2db&deviceId=452735839609304386
2024-10-12T17:30:28.065+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"90c801bc26801ca20a97083379edf9b2\\\",\\\"dbIdValue\\\":\\\"90c801bc26801ca20a97083379edf9b2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452735839609304386
2024-10-12T17:30:29.465+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:60462979330624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=28874e58ac651a44ced5a6dec3878ab7&deviceId=452728724056454964
2024-10-12T17:30:29.465+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f4a990a7238712b36719ff901192354\\\",\\\"dbIdValue\\\":\\\"1f4a990a7238712b36719ff901192354\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HXWJ\\\",\\\"targetValue\\\":\\\"HXWJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452728724056454964
2024-10-12T17:30:30.772+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:315452111704640 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=51641e93f6b7b93b3611cd09046413a7&deviceId=452711692464305219
2024-10-12T17:30:30.772+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5009eb747e24c400f2839077c2940174\\\",\\\"dbIdValue\\\":\\\"5009eb747e24c400f2839077c2940174\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"[E10_AXZCZS]\\\",\\\"targetValue\\\":\\\"[E10_AXZCZS]\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452711692464305219
2024-10-12T17:30:32.126+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:284042089181760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dd5c457eb2a0f7b4c70157dd4d6bb6b7&deviceId=452573065046410550
2024-10-12T17:30:32.126+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a8be74a3633d4842e345e4057baa8298\\\",\\\"dbIdValue\\\":\\\"a8be74a3633d4842e345e4057baa8298\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452573065046410550
2024-10-12T17:30:33.560+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321717674560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d5d348acc8ebf27d99d63ed46995ea18&deviceId=452582810394309688
2024-10-12T17:30:33.560+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b34a5ceb671c782908498cd4f2b99c73\\\",\\\"dbIdValue\\\":\\\"b34a5ceb671c782908498cd4f2b99c73\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452582810394309688
2024-10-12T17:30:34.888+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:327242104042048 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=be1bcf7d45d27dc434989a95deb13731&deviceId=452568400812586562
2024-10-12T17:30:34.888+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fa9e928d1a8703f00dc59ee4f352b56d\\\",\\\"dbIdValue\\\":\\\"fa9e928d1a8703f00dc59ee4f352b56d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TLS\\\",\\\"targetValue\\\":\\\"TLS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452568400812586562
2024-10-12T17:30:36.211+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318227792448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d7840c8b13a3a6262f82249efc5fd771&deviceId=452169917286855479
2024-10-12T17:30:36.211+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3588864fa93fa56dbfd4f481c5e8ec2e\\\",\\\"dbIdValue\\\":\\\"3588864fa93fa56dbfd4f481c5e8ec2e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LX\\\",\\\"targetValue\\\":\\\"LX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452169917286855479
2024-10-12T17:30:37.763+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323086492224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=afe93c4acfd7c05cad6a83007db9032e&deviceId=452168124809098307
2024-10-12T17:30:37.763+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0bf705d1bc04e6f8d368caec7ea25e60\\\",\\\"dbIdValue\\\":\\\"0bf705d1bc04e6f8d368caec7ea25e60\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"VEKAN\\\",\\\"targetValue\\\":\\\"VEKAN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85038e8571a9960f401efd833a2787e0a6ff97bb852115b9ebb626b1102e0d88a87e1dd57c8c304d8e9982fba74899f410474de3e0fdfd02329753f0bba359ac241ac3a746a46f1dcb820b75a756205a6fe7dd7c37aac70c045310de727cf100c18980c878118397fe1aff36785cd8676f891813eefad3fe67c7ff6bfb203b023640f34a8e7c9ed8226474ba97b8448d414bdd7fcd3dd2605f09837d9671e1df6c608bb4ac53dbabf475cff7736d75ed95c12d99804e46c59477433c457d2588f2a0b3f5adde8ed537069efe6eaa66f105423dd03353e9fd936278154304b70769f0a5c25da279e54d4061ea5f460f57403d625a898262d98c58","collectName":"E10附件数据采集","accId":779544910123584,"adimId":622384427725376,"id":779887575102016,"adcId":576388506145344,"execParamsVersion":"087ad20003d02ef9b3280fbad95859f7","aiId":576388623139393,"isEnable":1},"paramsMap":{"deviceId":"452168124809098307","eid":41323086492224,"aiId":576388623139393,"execParamsDbAiId":576388450165312,"execParamsDbId":"0bf705d1bc04e6f8d368caec7ea25e60"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779887575102016 acc incomplete","batchId":779887576138304,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   452168124809098307
2024-10-12T17:30:39.192+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320613888576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b854d542c3e0726c25c47645d2463d9e&deviceId=452162792238100784
2024-10-12T17:30:39.192+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"74d31fa87252c57246a9ac1f4045778c\\\",\\\"dbIdValue\\\":\\\"74d31fa87252c57246a9ac1f4045778c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JSFL\\\",\\\"targetValue\\\":\\\"JSFL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452162792238100784
2024-10-12T17:30:40.547+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:471722069742144 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=60804101c1ccb48d79d91a767c53af89&deviceId=451868323794138179
2024-10-12T17:30:40.547+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"011af21441f9d18562f93e61723ac449\\\",\\\"dbIdValue\\\":\\\"011af21441f9d18562f93e61723ac449\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FUJIAdata\\\",\\\"targetValue\\\":\\\"FUJIAdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451868323794138179
2024-10-12T17:30:41.885+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319249560128 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2e9d74e1ea9f881e3a0414137a014b24&deviceId=452130054118324548
2024-10-12T17:30:41.885+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"35a1aed84566e843aa9d5ec04eb16906\\\",\\\"dbIdValue\\\":\\\"35a1aed84566e843aa9d5ec04eb16906\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452130054118324548
2024-10-12T17:30:43.438+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:248135430009408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1b8c89d22152a5260e566cd713e539a9&deviceId=452126151855322179
2024-10-12T17:30:43.438+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"63f3b683ea00213480cea28af0b7af92\\\",\\\"dbIdValue\\\":\\\"63f3b683ea00213480cea28af0b7af92\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_MSDS\\\",\\\"targetValue\\\":\\\"E10_MSDS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452126151855322179
2024-10-12T17:30:44.809+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:333170163036736 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=47066fb44aa0acb172a8126cd50b249f&deviceId=452009088562246962
2024-10-12T17:30:44.810+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c56ab77b6f47266e5db47f05ebb6e95e\\\",\\\"dbIdValue\\\":\\\"c56ab77b6f47266e5db47f05ebb6e95e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JXJT\\\",\\\"targetValue\\\":\\\"JXJT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452009088562246962
2024-10-12T17:30:46.270+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:328200457912896 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ce9f2ab4c90ef9b3f1fc75b90f6ee067&deviceId=452023510441669429
2024-10-12T17:30:46.271+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d509c0f6aca141f5926d70e83824b8b1\\\",\\\"dbIdValue\\\":\\\"d509c0f6aca141f5926d70e83824b8b1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452023510441669429
2024-10-12T17:30:47.638+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233421788701248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=43d00b3ee6e4658c64b84bdf7a0a8003&deviceId=452018454308859952
2024-10-12T17:30:47.638+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"85f4e5ddef48d5025a2c0c8a4c90ad51\\\",\\\"dbIdValue\\\":\\\"85f4e5ddef48d5025a2c0c8a4c90ad51\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   452018454308859952
2024-10-12T17:30:48.967+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320709861952 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=34dd1dfa04c71ff35ff32dfe4822f5a7&deviceId=447823124113667139
2024-10-12T17:30:48.967+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0439abad3b7303237be2ace0fa27bec0\\\",\\\"dbIdValue\\\":\\\"0439abad3b7303237be2ace0fa27bec0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JINYU\\\",\\\"targetValue\\\":\\\"JINYU\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503f7b826d557f0960a16bf98b754c286bf0a18f35ead6cc24c0baaf273e3b7bdeb00f1463ac05a65e6103e9b41e9de525639a02d398430cf6d0b9446092ae786f3fe6401a68746257fae0fef5bbf509189c6772fd408db33a4d2476ccc6e6ac0548aaabe5fae6918c4387d517d842a993c333489d2f722d5b341b50cac095607d12f60df43058bb7c9917647c964ac38ad83de6f91a99a0c99a24028a878331c6561a15ec17b18e47f8861643b3ffa3a0b7dba44e4f23e5dad30c9ba37f8d5e7c9e066bc20c497df536ef73610799b3349e56b18e83e8bcd4734932d621a0acca701e286956fd291baaf7764bd93efa78ba72a9d3fbd808efc","collectName":"E10附件数据采集","accId":779544910123584,"adimId":617087968490048,"id":779887620891200,"adcId":565781481529920,"execParamsVersion":"894d54c1df201a387e0dc299340beb19","aiId":575936926736961,"isEnable":0},"paramsMap":{"deviceId":"447823124113667139","eid":41320709861952,"aiId":575936926736961,"execParamsDbAiId":617086321443392,"execParamsDbId":"0439abad3b7303237be2ace0fa27bec0"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779887620891200 acc incomplete","batchId":779887621907008,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   447823124113667139
2024-10-12T17:30:50.322+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320695378496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=17680373459eb52faee79e033af6141b&deviceId=451728461237597251
2024-10-12T17:30:50.322+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"eb8ddde664cd135d496071377e58e4e2\\\",\\\"dbIdValue\\\":\\\"eb8ddde664cd135d496071377e58e4e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451728461237597251
2024-10-12T17:30:51.759+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:372881942684224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9a4ea21c241396d8c55a5ed732b2eaa1&deviceId=451548746115724355
2024-10-12T17:30:51.759+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b8f40991078151fe40b6be30347f3c93\\\",\\\"dbIdValue\\\":\\\"b8f40991078151fe40b6be30347f3c93\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XCdata\\\",\\\"targetValue\\\":\\\"XCdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451548746115724355
2024-10-12T17:30:53.098+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317582430784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=45203e61f199f9d4bc7d32455a4807ce&deviceId=451113715790529861
2024-10-12T17:30:53.098+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7503aef4f64018cb9f0b68ebc1d4d863\\\",\\\"dbIdValue\\\":\\\"7503aef4f64018cb9f0b68ebc1d4d863\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LSdata\\\",\\\"targetValue\\\":\\\"LSdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   451113715790529861
2024-10-12T17:30:54.437+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41317406003776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8761320e9d7b6b7a65f8a57a80aa89fe&deviceId=408056570115539781
2024-10-12T17:30:54.437+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"648a68240bb5fed213da3d197e3aedc5\\\",\\\"dbIdValue\\\":\\\"648a68240bb5fed213da3d197e3aedc5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"\\\",\\\"targetValue\\\":\\\"\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503520fa07ecceedf4351510e3fed54b23dc904ea813eeb4ebccfd563f9d242d7ce146002ef7ecc7f9e33cc1ee6cd061ea940fc6a8d32cdb374ceb47ba0bcd231cd4538b99bd82748c4adbbebc1b951806f045edeedb45c385918fecbba0790fa8c8db2aec5a1a70d2590acaee02d8c438f1c9d2f33333e259da3a1d7a91453c2dfb654450345b84c68eb9638cd3a09ed1cd62d48299080d18cd7ec2bdffac80339812e680c13227dda94a34a799b8dcf2c9a3add54532ae5c251e0a91bb1d669362e67cd8cdf66c3eb2e9485a17865bab9906c3cb5edefd227003cf6f6ad6e6f9f313afbf0a1a30acc","collectName":"E10附件数据采集","accId":779544910123584,"adimId":573802148819520,"id":779887643259456,"adcId":468695233630784,"execParamsVersion":"c632a5ab2e965567066aab7d8fc205af","aiId":573802148774464,"isEnable":0},"paramsMap":{"deviceId":"408056570115539781","eid":41317406003776,"aiId":573802148774464,"execParamsDbAiId":468695104365120,"execParamsDbId":"648a68240bb5fed213da3d197e3aedc5"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779887643259456 acc incomplete","batchId":779887644246592,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   408056570115539781
2024-10-12T17:30:55.791+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318968259136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d0cfb09f867efd4ef680211cf416749e&deviceId=382135512288212803
2024-10-12T17:30:55.791+08:00  INFO 35088 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a0b9aa9663b27b79861527f81cdd1eff\\\",\\\"dbIdValue\\\":\\\"a0b9aa9663b27b79861527f81cdd1eff\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TS\\\",\\\"targetValue\\\":\\\"TS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   382135512288212803
deviceIds:,'475203782385349699','475062791342928963','475052141954741298','475042155014928178','475040805724112185','434741637629887288','474481020574385713','474460945729467956','424619881275600965','474459430864303684','474456369928353845','474448190297354804','474446081736848945','474346771556873283','474330452929294391','474327498159567939','440100375932977714','474197002423386418','474190285128090689','474184985792743995','474167337822466096','474158615096210499','474050649969932342','474025426985961030','474013838392439619','473911304034206788','473879242958975554','473868791575491651','473296918290641987','473152592222897459','473142455512872003','473044730360379457','473032836958401603','473028661964977219','473025439783207491','473022663053292611','473009164558283843','473005130963235654','472890954525717317','472457409118352451','472449630999689017','472444964266066742','472312929287419459','472298839496209968','472299007838794819','472159326224528965','396051271791948598','471985996813055043','471264718225421379','421279876587210054','471141185503639105','470996703961429829','470862261200630329','470277792236319811','470246314739774531','470106627236705347','470103376718214211','469961079653417521','469846284774163766','469841132390465843','469820653533414210','469810678555751491','469404145841877049','469377426649003075','434923135012188471','469256851062536248','469256648125329730','469124003429103683','469082670895219777','467808158157387065','468978436568790083','468973442360685635','468794953586193202','468806764159055929','468398152513238083','432292048800855107','468114871435670853','468102509295060273','467961577308440132','467956564930607426','464201839827498041','467934635666457913','467350461242946627','466793013780165699','466665977656915011','466628426338415683','466618914378363971','399253820497998644','465763606437311028','465755298628383801','465345370289877047','465341799125235014','465338614557979715','465317050332492080','437651800129025091','465198001070028853','465178163001963332','465175169325872195','465066933381771574','465056117244834883','464911243350651971','464908671436993603','464893664653489219','464886528884094019','464883337320739907','464746511742288692','464338914681829687','464332858358514241','464194314289824821','463892416408072756','463758910285689923','463734806761451568','463025207011980355','435479882227529538','463151602128138288','463142253007747137','431832336808555570','462898555758065473','462859431911109699','462855865662189623','451573999500013878','462297770967974470','461875906529473603','461721480510583875','461703602222675011','461699186912408386','461691477160899651','461260476035118147','461120118315824449','460973531820209465','460864055435867203','460855145844980038','449697624086294834','450999743800161347','450705649806881585','449991898149434435','450098317423035460','405038473695736377','429800008414410566','451006382930408755','450132715547673410','449693335058134066','449553448862495541','385627501172373045','450975154877510470','430848485294618692','421583480711819320','448831002916176450','448648599765530936','447629864275554371','459950992717263681','459689414042334275','457953039513302083','457944780156974903','457934657321711670','431732625183360581','457641298002719792','449082269659838515','457202982061093444','448968263964050737','448950308752602179','448948806050919480','448788730707195954','448814031302575666','448658403665918262','448668501620568880','447512815628988980','457194514247857976','457083271507882039','456932285908795961','456917541185406018','456799204434523203','456791594104796998','456207384969229379','456184590621488195','456044179718420016','455921677621015620','455915183546582326','455791644868883523','455792820414858552','455780901763168577','455755585481093443','455750660848108610','455746214583877956','455035416957958449','454763203876893493','454595298489021766','454161330446808131','454038149828457780','454015059429963073','384025625507153459','451146238776062000','453746928681759032','453718829780976435','453580719436870723','452892555818844995','452876965104333891','452857525646144579','452743872422884921','452739352271795267','452735839609304386','452728724056454964','452711692464305219','452573065046410550','452582810394309688','452568400812586562','452169917286855479','452168124809098307','452162792238100784','451868323794138179','452130054118324548','452126151855322179','452009088562246962','452023510441669429','452018454308859952','447823124113667139','451728461237597251','451548746115724355','451113715790529861','408056570115539781','382135512288212803'
