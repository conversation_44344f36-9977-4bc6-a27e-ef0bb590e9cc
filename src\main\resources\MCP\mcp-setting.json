{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/github": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "disabled": false, "autoApprove": []}, "filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Downloads"]}, "time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Shanghai"]}, "browser-tools-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "autoApprove": ["getNetworkLogs", "runAccessibilityAudit"], "disabled": false}, "sequential-thinking": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking"]}, "fetch": {"name": "fetch", "isActive": true, "command": "uvx", "args": ["mcp-server-fetch"], "autoApprove": ["fetch"]}, "baidu-map": {"command": "uv", "args": ["run", "--with", "mcp[cli]", "mcp", "run", "C:\\demo\\src\\main\\resources\\MCP\\baidu_map_mcp_server\\map.py"], "env": {"BAIDU_MAPS_API_KEY": "NT77dYwqvyNctvy5v0gTUnLf5ha9Fo5L"}, "autoApprove": ["map_geocode", "map_search_places", "map_place_details", "map_directions"]}, "slack": {"name": "slack机器人", "isActive": true, "command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-slack"], "env": {"SLACK_BOT_TOKEN": "*********************************************************", "SLACK_TEAM_ID": "T08KZ13UJ2X"}, "autoApprove": ["slack_post_message"]}, "playwright": {"name": "playwright-浏览器控制", "description": "playwright-浏览器控制", "isActive": true, "command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server"]}}}