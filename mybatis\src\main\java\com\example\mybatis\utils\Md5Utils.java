package com.example.mybatis.utils;



import jakarta.xml.bind.DatatypeConverter;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;

/**
 * Md5计算实用类
 */
public final class Md5Utils {
    /**
     * 获取Md5字串
     * @param src 来源字串
     * @return Md5字串(默认转小写)
     */
    public static String getMd5(String src) {
        return getMd5(src, true);
    }

    /**
     * 获取Md5字串
     * @param src 来源字串
     * @return Md5字串
     */
    public static String getMd5(String src, boolean toLowerCase) {
        byte[] byteValue = getMd5ByBytes(src);
        if (byteValue == null) {
            return null;
        }
        String result = DatatypeConverter.printHexBinary(byteValue);
        return toLowerCase && StringUtils.isNotBlank(result) ? result.toLowerCase() : result;
    }

    public static byte[] getMd5ByBytes(String src) {
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(src.getBytes());
            return digest.digest();
        } catch (Exception ex) {
            System.out.println(src + " getMd5 error...");
            ex.printStackTrace();
            return null;
        }
    }
}