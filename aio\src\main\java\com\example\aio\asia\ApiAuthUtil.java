package com.example.aio.asia;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

public class ApiAuthUtil {
    private static final String AUTHORIZATION_HEADER_TEMPLATE = "Access={0}, ExpireTime={1}, Signature={2}";

    public static String buildAuthorization(String accessId, String appSecret, String url) {
        // Access=[Access Id], ExpireTime=[Expire Time], Signature=[signature]
        long expireTime = getExpireTime();
        String sign = sign(appSecret, url, expireTime);
        return "Access=" + accessId + ", ExpireTime=" + expireTime + ", Signature=" + sign;
    }

    public static String buildAuthorization(String accessId, String appSecret, String url, String body) {
        // Access=[Access Id], ExpireTime=[Expire Time], Signature=[signature]
        long expireTime = getExpireTime();
        String sign = sign(appSecret, url, expireTime, body);
        return "Access=" + accessId + ", ExpireTime=" + expireTime + ", Signature=" + sign;
    }

    public static String sign(String appSecret, String url, long expireTime) {
        return sign(appSecret, url, expireTime, null);
    }

    private static String sign(String appSecret, String url, long expireTime, String body) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isBlank(body)) {
            // signature = HexEncode(sha1([Secret Key]+[Expire Time]+[URL]))
            sb.append(appSecret).append(expireTime).append(url);
        } else {
            // signature = HexEncode(sha1([Secret Key]+[Expire Time]+[URL]+[POST BODY]))
            sb.append(appSecret).append(expireTime).append(url).append(body);
        }

        return DigestUtils.sha1Hex(sb.toString());
    }

    private static long getExpireTime() {
        // [Expire Time] 是以毫秒为单位的时间戳字符串，当前系统时间加10分钟。
        return System.currentTimeMillis() + 10 * 60 * 1000;
    }

    public static void main(String[] args) {
        System.out.println(buildAuthorization("ak-a00ff28a7f84426a8b9d712a322c4da6", "sk-bf1a59d910f14f268f6627f6e9f9ff28", "/umc/api/v1/tenant/get_agent_download_url/6692f2009ea04079941c6615f1db5959"));
        System.out.println(buildAuthorization("ak-a00ff28a7f84426a8b9d712a322c4da6", "sk-bf1a59d910f14f268f6627f6e9f9ff28", "/umc/api/v1/tenant/create", "{\n" + "  \"tenant_name\": \"测试租户\",\n" + "  \"alias\": \"test\",\n" + "  \"email\": \"<EMAIL>\",\n" + "  \"description\": \"测试租户的描述\",\n" + "  \"license\": {\n" + "    \"vulnerability\": true,\n" + "    \"total\": 90,\n" + "    \"start_time\": **********,\n" + "    \"expiration_time\": **********\n" + "  }\n" + "}"));

    }
}
