function main(args) {
    var str = args.input

    // 處理 json 內 不正常的內容
    var filterJson = str
    filterJson = filterJson.replace(/<[^>]*>/g, '');      // 移除所有 HTML 标签
    filterJson = filterJson.replace(/(\r\n|\n|\r)/g, ''); // 移除所有换行符（保留空格字符）
    var obj = JSON.parse(filterJson);
    if (!obj.eid) {
        return {
            "new_contract_res_data": ""
        }
    }
    // 檢查有沒有合約
    var contractItem = {};
    var isNoContract = true;
    if (obj.serviceRight) {
        for (var i = 0; i < obj.serviceRight.length; i++) {
            var item = obj.serviceRight[i];
            if (item.isOpen) {
                contractItem = item;
                isNoContract = false;
                break;
            }
        }
    }

    // 日期判斷
    var nowDate = new Date();
    var isExpiringSoon = false;
    var isExpired = false;
    var isValid = false;
    if (obj.contractExpiryDate) {
        var expiryDate = new Date(obj.contractExpiryDate);
        // 是否已過期
        isExpired = nowDate > expiryDate;

        // 是否未過期（含今天）
        isValid = expiryDate >= nowDate;

        // 是否即將過期（30天內、未過期）
        if (isValid) {
            var timeDiff = expiryDate - nowDate;
            var daysDiff = timeDiff / (1000 * 60 * 60 * 24);
            isExpiringSoon = daysDiff < 30;
            if (isExpiringSoon) {
                isValid = false;
            }
        }
    }


    var contractExtraText = "";
    if (isExpired == true) {
        contractExtraText = "您订阅的服务已到期。";
    }
    if (isExpiringSoon == true) {
        contractExtraText = "您订阅的服务距离到期日已不足30天。";
    }

    var procData = {
        "serviceStaffName": obj.serviceStaffName ? obj.serviceStaffName : "",
        "contractExpiryDate": obj.contractExpiryDate ? obj.contractExpiryDate : "",
        "contractStartDate": obj.contractStartDate ? obj.contractStartDate : "",
        "contractcode": contractItem.code ? contractItem.code : "",
        "contractMarketUrl": contractItem.marketUrl ? contractItem.marketUrl : "",
        "isContractExpiringSoon": isExpiringSoon,
        "isContractExpired": isExpired,
        "isNoContract": isNoContract,
        "isContractValid": isValid,
        "contractExtraText": contractExtraText
    };

    var res = JSON.stringify(procData);
    return {
        "new_contract_res_data": res,
        "isContractExpiringSoon": isExpiringSoon,
        "isContractExpired": isExpired,
        "isNoContract": isNoContract,
        "isContractValid": isValid,
        "serviceStaffName": obj.serviceStaffName ? obj.serviceStaffName : ""
    }
}