$ErrorActionPreference = "stop";
# 產品代號(如：Athena、02、10、61等等...僅填一個產品代號)
$ProductCode = '02';
# 應用所在目錄
$global:CurrentAppBasicPath = "";
# 程序名
$global:ExeName = "";
# ERP安裝路徑
$global:ERPInstallPath = "" ;
# 工作排程的名稱
$TaskName = '每日執行Workflow應用更新檢查';
# 工作排程的描述
$TaskDescription = 'in test';
# 任務執行時間點(使用執行參數值，因此值固定{{taskExecuteTime}})
$TaskExecuteTime = '{{taskExecuteTime}}'; #'01:00'; #
# 默認任務執行時間點
$DefaultTaskExecuteTime = '01:00';

# 地端API成功代號
$ApiSuccessCode = 'L200';
# 日誌上傳回寫重試上限
$WriteLogRetryLimit = 2;
# 獲取雲端狀態重試上限
$GetStatusRetryLimit = 2;
# 每批下載大小(100kb)
$BatchDownloadSize = 104857600;
# 下載重試上限
$DownloadFileRetryLimit = 2;

# 以下變量透過kit運行時不用調整，會由aiopskit動態參數進行填充替換
#$AiopsKitLocalUrl = "http://127.0.0.1:7513"; # '{{aiopskit_local_url}}'; #
#$DeviceName =  "H-06436";# '{{device_name}}'; #
#$TempFloder =  "C:\ProgramData\digiwin\aiopskit\temp";# '{{temp_dir_path}}'; #

$AiopsKitLocalUrl = '{{aiopskit_local_url}}';
$DeviceName = '{{device_name}}';
$TempFloder = '{{temp_dir_path}}';

# 取得ERP安裝版本
function Get-ERPVersion() {
    $Version = ''
    try{
        $Version = Get-ItemProperty -Path Registry::HKEY_LOCAL_MACHINE\SOFTWARE\DSC\Conductor -Name Version
        $global:ERPInstallPath = Get-ItemProperty -Path Registry::HKEY_LOCAL_MACHINE\SOFTWARE\DSC\Conductor -Name ClientPath
        #Write-Output $Version
        #Write-Output $global:ERPInstallPath.ClientPath
    }catch{
        try{
            $Version = Get-ItemProperty -Path Registry::HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\DSC\Conductor -Name Version
            $global:ERPInstallPath = Get-ItemProperty -Path Registry::HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\DSC\Conductor -Name ClientPath
            #Write-Output $Version
            #Write-Output $global:ERPInstallPath.ClientPath
        }catch{
            $Version = 'NO ERP';
            #Write-Output $Version
        }
    }

    if( $Version -eq 'NO ERP'){
        return
    }else{
        $ERPPlatformVersion = ''
        $SplitVersion = ($Version.Version.Split('.'));
        if ($SplitVersion.Length -eq 4) {
            # 1~2碼為平台版號
            $ERPPlatformVersion = ($SplitVersion[0] + "." + $SplitVersion[1]);
        }

        return [PSCustomObject]@{
            "ClientPath" = $global:ERPInstallPath.ClientPath ;
            "ERPPlatformVersion" = "$ERPPlatformVersion";
        };
    }
}

# 獲取地端應用版本
function Get-LocalAppVersion() {

    $ERPInfo = Get-ERPVersion;

    if (-not $ERPInfo){
        #Write-Host( $DateStr + "無法識別此電腦ERP安裝版本")
        return
    }

    if ($ERPInfo.ERPPlatformVersion -eq '1.0'){
        #WorkflowERP iGP 1.0
        $AppName = 'iGP10_ATN_AUTOUPDATE';
        $global:CurrentAppBasicPath = "C:\aiopskit\iGP10_ATN_AUTOUPDATE";  #Join-Path -Path $ERPInfo.ClientPath -ChildPath "iGP10_ATN_AUTOUPDATE";
        $global:ExeName = 'ATN_DB upgrade_WFiGP10_x64.exe';
        $PlatformVersion = "1.0";
        $BasicVersion = "0";

    }elseif ($ERPInfo.ERPPlatformVersion -eq '4.0'){
        #WorkflowERP GP40
        $AppName = 'GP40_ATN_AUTOUPDATE';
        $global:CurrentAppBasicPath = "C:\aiopskit\GP40_ATN_AUTOUPDATE"; #Join-Path -Path $ERPInfo.ClientPath -ChildPath "GP40_ATN_AUTOUPDATE";
        $global:ExeName = 'ATN_DB upgrade_WFGP40.exe';
        $PlatformVersion = "4.0";
        $BasicVersion = "0";

    }elseif ($ERPInfo.ERPPlatformVersion -eq '3.1'){
        #WorkflowERP GP31
        $AppName = 'GP31_ATN_AUTOUPDATE';
        $global:CurrentAppBasicPath = "C:\aiopskit\GP31_ATN_AUTOUPDATE"; #Join-Path -Path $ERPInfo.ClientPath -ChildPath "GP31_ATN_AUTOUPDATE";
        $global:ExeName = 'ATN_DB upgrade_WFGP31.exe';
        $PlatformVersion = "3.1";
        $BasicVersion = "0";

    }else{
        #Write-Host("不支援的ERP版本:",$ERPInfo.ERPPlatformVersion)
        return
    }

    #Write-Host ("ERP Info:", $ERPInfo)
    $CurrentVersion = "0.0.0.0";

    # 取得實際應用全路徑(含檔名)
    $CurrentAppFilePath = (Join-Path -Path $global:CurrentAppBasicPath -ChildPath $global:ExeName);
    #Write-Host ("CurrentAppFilePath:" + "$CurrentAppFilePath");

    if (Test-Path -path $CurrentAppFilePath) {
        # 找到文件獲取版號
        $Version = (Get-Item "$CurrentAppFilePath").VersionInfo.FileVersion
        #Write-Host ("Get-LocalAppVersion Version:" + "$Version");
        if ($Version) {
            # 有取到版號在處理
            $SplitVersion = ($Version.Split('.'));
            # Write-Host ("Get-LocalAppVersion SplitVersion.Length:" + $SplitVersion.Length);
            if ($SplitVersion.Length -ge 4) {
                # 版號合法填充版號
                # 1~2碼為平台版號
                $PlatformVersion = ($SplitVersion[0] + "." + $SplitVersion[1]);
                # 2~3碼為基礎版號
                # $BasicVersion = $SplitVersion[2] ;
                # 填充實際版號
                $CurrentVersion = $Version;
            }
        }
    }
    #Write-Host ("AppName:", $AppName) ;
    #Write-Host ("CurrentAppBasicPath:", $global:CurrentAppBasicPath) ;
    #Write-Host ("ExeName:", $global:ExeName) ;
    #Write-Host ("PlatformVersion:", $PlatformVersion) ;
    #Write-Host ("BasicVersion:", $BasicVersion);

    return [PSCustomObject]@{
        "ReleaseNo" = "$AppName";                  # 發布編號(類似工具名稱)-與雲端後台網站設定有關
        "CurrentVersion" = "$CurrentVersion";      # 當前版本號-與雲端後台網站設定有關
        "PlatformVersion" = "$PlatformVersion";    # 平台版號-與雲端後台網站設定有關
        "BasicVersion" = "$BasicVersion";          # 大小版版號與雲端後台網站設定有關
    };
}

# 取得雲端最新版號
function Get-NewVersion() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [object] $CurrentLocalVersion
    );
    try {
        # Write-Host ("Get-NewVersion CurrentLocalVersion:" + ($CurrentLocalVersion | ConvertTo-Json -Compress));
        $Method = "GET";
        $Url = $AiopsKitLocalUrl + "/aiopskit/forward?requestPartPath=/aiogateway/aioappupdate/au/release/new/version&productCode=" + $ProductCode +
                "&baseVersion=" + $CurrentLocalVersion.BasicVersion + "&platformVersion=" + $CurrentLocalVersion.PlatformVersion +
                "&releaseNo=" + $CurrentLocalVersion.ReleaseNo + "&currentVersion=" + $CurrentLocalVersion.CurrentVersion;
        # Write-Host ("Get-NewVersion Url:" + $Url);
        # 請求API
        # 返回範例: { "code": "0", "errMsg": "success", "data": [ { "id": 484297947550272, "sid": 241199971893824, "productCode": "Athena", "productCategory": "雅典娜", "abvId": 459559639704128, "platformVersion": "3.1.0", "releaseVersion": "1.1.1.1000.10021", "sortVersion": "00001.00001.00001.01000.10021", "releaseNo": "kscland", "releaseName": "kscland-3.1.0:1.1.1.1000.10021更新", "description": "<p>因为安装包大小超过100M,上传文件功能不可用，使用人工上传的方式</p>", "releaseExplain": "", "arfId": 484297785344576, "fileType": "FILE", "autoUpdateScript": "cd /root; unzip pack.zip; cd /root/pack/install; bash install.sh install ksc \"http://127.0.0.1:7513\" \"{{cloudUrl}}\" \"{{eid}}\"\n", "releaser": "<EMAIL>", "releaseTime": "2022-06-30 11:33:23", "status": 1, "releaseFile": { "id": 484297785344576, "sid": 241199971893824, "fileId": "9805a718-f205-4ebf-ba7b-71f14a1308d8", "fileName": "地端安装操作手册.pdf", "url": "https://dmc.digiwincloud.com.cn/api/dmc/v2/file/knowledge/share/95d4caab-4042-4cd5-b3be-d26fe103d819", "uploadTime": "2022-06-30 11:32:44" }, "whiteTenants": null, "baseVersion": { "id": 459559639704128, "sid": 241199971893824, "productCode": "Athena", "baseVersion": "1.1.1", "authorizationMethod": "UNCONTROLLED", "remark": "", "autoUpdate": true }, "openWhite": true } ] }
        $Response = (Invoke-RestMethod -Method $Method -Uri "$Url");
    } catch {
        $Response = [PSCustomObject]@{ "code" = "-1"; "message" = (Out-String -InputObject $_); };
    }
    # Write-Host ("Get-NewVersion Response:" + ($Response | ConvertTo-Json -Compress));
    return $Response;
}

# 檢查回覆是否成功
function Check-ResponseSuccess() {
    param (
        [Parameter(Position=0)]
        [object] $Response    # 回覆物件
    );
    # Write-Host "$Response  $IsCloudCode";
    $code = Get-ResponseCode($Response);
    # Write-Host $code;
    if ($code -eq -1) {
        return $false;
    }
    return ($code -eq $ApiSuccessCode);
}

# 取得回覆代號
function Get-ResponseCode() {
    param (
        [Parameter(Position=0)]
        [object]$Response
    )
    # Write-Host "$Response  $IsCloudCode";
    if ($Response -eq $null) {
        # 如果傳入的回覆物件為空，直接返回-1
        return -1;
    }
    if (-not (Get-Member -inputobject $Response -name "code")) {
        # 如果傳入的回覆物件沒有code屬性，直接返回失敗
        return -1;
    }
    return $Response.code;
}

# 取得回覆錯誤
function Get-ResponseError() {
    param (
        [Parameter(Position=0)]
        [object]$Response
    )
    # Write-Host "$Response  $IsCloudCode";
    if ($Response -eq $null) {
        # 如果傳入的回覆物件為空，直接返回空字串
        return "";
    }
    if (-not (Get-Member -inputobject $Response -name "message")) {
        # 如果傳入的回覆物件沒有message屬性，直接返回空字串
        return "";
    }
    return $Response.message;
}

# 取得回覆數據
function Get-ResponseData() {
    param (
        [Parameter(Position=0)]
        [object]$Response
    )
    # Write-Host "$Response  $IsCloudCode";
    if ($Response -eq $null) {
        # 如果傳入的回覆物件為空，直接返回空物件
        return @{};
    }
    if (-not (Get-Member -inputobject $Response -name "data")) {
        # 如果傳入的回覆物件沒有code屬性，直接返回空物件
        return @{};
    }
    return $Response.data;
}

# 備份App
function Backup-App() {
    $BakAppBasicPath = $global:CurrentAppBasicPath + "_bak";
    if (-not (Test-Path -path $global:CurrentAppBasicPath)) {
        # 原始應用目錄不存在，直接返回
        return "$BakAppBasicPath";
    }
    # 移動原始應用目錄到備份目錄
    Move-Item -Path "$global:CurrentAppBasicPath" -Destination "$BakAppBasicPath" -FORCE;
    return "$BakAppBasicPath";
}

# 獲取處理項目(單筆)物件
function Get-ProcessItem() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string] $Operation,           # 操作
        [Parameter(Mandatory=$true, Position=1)]
        [AllowEmptyString()]
        [string] $OperationContent,    # 操作內容
        [Parameter(Mandatory=$true, Position=2)]
        [AllowEmptyString()]
        [string] $Remark               # 備註
    );
    # 避免日誌混亂，每次取得前都先睡1秒
    Start-Sleep -Seconds 1;
    return [PSCustomObject]@{ "operator" = "KIT"; "operation" = $Operation; "operationTime" = Get-Date -Format "yyyy-MM-dd HH:mm:ss";
    "operationContent" = $OperationContent; "remark"= $Remark; };
}

# 儲存雲端更新日誌
function Save-UpdateLog() {
    param (
        [Parameter(Position=0)]
        [AllowEmptyString()]
        [string] $LogId,              # 日誌Id(為空字串時將重新產生)
        [Parameter(Position=1)]
        [AllowEmptyString()]
        [string] $BaseVersion,        # 基礎版號(僅日誌Id為空字串時需要填寫)
        [Parameter(Position=2)]
        [AllowEmptyString()]
        [string] $PlatformVersion,    # 平台版號(僅日誌Id為空字串時需要填寫)
        [Parameter(Position=3)]
        [AllowEmptyString()]
        [string] $ReleaseVersion,     # 發布版號(僅日誌Id為空字串時需要填寫)
        [Parameter(Position=4)]
        [object[]] $ProcessItems,     # 處理項目陣列
        [Parameter(Position=5)]
        [int] $RetryCount = 0         # 請求失敗已重試次數
    );
    # Write-Host ("Save-UpdateLog LogId:" + $LogId + " BaseVersion:" + $BaseVersion + " PlatformVersion:" + $PlatformVersion + " ReleaseVersion:" + $ReleaseVersion + " ProcessItems:" + ($ProcessItems | ConvertTo-Json -Compress) + " RetryCount:" + $RetryCount);
    if ( $RetryCount -le 0 ) {
        $RetryCount = 0;
    }
    $RequestPartPath = "";
    $LogIdParam = "";
    if ("$LogId") {
        $RequestPartPath="/aiogateway/aioappupdate/au/update/log/save";
        $LogIdParam = "&updateReqNo=$LogId";
    } else {
        $RequestPartPath="/aiogateway/aioappupdate/au/update/log/sendRequest";
    }
    $Method = "POST"
    $Url = $AiopsKitLocalUrl + "/aiopskit/forward?requestPartPath=" + $RequestPartPath + "&productCode=" + $ProductCode +
            "&baseVersion=" + $BaseVersion + "&platformVersion=" + $PlatformVersion +
            "&releaseVersion=" + $ReleaseVersion + $LogIdParam;
    # Write-Host ("Save-UpdateLog Url:" + $Url);
    $Body = [PSCustomObject]@{ "deviceName" = $DeviceName; "updateMode" = "AUTO"; "updateChannel" = "KIT"; "processList" = $ProcessItems; };
    $BodyJson = ($Body | ConvertTo-Json);
    # Write-Host ("Save-UpdateLog Body:" + ($Body | ConvertTo-Json -Compress));
    $Response = (Invoke-RestMethod -Method $Method -Uri "$Url" -ContentType 'application/json' -Body ([System.Text.Encoding]::UTF8.GetBytes($BodyJson)));
    # Write-Host ("Save-UpdateLog Response:" + ($Response | ConvertTo-Json -Compress));
    if (-not (Check-ResponseSuccess($Response))) {
        $RetryCount += 1;
        if ($RetryCount -gt $WriteLogRetryLimit) {
            return;
        }
        # 休息一下，避免雲端壓力
        Start-Sleep -Seconds 5;
        # 遞迴調用
        return (Save-UpdateLog $LogId $BaseVersion $PlatformVersion $ReleaseVersion $ProcessItems $RetryCount);
    }
    return $Response.data;
}

# 下載文件
function Download-File() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string] $Url,           # 下載地址
        [Parameter(Position=5)]
        [int] $RetryCount = 0    # 請求失敗已重試次數
    );
    try {
        # Write-Host ("Download-File Url:" + $Url);
        $DownloadedFileName = "";
        $DownloadedFileSize = "";
        $TempFileName = (Get-Date -Format "yyyyMMddHHmmssfffffff");
        $FullFileName = "$TempFloder/$TempFileName";
        $Method = "GET";
        $Url = $AiopsKitLocalUrl + "/aiopskit/download/file?requestFullPath=$Url&targetFileName=$FullFileName&downloadBatchSize=$BatchDownloadSize&keepFileSuffix=true";
        # Write-Host ("Download-File Url:" + $Url);
        # 請求API
        # 返回範例: {"code":"L200","data":{"file_full_name":"C:\ProgramData\digiwin\aiopskit\temp\\202209011424001234567.tgz","download_size":223024855,"download_error":null}}
        $Response = (Invoke-RestMethod -Method $Method -Uri "$Url");
        # Write-Host ("Download-File Response:" + ($Response | ConvertTo-Json -Compress));
        $Data = $Response.data;
        if (Check-ResponseSuccess $Response) {
            return [PSCustomObject]@{ "IsSuccess" = $true; "FileName" = $Data.file_full_name; "FileSize" = $Data.download_size; "Error" = $Data.download_error; };
        }
        if ($RetryCount -le $DownloadFileRetryLimit) {
            # 如果失敗，並且重試次數未滿，進行重試
            # 休息一下，避免雲端壓力
            Start-Sleep -Seconds 5;
            $RetryCount += 1;
            return (Download-File "$Url" $RetryCount);
        }
        $ResponseError = (Get-ResponseError $Response);
        if (-not $ResponseError) {
            if (Get-Member -inputobject $Data -name "download_error") {
                $ResponseError = $Data.download_error;
            }
        }
        return [PSCustomObject]@{ "IsSuccess" = $false; "FileName" = ''; "FileSize" = ''; "Error" = $ResponseError; };
    } catch {
        if ($RetryCount -le $DownloadFileRetryLimit) {
            # 如果失敗，並且重試次數未滿，進行重試
            # 休息一下，避免雲端壓力
            Start-Sleep -Seconds 5;
            $RetryCount += 1;
            return (Download-File "$Url" $RetryCount);
        }
        return [PSCustomObject]@{ "IsSuccess" = $false; "FileName" = ''; "FileSize" = ''; "Error" = ($_ | Out-String -Width 250); };
    }
}

# 檢查狀態-是否要停止更新(返回true:需停止更新；false:可繼續更新)
function Check-Status() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string] $LogId,      # 日誌Id
        [Parameter(Mandatory=$true, Position=1)]
        [int] $RetryCount     # 嘗試次數
    );
    # Write-Host ("Check-Status LogId:" + $LogId + " RetryCount:" + $RetryCount);
    if ($retryCount -le 0) {
        $RetryCount = 0;
    }
    if ($retryCount -gt $GetStatusRetryLimit) {
        return $true;
    }
    try {
        $Method = "GET";
        $Url = $AiopsKitLocalUrl + "/aiopskit/forward?requestPartPath=/aiogateway/aioappupdate/au/update/log/status&updateReqNo=$LogId";
        # Write-Host ("Check-Status Url:" + $Url);
        $Response = (Invoke-RestMethod -Method $Method -Uri "$Url");
        # Write-Host ("Check-Status Response:" + ($Response | ConvertTo-Json -Compress));
    } catch {
        $Response = $null;
    }
    if (Check-ResponseSuccess $Response) {
        if ($Response.data -eq 'HAND_STOP') {
            return $true;
        }
        return $false;
    } else {
        # 失敗進行重試
        $RetryCount += 1;
        Start-Sleep -Seconds 5;
        return Check-Status "$LogId" $RetryCount;
    }
}

# 解壓縮文件
function Unzip-File() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string] $SourcePath,                   # 來源路徑(來源必定是壓縮包)
        [Parameter(Mandatory=$true, Position=1)]
        [string] $TargetPath,                   # 目標路徑
        [Parameter(Mandatory=$true, Position=2)]
        [bool] $SuccessDeleteZipFile = $true    # 成功是否刪除壓縮文件
    );
    try {
        # Write-Host ("Unzip-File SourcePath:" + $SourcePath + " TargetPath:" + $TargetPath + " SuccessDeleteZipFile:" + $SuccessDeleteZipFile);
        $Method = "GET";
        $Url = $AiopsKitLocalUrl + "/aiopskit/unzip/file/by/path?sourceFileName=$SourcePath&targetPath=$TargetPath&overwriteExisting=true";
        # Write-Host ("Unzip-File Url:" + $Url);
        if ($SuccessDeleteZipFile) {
            $Url += "&successDeleteSourceFile=true";
        }
        # 請求API
        # 返回範例: {"code":"L200"}
        $Response = (Invoke-RestMethod -Method $Method -Uri "$Url");
    } catch {
        $Response = [PSCustomObject]@{ "code" = "-1"; "message" = (Out-String -InputObject $_); };
    }
    # Write-Host ("Unzip-File Response:" + ($Response | ConvertTo-Json -Compress));
    return $Response;
}

# 最終的方法-回滾或者刪除備份
function Final-Function() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [bool] $HasSuccess,          # 是否成功
        [Parameter(Mandatory=$true, Position=1)]
        [string] $AppBasicPath,      # 應用基礎目錄
        [Parameter(Mandatory=$true, Position=2)]
        [string] $BakAppBasicPath    # 備份應用基礎目錄
    );
    # Write-Host ("Final-Function HasSuccess:" + $HasSuccess + " AppBasicPath:" + $AppBasicPath + " BakAppBasicPath:" + $BakAppBasicPath);
    if ($HasSuccess -eq $null) {
        $HasSuccess = $true;
    }
    if ($HasSuccess) {
        # 成功，刪除備份
        if (Test-Path -Path $BakAppBasicPath) {
            # 文件存在才刪除
            try {
                Remove-Item -Path "$BakAppBasicPath" -Recurse -Force;
            } catch {
                # TODO:看情況，回寫失敗異常
            }
        }
    } else {
        # 失敗，刪除當前文件，並回滾
        if (Test-Path -Path "$AppBasicPath") {
            # 文件存在才刪除
            try {
                Remove-Item -Path "$AppBasicPath" -Recurse -Force;
            } catch {
                # TODO:看情況，回寫失敗異常
            }
        }
        if (Test-Path -Path "$BakAppBasicPath") {
            # 文件存在才回滾
            try {
                Move-Item -Path "$BakAppBasicPath" -Destination "$AppBasicPath" -Force;
            } catch {
                # TODO:看情況，回寫失敗異常
            }
        }
    }
}

# 執行核心
function Execute-Core() {
    param (
        [Parameter(Mandatory=$true, Position=0)]
        [string] $LogId          # 日誌Id
    );
    # 處理默認值
    if (-not($TaskExecuteTime)) {
        # 如果為空就填入默認時間
        $TaskExecuteTime = "$DefaultTaskExecuteTime";
    }
    try {
        # 確定路徑
        $CurrentAppFilePath = (Join-Path -Path "$global:CurrentAppBasicPath" -ChildPath "$global:ExeName");
        #呼叫簡易安裝包(解壓縮)
        Start-Process -NoNewWindow -Wait -FilePath $CurrentAppFilePath

        # 回傳狀態網址
        $Arg = "http://127.0.0.1:7513/aiopskit/forward?requestPartPath=/aiogateway/aioappupdate/au/update/log/save&updateReqNo=" + "$LogId" ;
        $Arg = "'"+$Arg+"'"
        #Write-Host($Arg)

        $WindowsVerInfo = [System.Environment]::OSVersion.Version
        $admins = New-Object System.Collections.Generic.List[System.Object]
        if (($WindowsVerInfo.Major -eq 6) -and ($WindowsVerInfo.Minor -eq 1)){

            $administrators = net localgroup administrators;
            if (-not($administrators) -or $administrators.Length -lt 3) {
                # 表示群組找不到人員
                return [PSCustomObject]@{ "IsSuccess" = $false; "Result" = ""; "Error" = "Execute-Core error by not get any user in administrators group"; };
            }

            $members = $administrators[6..($administrators.Length-3)];
            $hostname = $env:computername

            foreach ($user in $members){
                if (-not $user.contains('Domain Admins')){

                    if(-not $user.contains('\')){
                        $name = $hostname + "\" + $user
                    }else{
                        $name = $user
                    }
                    $admins.add($name)
                }
            }

        }else{
            [System.Collections.ArrayList]$excludeList = @()
            $win32_group = Get-CimInstance -ClassName win32_group
            foreach ($groupInfo in $win32_group){
                $excludeList.Add($groupInfo.Name)
            }

            $administrator = Get-CimInstance -ClassName win32_group -Filter "name = 'Administrators'" | Get-CimAssociatedInstance -Association win32_groupuser
            foreach ($user in $administrator){
                if ($user.Name -inotin $excludeList){
                    $name = $user.Domain + "\" + $user.Name
                    $admins.add($name)
                }
            }
        }

        $id = 0
        foreach ($UserId in $admins){
            #$UserId = $member.Domain + "\" + $member.Name
            #Write-Host($UserId)
            $TaskName = "ATNProcesser" + "_" + $id
            $id = $id + 1
            # 先刪除已經存在的排程(避免重複添加)
            if ((schtasks /query /fo CSV) | Where-Object { $_ | Select-String "$TaskName" }) {
                $DropMsg = (schtasks /delete /tn "$TaskName" /f);
            }

            $UpdateDSCSYS = (Join-Path -Path "$global:CurrentAppBasicPath" -ChildPath "\SP_DB\UpdateDSCSYS.exe");
            $WorkDirectory = (Join-Path -Path "$global:CurrentAppBasicPath" -ChildPath "\SP_DB\");

            #$action = New-ScheduledTaskAction -Execute $UpdateDSCSYS -Argument $Arg -WorkingDirectory $WorkDirectory
            $RunDate = '{0:yyyy-MM-dd}' -f(Get-Date).AddDays(1)

            #Write-Host("執行時間:"+$RunDate)
            #$trigger =  New-ScheduledTaskTrigger -Once -At $RunDate
            #$Principal = New-ScheduledTaskPrincipal -UserId $UserId -LogonType Interactive -RunLevel Highest
            #Register-ScheduledTask -Action $action -Trigger $trigger -TaskName $TaskName -Description "Workflow ERP 雅典娜應用自動更新" -Principal $Principal

            $Result = (schtasks /create /tn "$TaskName" /tr "$UpdateDSCSYS $Arg" /ru $UserId /sc ONCE /sd "$RunDate" /st "$TaskExecuteTime" /it /RL HIGHEST );
        }

        return [PSCustomObject]@{ "IsSuccess" = $true; "Result" = $Result.State; "Error" = ""; };
    } catch {
        return [PSCustomObject]@{ "IsSuccess" = $false; "Result" = ""; "Error" = (Out-String -InputObject $_); };
    }
}

# 主入口
function Main-Function() {

    # 日誌Id(運行中產生)
    $CurrentLogId = '';
    # 獲取地端版號
    $LocalVersion = Get-LocalAppVersion;

    if (-not $LocalVersion){
        #Write-Host($DateStr + "執行中止");
        return [PSCustomObject]@{ "status" = "INSTALL_FAILED"; "message" = "NO_ERP"; };
    }else{
        #Write-Host ("Main localVersion:" + $LocalVersion);
    }


    # 獲取雲端新版本
    $NewVersionResponse = (Get-NewVersion $LocalVersion);
    if (-not (Check-ResponseSuccess $NewVersionResponse)) {
        $ResponseCode = (Get-ResponseCode $NewVersionResponse);
        $Data = ($NewVersionResponse.data);
        if ($Data -and ($Data.Contains("code: 3008 "))) {
            # Write-Host "NO_NEW_VERSION";
            # 表示無新版本，離開
            $CurrentMsg = "no new version releaseNo:" + $LocalVersion.ReleaseNo + " currentVersion:" + $LocalVersion.CurrentVersion + " basicVersion:" + $LocalVersion.BasicVersion;
            return [PSCustomObject]@{ "status" = "NO_NEW_VERSION"; "message" = "$CurrentMsg"; };
        }
        #Write-Host "OCCUR_ERROR";
        # 其他表示有異常，輸出異常，離開
        $ErrorMsg = ("Get-NewVersion response code:" + $ResponseCode + " not " + $ApiSuccessCode + ", errorMsg:" + $Data);
        $CurrentProcessItems = @(Get-ProcessItem "GET_NEW_VERSION_FAIL" "$ErrorMsg" "");
        # 因為無法取得新版本，這裡先拿地端版本作為發佈版本儲存錯誤到雲端
        $DropLogId = (Save-UpdateLog "$CurrentLogId" $LocalVersion.BasicVersion $LocalVersion.PlatformVersion $LocalVersion.CurrentVersion $CurrentProcessItems 0);
        return [PSCustomObject]@{ "status" = "OCCUR_ERROR"; "message" = "$ErrorMsg"; };
    }

    # 備份App
    $AppBasicPath = "$global:CurrentAppBasicPath";
    $BakAppBasicPath = Backup-App;
    # Write-Host "Main-Function bakupPath:$BakAppBasicPath";

    $NewVersionData = $NewVersionResponse.data;
    $BaseVersion = $NewVersionData.baseVersion.baseVersion;
    $PlatformVersion = $NewVersionData.platformVersion;
    $ReleaseVersion = $NewVersionData.releaseVersion;
    # 取得LogId
    $CurrentProcessItems = @(Get-ProcessItem "REQUEST" "" "");
    # Write-Host ("Main-Function curProcessItems:" + ($CurrentProcessItems | ConvertTo-Json -Compress));
    $CurrentLogId = (Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0);
    #Write-Host ("Main-Function logId:" + $CurrentLogId);

    # 下載
    $DownloadUrl = $NewVersionData.releaseFile.url;
    $CurrentProcessItems = @(Get-ProcessItem "DOWNLOAD_START" "downloadUrl:$DownloadUrl \n batchDownloadSize:$BatchDownloadSize" "");
    $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
    $DownLoadResponse = (Download-File "$DownloadUrl");
    if (-not ($DownLoadResponse.IsSuccess)) {
        # 下載失敗
        # Write-Host "DOWNLOAD_FAIL";
        $ErrorMsg = "downloadUrl:$DownloadUrl \n errMsg:" + $DownLoadResponse.Error;
        $CurrentProcessItems = @(Get-ProcessItem "DOWNLOAD_FAIL" "$ErrorMsg" "");
        $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
        Final-Function $false "$AppBasicPath" "$BakAppBasicPath";
        return [PSCustomObject]@{ "status" = "DOWNLOAD_FAIL"; "message" = $DownLoadResponse.Error; };
    }
    # 取得狀態
    if (Check-Status $CurrentLogId 0) {
        Final-Function $false "$AppBasicPath" "$BakAppBasicPath";
        return [PSCustomObject]@{ "status" = "HAND_STOP"; "message" = ''; };
    }
    # 繼續更新，回寫狀態
    $FileName = $DownLoadResponse.FileName;
    $FileSize = $DownLoadResponse.FileSize;
    $CurrentProcessItems = @(Get-ProcessItem "DOWNLOAD_SUCCESS" "downloadSize:$FileSize" "");
    $CurrentProcessItems += (Get-ProcessItem "UNZIP_START" "" "");
    $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
    # 解壓(刪除解壓後的包)
    $TargetPath = $global:CurrentAppBasicPath;
    $UnzipResponse = (Unzip-File "$FileName" "$TargetPath" $true);
    if (-not (Check-ResponseSuccess $UnzipResponse)) {
        # 解壓失敗
        # Write-Host "UNZIP_FAIL";
        $ErrorMsg = (Get-ResponseError $UnzipResponse);
        $CurrentProcessItems = @(Get-ProcessItem "UNZIP_FAIL" "errMsg:$ErrorMsg" "");
        $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
        # 解壓失敗，刪除壓縮包
        # Remove-Item "$FileName" -Force;
        Final-Function $false "$AppBasicPath" "$BakAppBasicPath";
        return [PSCustomObject]@{ "status" = "UNZIP_FAIL"; "message" = $ErrorMsg; };
    }
    # 取得狀態
    if (Check-Status "$CurrentLogId" 0) {
        Final-Function $false "$AppBasicPath" "$BakAppBasicPath";
        return [PSCustomObject]@{ "status" = "HAND_STOP"; "message" = ''; };
    }
    # 繼續更新，回寫狀態
    $CurrentProcessItems = @(Get-ProcessItem "UNZIP_SUCCESS" "" "");
    $CurrentProcessItems += (Get-ProcessItem "CREATE_TASK_START" "" "");
    $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
    # 實際邏輯運行
    $ExecuteResponse = (Execute-Core "$CurrentLogId");
    if (-not ($ExecuteResponse.IsSuccess)) {
        # 失敗
        # Write-Host "INSTALL_FAIL";
        $CurrentProcessItems = @(Get-ProcessItem "CREATE_TASK_FAIL" $ExecuteResponse.Error "");
        $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
        Final-Function $false "$AppBasicPath" "$BakAppBasicPath";
        return [PSCustomObject]@{ "status" = "CREATE_TASK_FAIL"; "message" = $ExecuteResponse.Error; };
    }
    # 成功
    $ExecuteRueslt = $ExecuteResponse.Result;
    $CurrentProcessItems = @(Get-ProcessItem "CREATE_TASK_SUCCESS" "$ExecuteRueslt" "");
    $DropLogId = Save-UpdateLog "$CurrentLogId" "$BaseVersion" "$PlatformVersion" "$ReleaseVersion" $CurrentProcessItems 0;
    Final-Function $true "$AppBasicPath" "$BakAppBasicPath";
    return [PSCustomObject]@{ "status" = "CREATE_TASK_SUCCESS"; "message" = ""; };
}

# 運行主入口，並輸出json字串
$Result = Main-Function;
Write-Host ($Result | ConvertTo-Json -Compress);