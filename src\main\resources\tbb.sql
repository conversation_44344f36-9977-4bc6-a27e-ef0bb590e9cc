CREATE TABLE tbb.aiops_cxo_tag
(
    eid STRING,
    calDateTime    TIMESTAMP,
    cxoType STRING,
    cxoTag STRING,
    cxochildTag       STRING,
    cxoScore INT,
    cxoContent STRING,
    cxoSuject STRING
)
WITH SERDEPROPERTIES ('serialization.format'='1') STORED AS PARQUET LOCATION 'hdfs://nameservice1/user/hive/warehouse/tbb.db/aiops_cxo_tag' TBLPROPERTIES
(
    'COLUMN_STATS_ACCURATE'=
    '{\"BASIC_STATS\":\"true\"}',
    'numFiles'=
    '0',
    'numFilesErasureCoded'=
    '0',
    'numRows'=
    '0',
    'rawDataSize'=
    '0',
    'totalSize'=
    '0'
)
;

