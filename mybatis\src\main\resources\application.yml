spring:
  main:
    allow-circular-references: true
  datasource:
    slave:
      jdbcUrl: **************************************************************************
      username: readniss
      password: Escread001
      driver-class-name: com.mysql.jdbc.Driver

#    master:
#      jdbcUrl: *************************************
#      username: root
#      password: 123456
#      driver-class-name: com.mysql.jdbc.Driver

    esclientserver:
      jdbcUrl: *****************************************
      username: root
      password: 123456
      driver-class-name: com.mysql.jdbc.Driver

    master:
      jdbcUrl: *****************************************************************
      username: readniss
      password: Escread001
      driver-class-name: com.mysql.jdbc.Driver

#    大陆正式
#    master:
#      jdbcUrl: *****************************************************************
#      username: readniss
#      password: Escread001
#      driver-class-name: com.mysql.jdbc.Driver
#      台湾预发布
#    master:
#      jdbcUrl: ******************************************************
#      username: readuser@esctestdb
#      password: digiwin&200
#      driver-class-name: com.mysql.jdbc.Driver
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
server:
  port: 8081

