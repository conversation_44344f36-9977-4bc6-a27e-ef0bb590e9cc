{"9999_mssql_db_report_cn_152": {"aliases": {}, "mappings": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "customerFullName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "databaseBackup": {"properties": {"appType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eid": {"type": "long"}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreGroupItemMap": {"properties": {"BACKUP": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}}}, "databaseHealth": {"properties": {"appType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eid": {"type": "long"}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreGroupItemMap": {"properties": {"PERFORMANCE": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}}}, "detectPeriod": {"type": "long"}, "groupInterval": {"type": "long"}, "id": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "informationSecurity": {"properties": {"appType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "customList": {"properties": {"total": {"type": "long"}, "vuln_tag": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "wType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eid": {"type": "long"}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "platformType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreGroupItemMap": {"properties": {"BACKUP": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}, "SECURITY": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}}}, "productName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportDataEndDate": {"type": "long"}, "reportDataStartDate": {"type": "long"}, "reportDate": {"type": "long"}, "reportName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "resSuggestion": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreMap": {"properties": {"BACKUP": {"properties": {"itemGroupScore": {"type": "float"}, "rsiList": {"properties": {"groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}, "PERFORMANCE": {"properties": {"itemGroupScore": {"type": "float"}, "rsiList": {"properties": {"groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}, "SECURITY": {"properties": {"itemGroupScore": {"type": "float"}, "rsiList": {"properties": {"groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}, "STABLE": {"properties": {"itemGroupScore": {"type": "float"}, "rsiList": {"properties": {"groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}, "STANDARD": {"properties": {"itemGroupScore": {"type": "float"}, "rsiList": {"properties": {"groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}}}, "serverConfiguration": {"properties": {"appType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "cpuInfo": {"properties": {"aggData": {"properties": {"avg_cpu__usage_idle": {"type": "float"}, "max_cpu__usage_idle": {"type": "float"}, "min_cpu__usage_idle": {"type": "float"}}}, "data": {"properties": {"avg_cpu__usage_idle": {"type": "float"}, "groupTime": {"type": "date"}, "max_cpu__usage_idle": {"type": "float"}, "min_cpu__usage_idle": {"type": "float"}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "diskCapacityInfo": {"properties": {"data": {"properties": {"avg_disk__used_percent": {"type": "float"}, "disk__path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "max_disk__total": {"type": "long"}, "max_disk__used_percent": {"type": "float"}, "min_disk__used_percent": {"type": "float"}}}}}, "eid": {"type": "long"}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "memoryInfo": {"properties": {"aggData": {"properties": {"avg_mem__available_percent": {"type": "float"}, "max_mem__available_percent": {"type": "float"}, "min_mem__available_percent": {"type": "float"}}}, "data": {"properties": {"avg_mem__available_percent": {"type": "float"}, "groupTime": {"type": "date"}, "max_mem__available_percent": {"type": "float"}, "min_mem__available_percent": {"type": "float"}}}}}, "platformType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreGroupItemMap": {"properties": {"STANDARD": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}, "securityVulnInfo": {"properties": {"highRiskVulnCount": {"type": "long"}, "lastCheckTime": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "severityVulnCount": {"type": "long"}, "vulnTag": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}}}, "serverHealth": {"properties": {"appType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "eid": {"type": "long"}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "platformType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "reportReferenceScoreType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "scoreGroupItemMap": {"properties": {"STABLE": {"properties": {"_class": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "groupItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "itemScore": {"type": "float"}}}}}}}, "serverHealthInfoList": {"properties": {"checkItemList": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "checkItems": {"properties": {"abnormalDescription": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "abnormalInfo": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "actualValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "checkItemCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "referenceValue": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "cpuInfo": {"properties": {"aggData": {"properties": {"avg_cpu__usage_idle": {"type": "float"}, "max_cpu__usage_idle": {"type": "float"}, "min_cpu__usage_idle": {"type": "float"}}}, "data": {"properties": {"avg_cpu__usage_idle": {"type": "float"}, "groupTime": {"type": "date"}, "max_cpu__usage_idle": {"type": "float"}, "min_cpu__usage_idle": {"type": "float"}}}}}, "deviceId": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "diskCapacityInfo": {"properties": {"data": {"properties": {"avg_disk__used_percent": {"type": "float"}, "disk__path": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "max_disk__total": {"type": "long"}, "max_disk__used_percent": {"type": "float"}, "min_disk__used_percent": {"type": "float"}}}}}, "instanceName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "memoryInfo": {"properties": {"aggData": {"properties": {"avg_mem__available_percent": {"type": "float"}, "max_mem__available_percent": {"type": "float"}, "min_mem__available_percent": {"type": "float"}}}, "data": {"properties": {"avg_mem__available_percent": {"type": "float"}, "groupTime": {"type": "date"}, "max_mem__available_percent": {"type": "float"}, "min_mem__available_percent": {"type": "float"}}}}}, "modelCode": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "modelCodeList": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "modelName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "platformType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "unitType": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "userName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "settings": {"index": {"refresh_interval": "1s", "number_of_shards": "3", "provided_name": "9999_mssql_db_report_cn_152", "creation_date": "1719565620475", "store": {"type": "fs"}, "number_of_replicas": "1", "uuid": "tvX2Y1eCQ6OnjwZxH-5h9g", "version": {"created": "7080099"}}}}}