### 2. 稽查记录

```plantuml
@startuml
left to right direction
actor 用户 as User


rectangle "网安稽查记录" {
  usecase "跟踪评估进度" as UC2
  usecase "创建稽查记录" as UC1
  usecase "维护资产清单" as UC3
  usecase "创建评估分数" as UC4
  usecase "生成检测报告" as UC5
  usecase "查看历史记录" as UC6
  usecase "取消/重新评估" as UC7
}
User --~ UC2 : 查看进度条状态\n（资产清单/检测评估/报告）
User --~ UC1 : 点击【新建检测】\n设置核心系统
User --~ UC3 : 点击【维护资产清单】\n更新系统下设备列表
User --~ UC4 : 点击【检测评估】\n计算分数
User --~ UC5 : 点击【生成报告】\n生成报告
User --~ UC6 : 分页查看\n（10条/页）
User --~ UC7 : 终止进行中评估\n或重新生成报告
@enduml
```

```plantuml
@startuml
left to right direction
actor 用户 as User


rectangle "网安稽查记录" {
  usecase "创建稽查记录" as UC1
  usecase "跟踪评估进度" as UC2
  usecase "维护资产清单" as UC3
}

User --~ UC1 : 点击【新建检测】\n设置核心系统
User --~ UC2 : 查看进度条状态\n（资产清单/检测评估/报告）
User --~ UC3 : 点击【维护资产清单】\n更新系统下设备列表
@enduml
```



```plantuml
@startuml
|MIS|
start
:点击“网安稽查记录”;
|智管家（页面）|
:发送网安稽查记录查询请求;
|后台服务（网安稽查服务）|
:调用一键体检服务查询网安稽查体检记录;
:调用一键体检服务获取体检实例列表;
|后台服务（一键体检服务）|
:查询Mysql获取网安稽查体检记录;
:查询Mysql获取体检实例列表;
|Mysql|
:返回网安稽查体检记录;
:返回体检实例列表;
|后台服务（一键体检服务）|
:返回网安稽查体检记录;
:返回体检实例列表到网安稽查服务;
|后台服务（网安稽查服务）|
:发起查询体检记录报告数据;
|后台服务（一键体检服务）|
:查询体检记录报告数据;
|Mysql|
:返回体检记录报告数据;
|后台服务（一键体检服务）|
:返回体检记录报告数据;
|后台服务（网安稽查服务）|
:查询体检记录模型列表数据;
:查询体检记录模型列表项目数据;
|Mysql|
:返回体检记录模型列表数据;
:返回体检记录模型列表项目数据;
|后台服务（网安稽查服务）|
:组装体检实例与体检记录数据、报告数据、\n模型列表、模型项目列表数据;
:计算资产进度数据;
:计算体检实例进度数据;
:计算体检报告进度数据;
:返回网安稽查记录数据到页面;
note right: EXAM_ING PAUSED CANCELLED EXAM_COMPLETE 稽查记录与体检记录状态一致
|智管家（页面）|
:显示网安稽查记录数据;
stop
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击“新建检测”;
|智管家（页面）|
:发起查询资产类别信息系统数据;
|后台服务（网安稽查服务）|
:查询资产类别信息系统数据;
|大数据平台|
:返回信息系统数据;
|智管家（页面）|
:显示创建表单;
:带入当前MIS客户;
:显示资产类别模型数据;
|MIS|
:输入标题;
|MIS|
:选择模型名称;
:点击“确定”;
|智管家（页面）|
:发送保存请求;
|后台服务（网安稽查服务）|
:发起保存体检记录请求;
|后台服务（一键体检服务）|
:保存网安稽查体检记录;
|Mysql|
:保存网安稽查体检记录;
|后台服务（网安稽查服务）|
:保存资产类别模型数据;
|Mysql|
:保存资产类别模型数据;
|后台服务（网安稽查服务）|
:返回保存成功;
|智管家（页面）|
:显示保存成功;
stop
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击“维护资产清单”;
|智管家（页面）|
:发送资产类别数据查询请求;
|后台服务（网安稽查服务）|
:查询资产类别数据;
note
信息系统查询 
aiops_item_group_mapping aiopsItemGroup='PRODUCT_APP' 的运维项目下的实例

设备则查询 
设备列表下所有的设备实例

数据库则查询 
aiops_item_group_mapping aiopsItemGroup='DATABASE' 的运维项目下的实例
end note
|Mysql|
:返回资产数据;
|后台服务（网安稽查服务）|
:发起查询已选资产数据;
|后台服务（一键体检服务）|
:查询已选资产数据;
|Mysql|
:查询已选资产数据;
|后台服务（网安稽查服务）|
:返回资产类别数据;
:返回已选资产数据;
note
携带是否有实例标识
end note
|智管家（页面）|
:显示资产列表;
|MIS|
:选择资产;
:点击“保存”;
|智管家（页面）|
:发送保存请求到一键体检服务;
|后台服务（一键体检服务）|
:保存资产清单到一键体检实例表;
|Mysql|
:保存资产清单到意见体检实例表;
|后台服务（一键体检服务）|
:返回保存结果到页面;
|智管家（页面）|
:显示保存结果;
stop
@enduml
```

```mermaid
classDiagram

    note for aiops_exam_item_instance_score "是否需要新增字段 保存实例对应的系统名称"
    class aiops_exam_item_instance_score~体检实例~ {
        + id: long
        + 体检记录ID: long
        + aiopsItem: string
        + aiopsItemId: string
        + 是否体检完成: boolean
        + 设备得分: decimal
        + 分类生成时间: datetime
        + 体检等级编号: string
    }

    class aiops_exam_instance_index_score~体检设备指标得分~ {
        + id: long
        + 体检实例id: long
        + 指标id: long
        + 得分: decimal
        + 标签值: string
        + 分数生成时间: datetime
        + 体检等级编号: string
    }
    note for aiops_exam_record "新增字段 标题 体检环境"
    class aiops_exam_record~体检记录~ {
        + id: long
        + 体检id: long
        + sid: long
        + eid: long
        + serviceCode: string
        + customerName: string
        + customerFullName: string
        + examBeginTime: datetime
        + examEndTime: datetime
        + examStatus: string
        + examScore: decimal
        + userId: string
        + userName: string
        + 标题: string
        + 体检环境: string

        + getExamRecord(体检id) List~ExamRecord~
        - getExamInstance(aerId) List~ExamInstance~
        - getExamReportRecord(aerId) List~ExamReportRecord~
        + getNetworkSecurityExaminationModel(aerId) List~NetworkSecurityExaminationModel~
        - getNetworkSecurityProject(modelCode,sid,eid) List~NetworkSecurityProject~
        - buildResultData(ExamReportRecord,ExamInstance,NetworkSecurityProject,NetworkSecurityExaminationModel) List~ExamRecord~
        - computeProcess(ExamReportRecord,ExamInstance,NetworkSecurityProject) List~ExamInstance~
        + getExamEnvList(sid,eid) List~String~
        + saveAiopsExamRecord(ExamRecord) ResponseBase
        + saveSecurityExaminationModel(List modelCode,体检记录Id) ResponseBase

        + saveAiopsExamItemInstanceScore(List~NetworkSecurityProject~,体检记录Id)
        + computeScore(Long aeId, Long aerId, String aiopsItemId,其他资产Id)
        + getAerReport(Long aerId) ResponseBase
        + getModelData(modelCode) ResponseBase
        + getWarningData(eid,startDate ,endDate) ResponseBase
    }

    class  aiops_exam_index_score~体检指标得分~{
        + id: long
        + 体检记录id: long
        + 指标id: long
        + 得分: decimal
        + 分数生成时间: datetime
        + 分数等级编号: string
        + 创建时间: datetime
        + 更新时间: datetime
        + handleScore(ScoreContext context)
    }
    style aiops_exam_index_score fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5

%%    class network_security_examination_record_project~网安稽查体检记录项目~ {
%%        + id: long
%%        + 体检记录Id: long
%%        + 网安稽查项目Id: long
%%        + 创建时间: datetime
%%        + 更新时间: datetime
%%    }
%%    style network_security_examination_record_project fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5


    class  aiops_exam_records_report_records~网安稽查记录报告记录~{
        + id: long
        + sid: long
        + eid: long
        + 体检记录Id: long
        + serviceCode: string
        + customerName: string
        + customerFullName: string
        + 状态(生成中 未发送 已发送): string
        + 报告生成人: string
        + 报告时间: date
        + 生成时间: datetime
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style aiops_exam_records_report_records fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5


    aiops_exam_record "1" -- "*" aiops_exam_item_instance_score
    aiops_exam_item_instance_score "1" -- "*" aiops_exam_instance_index_score
    aiops_exam_record "1" -- "*" aiops_exam_records_report_records
%%    aiops_exam_record "1" -- "*" network_security_examination_record_project
    aiops_exam_record "1" -- "*" aiops_exam_index_score


    class aiops_instance {
        + id: long
        + samcd: long
        + aiopsItemId: string
        + getInstanceDetail(eid) List<AiopsInstance>
    }

    class cmdb_field {
        + id: long
        + moduleId: long
        + fieldCode: string
        + fieldName: string
        + getModelField(Long moduleId) List<ModelField>
    }

    class network_security_examination_project_type~网安稽查项目类别~ {
        + id: long
        + parentCode: string
        + 类别名称: string
        + 类别Code: string
        + modelCode: string
        + modelName: string
        + 创建时间: datetime
        + 更新时间: datetime
        + getProjectType(类别Code,类别名称) List<ProjectType>
    }
    style network_security_examination_project_type fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5


    class network_security_examination_info_system~SR 网安稽查信息系统模型~ {
        + id: long
        + sid: long
        + eid: long
        + 稽查项目类别code: string
        + 运维项目aiopsItem: string
        + 运维项目id aiopsItemId: string
        + 运维项目实例id aiId: long
        + 系统名称: string
        + 安全保护等级: string
        + 是否备案: boolean
        + 备案编号: string
        + 备注: string
        + 创建时间: datetime
        + 更新时间: datetime
        + getProject(modelCode,pageNum, pageSize) Page<project>
        - getProjectInstanceDetailData(List instanceIdList, eid) List<ProjectModel>
        - getProjectModelData(List projectId) List<ProjectModel>
        + saveProject(modelData, instanceId) ResponseBase
        - saveProjectModelData(modelData, projectId) ResponseBase
        + deleteProject(projectId) ResponseBase
        - deleteProjectModelData(projectId) ResponseBase

        + updateProject(modelData, instanceId) ResponseBase
    }
    style network_security_examination_info_system fill:#F0FFF0,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5
%% 定义关联关系
    network_security_examination_project_type "1" -- "*" network_security_examination_info_system
    network_security_examination_info_system <.. aiops_instance
    network_security_examination_info_system "1" -- "1" cmdb_field

    aiops_exam_item_instance_score "1" -- "1" network_security_examination_info_system
```















~根据上文得UI图和用例图的前三个用例，帮我生成一份活动图，生成的内容同样也是用markdown的plantuml。要求如下
~1. 活动图的泳道有，角色（MIS）、智管家（页面）、后台服务（网安稽查服务）、后台服务（一键体检服务）、Mysql、大数据平台
~2. 每个活动都要有开始和结束节点。
~3. 除去开始结束节点的每个节点应该都是动词+名词的结构
~3. 每一个用例都要有活动图，且都分开。
~4. 每一个服务查询都会查询对应的Mysql库
~5. 活动图需要比较细致，包括角色点击 操作，页面发起请求等
~6. 根据UI图3，是创建网安稽查记录，首先带入当前MIS的客户，然后填入标题，然后可以选择资产类别中模型名称（需要从mysql查询），之后保存到mysql。
~7. 第二个用例图是根据UI图4，就是通过过稽查服务 调用一键体检服务查看资产有没有选择 或者查询选择了多少资产，体检记录是否完成，体检记录报告是否产出，来组成每个步骤的进度图。
~8. 第三个用例图，就是维护资产清单，首先可以弹出第一个用例图所选择的模型下的资产数据，返回的资产数据需要带是否和我们系统有关联的标识，没有标识的不可以选择，有标识的可以选择成为资产清单的资产，然后点击保存，通过一键体检服务保存到mysql


```plantuml
@startuml
left to right direction
actor 用户 as User


rectangle "网安稽查记录" {

  usecase "创建评估分数" as UC4
  usecase "生成检测报告" as UC5
  usecase "取消/重新评估" as UC7
}


User --~ UC4 : 点击【检测评估】\n计算分数
User --~ UC5 : 点击【生成报告】\n生成报告
User --~ UC7 : 终止进行中评估
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击检测评估;
|智管家(页面)|
:发起查询体检实例请求;
|后台服务(一键体检服务)|
:查询体检实例列表;
|Mysql|
:返回体检实例列表;
|后台服务(一键体检服务)|
:返回体检实例列表;
|智管家(页面)|
repeat :循环调用一键体检服务;
note right: 对列表中的每条记录
|后台服务(一键体检服务)|
if (有实例Id?) then (是)
  :计算评估分数;
else (否)
  :返回分数0;
endif
:保存分数至Mysql;
|Mysql|
:存储评估分数;
|后台服务(一键体检服务)|
:返回计算结果至页面;
|智管家(页面)|
repeat while (体检实例未处理完)
:展示计算结果;
stop
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击“生成报告”;
|智管家(页面)|
:发起报告详情查询请求;
|后台服务(网安稽查服务)|
:查询网安稽查报告类别模型列表数据;
|Mysql|
:返回报告类别模型列表数据;
|后台服务(网安稽查服务)|
:发起查询模型详情数据请求;
|后台服务(模型服务)|
:查询模型详情数据;
|大数据平台|
:查询模型详情数据;
|后台服务(网安稽查服务)|
:发起查询体检记录分数请求;
|后台服务(一键体检服务)|
:查询体检记录分数数据;
|Mysql|
:返回分数数据;
|后台服务(一键体检服务)|
:返回分数数据;
|后台服务(网安稽查服务)|
:组合报告数据和分数数据;
:创建报告记录;
:保存报告记录;
|Mysql|
:存储报告记录;
|后台服务(网安稽查服务)|
:返回报告数据和报告记录数据;
|智管家(页面)|
:展示报告数据;
stop
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击取消评估;
|智管家(页面)|
:发起修改体检记录状态请求;
|后台服务(一键体检服务)|
:修改体检记录状态为取消;
|Mysql|
:修改状态;
|后台服务(一键体检服务)|
if (正在进行一键体检?) then (是)
  :停止体检;
  :返回体检异常状态;
  |智管家(页面)|
  :展示停止体检信息;
  stop
else (否)
endif
:返回成功状态至页面;
|智管家(页面)|
:展示终止成功;
stop
@enduml
```

~根据上文得UI图和用例图的三个用例，帮我生成一份活动图，生成的内容同样也是用markdown的plantuml。要求如下
~1. 活动图的泳道有，角色（MIS）、智管家（页面）、后台服务（网安稽查服务）、后台服务（一键体检服务）、Mysql
~2. 每个活动都要有开始和结束节点。
~3. 除去开始结束节点的每个节点应该都是动词+名词的结构
~3. 每一个用例都要有活动图，且都分开。
~4. 每一个服务查询都会查询对应的Mysql库
~5. 活动图需要比较细致，包括角色点击 操作，页面发起请求等
~6. 创建评估分数，根据网安稽查记录实例列表数据，网页循环调用 一键体检服务，计算分数，保存入库。
~7. 第二个用例图生成检测报告，调用网安稽查服务获取网安稽查报告数据，在调用一键体检服务获取一键体检分数数据，组合两个服务数据，网安稽查服务创建报告记录，保存之mysql，返回。
~8. 第三个用例图，终止评估，调用一键体检服务，修改体检记录状态为取消。




### 任务工时 38H
>1. 网安稽查项目查询、数据内置，提供接口支持修改、除参与计算分数的网安稽查项目数据CRUD 7H
>2. 参与计算分数的网安稽查项目模型创建 2H 模型保存（已有） 模型查询（已有）增加与实例管理数据2H 客户家实例查询2H 模型数据与实例数据关联保存2H
>3. 网安稽查记录查询(携带资产数据与检测数据与报告数据) 3H
>4. 新增网安稽查记录保存（支持更新网安稽查资产清单）4H 查询参与计算分数的网安稽查项目2H
>5. 检测评估api，由后端批量触发2H,修改原有计算分数api（增加体检记录判断） 1H
>6. 计算逻辑新增指标单独计算逻辑2H
>7. 新增api修改网安稽查记录状态（支持一键体检暂停）2H
>8. 新增api根据稽查记录查询体检记录 查询体检分数 查询非参与分数计算网安稽核项目数据 查询预警数据 组成报告想要数据、查询报告记录 7H
