/**
 * @filename:${entityName}Service ${createTime}
 * @project ${project}  ${version}
 * Copyright(c) 2020 ${author} Co. Ltd. 
 * All right reserved. 
 */
package ${serviceUrl};

import ${entityUrl}.${entityName};
import com.baomidou.mybatisplus.extension.service.IService;
/**   
 * @Description:TODO(${entityComment}服务层)
 * @version: ${version}
 * @author: ${author}
 * 
 */
public interface ${entityName}Service extends IService<${entityName}> {
	
}