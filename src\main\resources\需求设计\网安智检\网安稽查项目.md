### 1. 查看网安稽查项目
```plantuml
@startuml
left to right direction
actor MIS as User

rectangle "网安稽查项目" {
  usecase "查看网安稽查项目类别" as UC1
  usecase "搜索网安稽查项目类别" as UC2
  usecase "查看网安稽查项目" as UC3
  usecase "查询产品" as UC5
  usecase "新增网安稽查项目" as UC4
  usecase "删除网安稽查项目" as UC6
  usecase "编辑网安稽查项目" as UC7
  usecase "关联产品" as UC8
 
}

User --> UC1 : 访问主界面
User --> UC2 : 使用搜索框+筛选条件
User --> UC3 : 点击类别+访问右侧稽查项目列表
User --> UC4 : 点击新增按钮
User --> UC5 : 点击下拉选择已有产品（实例）
User --> UC6 : 点击删除网安稽查项目
User --> UC7 : 点击编辑网安稽查项目
User --> UC8 : 点击关联产品（实例）
@enduml
```

```plantuml
@startuml
|角色 (MIS)|
start
:输入智管家URL进入页面;
:点击合规评估菜单;
:点击网安稽查项目;
|智管家 (页面)|
:发起类别查询请求;
|后台服务 (网安稽查服务)|
:查询类别数据;
|Mysql|
:返回类别数据;
|后台服务 (网安稽查服务)|
:组装类别数据;
|智管家 (页面)|
:显示类别数据;
|角色 (MIS)|
stop;
@enduml
```

```plantuml
@startuml
|角色 (MIS)|
start
:输入搜索条件;
|智管家 (页面)|
:发起搜索请求;
|后台服务 (网安稽查服务)|
:查询搜索类别数据;
|Mysql|
:返回搜索类别数据;
|后台服务 (网安稽查服务)|
:组装搜索类别数据;
|智管家 (页面)|
:显示搜索类别数据;
|角色 (MIS)|
stop;
@enduml
```
```plantuml
@startuml
|角色 (MIS)|
start
:点击类别;
|智管家 (页面)|
:发起模型列表查询请求;
|后台服务 (网安稽查服务)|
:查询模型列表数据;
|Mysql|
:返回模型列表数据;
|后台服务 (网安稽查服务)|
:组装模型列表数据;
|智管家 (页面)|
:显示模型列表数据;
|角色 (MIS)|
:点击模型按钮;
|智管家 (页面)|
:发起稽查项目数据查询请求;
|后台服务 (网安稽查服务)|
:查询稽查项目数据;
|Mysql|
:返回稽查项目数据;
|后台服务 (网安稽查服务)|
:发起稽查项目实例数据查询请求;
note
为了关联显示实例所需字段
end note
|后台服务 (运维实例服务)|
:查询实例数据;
|Mysql|
:返回实例数据;
|后台服务 (网安稽查服务)|
:发起稽查项目模型数据查询请求;
|大数据平台|
:返回稽查项目模型数据;
|后台服务 (网安稽查服务)|
:组装稽查项目数据和模型数据;
|智管家 (页面)|
:显示最终数据;
|角色 (MIS)|
stop;
@enduml
```
```plantuml
@startuml
|角色 (MIS)|
start
:点击新增按钮;
|智管家 (页面)|
:发起表单模型字段数据查询请求;
|后台服务 (模型服务)|
:查询模型字段表单数据;
|Mysql|
:返回模型字段表单数据;
|后台服务 (模型服务)|
:组装模型字段表单数据;
|智管家 (页面)|
:渲染动态表单;
:发起租户实例数据请求;
|后台服务 (运维实例服务)|
:查询租户实例数据;
note
信息系统查询 
aiops_item_group_mapping aiopsItemGroup='PRODUCT_APP' 的运维项目下的实例

设备则查询 
设备列表下所有的设备实例

数据库则查询 
aiops_item_group_mapping aiopsItemGroup='DATABASE' 的运维项目下的实例
end note
|Mysql|
:返回租户实例数据;
|后台服务 (运维实例服务)|
:组装租户实例数据;
|智管家 (页面)|
:渲染租户实例数据;
:弹出新增表单弹框;
:显示表单、实例数据;
|角色 (MIS)|
:填写表单数据;
:选择实例数据;
:点击保存;
|智管家 (页面)|
:发起保存请求;
|后台服务 (网安稽查服务)|
:保存稽查项目数据;
|Mysql|
:存储稽查项目数据;
|后台服务 (网安稽查服务)|
:发起稽查项目模型数据保存请求;
|大数据平台|
:保存稽查项目模型数据;
|后台服务 (网安稽查服务)|
:返回保存成功信息;
|智管家 (页面)|
:显示保存成功提示;
|角色 (MIS)|
stop;
@enduml
```

```plantuml
@startuml
|MIS|
start
:点击删除网安稽查项目;
|智管家|
:发起删除请求;
|网安稽查服务|
:接收删除请求;
:删除项目;
|Mysql|
:删除项目数据;
|大数据平台|
:删除项目数据;
|网安稽查服务|
:返回删除成功;
|智管家|
:显示删除成功;
|MIS|
:查看删除结果;
stop
@enduml
```

```plantuml
|角色（MIS）|
start
:点击编辑按钮;
|智管家 (页面)|
:发起表单模型字段数据查询请求;
|后台服务 (模型服务)|
:查询模型字段表单数据;
|Mysql|
:返回模型字段表单数据;
|后台服务 (模型服务)|
:组装模型字段表单数据;
|智管家 (页面)|
:渲染动态表单;
:发起租户实例数据请求;
|后台服务 (运维实例服务)|
:查询租户实例数据;
|Mysql|
:返回租户实例数据;
|后台服务 (运维实例服务)|
:组装租户实例数据;
|智管家 (页面)|
:渲染租户实例数据;
:弹出新增表单弹框;
:显示表单、实例数据;
|智管家（页面）|
:渲染表单内容;
note
根据当前列表内容
end note
:展示表单内容;
|角色（MIS）|
:修改表单数据;
note 
设备、数据库只能修改名称
end note
:点击保存;
|智管家（页面）|
:发起编辑内容请求;
|后台服务（网安稽查服务）|
:更新项目数据;
|Mysql|
:更新项目数据;
|大数据平台|
:更新数据;
|角色（MIS）|
stop;
```

```plantuml
|角色（MIS）|
start
:点击关联产品按钮;
|智管家（页面）|
:发起实例查询请求;
note
信息系统查询 
aiops_item_group_mapping aiopsItemGroup='PRODUCT_APP' 的运维项目下的实例
end note
|后台服务（运维实例服务）|
:查询租户运维实例数据;
|Mysql|
:查询租户实例数据;
|智管家（页面）|
:渲染实例选择列表;
:选择目标实例;
:提交关联请求;
|后台服务（网安稽查服务）|
:保存关联数据;
|Mysql|
:存储关联关系;
|角色（MIS）|
stop;
```


```mermaid
classDiagram

    class aiops_instance {
        + id: long
        + samcd: long
        + aiopsItemId: string
        + getInstanceDetail(eid,modelCode) List<AiopsInstance>
    }

    class cmdb_field {
        + id: long
        + moduleId: long
        + fieldCode: string
        + fieldName: string
        + getModelField(Long moduleId) List<ModelField>
    }

    class network_security_examination_project~网安稽查项目~ {
        + id: long
        + sid: long
        + eid: long
        + modelCode: string
        + aiopsItemId: string
        + aiId: long
        + 创建时间: datetime
        + 更新时间: datetime
        + getProjectModelMapping(projectType) List<ProjectModelMapping>
        + getProject(modelCode,pageNum, pageSize) Page<project>
        - getProjectInstanceDetailData(List instanceIdList, eid) List<ProjectModel>
        - getProjectModelData(List projectId) List<ProjectModel>
        + saveProject(modelData, instanceId) ResponseBase
        - saveProjectModelData(modelData, projectId) ResponseBase
        + deleteProject(projectId) ResponseBase
        - deleteProjectModelData(projectId) ResponseBase
        
        + updateProject(modelData, instanceId) ResponseBase
      
     
    }
    style network_security_examination_project fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5

    class network_security_examination_project_type~网安稽查项目类别~ {
        + id: long
        + 类别名称: string
        + 类别Code: string
        + 创建时间: datetime
        + 更新时间: datetime
        + getProjectType(类别Code) List<ProjectType>
    }
    style network_security_examination_project_type fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5

    class network_security_examination_project_model_mapping~网安稽查项目模型mapping~ {
        + id: long
        + 类别Code: long
        + 模型名称: string
        + 模型Code: string
        + 创建时间: datetime
        + 更新时间: datetime
        - getProjectModelDataCount(List modelCode)
    }
    style network_security_examination_project_model_mapping fill:#E0FFFF,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5

    class network_security_examination_info_system~SR 网安稽查信息系统模型~ {
        + projectId: long
        + 系统名称: string
        + 安全保护等级: string
        + 是否备案: boolean
        + 备案编号: string
        + 备注: string
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_info_system fill:#F0FFF0 ,stroke:#f66,stroke-width:2px,color:#000000,stroke-dasharray: 5 5
%% 定义关联关系
    network_security_examination_project_type "1" -- "*" network_security_examination_project_model_mapping
    network_security_examination_project_model_mapping "1" -- "*" network_security_examination_project
    network_security_examination_project "1" -- "1" network_security_examination_info_system
    network_security_examination_project <.. aiops_instance
    network_security_examination_info_system "1" -- "1" cmdb_field
```