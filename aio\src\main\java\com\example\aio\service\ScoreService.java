package com.example.aio.service;

import java.math.BigDecimal;
import java.util.*;

public class ScoreService {
//    private static Map<String, Object> getStringObjectMap(AiopsExamIndexType indexType, Map<Long, PriorityQueue<TagLayer>> aeiAdditionalContentMap) {
//        Map<String, Object> indexTypeMap = new HashMap<>();
//        indexTypeMap.put("indexTypeName", indexType.getName());
//        List<Map<String, Object>> aeiList = new ArrayList<>();
//        if (indexType.getAeiList() != null) {
//            for (AiopsExamIndex aei : indexType.getAeiList()) {
//                PriorityQueue<TagLayer> tagLayers = aeiAdditionalContentMap.get(aei.getTagId());
//
//                Map<String, Object> aeiMap = new HashMap<>();
//                aeiMap.put("name", aei.getName());
//                aeiMap.put("referenceValue", aei.getReferenceValue());
//                if (aei.getAeis() != null && tagLayers != null) {
//                    boolean isNormal = "HEALTH".equals(aei.getAeis().getLevelCode());
//                    BigDecimal examScore = aei.getAeis().getExamScore();
//
//                    // 根据examScore选择合适的TagLayer
//                    TagLayer selectedTagLayer = selectTagLayerByScore(tagLayers, examScore);
//
//                    aeiMap.put("isNormal", isNormal);
//                    String desc = selectedTagLayer != null ? selectedTagLayer.getName() : "";
//                    aeiMap.put("additionalDesc", desc);
//                    aei.setAdditionalDesc(desc);
//                    aei.setIsNormal(isNormal);
//                }
//                aeiList.add(aeiMap);
//            }
//        }
//        indexTypeMap.put("aeiList", aeiList);
//        return indexTypeMap;
//    }

    /**
     * 根据examScore选择合适的TagLayer
     * 将PriorityQueue中的TagLayer按照分数*权重形成分数范围，根据examScore落在哪个范围选择对应的TagLayer
     *
     * @param tagLayers TagLayer的优先队列（已按分数*权重降序排列）
     * @param examScore 考试分数
     * @return 选中的TagLayer，如果没有合适的则返回null
     */
//    private static TagLayer selectTagLayerByScore(PriorityQueue<TagLayer> tagLayers, BigDecimal examScore) {
//        if (tagLayers == null || tagLayers.isEmpty() || examScore == null) {
//            return null;
//        }
//
//        // 将PriorityQueue转换为List，保持降序排列
//        List<TagLayer> sortedTagLayers = new ArrayList<>();
//        PriorityQueue<TagLayer> tempQueue = new PriorityQueue<>(tagLayers);
//        while (!tempQueue.isEmpty()) {
//            sortedTagLayers.add(tempQueue.poll());
//        }
//
//        if (sortedTagLayers.isEmpty()) {
//            return null;
//        }
//
//        // 计算分数范围并选择TagLayer
//        double examScoreValue = examScore.doubleValue();
//
//        // 如果只有一个TagLayer，直接返回
//        if (sortedTagLayers.size() == 1) {
//            return sortedTagLayers.get(0);
//        }
//
//        // 计算每个TagLayer的分数
//        List<Double> scores = new ArrayList<>();
//        for (TagLayer tagLayer : sortedTagLayers) {
//            double score = 0.0;
//            if (tagLayer.getTlwm() != null && tagLayer.getTlwm().getScore() != null && tagLayer.getTlwm().getWeight() != null) {
//                score = tagLayer.getTlwm().getScore() * (tagLayer.getTlwm().getWeight() / 100.0);
//            }
//            scores.add(score);
//        }
//
//        // 根据examScore选择合适的TagLayer
//        // 分数范围逻辑：examScore >= 当前TagLayer分数时，选择该TagLayer
//        for (int i = 0; i < sortedTagLayers.size(); i++) {
//            if (examScoreValue >= scores.get(i)) {
//                return sortedTagLayers.get(i);
//            }
//        }
//
//        // 如果examScore小于所有TagLayer的分数，返回分数最低的TagLayer
//        return sortedTagLayers.get(sortedTagLayers.size() - 1);
//    }
}
