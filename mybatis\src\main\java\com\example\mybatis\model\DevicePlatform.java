package com.example.mybatis.model;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 设备平台枚举
 */
public enum DevicePlatform {
    UNKNOWN("UNKNOWN", "UNKNOWN", "未知"),
    UNIX("UNIX", "UNIX", "UNIX"),
    LINUX("LINUX", "LINUX", "LINUX"),
    WINDOWS("WINDOWS", "WINDOWS", "WINDOWS"),
    DARWIN("DARWIN", "MAC", "MAC"),
    TMP_RH("TMP_RH", "TMP_RH", "TMP_RH")
    ;

    // 成员变量
    private String code;
    private String name;
    private String description;

    // 构造方法
    DevicePlatform(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getDynamicDescription(Object... params) {
        return String.format(this.description, params);
    }

    public boolean isSame(String name) {
        return this.name().equals(name);
    }

    public boolean isSame(DevicePlatform platform) {
        if (Objects.isNull(platform)) {
            return false;
        }
        return this.isSame(platform.name);
    }

    public static Optional<DevicePlatform> getStatusByString(String code) {
        return Arrays.stream(DevicePlatform.values())
                .filter(row -> row.isSame(code))
                .findFirst();
    }
}
