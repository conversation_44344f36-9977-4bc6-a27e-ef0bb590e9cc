f_highAvailabilityDescription,VARCHAR
deviceId,VARCHAR
deviceName,VARCHAR
deviceStatus,ENUM
owner,VARCHAR
createTime,DATETIME
updateTime,DATETIME
snmpServiceIP,VARCHAR
f_deviceType_kw,ENUM
f_deviceRole_kw,ENUM
f_osVersion,ENUM
f_osBit,ENUM
f_roleRemark,VARCHAR
f_physicalHostIPMI,ENUM
f_physicalHostIPMIDesc,VARCHAR
f_sqlVersion,VARCHAR
f_erpVersion,VARCHAR
f_erpVoucherPath,VARCHAR
f_kwContractState,ENUM
f_kwContractExprityDate,DATE
f_kwContractDesc,VARCHAR
f_remark,TEXT
f_hardwareHealth,ENUM
f_status,ENUM
cpuUsage,DECIMAL
ramSize,DECIMAL
ramUsage,DECIMAL
f_cpuStatus,ENUM
f_ramStatus,ENUM
f_raid,ENUM
f_dynamicDisk,ENUM
f_raidStatus,ENUM
f_basicSmart,ENUM
f_basicSmartStatus,ENUM
f_usbSmart,ENUM
f_usbSmartStatus,ENUM
f_hddStatus,ENUM
f_diskCode,VARCHAR
f_diskType,ENUM
f_diskCapacity,DECIMAL
f_diskAvailableCapacity,DECIMAL
f_diskInfo,VARCHAR
f_performanceOperation,ENUM
f_otherPerformanceDesc,TEXT
f_backupFolder,ENUM
f_backupMethod,VARCHAR
f_backupStatus,ENUM
f_sqlScheduleLogCheck,ENUM
f_backupVerify,ENUM
f_sqlBackupFolder,LABEL
f_sqlDataFilePath,VARCHAR
f_sqlDataFileCapacity,DECIMAL
f_sqlDataFileCount,DECIMAL
f_storageEquipment,ENUM
f_kwBackupContract,ENUM
f_ipRequired,VARCHAR
f_RemainingSpace,DECIMAL
f_builtInBackupSoftware,VARCHAR
f_backupMethodSetting,ENUM
f_wholeMachineBackupSoftware,ENUM
f_backupLocation,ENUM
f_description,VARCHAR
f_backupOperationStatus,ENUM
f_backupDevice,ENUM
f_wholeMachineBackupStatus,ENUM
f_remoteBackupLocation,ENUM
f_backupPathSecurity,ENUM
f_dataSafely,ENUM
f_offline Backup,ENUM
f_otherBackupStatusDesc,VARCHAR
f_eventViewer,ENUM
f_antiDrugBrand,ENUM
f_updateStatus,ENUM
f_contractInspection,ENUM
f_antiVirusOperationStatus,ENUM
f_operatingSystemStatus,ENUM
f_otherSystemStatusDesc,VARCHAR
f_connectionMode,ENUM
f_connectionDesc,VARCHAR
f_upsConnectionStatus,VARCHAR
snmpServicePort,VARCHAR
f_externalIp,VARCHAR
f_externalPort,VARCHAR
f_purpose,ENUM
f_otherPurpose,VARCHAR
f_profileBackupPath,VARCHAR
f_backupProfilebackup,ENUM
f_joinDomain,ENUM
f_highAvailability,ENUM
f_joinDomainDesc,VARCHAR
f_otherDescription,TEXT
diskDescr,VARCHAR
diskSize,DECIMAL
diskUsage,DECIMAL
diskFreeSpace,DECIMAL
f_diskHealthSMART,ENUM
f_smartScheduling,ENUM
f_firmwareVersion,VARCHAR
f_resultsAndJudgment,ENUM
f_systemLogCheck,ENUM
f_logCenter,ENUM
f_checkResult,ENUM
f_backupResourceBucket,ENUM
f_snapshot,ENUM
f_signatureResultsAndJudgment,ENUM
f_domainName,VARCHAR
f_dnsRole,ENUM
f_position,VARCHAR
f_diskCapacityReq,DECIMAL
f_diskUsedCapacity,DECIMAL
f_diskAvailableCapacityReq,DECIMAL
f_backupCapacity,DECIMAL
f_backupSchedule,ENUM
f_cpuNum,DECIMAL
f_cpuTotal,DECIMAL
f_cpuRemaining,DECIMAL
f_vCpuConfig,DECIMAL
f_vCpuRemaining,DECIMAL
f_ramTotalReq,DECIMAL
f_ramTotal,DECIMAL
f_ramRemaining,DECIMAL
f_vRamConfig,DECIMAL
f_vRamRemaining,DECIMAL
hddSize,DECIMAL
f_hddRemaining,DECIMAL
f_equipmentFramework,ENUM
f_esxiCheckResult,ENUM
f_FTStatus,ENUM
f_HAStatus,ENUM
f_esxiLogCheck,LABEL
f_performanceStatus,LABEL
f_warrantyPeriod,DATE
f_hardDiskCapacity,DECIMAL
f_lunZoneName,VARCHAR
f_lunConfig,DECIMAL
f_lunUsed,DECIMAL
f_lunRemaining,DECIMAL
f_hypervisor,VARCHAR
f_localFramework,ENUM
f_IPMIIP,VARCHAR
f_localDiskName,VARCHAR
f_localDiskConfig,DECIMAL
f_localDiskUsage,DECIMAL
f_localDiskRemaining,DECIMAL
f_localDiskIsCalculated,BOOLEAN
f_localDiskRemark,VARCHAR
f_localDiskTotal,DECIMAL
f_localDiskRemainingReq,DECIMAL
f_guestOS,VARCHAR
f_guestOSArea,VARCHAR
f_guestOSStatus,ENUM
f_guestOSvCpu,DECIMAL
f_guestOSvRam,DECIMAL
f_guestOSvHdd,DECIMAL
f_cpuUsageAvgReq,DECIMAL
f_ramUsageAvgReq,DECIMAL
f_health,ENUM
f_softwareCaption,VARCHAR
version,VARCHAR
installDate,DATE
cpuCaption,TEXT
memoryCpation,TEXT
hddCaption,TEXT
networkCaption,TEXT
computerPlacement,VARCHAR
computerAddress,VARCHAR
assetCategory,ENUM
f_backupSoftware,VARCHAR
f_angentRole,VARCHAR
f_sourceDeviceName,VARCHAR
f_backupSchName,VARCHAR
f_backupSourceLocation,VARCHAR
f_backupType,VARCHAR
f_backupCycle,VARCHAR
f_backupDestDevice,VARCHAR
f_backupDest,VARCHAR
f_deservedDays,VARCHAR
f_cpuConfig,DECIMAL
f_cpuRemainingAfterConfig,DECIMAL
f_ramConfig,DECIMAL
f_ramRemainingAfterConfig,DECIMAL
f_softwareBackupStatus,ENUM
f_existMaintenanceList,BOOLEAN
f_deviceCatalog,VARCHAR
f_cpuStatusDescription,VARCHAR
f_ramStatusDescription,VARCHAR
f_hardwareHealthStatus,ENUM
f_hddStatusDescription,VARCHAR
f_backupLocationDescription,VARCHAR
f_backupOperationStatusDescription,VARCHAR
f_backupDeviceDescription,VARCHAR
f_wholeMachineBackupStatusDescription,VARCHAR
f_remoteBackupLocationDescription,VARCHAR
f_backupPathSecurityDescription,VARCHAR
f_dataSafelyDescription,VARCHAR
f_offlineBackupDescription,VARCHAR
f_antiVirusOperationStatusDescription,VARCHAR
f_operatingSystemStatusDescription,VARCHAR
f_logCenterDescription,VARCHAR
f_backupResourceBucketDescription,VARCHAR
f_snapshotDescription,VARCHAR
f_dataBackupCheckResult,ENUM
f_signatureResultsAndJudgmentDescription,VARCHAR
f_localDiskRemainingReal,DECIMAL
f_lunRemainingReal,DECIMAL
f_cpuStatus_None,ENUM
f_ramStatus_None,ENUM
f_ramTotalNoReq,DECIMAL
f_diskDescr,VARCHAR
f_diskSize,DECIMAL
f_diskUsage,DECIMAL
f_diskFreeSpace,DECIMAL
f_diskHealthSMART_M,ENUM
f_esxiCheckResult_none,ENUM
f_hddStatus_none,ENUM
systemRemark,VARCHAR
type,ENUM
cpu_time_cpu,BOOLEAN
cpu_time_guest,BOOLEAN
cpu_time_guest_nice,BOOLEAN
cpu_time_idle,BOOLEAN
cpu_time_iowait,BOOLEAN
cpu_time_irq,BOOLEAN
cpu_time_nice,BOOLEAN
cpu_time_softirq,BOOLEAN
cpu_time_steal,BOOLEAN
cpu_time_system,BOOLEAN
cpu_time_user,BOOLEAN
cpu_usage_cpu,BOOLEAN
cpu_usage_guest,BOOLEAN
cpu_usage_guest_nice,BOOLEAN
cpu_usage_idle,BOOLEAN
cpu_usage_iowait,BOOLEAN
cpu_usage_irq,BOOLEAN
cpu_usage_nice,BOOLEAN
cpu_usage_softirq,BOOLEAN
cpu_usage_steal,BOOLEAN
cpu_usage_system,BOOLEAN
cpu_usage_user,BOOLEAN
collect_cpu_time,BOOLEAN
per_cpu,BOOLEAN
total_cpu,BOOLEAN
disk_device,BOOLEAN
disk_free,BOOLEAN
disk_fstype,BOOLEAN
disk_inodes_fress,BOOLEAN
disk_inodes_total,BOOLEAN
disk_inodes_used,BOOLEAN
disk_path,BOOLEAN
disk_total,BOOLEAN
disk_used,BOOLEAN
disk_used_percent,BOOLEAN
ignore_fs,LIST
mount_points,LIST
mem_active,BOOLEAN
mem_available,BOOLEAN
mem_available_percent,BOOLEAN
mem_buffered,BOOLEAN
mem_cached,BOOLEAN
mem_free,BOOLEAN
mem_inactive,BOOLEAN
mem_total,BOOLEAN
mem_used,BOOLEAN
mem_used_percent,BOOLEAN
net_bytes_sent,BOOLEAN
net_bytes_sent_per_sec,BOOLEAN
net_bytes_recv,BOOLEAN
net_bytes_recv_per_sec,BOOLEAN
net_packets_sent,BOOLEAN
net_packets_sent_per_sec,BOOLEAN
net_packets_recv,BOOLEAN
net_packets_recv_per_sec,BOOLEAN
net_err_in,BOOLEAN
net_err_out,BOOLEAN
net_drop_in,BOOLEAN
net_drop_out,BOOLEAN
net_interface,BOOLEAN
net_hw_addr,BOOLEAN
interfaces,VARCHAR
skip_protocols_state,BOOLEAN
mode,ENUM
name,VARCHAR
runner_name,VARCHAR
script_exec_interprepter,ENUM
script_params,ENUM
script_params_spliter,VARCHAR
script_content,TEXT
script_timeout,VARCHAR
script_cron,VARCHAR
script_exec_onstart,BOOLEAN
script_check_command,VARCHAR
script_fix_result_encoding,BOOLEAN
script_detect_options,VARCHAR
script_detect_error_options,VARCHAR
script_fix_result_by_os_encoding,BOOLEAN
script_command_not_exist_result_key,VARCHAR
execute_error_return,BOOLEAN
execute_error_return_key,VARCHAR
script_env_sources,VARCHAR
oracle_datasource,VARCHAR
oracle_server_name,VARCHAR
oracle_database,VARCHAR
oracle_schema,VARCHAR
oracle_sql,TEXT
datasource_tag,VARCHAR
oracle_limit_batch,INT
oracle_cron,VARCHAR
oracle_exec_onstart,BOOLEAN
is_batch_return,BOOLEAN
contain_source_id,BOOLEAN
source_id_key,VARCHAR
source_id_value,VARCHAR
log_path,VARCHAR
ignore_log_path,VARCHAR
read_from,VARCHAR
stat_interval,VARCHAR
encoding,VARCHAR
expire,VARCHAR
expire_delete,BOOLEAN
submeta_expire,VARCHAR
fileModifyReload,BOOLEAN
ignore_file_suffix,VARCHAR
http_method,ENUM
http_service_address,VARCHAR
http_service_path,VARCHAR
http_cron,VARCHAR
http_exec_onstart,BOOLEAN
http_dial_timeout,INT
http_response_timeout,INT
http_headers,VARCHAR
http_body,VARCHAR
wmi_namespace,VARCHAR
wmi_instance_name,VARCHAR
wmi_fields,VARCHAR
wmi_where_string,VARCHAR
wmi_exec_onstart,BOOLEAN
wmi_cron,VARCHAR
wmi_is_convert_to_true_type,BOOLEAN
wmi_field_name_case_type,ENUM
event_log_name,VARCHAR
event_log_ignore_older,VARCHAR
event_log_ids,VARCHAR
event_log_levels,VARCHAR
event_log_providers,VARCHAR
event_log_max_batch_read,INT
event_log_include_xml,BOOLEAN
event_log_forwarded,BOOLEAN
event_log_scan_interval,VARCHAR
dir_or_file_path,VARCHAR
read_at_once_cron,VARCHAR
read_at_once_exec_onstart,BOOLEAN
read_at_once_batch_read_size,INT
read_at_once_result_field_name,VARCHAR
read_empty_return,BOOLEAN
read_empty_return_field_name,VARCHAR
read_at_once_v2_cron,VARCHAR
read_at_once_v2_exec_onstart,BOOLEAN
is_watch_read_file,BOOLEAN
read_file_removed_return_content,VARCHAR
read_file_removed_name_key,VARCHAR
read_empty_return_content,VARCHAR
is_read_outlook_info_file,BOOLEAN
updateCollect,LIST
collectListVersion,VARCHAR
collectFileName,VARCHAR
collectVersion,VARCHAR
collectName,VARCHAR
execParamsVersion,VARCHAR
collectChangeFlag,INT
hasNewVersion,BOOLEAN
updateRemark,VARCHAR
localDeletedCollect,LIST
accId,BIGINT
collectorType,ENUM
toCronIntervalValue,INT
toCronValue,VARCHAR
headers,VARCHAR
only_headers_value,BOOLEAN
discard_headers,VARCHAR
disable_record_errdata,BOOLEAN
keep_raw_data,BOOLEAN
newline_ignore_empty_new_line,BOOLEAN
not_exist_error_tag,VARCHAR
error_tag,VARCHAR
error_continue_parse,BOOLEAN
special_error_tags,VARCHAR
white_space_split_ignore_header_lines,INT
white_space_split_merge_column_values,VARCHAR
xml_cast_string_to_value,BOOLEAN
host_name_key,VARCHAR
address_key,VARCHAR
stage,ENUM
key,VARCHAR
old,VARCHAR
new,VARCHAR
regex,BOOLEAN
value,VARCHAR
override,BOOLEAN
allow_ignore_override,BOOLEAN
pattern,VARCHAR
target_key,VARCHAR
other_keys,VARCHAR
expression,VARCHAR
nested_setting,VARCHAR
ignore_first_level,BOOLEAN
has_error_break,BOOLEAN
replace_old,VARCHAR
replace_new,VARCHAR
replace_regex,BOOLEAN
convert_to_number,VARCHAR
start_index,INT
end_index,INT
interval_displacement,INT
fields_definition,VARCHAR
continue_by_fields_values,VARCHAR
parse_to_int_32_fields,VARCHAR
parse_to_hex_fields,VARCHAR
delete_parsed_key,BOOLEAN
result_key,VARCHAR
values_mapping_definition,VARCHAR
reverse_result,BOOLEAN
field_position_definition,VARCHAR
out_of_range_error,BOOLEAN
out_of_range_value,VARCHAR
standard_key,VARCHAR
condition_expression,VARCHAR
target_field,VARCHAR
target_value_expression,VARCHAR
error_default_value,VARCHAR
source_path,VARCHAR
target_path,VARCHAR
conflict_option,ENUM
final_source_last_path,VARCHAR
final_target_last_path,VARCHAR
merge_option,ENUM
merge_array_all_target,BOOLEAN
keep_source_last_path,BOOLEAN
same_key_option,ENUM
override_when_target_is_root,BOOLEAN
result_group_key,VARCHAR
regex_string,VARCHAR
ignore_case,BOOLEAN
keep_last_path,BOOLEAN
separator,VARCHAR
map_key_sequence,VARCHAR
atmId,BIGINT
sender_type,VARCHAR
http_sender_url,VARCHAR
http_sender_protocol,VARCHAR
http_sender_escape_html,BOOLEAN
http_sender_package_template,VARCHAR
http_sender_send_data_to_string,BOOLEAN
http_sender_quote_package_data,BOOLEAN
http_sender_parent_package_template,VARCHAR
http_sender_csv_split,VARCHAR
http_sender_gzip,BOOLEAN
http_sender_timeout,VARCHAR
http_sender_other_headers,VARCHAR
http_sender_collect_config_id,VARCHAR
http_sender_upload_data_model_code,VARCHAR
ft_strategy,VARCHAR
ft_discard_failed_data,BOOLEAN
ft_retry_count_before_discard,INT
ft_memory_channel,BOOLEAN
ft_long_data_discard,BOOLEAN
max_disk_used_bytes,INT
max_size_per_file,INT
http_sender_encrypt_self_data,BOOLEAN
http_sender_use_proxy,BOOLEAN
http_sender_data_batch_send_size,INT
http_sender_fixed_sending_data,VARCHAR
eid,VARCHAR
collectedTime,VARCHAR
collectConfigId,VARCHAR
uploadDataModelCode,VARCHAR
deviceCollectDetailId,VARCHAR
usedType,ENUM
targetKey,VARCHAR
targetValue,VARCHAR
dbIdValue,VARCHAR
appIdValue,VARCHAR
relateIdValue,VARCHAR
sourceAppIdValue,VARCHAR
adcdId,BIGINT
db_id,VARCHAR
db_type,VARCHAR
db_ip_address,VARCHAR
db_port,VARCHAR
db_server_name,VARCHAR
db_instance_name,VARCHAR
ds_ip_address,VARCHAR
ds_port,VARCHAR
ds_server_name,VARCHAR
cpu__time_cpu,VARCHAR
cpu__time_guest,INT
cpu__time_guest_nice,INT
cpu__time_idle,DECIMAL
cpu__time_iowait,INT
cpu__time_irq,INT
cpu__time_nice,INT
cpu__time_softirq,INT
cpu__time_steal,INT
cpu__time_system,DECIMAL
cpu__time_user,DECIMAL
cpu__usage_cpu,VARCHAR
cpu__usage_guest,INT
cpu__usage_guest_nice,INT
cpu__usage_idle,INT
cpu__usage_iowait,INT
cpu__usage_irq,INT
cpu__usage_nice,INT
cpu__usage_softirq,INT
cpu__usage_steal,INT
cpu__usage_system,INT
cpu__usage_user,INT
timestamp,VARCHAR
disk__device,VARCHAR
disk__free,BIGINT
disk__fstype,VARCHAR
disk__inodes_fress,INT
disk__inodes_total,INT
disk__inodes_used,INT
disk__path,VARCHAR
disk__total,BIGINT
disk__used,BIGINT
disk__used_percent,DECIMAL
mem__active,INT
mem__available,BIGINT
mem__available_percent,DECIMAL
mem__buffered,INT
mem__cached,INT
mem__free,BIGINT
mem__inactive,INT
mem__total,BIGINT
mem__used,BIGINT
mem__used_percent,DECIMAL
net__bytes_sent,BIGINT
net__bytes_sent_per_sec,BIGINT
net__bytes_recv,BIGINT
net__bytes_recv_per_sec,BIGINT
net__packets_sent,BIGINT
net__packets_sent_per_sec,BIGINT
net__packets_recv,BIGINT
net__packets_recv_per_sec,BIGINT
net__err_in,BIGINT
net__err_out,BIGINT
net__drop_in,BIGINT
net__drop_out,BIGINT
net__interface,VARCHAR
net__hw_addr,VARCHAR
tns_version,VARCHAR
tns_ping_request,VARCHAR
tns_result,VARCHAR
tns_ping_ip,VARCHAR
tns_ping_port,VARCHAR
cmdNotExist,VARCHAR
execError,VARCHAR
df_file_system,VARCHAR
df_blocks,VARCHAR
df_used,VARCHAR
df_available,VARCHAR
df_percent_use,VARCHAR
df_mounted_on,VARCHAR
df_percent_use_value,VARCHAR
open_mode,VARCHAR
status,VARCHAR
sum_MB,VARCHAR
used_MB,VARCHAR
free_MB,VARCHAR
percent_used,VARCHAR
tablespace_name,VARCHAR
backupTag,VARCHAR
backupTime,VARCHAR
scriptPath,VARCHAR
backupStatus,VARCHAR
errMsg,TEXT
datasource,VARCHAR
process_id,VARCHAR
process_user,VARCHAR
process_priority,VARCHAR
process_nice,VARCHAR
process_virt,VARCHAR
process_res,VARCHAR
process_shr,VARCHAR
process_status,VARCHAR
process_cpu_percent,VARCHAR
process_mem_percent,VARCHAR
process_time,VARCHAR
process_command,VARCHAR
du_no,VARCHAR
du_size,VARCHAR
du_path,VARCHAR
process_cmd,VARCHAR
process_etime,VARCHAR
source_db_id,VARCHAR
redo_date,VARCHAR
redo_cnt,INT
redo_detail_date,VARCHAR
redo_detail_cnt,INT
table_space_name,VARCHAR
table_total_size_mb,DECIMAL
table_total_free_size_mb,DECIMAL
table_used_size,DECIMAL
table_used_percent,DECIMAL
temptabs_table_space_name,VARCHAR
temptabs_total_size,DECIMAL
temptabs_total_free_size_mb,DECIMAL
temptabs_used_size,DECIMAL
temptabs_used_percent,DECIMAL
temptabs_sid,VARCHAR
temptabs_serial,VARCHAR
temptabs_process,VARCHAR
temptabs_segment_name,VARCHAR
temptabs_segment_type,VARCHAR
temptabs_owner,VARCHAR
temptabs_mb,DECIMAL
undo_table_space_name,VARCHAR
undo_total_size,DECIMAL
undo_total_free_size_mb,DECIMAL
undo_used_size,DECIMAL
undo_used_percent,DECIMAL
undo_sid,VARCHAR
undo_serial,VARCHAR
undo_process,VARCHAR
undo_sql_id,VARCHAR
undo_segment_name,VARCHAR
undo_status,VARCHAR
undo_mb,DECIMAL
temp_table_sapce_name,VARCHAR
temp_total_size,DECIMAL
temp_total_free_size,DECIMAL
temp_used_size,DECIMAL
temp_used_percent,DECIMAL
temp_auto_ext,VARCHAR
temp_max_size,DECIMAL
bak_result,VARCHAR
lockwait_n,VARCHAR
lockwait_objects_owner,VARCHAR
lockwait_object_name,VARCHAR
lockwait_os_user_name,VARCHAR
lockwait_program_gzwl002,VARCHAR
lockwait_object_process,VARCHAR
lockwait_program_gzwl003,VARCHAR
lockwait_program_gzwl007,VARCHAR
lockwait_program,VARCHAR
lockwait_machine,VARCHAR
lockwait_terminal,VARCHAR
lockwait_inst_id,VARCHAR
lockwait_sid,VARCHAR
lockwait_serial,VARCHAR
lockwait_spid,VARCHAR
lockwait_status,VARCHAR
lockwait_seconds_in_wait,INT
appServiceId,VARCHAR
appServiceName,VARCHAR
productCode,VARCHAR
appServiceType,ENUM
appServiceCategory,ENUM
registerTime,DATE
f_ServiceUrl,VARCHAR
appServiceIp,VARCHAR
appServicePort,VARCHAR
dbId,VARCHAR
dbName,VARCHAR
dbType,ENUM
f_sid,VARCHAR
redo_group,VARCHAR
redo_status,VARCHAR
redo_type,VARCHAR
redo_member,VARCHAR
tablespace_status,VARCHAR
db_file_name,VARCHAR
db_file_tablespace_name,VARCHAR
db_file_autoextensible,VARCHAR
db_file_current_size_mb,DECIMAL
db_file_max_size_mb,DECIMAL
db_file_status,VARCHAR
index_name,VARCHAR
index_table_name,VARCHAR
index_tablespace_name,VARCHAR
index_column_name,VARCHAR
index_size_mb,DECIMAL
index_status,VARCHAR
trigger_owner,VARCHAR
trigger_name,VARCHAR
trigger_table_name,VARCHAR
trigger_status,VARCHAR
wait_event,VARCHAR
wait_event_count,INT
buffer_logical_reads,BIGINT
buffer_phys_reads,BIGINT
buffer_hit_rate,DECIMAL
shared_pool_hit_rate,DECIMAL
sorted_pool_hit_rate,DECIMAL
log_buffer_rate,DECIMAL
redo_switch_frequency,INT
bios_manufacturer,VARCHAR
bios_family,VARCHAR
bios_product_name,VARCHAR
os_product_name,VARCHAR
os_installation_type,VARCHAR
os_driver,VARCHAR
os_edition,VARCHAR
os_version,VARCHAR
domain_machine_name,VARCHAR
domain_user_name,VARCHAR
domain_groups,LIST
iis_install_path,VARCHAR
iis_www_root_path,VARCHAR
iis_version,VARCHAR
net_framework_name,VARCHAR
net_framework_display_name,VARCHAR
net_framework_service_pack,VARCHAR
net_framework_version,VARCHAR
python_version,VARCHAR
processor_vendor_id,VARCHAR
processor_name,VARCHAR
processor_number_of_cores,INT
processor_max_clock_speed,DECIMAL
processor_l2_cache_size,INT
processor_l3_cache_size,INT
physical_memory_total_gb,INT
physical_memory_capacity_gb,INT
physical_memory_device_locator,VARCHAR
disk_capacity_device,VARCHAR
disk_capacity_path,VARCHAR
disk_capacity_driver_format,VARCHAR
disk_capacity_total_gb,DECIMAL
disk_capacity_free_gb,DECIMAL
disk_capacity_used_gb,DECIMAL
disk_capacity_percent,DECIMAL
network_adapter_name,VARCHAR
network_adapter_type,VARCHAR
network_adapter_ip,LIST
network_adapter_mac,VARCHAR
network_adapter_speed_mb,INT
disk_driver_name,VARCHAR
disk_driver_vendor,VARCHAR
disk_driver_serial_number,VARCHAR
disk_driver_size_gb,DECIMAL
disk_driver_storage_controller,VARCHAR
disk_driver_model,VARCHAR
disk_driver_is_removable,BOOLEAN
sort_minus_1_field,VARCHAR
checkCode,INT
checkMessage,VARCHAR
is_invalid,VARCHAR
account,VARCHAR
default_table_space,VARCHAR
profile_source,VARCHAR
account_status,VARCHAR
password_expiration_setting,VARCHAR
lock_date,VARCHAR
expiry_date,VARCHAR
create_date,VARCHAR
last_login_time,VARCHAR
filesystem,VARCHAR
inodes,VARCHAR
iUsed,VARCHAR
iFree,VARCHAR
iUse,VARCHAR
mountedOn,VARCHAR
percent_processor_time,DECIMAL
display_name,VARCHAR
start_state,VARCHAR
scan_state,VARCHAR
update_state,VARCHAR
wel_channel,VARCHAR
wel_computer,VARCHAR
wel_activity_id,VARCHAR
wel_related_activity_id,VARCHAR
wel_key,VARCHAR
wel_value,VARCHAR
wel_id,INT
wel_qualifiers,INT
wel_kernel_time,INT
wel_process_id,INT
wel_processor_id,INT
wel_processor_time,INT
wel_session_id,INT
wel_thread_id,INT
wel_user_time,INT
wel_keywords,VARCHAR
wel_level,VARCHAR
wel_level_raw,INT
wel_message,TEXT
wel_opcode,VARCHAR
wel_opcode_raw,INT
wel_event_source_name,VARCHAR
wel_guid,VARCHAR
wel_name,VARCHAR
wel_record_id,BIGINT
wel_render_error,LIST
wel_render_error_code,INT
wel_render_error_data_item_name,VARCHAR
wel_task,VARCHAR
wel_task_raw,BIGINT
wel_system_time,VARCHAR
wel_domain,VARCHAR
wel_identifier,VARCHAR
wel_type,INT
wel_local,VARCHAR
wel_space,VARCHAR
wel_version,INT
wel_source_ip,VARCHAR
code,VARCHAR
message,VARCHAR
ps_code,VARCHAR
data,VARCHAR
error_message,VARCHAR
compliance,VARCHAR
enforcement,VARCHAR
domain_controller,VARCHAR
operating_system,VARCHAR
update_state_message,VARCHAR
ms_17_010_data,LIST
caption,VARCHAR
device_id,VARCHAR
interface_type,VARCHAR
active,BOOLEAN
length,INT
id,INT
attribute,VARCHAR
worst,INT
flags,INT
temperature,INT
tag,VARCHAR
capacity,DECIMAL
device_locator,VARCHAR
manufacturer,VARCHAR
serial_number,VARCHAR
relateId,VARCHAR
relateSourceAppId,VARCHAR
relateSourceModelCode,VARCHAR
relateTargetModelCode,VARCHAR
relateTargetJsonString,TEXT
minimum,INT
maximum,INT
config_value,INT
run_value,INT
sqlserver_biz_sbh_backup_start_date,DATETIME
sqlserver_biz_sbh_backup_finish_date,DATETIME
sqlserver_biz_sbh_name,VARCHAR
sqlserver_biz_sbh_type,ENUM
sqlserver_biz_sbh_recovery_model,VARCHAR
sqlserver_biz_sbh_backup_size,BIGINT
sqlserver_biz_sbh_physical_device_name,VARCHAR
sqlserver_biz_sbh_first_lsn,VARCHAR
sqlserver_biz_sbh_last_lsn,VARCHAR
sqlserver_biz_smp_name,VARCHAR
sqlserver_biz_smp_description,VARCHAR
sqlserver_biz_smp_createdate,DATETIME
sqlserver_biz_smp_packagedata,VARCHAR
sqlserver_biz_sst_selecttime,DATETIME
sqlserver_biz_sst_plan_handle,VARCHAR
sqlserver_biz_sst_sum_execution_count,INT
sqlserver_biz_sst_text,VARCHAR
sqlserver_biz_sst_sum_total_elapsed_time,BIGINT
sqlserver_biz_sst_max_total_elapsed_time,BIGINT
sqlserver_biz_sst_min_total_elapsed_time,BIGINT
sqlserver_biz_sst_sum_total_worker_time,BIGINT
sqlserver_biz_sst_max_total_worker_time,BIGINT
sqlserver_biz_sst_min_total_worker_time,BIGINT
sqlserver_biz_sst_max_total_logical_writes,BIGINT
sqlserver_biz_sst_min_total_logical_writes,BIGINT
sqlserver_biz_sst_sum_total_logical_writes,BIGINT
sqlserver_biz_sst_sum_total_logical_reads,BIGINT
sqlserver_biz_sst_max_total_logical_reads,BIGINT
sqlserver_biz_sst_min_total_logical_reads,BIGINT
sqlserver_biz_sst_sum_total_physical_reads,BIGINT
sqlserver_biz_sst_max_total_physical_reads,BIGINT
sqlserver_biz_sst_min_total_physical_reads,BIGINT
sqlserver_biz_sima_name,VARCHAR
sqlserver_biz_sima_statement,VARCHAR
sqlserver_biz_sima_object_id,VARCHAR
sqlserver_biz_sima_user_seeks,INT
sqlserver_biz_sima_user_scans,INT
sqlserver_biz_sima_equality_columns,VARCHAR
sqlserver_biz_sima_inequality_columns,VARCHAR
sqlserver_biz_sima_included_columns,VARCHAR
sqlserver_biz_sima_createsqltext,VARCHAR
sqlserver_biz_sdbp_sid,VARCHAR
sqlserver_biz_sdbp_date_now,DATETIME
sqlserver_biz_sdbp_spflag,VARCHAR
sqlserver_biz_sdbp_hostprocess,VARCHAR
sqlserver_biz_sdbp_blocked,VARCHAR
sqlserver_biz_sdbp_program_name,VARCHAR
sqlserver_biz_sdbp_net_address,VARCHAR
sqlserver_biz_sdbp_content,VARCHAR
sqlserver_biz_sdbp_cpu,INT
sqlserver_biz_sdbp_last_batch,DATETIME
sqlserver_biz_sdbp_open_tran,INT
sqlserver_biz_sdbp_loginame,VARCHAR
sqlserver_biz_sdbp_login_time,DATETIME
sqlserver_biz_sdbp_nt_domain,VARCHAR
sqlserver_biz_sdbp_hostname,VARCHAR
sqlserver_biz_sdbp_nt_username,VARCHAR
sqlserver_biz_sdbp_status,VARCHAR
sqlserver_biz_sdbp_spid,INT
sqlserver_biz_sdbp_kpid,INT
counter_biz_collect_001,VARCHAR
counter_biz_collect_002,VARCHAR
counter_biz_collect_003,VARCHAR
counter_biz_collect_004,VARCHAR
counter_biz_collect_005,BIGINT
counter_biz_collect_006,VARCHAR
snmp_cpu_usage,DECIMAL
snmp_nas_raid_name,VARCHAR
snmp_storage_remain,BIGINT
snmp_ups_output_load,BIGINT
snmp_ups_temperature,BIGINT
snmp_ups_battery_capacity,BIGINT
snmp_fw_sn_number,VARCHAR
ilo_collect_address,VARCHAR
ilo_sys_health,VARCHAR
ilo_ams_health,VARCHAR
ilo_bios_hardware_health,VARCHAR
ilo_fans_health,VARCHAR
ilo_memory_health,VARCHAR
ilo_network_health,VARCHAR
ilo_power_health,VARCHAR
ilo_processors_health,VARCHAR
ilo_battery_health,VARCHAR
ilo_storage_health,VARCHAR
ilo_temperatures_health,VARCHAR
ilo_collect_time,DATETIME
ilo_error,VARCHAR
idrac_collect_address,VARCHAR
idrac_sys_health,VARCHAR
idrac_storage_health,VARCHAR
idrac_collect_time,DATETIME
idrac_error,VARCHAR
backup_software_license_status,VARCHAR
backup_software_license_expiry_date,VARCHAR
backup_status,VARCHAR
backup_msg,VARCHAR
backup_collect_time,DATETIME
backup_plan_name,VARCHAR
backup_software_license_expiry_days,BIGINT
backup_software_status,BOOLEAN
backup_software_connect_status,BOOLEAN
snmp_storage_size,BIGINT
snmp_storage_used,BIGINT
snmp_storage_used_rate,DECIMAL
snmp_storage_allocation_units,INT
snmp_storage_status,VARCHAR
snmp_agent_host,VARCHAR
snmp_collect_time,DATETIME
snmpId,VARCHAR
snmp_brand,VARCHAR
index,VARCHAR
snmp_storage_model,VARCHAR
snmp_storage_status_type,VARCHAR
snmp_firmware_version,VARCHAR
snmp_storage_enclosure_id,VARCHAR
http_basic_auth_code,VARCHAR
backup_software_name,ENUM
backup_software_reader_other_params,VARCHAR
backup_software_reader_collect_name,VARCHAR
snmp_tables,TEXT
snmp_time_out,VARCHAR
snmp_interval,VARCHAR
snmp_agents,VARCHAR
snmp_community,VARCHAR
snmp_version,VARCHAR
snmp_sec_name,VARCHAR
snmp_auth_password,VARCHAR
snmp_priv_password,VARCHAR
summary_column,VARCHAR
summary_result_column_name,VARCHAR
summary_group_keys,VARCHAR
use_summary_group,BOOLEAN
md5_summary_group_key,BOOLEAN
eai_trbi_degrees,DECIMAL
eai_trbi_wetness,DECIMAL
eai_trbi_device_serial,VARCHAR
eai_trbi_device_collect_unixtime,VARCHAR
eai_trbi_last_row_of_this_query,VARCHAR
device_serial_number,VARCHAR
device_model,VARCHAR
assetFrom,ENUM
f_cpuWarningCount,INT
f_hddWarningCount,INT
f_ramWarningCount,INT
net__bytes_recv_per_sec_warningCount,INT
net__bytes_sent_per_sec_wainingCount,INT
process_name,VARCHAR
process_execute_account,VARCHAR
process_working_set_size,BIGINT(30)
process_working_set_private,BIGINT(30)
process_page_file_usage,BIGINT(30)
process_private_usage,BIGINT(30)
process_mem_usage,BIGINT(30)
process_creation_time,VARCHAR
process_user_time,DECIMAL
process_kernel_time,DECIMAL
process_real_diff,VARCHAR
process_cpu_usage,VARCHAR
process_cpu_core,VARCHAR
process_elapsed_time,BIGINT
process_is_running,BIGINT
process_status_code,BIGINT
product_code,VARCHAR
process_collect_info_params,TEXT
acp_crmab_ec_ec001,VARCHAR
acp_crmab_ec_ec002,VARCHAR
acp_crmab_ec_ec003,VARCHAR
acp_crmab_ec_ec004,VARCHAR
acp_crmab_ec_ec005,VARCHAR
acp_crmab_ec_ec006,VARCHAR
acp_crmab_ec_ec007,VARCHAR
acp_crmab_ec_ec008,VARCHAR
acp_crmab_ec_ec009,VARCHAR
acp_crmab_ec_ec010,VARCHAR
acp_crmab_ec_ec011,VARCHAR
acp_crmab_ec_ec012,VARCHAR
acp_crmab_ec_ec013,VARCHAR
acp_crmab_ec_a01,VARCHAR
acp_crmab_ec_a02,VARCHAR
acp_crmab_ec_a03,VARCHAR
acp_crmab_ec_a04,VARCHAR
acp_crmab_ec_a05,VARCHAR
acp_crmab_ec_a06,VARCHAR
acp_crmab_ec_a07,VARCHAR
acp_crmab_ec_a08,VARCHAR
acp_crmab_ec_a09,VARCHAR
acp_crmab_ec_a10,VARCHAR
acp_crmab_ec_a11,VARCHAR
acp_crmab_ec_b01,VARCHAR
acp_crmab_ec_b02,VARCHAR
acp_crmab_ec_b03,VARCHAR
acp_crmab_ec_b04,VARCHAR
acp_crmab_ec_b05,VARCHAR
acp_crmab_ec_b06,VARCHAR
acp_crmab_ec_b07,VARCHAR
acp_crmab_ec_c01,VARCHAR
acp_crmab_ec_c02,VARCHAR
acp_crmab_ec_c03,VARCHAR
acp_crmab_ec_c04,VARCHAR
acp_crmab_ec_c05,VARCHAR
acp_crmab_ec_d01,VARCHAR
acp_crmab_ec_d02,VARCHAR
acp_crmab_ec_d03,VARCHAR
acp_crmab_ec_d04,VARCHAR
acp_crmab_ec_a011,VARCHAR
acp_crmab_ec_a021,VARCHAR
acp_crmab_ec_a031,VARCHAR
acp_crmab_ec_a041,VARCHAR
acp_crmab_ec_a051,VARCHAR
acp_crmab_ec_a061,VARCHAR
acp_crmab_ec_a071,VARCHAR
acp_crmab_ec_a081,VARCHAR
acp_crmab_ec_a091,VARCHAR
acp_crmab_ec_a101,VARCHAR
acp_crmab_ec_a111,VARCHAR
acp_crmab_ec_b011,VARCHAR
acp_crmab_ec_b021,VARCHAR
acp_crmab_ec_b031,VARCHAR
acp_crmab_ec_b041,VARCHAR
acp_crmab_ec_b051,VARCHAR
acp_crmab_ec_b061,VARCHAR
acp_crmab_ec_b071,VARCHAR
acp_crmab_ec_c011,VARCHAR
acp_crmab_ec_c021,VARCHAR
acp_crmab_ec_c031,VARCHAR
acp_crmab_ec_c041,VARCHAR
acp_crmab_ec_c051,VARCHAR
acp_crmab_ec_d011,VARCHAR
acp_crmab_ec_d012,VARCHAR
acp_crmab_ec_d021,VARCHAR
acp_crmab_ec_d031,VARCHAR
acp_crmab_ec_d041,VARCHAR
acp_crmgz_gz001,VARCHAR
acp_crmgz_gz002,VARCHAR
acp_crmgz_gz003,VARCHAR
acp_crmgz_gz004,VARCHAR
acp_crmgz_gz005,VARCHAR
acp_crmgz_gz006,VARCHAR
acp_crmgz_gz007,VARCHAR
acp_crmgz_gz008,VARCHAR
acp_crmgz_gz009,INT
acp_crmin_in001,VARCHAR
acp_crmin_in002,VARCHAR
acp_crmin_in002c,VARCHAR
acp_crmin_in003,VARCHAR
acp_crmin_in003c,VARCHAR
acp_crmhg_hg001,VARCHAR
acp_crmhg_hg003,VARCHAR
acp_crmhg_hg005,VARCHAR
acp_crmhg_hg007,VARCHAR
acp_crmhg_hg009,VARCHAR
acp_crmhg_hg010,VARCHAR
acp_crmhg_hg012,VARCHAR
acp_crmhg_hg031,VARCHAR
outlook_version,VARCHAR
outlook_account_name,VARCHAR
outlook_pst_file_location,VARCHAR
outlook_pst_file_name,VARCHAR
outlook_pst_file_size,BIGINT
outlook_error_code,VARCHAR
outlook_error_description,VARCHAR
eai_cbsbi_client,VARCHAR
eai_cbsbi_host,VARCHAR
eai_cbsbi_plan,VARCHAR
eai_cbsbi_day_used,DECIMAL
eai_cbsbi_total_used,DECIMAL
eai_cbsbi_record_time,DECIMAL
eai_cbjbi_client,VARCHAR
eai_cbjbi_host,VARCHAR
eai_cbjbi_plan,VARCHAR
eai_cbjbi_id,DECIMAL
eai_cbjbi_type,VARCHAR
eai_cbjbi_start_time,DECIMAL
eai_cbjbi_end_time,DECIMAL
eai_cbjbi_ap_size,DECIMAL
eai_cbjbi_status,VARCHAR
eai_cbjbi_vm,VARCHAR
eai_cbjbi_record_time,DECIMAL
eai_cbebi_job_id,DECIMAL
eai_cbebi_id,DECIMAL
eai_cbebi_severity,DECIMAL
eai_cbebi_description,VARCHAR
eai_cbebi_event_time,DECIMAL
eai_cbebi_record_time,DECIMAL
lx_INT,INT
lx_CHAR,VARCHAR
lx_VARCHAR,TEXT
lx_BOOLEAN,BOOLEAN
lx_ENUM,ENUM
lx_DECIMAL,DECIMAL
lx_DATE,DATE
lx_bolean_test,BOOLEAN
123465,INT
INT_LX,INT
LX_ZD_01,INT
LX_ZD_02,VARCHAR
LX_ZD_03,TEXT
LX_ZD_04,BOOLEAN
LX_ZD_05,DECIMAL
LX_ZD_06,DATE
LX_ZD_07,INT
LX_ZD_00001,INT
LX_ZD_08,DECIMAL
LX_ZD_09,INT
LX_ZD_10,INT
mssql_database,VARCHAR
mssql_datasource,VARCHAR
mssql_exec_onstart,BOOLEAN
mssql_limit_batch,INT
mssql_sql,TEXT
mssql_cron,VARCHAR
file_id,VARCHAR
physical_name,VARCHAR
size,DECIMAL
used_space,DECIMAL
max_size,DECIMAL
growth_type,INT
growth,DECIMAL
test0809,VARCHAR
product,VARCHAR
product_version,VARCHAR
language,VARCHAR
platform,VARCHAR
edition,VARCHAR
internal_value,INT
name_info,VARCHAR
processor_count,INT
rows_info,DECIMAL
is_clustered,BOOLEAN
collation,VARCHAR
server_name,VARCHAR
instance_name,VARCHAR
reserved,DECIMAL
data_info,DECIMAL
unused,DECIMAL
index_size,DECIMAL
collation_name,VARCHAR
database_name,VARCHAR
database_size,VARCHAR
unallocated_space,VARCHAR
fragmentation,DECIMAL
min_memory,DECIMAL
max_memory,DECIMAL
awe_enabled,INT
test0812,TEXT
dts_id,VARCHAR
object_name,VARCHAR
creator_name,VARCHAR
creator_computer_name,VARCHAR
creation_date,VARCHAR
selected_databases,VARCHAR
creation_name1,VARCHAR
description1,VARCHAR
creation_name_,VARCHAR
description_,VARCHAR
object_name_,VARCHAR
keep,INT
discard_key,BOOLEAN
expand,BOOLEAN
no_attr,BOOLEAN
enabled,INT
last_run_date,VARCHAR
last_run_time,VARCHAR
next_run_date,VARCHAR
next_run_time,VARCHAR
last_run_outcome,INT
job_id,VARCHAR
originating_server,VARCHAR
description,VARCHAR
test-0033,INT
runner_track_content,TEXT
runner_track_error_count,INT
ipg_agt_id,INT
ipg_agt_alias,VARCHAR
ipg_agt_name,VARCHAR
ipg_agt_online_time,DATE
ipg_agt_online_time_utc,DATE
ipg_agt_active_time,DATE
ipg_agt_active_time_utc,DATE
ipg_agt_os_str,VARCHAR
ipg_agt_ip_mac_str,VARCHAR
ipg_agt_ip_mac_real_str,VARCHAR
ipg_agt_last_user_id,INT
ipg_agt_grp_id,INT
ipg_agt_grp_name,VARCHAR
ipg_agt_grp_pid,INT
ipg_agt_status,VARCHAR
ipg_ast_type,INT
ipg_ast_atb_type_ori,INT
ipg_ast_cls_name,VARCHAR
ipg_ast_cls_id,INT
ipg_ast_atb_name,VARCHAR
ipg_ast_atb_type,INT
ipg_ast_summary,INT
ipg_ast_default,INT
ipg_ast_detail_id,INT
ipg_ast_agt_id,INT
ipg_ast_ordinal,INT
ipg_ast_atb_id,INT
ipg_ast_str,VARCHAR
ipg_ast_int,INT
ipg_ast_time,DATE
ipg_ast_time_utc,DATE
ipg_ast_float,DECIMAL
ipg_uk_name,VARCHAR
ipg_uk_id,VARCHAR
ipg_uk_title,VARCHAR
ipg_uk_os_info,VARCHAR
ipg_uk_serverity,INT
ipg_uk_auto_download,INT
ipg_uk_lang_id,INT
ipg_uk_desc,TEXT
ipg_uk_support_url,VARCHAR
ipg_ua_agt_id,INT
ipg_ua_uk_id,VARCHAR
ipg_ua_status,INT
ipg_ua_err_code,INT
ipg_ua_time,DATE
ipg_ua_time_utc,DATE
ipg_ua_install,INT
ipg_ua_download,INT
ipg_ua_send,INT
ipg_vss_agt_id,INT
ipg_vss_id,INT
ipg_vss_exist,INT
ipg_vss_level,INT
ipg_vss_msg_id,INT
ipg_vss_msg_txt,TEXT
ipg_net_agt_id,INT
ipg_net_ses_id,INT
ipg_net_usr_id,INT
ipg_net_date,INT
ipg_net_week,INT
ipg_net_hour,INT
ipg_net_direction,INT
ipg_net_ip,INT
ipg_net_local,INT
ipg_net_port,INT
ipg_net_protocol,INT
ipg_net_send,DECIMAL
ipg_net_recv,DECIMAL
ipg_net_ip_str,VARCHAR
ipg_net_date_str,VARCHAR
ipg_agt_install_time,DATE
ipg_agt_install_time_utc,DATE
ipg_agt_ip,INT
ipg_agt_ip_str,VARCHAR
ipg_agt_last_user_name,VARCHAR
ipg_agt_uninstall_time,DATE
ipg_agt_uninstall_time_utc,DATE
ipg_agt_boot_time,DATE
ipg_agt_boot_time_utc,DATE
ipg_agt_cpu,VARCHAR
ipg_agt_memory,VARCHAR
ipg_agt_logical_disk,VARCHAR
ipg_agt_network_card,VARCHAR
ipg_uk_ub_id,INT
ipg_uk_ub_name,VARCHAR
ipg_uk_uf_file_size,INT
ipg_uk_uf_status,INT
ipg_uk_uninstall_count,INT
ipg_uk_install_count,INT
ipg_uk_agt_count,INT
ipg_uk_release_time,VARCHAR
ipg_ua_last_scan,DATE
ipg_ua_last_scan_utc,DATE
ipg_ua_auto_install,INT
ipg_ua_uninstall_count,INT
ipg_ua_install_count,INT
ipg_ua_count,INT
ipg_ua_uk_name,VARCHAR
ipg_ua_uk_severity,INT
ipg_ua_uk_ub_id,INT
ipg_ua_uk_ub_name,VARCHAR
ipg_ua_uk_release_time,VARCHAR
ipg_ua_us_cmd,INT
ipg_ua_us_install,INT
ipg_ua_install_set_status,INT
ipg_vss_exist_count,INT
ipg_vss_not_exist_count,INT
ipg_sd_id,INT
ipg_sd_agt_id,INT
ipg_sd_sl_id,VARCHAR
ipg_sd_ast_cls_id,INT
ipg_sd_version,VARCHAR
ipg_sd_status,INT
ipg_sd_delete,INT
ipg_sd_size,INT
ipg_sd_install_time,DATE
ipg_sd_install_time_utc,DATE
ipg_sd_path,VARCHAR
ipg_sd_product_id,VARCHAR
ipg_sd_product_key,VARCHAR
ipg_sd_copyright,VARCHAR
ipg_sd_genuine,INT
ipg_sd_sl_name,VARCHAR
ipg_sd_sl_company,VARCHAR
ipg_sd_install_count,INT
ipg_sd_task_count,INT
ipg_sd_version_count,INT
eai_name,VARCHAR
eai_ap_id,VARCHAR
eai_uid,VARCHAR
eai_ip_address,VARCHAR
eai_version,VARCHAR
eai_wsdl,VARCHAR
eai_prod_state,INT
eai_rest_url,VARCHAR
eai_prod_stae_resful,INT
eai_build_time,DATE
eai_last_update_time,DATE
eai_has_warning,INT
eai_last_update_time_str,VARCHAR
eai_build_time_str,VARCHAR
snmpIpValue,VARCHAR
snmpPortValue,VARCHAR
snmpVersionValue,VARCHAR
snmpCommunityValue,VARCHAR
snmpUserNameValue,VARCHAR
snmpAuthPasswordValue,VARCHAR
com_cron,VARCHAR
com_exec_onstart,BOOLEAN
com_is_convert_to_true_type,BOOLEAN
com_execute_content,TEXT
snmpTypeValue,VARCHAR
snmpIdValue,VARCHAR
windows_update_title,VARCHAR
windows_update_description,TEXT
windows_update_last_deployment_change_time,VARCHAR
windows_update_max_download_size,DECIMAL
offset,ENUM
time_layout_before,VARCHAR
time_layout_after,VARCHAR
compare_keys,VARCHAR
md5_compare_key,BOOLEAN
keep_only_extreme_result,BOOLEAN
com_result_field,VARCHAR
mysql_version,VARCHAR
mysql_datasource,VARCHAR
mysql_database,VARCHAR
mysql_exec_onstart,BOOLEAN
mysql_limit_batch,INT
mysql_sql,TEXT
mysql_cron,VARCHAR
mysql_fullQuery,BOOLEAN
mysql_calc_total,BOOLEAN
sql_schema,VARCHAR
Variable_name,VARCHAR
Value,VARCHAR
ipAddress,VARCHAR
placementPoint,TEXT
remark,TEXT
aiopskitVersion,VARCHAR
escliVersion,VARCHAR
lastCheckInTime,DATE
isDeleted,INT
seqId,VARCHAR
warningItemCode,VARCHAR
warningTime,DATETIME
warningLevel,VARCHAR
deviceType,VARCHAR
win_registry_cron,VARCHAR
win_registry_exec_onstart,BOOLEAN
win_registry_read_content,VARCHAR
random_lag_duration,VARCHAR
discard_mode,ENUM
tile_level,INT
add_last_parent_name,BOOLEAN
last_parent_key_name,VARCHAR
tile_key,VARCHAR
key_case_type,ENUM
special_key_map,VARCHAR
add_key_prefix,VARCHAR
add_key_suffix,VARCHAR
sft_key,VARCHAR
sft_display_name,VARCHAR
sft_estimated_size,DECIMAL
sft_install_date,VARCHAR
sft_display_icon,VARCHAR
sft_version,VARCHAR
sft_publisher,VARCHAR
sft_install_location,VARCHAR
sft_system_comopnent,BOOLEAN
sft_windows_installer,BOOLEAN
sft_install_source,VARCHAR
sft_uninstall_string,VARCHAR
sft_display_version,VARCHAR
allow_key_not_exist,BOOLEAN
serviceCode,VARCHAR
sp_cmsmd_md001,VARCHAR
sp_cmsmd_md002,VARCHAR
sp_cmsmd_md003,VARCHAR
sp_cmsmd_md004,DECIMAL
sp_cmsmd_md005,DECIMAL
sp_cmsmd_md006,DECIMAL
sp_cmsmd_md007,DECIMAL
sp_cmsmd_md008,VARCHAR
sp_cmsmd_md009,DECIMAL
sp_cmsmd_md010,DECIMAL
sp_cmsmd_md011,VARCHAR
sp_cmsmd_md012,VARCHAR
sp_cmsmd_md013,DECIMAL
sp_cmsmd_md014,DECIMAL
sp_cmsmd_md015,VARCHAR
sp_cmsmd_md016,VARCHAR
sp_cmsmd_md017,VARCHAR
sp_cmsmd_md018,VARCHAR
sp_cmsmd_md019,VARCHAR
sp_cmsmd_md020,VARCHAR
sp_cmsmd_md021,DECIMAL
sp_cmsmd_md022,DECIMAL
sp_cmsmd_md023,VARCHAR
sp_cmsmd_md024,VARCHAR
sp_cmsmd_md025,VARCHAR
sp_cmsmd_md026,VARCHAR
sp_cmsmd_md027,VARCHAR
sp_cmsmd_md028,VARCHAR
sp_cmsmd_md029,VARCHAR
sp_cmsmd_md030,VARCHAR
sp_cmsmd_md031,VARCHAR
sp_cmsmd_md032,VARCHAR
sp_cmsmd_md033,VARCHAR
sp_cmsmd_md034,VARCHAR
sp_cmsmd_md035,VARCHAR
sp_cmsmd_md036,VARCHAR
sp_cmsmd_md037,VARCHAR
sp_cmsmd_md038,VARCHAR
sp_cmsmd_md039,VARCHAR
sp_cmsmd_md040,VARCHAR
sp_cmsmd_md041,VARCHAR
sp_cmsmd_md042,DECIMAL
sp_cmsmd_md043,DECIMAL
sp_cmsmd_md044,DECIMAL
sp_cmsmd_md045,DECIMAL
sp_cmsmd_md046,DECIMAL
sp_cmsmd_md047,DECIMAL
sp_cmsmd_md048,DECIMAL
sp_cmsmd_md049,DECIMAL
sp_cmsmd_md050,VARCHAR
sp_cmsmd_md051,VARCHAR
sp_cmsmh_mh001,VARCHAR
sp_cmsmh_mh002,VARCHAR
sp_cmsmh_mh003,VARCHAR
sp_cmsmh_mh004,VARCHAR
sp_cmsmh_mh005,VARCHAR
sp_cmsmh_mh006,VARCHAR
sp_cmsmh_mh007,VARCHAR
sp_cmsmh_mh008,DECIMAL
sp_cmsmh_mh009,DECIMAL
sp_cmsmh_mh010,DECIMAL
sp_cmsmh_mh011,DECIMAL
sp_cmsmh_mh012,VARCHAR
sp_cmsmh_mh013,VARCHAR
sp_cmsmh_mh014,VARCHAR
sp_cmsmk_mk001,VARCHAR
sp_cmsmk_mk002,VARCHAR
sp_cmsmk_mk003,VARCHAR
sp_cmsmk_mk004,VARCHAR
sp_cmsmk_mk005,VARCHAR
sp_cmsmk_mk006,DECIMAL
sp_cmsmk_mk007,DECIMAL
sp_cmsmk_mk008,VARCHAR
sp_cmsmk_mk009,VARCHAR
sp_cmsmk_mk010,VARCHAR
sp_cmsmv_mv001,VARCHAR
sp_cmsmv_mv002,VARCHAR
sp_cmsmv_mv003,VARCHAR
sp_cmsmv_mv004,VARCHAR
sp_cmsmv_mv005,VARCHAR
sp_cmsmv_mv006,VARCHAR
sp_cmsmv_mv007,VARCHAR
sp_cmsmv_mv008,VARCHAR
sp_cmsmv_mv009,VARCHAR
sp_cmsmv_mv010,VARCHAR
sp_cmsmv_mv011,VARCHAR
sp_cmsmv_mv012,VARCHAR
sp_cmsmv_mv013,VARCHAR
sp_cmsmv_mv014,VARCHAR
sp_cmsmv_mv015,VARCHAR
sp_cmsmv_mv016,VARCHAR
sp_cmsmv_mv017,VARCHAR
sp_cmsmv_mv018,VARCHAR
sp_cmsmv_mv019,VARCHAR
sp_cmsmv_mv020,VARCHAR
sp_cmsmv_mv021,VARCHAR
sp_cmsmv_mv022,VARCHAR
sp_cmsmv_mv023,VARCHAR
sp_cmsmv_mv024,VARCHAR
sp_cmsmv_mv025,VARCHAR
sp_cmsmv_mv026,VARCHAR
sp_cmsmv_mv027,VARCHAR
sp_cmsmv_mv028,VARCHAR
sp_cmsmv_mv029,VARCHAR
sp_cmsmv_mv030,VARCHAR
sp_cmsmv_mv031,DECIMAL
sp_cmsmv_mv032,VARCHAR
sp_cmsmv_mv033,DECIMAL
sp_cmsmv_mv034,VARCHAR
sp_cmsmv_mv035,VARCHAR
sp_cmsmv_mv036,VARCHAR
sp_cmsmv_mv037,DECIMAL
sp_cmsmv_mv038,VARCHAR
sp_cmsmv_mv039,DECIMAL
sp_cmsmv_mv040,DECIMAL
sp_cmsmv_mv041,VARCHAR
sp_cmsmv_mv042,VARCHAR
sp_cmsmv_mv043,DECIMAL
sp_cmsmv_mv044,VARCHAR
sp_cmsmv_mv045,DECIMAL
sp_cmsmv_mv046,VARCHAR
sp_cmsmv_mv047,VARCHAR
sp_cmsmv_mv048,VARCHAR
sp_cmsmv_mv049,VARCHAR
sp_cmsmv_mv050,VARCHAR
sp_cmsmv_mv051,VARCHAR
sp_cmsmv_mv052,VARCHAR
sp_cmsmv_mv053,VARCHAR
sp_cmsmv_mv054,VARCHAR
sp_cmsmv_mv055,DECIMAL
sp_cmsmv_mv056,DECIMAL
sp_cmsmv_mv057,DECIMAL
sp_cmsmv_mv058,VARCHAR
sp_cmsmv_mv059,VARCHAR
sp_cmsmv_mv060,VARCHAR
sp_cmsmv_mv061,VARCHAR
sp_cmsmv_mv062,VARCHAR
sp_cmsmv_mv063,VARCHAR
sp_cmsmv_mv064,VARCHAR
sp_cmsmv_mv065,VARCHAR
sp_cmsmv_mv066,VARCHAR
sp_cmsmv_mv067,VARCHAR
sp_cmsmv_mv068,VARCHAR
sp_cmsmv_mv069,VARCHAR
sp_cmsmv_mv070,VARCHAR
sp_cmsmv_mv071,DECIMAL
sp_cmsmv_mv072,VARCHAR
sp_cmsmv_mv073,VARCHAR
sp_cmsmv_mv074,VARCHAR
sp_cmsmv_mv075,VARCHAR
sp_cmsmv_mv076,DECIMAL
sp_cmsmv_mv077,VARCHAR
sp_cmsmv_mv078,DECIMAL
sp_cmsmv_mv079,DECIMAL
sp_cmsmv_mv080,DECIMAL
sp_cmsmv_mv081,VARCHAR
sp_cmsmv_mv082,DECIMAL
sp_cmsmv_mv083,DECIMAL
sp_cmsmv_mv084,VARCHAR
sp_cmsmv_mv085,VARCHAR
sp_cmsmv_mv086,VARCHAR
sp_cmsmv_mv087,VARCHAR
sp_cmsmv_mv088,VARCHAR
sp_cmsmv_mv089,VARCHAR
sp_cmsmv_mv090,VARCHAR
sp_cmsmv_mv091,VARCHAR
sp_cmsmv_mv092,VARCHAR
sp_cmsmv_mv093,VARCHAR
sp_cmsmv_mv094,VARCHAR
sp_cmsmv_mv095,VARCHAR
sp_cmsmv_mv096,VARCHAR
sp_cmsmv_mv097,VARCHAR
sp_cmsmv_mv098,VARCHAR
sp_cmsmv_mv099,VARCHAR
sp_cmsmv_mv100,VARCHAR
sp_cmsmv_mv101,VARCHAR
sp_cmsmv_mv102,VARCHAR
sp_cmsmv_mv103,VARCHAR
sp_cmsmv_mv104,DECIMAL
sp_cmsmv_mv105,VARCHAR
sp_cmsmv_mv106,VARCHAR
sp_cmsmv_mv107,VARCHAR
sp_cmsmv_mv108,VARCHAR
sp_cmsmv_mv109,VARCHAR
sp_cmsmv_mv110,VARCHAR
sp_cmsmv_mv111,VARCHAR
sp_cmsmv_mv112,VARCHAR
sp_cmsmv_mv113,VARCHAR
sp_cmsmv_mv114,VARCHAR
sp_cmsmv_mv115,VARCHAR
sp_cmsmv_mv116,VARCHAR
sp_cmsmv_mv117,VARCHAR
sp_cmsmv_mv118,VARCHAR
sp_cmsmv_mv119,VARCHAR
sp_cmsmv_mv120,DECIMAL
sp_cmsmv_mv121,VARCHAR
sp_cmsmv_mv122,VARCHAR
sp_cmsmv_mv123,VARCHAR
sp_cmsmv_mv124,VARCHAR
sp_cmsmv_mv125,VARCHAR
sp_cmsmv_mv126,DECIMAL
sp_cmsmv_mv127,DECIMAL
sp_cmsmv_mv128,DECIMAL
sp_cmsmv_mv129,DECIMAL
sp_cmsmv_mv130,VARCHAR
sp_invmb_mb001,VARCHAR
sp_invmb_mb002,VARCHAR
sp_invmb_mb003,VARCHAR
sp_invmb_mb004,VARCHAR
sp_invmb_mb005,VARCHAR
sp_invmb_mb006,VARCHAR
sp_invmb_mb007,VARCHAR
sp_invmb_mb008,VARCHAR
sp_invmb_mb009,VARCHAR
sp_invmb_mb010,VARCHAR
sp_invmb_mb011,VARCHAR
sp_invmb_mb012,VARCHAR
sp_invmb_mb013,VARCHAR
sp_invmb_mb014,DECIMAL
sp_invmb_mb015,VARCHAR
sp_invmb_mb016,VARCHAR
sp_invmb_mb017,VARCHAR
sp_invmb_mb018,VARCHAR
sp_invmb_mb019,VARCHAR
sp_invmb_mb020,VARCHAR
sp_invmb_mb021,VARCHAR
sp_invmb_mb022,VARCHAR
sp_invmb_mb023,DECIMAL
sp_invmb_mb024,DECIMAL
sp_invmb_mb025,VARCHAR
sp_invmb_mb026,VARCHAR
sp_invmb_mb027,VARCHAR
sp_invmb_mb028,VARCHAR
sp_invmb_mb029,VARCHAR
sp_invmb_mb030,VARCHAR
sp_invmb_mb031,VARCHAR
sp_invmb_mb032,VARCHAR
sp_invmb_mb033,VARCHAR
sp_invmb_mb034,VARCHAR
sp_invmb_mb035,VARCHAR
sp_invmb_mb036,DECIMAL
sp_invmb_mb037,DECIMAL
sp_invmb_mb038,DECIMAL
sp_invmb_mb039,DECIMAL
sp_invmb_mb040,DECIMAL
sp_invmb_mb041,DECIMAL
sp_invmb_mb042,VARCHAR
sp_invmb_mb043,VARCHAR
sp_invmb_mb044,VARCHAR
sp_invmb_mb045,DECIMAL
sp_invmb_mb046,DECIMAL
sp_invmb_mb047,DECIMAL
sp_invmb_mb048,VARCHAR
sp_invmb_mb049,DECIMAL
sp_invmb_mb050,DECIMAL
sp_invmb_mb051,DECIMAL
sp_invmb_mb052,VARCHAR
sp_invmb_mb053,DECIMAL
sp_invmb_mb054,DECIMAL
sp_invmb_mb055,DECIMAL
sp_invmb_mb056,DECIMAL
sp_invmb_mb057,DECIMAL
sp_invmb_mb058,DECIMAL
sp_invmb_mb059,DECIMAL
sp_invmb_mb060,DECIMAL
sp_invmb_mb061,DECIMAL
sp_invmb_mb062,DECIMAL
sp_invmb_mb063,DECIMAL
sp_invmb_mb064,DECIMAL
sp_invmb_mb065,DECIMAL
sp_invmb_mb066,VARCHAR
sp_invmb_mb067,VARCHAR
sp_invmb_mb068,VARCHAR
sp_invmb_mb069,DECIMAL
sp_invmb_mb070,DECIMAL
sp_invmb_mb071,DECIMAL
sp_invmb_mb072,VARCHAR
sp_invmb_mb073,DECIMAL
sp_invmb_mb074,DECIMAL
sp_invmb_mb075,DECIMAL
sp_invmb_mb076,DECIMAL
sp_invmb_mb077,VARCHAR
sp_invmb_mb078,DECIMAL
sp_invmb_mb079,DECIMAL
sp_invmb_mb080,VARCHAR
sp_invmb_mb081,VARCHAR
sp_invmb_mb082,DECIMAL
sp_invmb_mb083,VARCHAR
sp_invmb_mb084,DECIMAL
sp_invmb_mb085,VARCHAR
sp_invmb_mb086,DECIMAL
sp_invmb_mb087,VARCHAR
sp_invmb_mb088,DECIMAL
sp_invmb_mb089,DECIMAL
sp_invmb_mb090,VARCHAR
sp_invmb_mb091,VARCHAR
sp_invmb_mb092,VARCHAR
sp_invmb_mb093,DECIMAL
sp_invmb_mb094,DECIMAL
sp_invmb_mb095,DECIMAL
sp_invmb_mb096,DECIMAL
sp_invmb_mb097,DECIMAL
sp_invmb_mb098,VARCHAR
sp_invmb_mb099,DECIMAL
sp_invmb_mb100,VARCHAR
sp_invmb_mb101,VARCHAR
sp_invmb_mb102,VARCHAR
sp_invmb_mb103,VARCHAR
sp_invmb_mb104,VARCHAR
sp_invmb_mb105,VARCHAR
sp_invmb_mb106,VARCHAR
sp_invmb_mb107,VARCHAR
sp_invmb_mb108,VARCHAR
sp_invmb_mb109,VARCHAR
sp_invmb_mb110,VARCHAR
sp_invmb_mb111,DECIMAL
sp_invmb_mb112,VARCHAR
sp_invmb_mb113,VARCHAR
sp_invmb_mb114,VARCHAR
sp_invmb_mb115,VARCHAR
sp_invmb_mb116,VARCHAR
sp_invmb_mb117,VARCHAR
sp_invmb_mb118,VARCHAR
sp_invmb_mb119,DECIMAL
sp_invmb_mb120,DECIMAL
sp_invmb_mb121,VARCHAR
sp_invmb_mb122,VARCHAR
sp_invmb_mb123,VARCHAR
sp_invmb_mb124,VARCHAR
sp_invmb_mb125,VARCHAR
sp_invmb_mb126,VARCHAR
sp_invmb_mb127,VARCHAR
sp_invmb_mb128,VARCHAR
sp_invmb_mb129,VARCHAR
sp_invmb_mb130,VARCHAR
sp_invmb_mb131,VARCHAR
sp_invmb_mb132,VARCHAR
sp_invmb_mb133,VARCHAR
sp_invmb_mb134,VARCHAR
sp_invmb_mb135,VARCHAR
sp_invmb_mb136,VARCHAR
sp_invmb_mb137,VARCHAR
sp_invmb_mb138,VARCHAR
sp_invmb_mb139,VARCHAR
sp_invmb_mb140,VARCHAR
sp_invmb_mb141,VARCHAR
sp_invmb_mb142,VARCHAR
sp_invmb_mb143,VARCHAR
sp_invmb_mb144,VARCHAR
sp_invmb_mb145,VARCHAR
sp_invmb_mb146,VARCHAR
sp_invmb_mb147,VARCHAR
sp_invmb_mb148,VARCHAR
sp_invmb_mb149,VARCHAR
sp_invmb_mb150,VARCHAR
sp_invmb_mb151,VARCHAR
sp_invmb_mb152,VARCHAR
sp_invmb_mb153,VARCHAR
sp_invmb_mb154,VARCHAR
sp_invmb_mb155,VARCHAR
sp_invmb_mb156,VARCHAR
sp_invmb_mb157,VARCHAR
sp_invmb_mb158,VARCHAR
sp_invmb_mb159,DECIMAL
sp_invmb_mb160,DECIMAL
sp_invmb_mb161,DECIMAL
sp_invmb_mb162,VARCHAR
sp_invmb_mb163,VARCHAR
sp_invmb_mb164,VARCHAR
sp_invmb_mb165,VARCHAR
sp_invmb_mb166,DECIMAL
sp_invmb_mb167,DECIMAL
sp_invmb_mb168,VARCHAR
sp_invmb_mb169,VARCHAR
sp_invmb_mb170,VARCHAR
sp_invmb_mb171,DECIMAL
sp_invmb_mb172,DECIMAL
sp_invmb_mb173,DECIMAL
sp_invmb_mb174,VARCHAR
sp_invmb_mb175,VARCHAR
sp_invmb_mb176,VARCHAR
sp_invmb_mb177,VARCHAR
sp_invmb_mb178,VARCHAR
sp_invmb_mb179,VARCHAR
sp_invmb_mb180,DECIMAL
sp_invmb_mb181,DECIMAL
sp_invmb_mb182,DECIMAL
sp_invmb_mb183,VARCHAR
sp_invmb_mb184,DECIMAL
sp_invmb_mb185,VARCHAR
sp_invmb_mb186,VARCHAR
sp_invmb_mb187,VARCHAR
sp_invmb_mb188,VARCHAR
sp_invmb_mb189,VARCHAR
sp_invmb_mb190,VARCHAR
sp_invmb_mb191,DECIMAL
sp_invmb_mb192,DECIMAL
sp_invmb_mb193,DECIMAL
sp_invmb_mb194,VARCHAR
sp_invmb_mb195,VARCHAR
sp_invmb_mb196,VARCHAR
sp_invmb_mb197,VARCHAR
sp_invmb_mb198,VARCHAR
sp_invmb_mb199,VARCHAR
sp_invmb_mb500,VARCHAR
sp_invmb_mb501,DECIMAL
sp_invmb_mb502,VARCHAR
sp_invmb_mb503,VARCHAR
sp_invmb_mb504,VARCHAR
sp_invmb_mb505,VARCHAR
sp_invmb_mb506,VARCHAR
sp_invmb_mb507,DECIMAL
sp_invmb_mb508,DECIMAL
sp_invmb_mb509,DECIMAL
sp_invmb_mb510,VARCHAR
sp_invmb_mb511,VARCHAR
sp_invmb_mb512,VARCHAR
sp_invmb_mb513,VARCHAR
sp_invmb_mb514,VARCHAR
sp_invmb_mb515,VARCHAR
sp_invmb_mb516,VARCHAR
sp_invmb_mb517,VARCHAR
sp_invmb_mb518,VARCHAR
sp_invmb_mb519,VARCHAR
sp_invmb_mb520,VARCHAR
sp_invmb_mb521,VARCHAR
sp_invmb_mb522,VARCHAR
sp_invmb_mb523,VARCHAR
sp_invmb_mb524,VARCHAR
sp_invmb_mb525,VARCHAR
sp_invmb_mb526,VARCHAR
sp_invmb_mb527,VARCHAR
sp_invmb_mb528,VARCHAR
sp_invmb_mb545,DECIMAL
sp_invmb_mb550,VARCHAR
sp_invmb_mb551,VARCHAR
sp_invmb_mb552,VARCHAR
sp_invmb_mb553,VARCHAR
sp_invmb_mb554,VARCHAR
sp_mocta_ta001,VARCHAR
sp_mocta_ta002,VARCHAR
sp_mocta_ta003,VARCHAR
sp_mocta_ta004,VARCHAR
sp_mocta_ta005,VARCHAR
sp_mocta_ta006,VARCHAR
sp_mocta_ta007,VARCHAR
sp_mocta_ta008,VARCHAR
sp_mocta_ta009,VARCHAR
sp_mocta_ta010,VARCHAR
sp_mocta_ta011,VARCHAR
sp_mocta_ta012,VARCHAR
sp_mocta_ta013,VARCHAR
sp_mocta_ta014,VARCHAR
sp_mocta_ta015,DECIMAL
sp_mocta_ta016,DECIMAL
sp_mocta_ta017,DECIMAL
sp_mocta_ta018,DECIMAL
sp_mocta_ta019,VARCHAR
sp_mocta_ta020,VARCHAR
sp_mocta_ta021,VARCHAR
sp_mocta_ta022,DECIMAL
sp_mocta_ta023,VARCHAR
sp_mocta_ta024,VARCHAR
sp_mocta_ta025,VARCHAR
sp_mocta_ta026,VARCHAR
sp_mocta_ta027,VARCHAR
sp_mocta_ta028,VARCHAR
sp_mocta_ta029,VARCHAR
sp_mocta_ta030,VARCHAR
sp_mocta_ta031,DECIMAL
sp_mocta_ta032,VARCHAR
sp_mocta_ta033,VARCHAR
sp_mocta_ta034,VARCHAR
sp_mocta_ta035,VARCHAR
sp_mocta_ta036,VARCHAR
sp_mocta_ta037,VARCHAR
sp_mocta_ta038,VARCHAR
sp_mocta_ta039,VARCHAR
sp_mocta_ta040,VARCHAR
sp_mocta_ta041,VARCHAR
sp_mocta_ta042,VARCHAR
sp_mocta_ta043,DECIMAL
sp_mocta_ta044,VARCHAR
sp_mocta_ta045,DECIMAL
sp_mocta_ta046,DECIMAL
sp_mocta_ta047,DECIMAL
sp_mocta_ta048,VARCHAR
sp_mocta_ta049,VARCHAR
sp_mocta_ta050,DECIMAL
sp_mocta_ta051,VARCHAR
sp_mocta_ta052,VARCHAR
sp_mocta_ta053,DECIMAL
sp_mocta_ta054,VARCHAR
sp_mocta_ta055,VARCHAR
sp_mocta_ta056,VARCHAR
sp_mocta_ta057,DECIMAL
sp_mocta_ta058,VARCHAR
sp_mocta_ta059,VARCHAR
sp_mocta_ta060,VARCHAR
sp_mocta_ta061,VARCHAR
sp_mocta_ta062,VARCHAR
sp_mocta_ta063,VARCHAR
sp_mocta_ta064,DECIMAL
sp_mocta_ta065,DECIMAL
sp_mocta_ta066,VARCHAR
sp_mocta_ta067,VARCHAR
sp_mocta_ta068,VARCHAR
sp_mocta_ta069,DECIMAL
sp_mocta_ta070,VARCHAR
sp_mocta_ta071,VARCHAR
sp_mocta_ta072,VARCHAR
sp_mocta_ta073,DECIMAL
sp_mocta_ta074,DECIMAL
sp_mocta_ta075,VARCHAR
sp_mocta_ta076,DECIMAL
sp_mocta_ta077,DECIMAL
sp_mocta_ta078,VARCHAR
sp_mocta_ta079,VARCHAR
sp_mocta_ta080,VARCHAR
sp_mocta_ta081,VARCHAR
sp_mocta_ta082,VARCHAR
sp_mocta_ta083,VARCHAR
sp_mocta_ta084,VARCHAR
sp_mocta_ta085,VARCHAR
sp_mocta_ta086,VARCHAR
sp_mocta_ta087,VARCHAR
sp_mocta_ta088,VARCHAR
sp_mocta_ta089,VARCHAR
sp_mocta_ta090,VARCHAR
sp_mocta_ta091,VARCHAR
sp_mocta_ta092,VARCHAR
sp_mocta_ta093,VARCHAR
sp_mocta_ta094,DECIMAL
sp_mocta_ta095,VARCHAR
sp_mocta_ta096,VARCHAR
sp_mocta_ta097,VARCHAR
sp_mocta_ta500,VARCHAR
sp_mocta_ta501,VARCHAR
sp_mocta_ta502,VARCHAR
sp_mocta_ta503,DECIMAL
sp_mocta_ta504,DECIMAL
sp_mocta_ta505,VARCHAR
sp_mocta_ta506,VARCHAR
sp_mocta_ta507,VARCHAR
sp_mocta_ta508,VARCHAR
sp_mocta_ta509,VARCHAR
sp_mocta_ta510,VARCHAR
sp_mocta_ta511,VARCHAR
sp_mocta_ta512,VARCHAR
sp_mocta_ta513,VARCHAR
sp_mocta_ta514,DECIMAL
sp_mocta_ta515,DECIMAL
sp_mocta_ta516,DECIMAL
sp_mocta_ta520,VARCHAR
sp_mocta_ta521,VARCHAR
sp_mocta_ta522,VARCHAR
sp_mocta_ta523,VARCHAR
sp_mocta_ta524,VARCHAR
sp_mocta_ta525,VARCHAR
sp_mocta_ta526,VARCHAR
sp_mocta_ta527,VARCHAR
sp_mocta_ta528,VARCHAR
sp_mocta_ta530,VARCHAR
sp_mocta_ta531,VARCHAR
sp_mocta_ta532,VARCHAR
sp_mocta_ta533,VARCHAR
sp_mocta_ta534,VARCHAR
sp_mocta_ta535,VARCHAR
sp_mocta_ta550,VARCHAR
sp_mocta_ta551,VARCHAR
sp_mocta_ta552,DECIMAL
sp_mocta_ta553,VARCHAR
sp_mocta_ta554,VARCHAR
sp_mocta_ta555,VARCHAR
sp_mocta_ta556,VARCHAR
sp_moctf_tf001,VARCHAR
sp_moctf_tf002,VARCHAR
sp_moctf_tf003,VARCHAR
sp_moctf_tf004,VARCHAR
sp_moctf_tf005,VARCHAR
sp_moctf_tf006,VARCHAR
sp_moctf_tf007,VARCHAR
sp_moctf_tf008,DECIMAL
sp_moctf_tf009,VARCHAR
sp_moctf_tf010,VARCHAR
sp_moctf_tf011,VARCHAR
sp_moctf_tf012,VARCHAR
sp_moctf_tf013,VARCHAR
sp_moctf_tf014,VARCHAR
sp_moctf_tf015,VARCHAR
sp_moctf_tf016,DECIMAL
sp_moctf_tf017,DECIMAL
sp_moctf_tf018,DECIMAL
sp_moctf_tf019,VARCHAR
sp_moctf_tf020,VARCHAR
sp_moctf_tf021,VARCHAR
sp_moctf_tf022,VARCHAR
sp_moctf_tf023,VARCHAR
sp_moctf_tf024,VARCHAR
sp_moctf_tf025,VARCHAR
sp_moctf_tf026,VARCHAR
sp_moctg_tg001,VARCHAR
sp_moctg_tg002,VARCHAR
sp_moctg_tg003,VARCHAR
sp_moctg_tg004,VARCHAR
sp_moctg_tg005,VARCHAR
sp_moctg_tg006,VARCHAR
sp_moctg_tg007,VARCHAR
sp_moctg_tg008,VARCHAR
sp_moctg_tg009,DECIMAL
sp_moctg_tg010,VARCHAR
sp_moctg_tg011,DECIMAL
sp_moctg_tg012,DECIMAL
sp_moctg_tg013,DECIMAL
sp_moctg_tg014,VARCHAR
sp_moctg_tg015,VARCHAR
sp_moctg_tg016,VARCHAR
sp_moctg_tg017,VARCHAR
sp_moctg_tg018,VARCHAR
sp_moctg_tg019,VARCHAR
sp_moctg_tg020,VARCHAR
sp_moctg_tg021,VARCHAR
sp_moctg_tg022,VARCHAR
sp_moctg_tg023,DECIMAL
sp_moctg_tg024,VARCHAR
sp_moctg_tg025,DECIMAL
sp_moctg_tg026,DECIMAL
sp_moctg_tg027,DECIMAL
sp_moctg_tg028,DECIMAL
sp_moctg_tg029,VARCHAR
sp_moctg_tg030,VARCHAR
sp_moctg_tg031,VARCHAR
sp_moctg_tg032,DECIMAL
sp_moctg_tg033,VARCHAR
sp_moctg_tg034,VARCHAR
sp_moctg_tg035,DECIMAL
sp_moctg_tg036,DECIMAL
sp_moctg_tg037,VARCHAR
sp_moctg_tg038,VARCHAR
sp_moctg_tg039,VARCHAR
sp_moctg_tg040,VARCHAR
sp_moctg_tg041,VARCHAR
sp_moctg_tg042,VARCHAR
sp_moctg_tg043,VARCHAR
sp_moctg_tg044,VARCHAR
sp_moctg_tg500,VARCHAR
sp_moctg_tg501,VARCHAR
sp_moctg_tg502,VARCHAR
sp_moctg_tg503,DECIMAL
sp_moctg_tg504,DECIMAL
sp_moctg_tg505,DECIMAL
sp_moctg_tg506,DECIMAL
sp_moctg_tg507,VARCHAR
sp_moctg_tg550,VARCHAR
sp_moctg_tg553,DECIMAL
sp_moctg_tg554,DECIMAL
sp_moctg_tg555,DECIMAL
sp_moctg_tg556,DECIMAL
sp_moctg_tg560,VARCHAR
sp_moctg_tg561,VARCHAR
sp_moctg_tg562,VARCHAR
sp_moctg_tg563,VARCHAR
sp_moctg_tg564,VARCHAR
sp_moctg_tg565,VARCHAR
sp_moctg_tg567,VARCHAR
sp_mocth_th001,VARCHAR
sp_mocth_th002,VARCHAR
sp_mocth_th003,VARCHAR
sp_mocth_th004,VARCHAR
sp_mocth_th005,VARCHAR
sp_mocth_th006,VARCHAR
sp_mocth_th007,VARCHAR
sp_mocth_th008,DECIMAL
sp_mocth_th009,DECIMAL
sp_mocth_th010,VARCHAR
sp_mocth_th011,VARCHAR
sp_mocth_th012,VARCHAR
sp_mocth_th013,VARCHAR
sp_mocth_th014,VARCHAR
sp_mocth_th015,VARCHAR
sp_mocth_th016,VARCHAR
sp_mocth_th017,VARCHAR
sp_mocth_th018,DECIMAL
sp_mocth_th019,DECIMAL
sp_mocth_th020,DECIMAL
sp_mocth_th021,DECIMAL
sp_mocth_th022,DECIMAL
sp_mocth_th023,VARCHAR
sp_mocth_th024,VARCHAR
sp_mocth_th025,DECIMAL
sp_mocth_th026,VARCHAR
sp_mocth_th027,DECIMAL
sp_mocth_th028,VARCHAR
sp_mocth_th029,VARCHAR
sp_mocth_th030,DECIMAL
sp_mocth_th031,DECIMAL
sp_mocth_th032,DECIMAL
sp_mocth_th033,VARCHAR
sp_mocth_th034,DECIMAL
sp_mocth_th035,VARCHAR
sp_mocth_th036,VARCHAR
sp_mocth_th037,VARCHAR
sp_mocth_th038,DECIMAL
sp_mocth_th039,DECIMAL
sp_mocth_th040,DECIMAL
sp_mocth_th041,VARCHAR
sp_mocth_th042,VARCHAR
sp_mocth_th043,VARCHAR
sp_mocth_th044,VARCHAR
sp_mocth_th045,VARCHAR
sp_mocth_th046,VARCHAR
sp_mocth_th047,DECIMAL
sp_mocth_th048,DECIMAL
sp_mocth_th049,DECIMAL
sp_mocth_th050,VARCHAR
sp_mocth_th051,VARCHAR
sp_mocth_th052,VARCHAR
sp_mocth_th053,VARCHAR
sp_mocth_th054,VARCHAR
sp_mocth_th055,VARCHAR
sp_mocth_th056,VARCHAR
sp_mocth_th057,VARCHAR
sp_mocth_th058,VARCHAR
sp_mocth_th059,VARCHAR
sp_mocth_th060,VARCHAR
sp_mocth_th061,VARCHAR
sp_mocth_th062,VARCHAR
sp_mocth_th063,VARCHAR
sp_mocth_th500,VARCHAR
sp_mocti_ti001,VARCHAR
sp_mocti_ti002,VARCHAR
sp_mocti_ti003,VARCHAR
sp_mocti_ti004,VARCHAR
sp_mocti_ti005,VARCHAR
sp_mocti_ti006,VARCHAR
sp_mocti_ti007,DECIMAL
sp_mocti_ti008,VARCHAR
sp_mocti_ti009,VARCHAR
sp_mocti_ti010,VARCHAR
sp_mocti_ti011,VARCHAR
sp_mocti_ti012,VARCHAR
sp_mocti_ti013,VARCHAR
sp_mocti_ti014,VARCHAR
sp_mocti_ti015,VARCHAR
sp_mocti_ti016,DECIMAL
sp_mocti_ti017,DECIMAL
sp_mocti_ti018,VARCHAR
sp_mocti_ti019,DECIMAL
sp_mocti_ti020,DECIMAL
sp_mocti_ti021,DECIMAL
sp_mocti_ti022,DECIMAL
sp_mocti_ti023,VARCHAR
sp_mocti_ti024,DECIMAL
sp_mocti_ti025,DECIMAL
sp_mocti_ti026,DECIMAL
sp_mocti_ti027,DECIMAL
sp_mocti_ti028,VARCHAR
sp_mocti_ti029,VARCHAR
sp_mocti_ti030,VARCHAR
sp_mocti_ti031,VARCHAR
sp_mocti_ti032,VARCHAR
sp_mocti_ti033,VARCHAR
sp_mocti_ti034,VARCHAR
sp_mocti_ti035,VARCHAR
sp_mocti_ti036,VARCHAR
sp_mocti_ti037,VARCHAR
sp_mocti_ti038,VARCHAR
sp_mocti_ti039,VARCHAR
sp_mocti_ti040,VARCHAR
sp_mocti_ti041,VARCHAR
sp_mocti_ti042,VARCHAR
sp_mocti_ti043,VARCHAR
sp_mocti_ti044,DECIMAL
sp_mocti_ti045,DECIMAL
sp_mocti_ti046,DECIMAL
sp_mocti_ti047,DECIMAL
sp_mocti_ti048,VARCHAR
sp_mocti_ti049,VARCHAR
sp_mocti_ti050,DECIMAL
sp_mocti_ti051,DECIMAL
sp_mocti_ti052,VARCHAR
sp_mocti_ti053,VARCHAR
sp_mocti_ti054,VARCHAR
sp_mocti_ti055,DECIMAL
sp_mocti_ti056,VARCHAR
sp_mocti_ti057,VARCHAR
sp_mocti_ti058,DECIMAL
sp_mocti_ti059,DECIMAL
sp_mocti_ti060,VARCHAR
sp_mocti_ti061,VARCHAR
sp_mocti_ti062,VARCHAR
sp_mocti_ti063,VARCHAR
sp_mocti_ti064,VARCHAR
sp_mocti_ti065,DECIMAL
sp_mocti_ti066,DECIMAL
sp_mocti_ti067,DECIMAL
sp_mocti_ti068,DECIMAL
sp_mocti_ti069,VARCHAR
sp_mocti_ti070,VARCHAR
sp_mocti_ti071,DECIMAL
sp_mocti_ti072,VARCHAR
sp_mocti_ti073,VARCHAR
sp_mocti_ti074,VARCHAR
sp_mocti_ti075,VARCHAR
sp_mocti_ti500,VARCHAR
sp_mocti_ti501,VARCHAR
sp_mocti_ti502,VARCHAR
sp_mocti_ti503,DECIMAL
sp_mocti_ti504,DECIMAL
sp_mocti_ti505,DECIMAL
sp_mocti_ti506,DECIMAL
sp_mocti_ti507,VARCHAR
sp_mocti_ti550,VARCHAR
sp_mocti_ti551,DECIMAL
sp_mocti_ti552,DECIMAL
sp_mocti_ti553,DECIMAL
sp_mocti_ti554,DECIMAL
sp_mocti_ti555,DECIMAL
sp_mocti_ti556,DECIMAL
sp_mocti_ti557,VARCHAR
sp_mocti_ti558,VARCHAR
sp_mocti_ti559,VARCHAR
sp_mocti_ti560,VARCHAR
sp_mocti_ti561,VARCHAR
sp_mocti_ti562,VARCHAR
sp_mocti_ti563,VARCHAR
sp_mocti_ti564,VARCHAR
sp_mocti_ti565,VARCHAR
sp_mocti_ti567,VARCHAR
sp_mocti_ti568,VARCHAR
sp_mocti_ti569,VARCHAR
sp_mocti_ti570,VARCHAR
sp_purma_ma060,VARCHAR
sp_purma_ma061,VARCHAR
sp_purma_ma062,VARCHAR
sp_purma_ma063,VARCHAR
sp_purma_ma064,VARCHAR
sp_purma_ma065,VARCHAR
sp_purma_ma066,VARCHAR
sp_purma_ma067,VARCHAR
sp_purma_ma068,VARCHAR
sp_purma_ma069,VARCHAR
sp_purma_ma070,VARCHAR
sp_purma_ma071,DECIMAL
sp_purma_ma072,DECIMAL
sp_purma_ma073,VARCHAR
sp_purma_ma074,VARCHAR
sp_purma_ma075,VARCHAR
sp_purma_ma076,VARCHAR
sp_purma_ma077,VARCHAR
sp_purma_ma078,VARCHAR
sp_purma_ma079,VARCHAR
sp_purma_ma080,VARCHAR
sp_purma_ma081,VARCHAR
sp_purma_ma082,VARCHAR
sp_purma_ma083,DECIMAL
sp_purma_ma084,VARCHAR
sp_purma_ma085,VARCHAR
sp_purma_ma086,DECIMAL
sp_purma_ma087,DECIMAL
sp_purma_ma088,VARCHAR
sp_purma_ma089,VARCHAR
sp_purma_ma090,VARCHAR
sp_purma_ma091,VARCHAR
sp_purma_ma092,VARCHAR
sp_purma_ma093,DECIMAL
sp_purma_ma094,VARCHAR
sp_purma_ma095,DECIMAL
sp_purma_ma096,VARCHAR
sp_purma_ma097,VARCHAR
sp_purma_ma098,VARCHAR
sp_purma_ma099,VARCHAR
sp_purma_ma100,VARCHAR
sp_purma_ma101,VARCHAR
sp_purma_ma102,VARCHAR
sp_purma_ma103,VARCHAR
sp_purma_ma104,VARCHAR
sp_purma_ma105,VARCHAR
sp_purma_ma106,VARCHAR
sp_purma_ma107,VARCHAR
sp_purma_ma108,VARCHAR
sp_purma_ma109,VARCHAR
sp_purma_ma110,VARCHAR
sp_purma_ma111,VARCHAR
sp_purma_ma112,VARCHAR
sp_purmb_mb001,VARCHAR
sp_purmb_mb002,VARCHAR
sp_purmb_mb003,VARCHAR
sp_purmb_mb004,VARCHAR
sp_purmb_mb005,VARCHAR
sp_purmb_mb007,VARCHAR
sp_purmb_mb008,VARCHAR
sp_purmb_mb009,VARCHAR
sp_purmb_mb010,VARCHAR
sp_purmb_mb011,DECIMAL
sp_purmb_mb012,VARCHAR
sp_purmb_mb013,VARCHAR
sp_purmb_mb014,VARCHAR
sp_purmb_mb015,VARCHAR
sp_purmb_mb016,VARCHAR
sp_purmb_mb017,DECIMAL
sp_purmb_mb018,DECIMAL
sp_purmb_mb019,VARCHAR
sp_purmb_mb020,VARCHAR
sp_purmb_mb021,VARCHAR
sp_purmb_mb022,VARCHAR
sp_purmc_mc001,VARCHAR
sp_purmc_mc002,VARCHAR
sp_purmc_mc003,VARCHAR
sp_purmc_mc004,VARCHAR
sp_purmc_mc005,DECIMAL
sp_purmc_mc006,DECIMAL
sp_purmc_mc007,VARCHAR
sp_purmc_mc008,VARCHAR
sp_purmc_mc009,DECIMAL
sp_purmc_mc010,DECIMAL
sp_purmc_mc011,VARCHAR
sp_purmc_mc012,VARCHAR
sp_purmc_mc013,VARCHAR
sp_purmc_mc014,VARCHAR
sp_purmc_mc015,DECIMAL
sp_purtc_tc001,VARCHAR
sp_purtc_tc002,VARCHAR
sp_purtc_tc003,VARCHAR
sp_purtc_tc004,VARCHAR
sp_purtc_tc005,VARCHAR
sp_purtc_tc006,DECIMAL
sp_purtc_tc007,VARCHAR
sp_purtc_tc008,VARCHAR
sp_purtc_tc009,VARCHAR
sp_purtc_tc010,VARCHAR
sp_purtc_tc011,VARCHAR
sp_purtc_tc012,VARCHAR
sp_purtc_tc013,DECIMAL
sp_purtc_tc014,VARCHAR
sp_purtc_tc015,VARCHAR
sp_purtc_tc016,VARCHAR
sp_purtc_tc017,VARCHAR
sp_purtc_tc018,VARCHAR
sp_purtc_tc019,DECIMAL
sp_purtc_tc020,DECIMAL
sp_purtc_tc021,VARCHAR
sp_purtc_tc022,VARCHAR
sp_purtc_tc023,DECIMAL
sp_purtc_tc024,VARCHAR
sp_purtc_tc025,VARCHAR
sp_purtc_tc026,DECIMAL
sp_purtc_tc027,VARCHAR
sp_purtc_tc028,DECIMAL
sp_purtc_tc029,DECIMAL
sp_purtc_tc030,VARCHAR
sp_purtc_tc031,DECIMAL
sp_purtc_tc032,VARCHAR
sp_purtc_tc033,VARCHAR
sp_purtc_tc034,VARCHAR
sp_purtc_tc035,VARCHAR
sp_purtc_tc036,VARCHAR
sp_purtc_tc037,VARCHAR
sp_purtc_tc038,VARCHAR
sp_purtc_tc039,VARCHAR
sp_purtc_tc040,VARCHAR
sp_purtc_tc041,VARCHAR
sp_purtc_tc042,DECIMAL
sp_purtc_tc043,DECIMAL
sp_purtc_tc044,VARCHAR
sp_purtc_tc045,VARCHAR
sp_purtc_tc046,VARCHAR
sp_purtc_tc047,VARCHAR
sp_purtc_tc048,VARCHAR
sp_purtc_tc049,VARCHAR
sp_purtc_tc050,VARCHAR
sp_purtc_tc051,VARCHAR
sp_purtc_tc052,VARCHAR
sp_purtc_tc500,VARCHAR
sp_purtc_tc501,VARCHAR
sp_purtc_tc502,VARCHAR
sp_purtc_tc503,VARCHAR
sp_purtc_tc550,VARCHAR
sp_purtd_td001,VARCHAR
sp_purtd_td002,VARCHAR
sp_purtd_td003,VARCHAR
sp_purtd_td004,VARCHAR
sp_purtd_td005,VARCHAR
sp_purtd_td006,VARCHAR
sp_purtd_td007,VARCHAR
sp_purtd_td008,DECIMAL
sp_purtd_td009,VARCHAR
sp_purtd_td010,DECIMAL
sp_purtd_td011,DECIMAL
sp_purtd_td012,VARCHAR
sp_purtd_td013,VARCHAR
sp_purtd_td014,VARCHAR
sp_purtd_td015,DECIMAL
sp_purtd_td016,VARCHAR
sp_purtd_td017,VARCHAR
sp_purtd_td018,VARCHAR
sp_purtd_td019,DECIMAL
sp_purtd_td020,VARCHAR
sp_purtd_td021,VARCHAR
sp_purtd_td022,VARCHAR
sp_purtd_td023,VARCHAR
sp_purtd_td024,VARCHAR
sp_purtd_td025,VARCHAR
sp_purtd_td026,VARCHAR
sp_purtd_td027,VARCHAR
sp_purtd_td028,VARCHAR
sp_purtd_td029,VARCHAR
sp_purtd_td030,DECIMAL
sp_purtd_td031,DECIMAL
sp_purtd_td032,VARCHAR
sp_purtd_td033,VARCHAR
sp_purtd_td034,VARCHAR
sp_purtd_td035,VARCHAR
sp_purtd_td036,VARCHAR
sp_purtd_td037,VARCHAR
sp_purtd_td038,VARCHAR
sp_purtd_td039,VARCHAR
sp_purtd_td040,VARCHAR
sp_purtd_td041,VARCHAR
sp_purtd_td042,VARCHAR
sp_purtd_td043,VARCHAR
sp_purtd_td044,VARCHAR
sp_purtd_td045,VARCHAR
sp_purtd_td046,VARCHAR
sp_purtd_td047,VARCHAR
sp_purtd_td048,DECIMAL
sp_purtd_td049,DECIMAL
sp_purtd_td050,VARCHAR
sp_purtd_td051,VARCHAR
sp_purtd_td052,VARCHAR
sp_purtd_td053,VARCHAR
sp_purtd_td054,VARCHAR
sp_purtd_td055,VARCHAR
sp_purtd_td056,VARCHAR
sp_purtd_td057,DECIMAL
sp_purtd_td058,DECIMAL
sp_purtd_td059,VARCHAR
sp_purtd_td060,DECIMAL
sp_purtd_td061,VARCHAR
sp_purtd_td062,DECIMAL
sp_purtd_td063,DECIMAL
sp_purtd_td500,VARCHAR
sp_purtd_td501,VARCHAR
sp_purtd_td502,VARCHAR
sp_purtd_td503,VARCHAR
sp_purtd_td550,VARCHAR
sp_purtd_td551,DECIMAL
sp_purtd_td552,VARCHAR
sp_purtd_td553,VARCHAR
sp_purtd_td554,VARCHAR
sp_purtd_td555,VARCHAR
sp_purtg_tg001,VARCHAR
sp_purtg_tg002,VARCHAR
sp_purtg_tg003,VARCHAR
sp_purtg_tg004,VARCHAR
sp_purtg_tg005,VARCHAR
sp_purtg_tg006,VARCHAR
sp_purtg_tg007,VARCHAR
sp_purtg_tg008,DECIMAL
sp_purtg_tg009,VARCHAR
sp_purtg_tg010,VARCHAR
sp_purtg_tg011,VARCHAR
sp_purtg_tg012,DECIMAL
sp_purtg_tg013,VARCHAR
sp_purtg_tg014,VARCHAR
sp_purtg_tg015,VARCHAR
sp_purtg_tg016,VARCHAR
sp_purtg_tg017,DECIMAL
sp_purtg_tg018,DECIMAL
sp_purtg_tg019,DECIMAL
sp_purtg_tg020,DECIMAL
sp_purtg_tg021,VARCHAR
sp_purtg_tg022,VARCHAR
sp_purtg_tg023,VARCHAR
sp_purtg_tg024,VARCHAR
sp_purtg_tg025,DECIMAL
sp_purtg_tg026,DECIMAL
sp_purtg_tg027,VARCHAR
sp_purtg_tg028,DECIMAL
sp_purtg_tg029,VARCHAR
sp_purtg_tg030,DECIMAL
sp_purtg_tg031,DECIMAL
sp_purtg_tg032,DECIMAL
sp_purtg_tg033,VARCHAR
sp_purtg_tg034,VARCHAR
sp_purtg_tg035,VARCHAR
sp_purtg_tg036,VARCHAR
sp_purtg_tg037,VARCHAR
sp_purtg_tg038,DECIMAL
sp_purtg_tg039,DECIMAL
sp_purtg_tg040,DECIMAL
sp_purtg_tg041,DECIMAL
sp_purtg_tg042,VARCHAR
sp_purtg_tg043,VARCHAR
sp_purtg_tg044,VARCHAR
sp_purtg_tg045,DECIMAL
sp_purtg_tg046,DECIMAL
sp_purtg_tg047,VARCHAR
sp_purtg_tg048,VARCHAR
sp_purtg_tg049,VARCHAR
sp_purtg_tg050,VARCHAR
sp_purtg_tg051,VARCHAR
sp_purtg_tg052,VARCHAR
sp_purtg_tg053,DECIMAL
sp_purtg_tg054,DECIMAL
sp_purtg_tg055,VARCHAR
sp_purtg_tg056,VARCHAR
sp_purtg_tg057,VARCHAR
sp_purtg_tg058,VARCHAR
sp_purtg_tg059,VARCHAR
sp_purtg_tg060,VARCHAR
sp_purtg_tg061,VARCHAR
sp_purtg_tg062,VARCHAR
sp_purtg_tg063,VARCHAR
sp_purtg_tg064,DECIMAL
sp_purtg_tg065,DECIMAL
sp_purtg_tg066,DECIMAL
sp_purtg_tg067,VARCHAR
sp_purtg_tg068,VARCHAR
sp_purtg_tg069,VARCHAR
sp_purtg_tg070,VARCHAR
sp_purtg_tg071,VARCHAR
sp_purtg_tg072,VARCHAR
sp_purtg_tg073,VARCHAR
sp_purtg_tg074,VARCHAR
sp_purtg_tg075,VARCHAR
sp_purtg_tg076,VARCHAR
sp_purtg_tg077,VARCHAR
sp_purtg_tg500,VARCHAR
sp_purtg_tg550,VARCHAR
sp_purth_th001,VARCHAR
sp_purth_th002,VARCHAR
sp_purth_th003,VARCHAR
sp_purth_th004,VARCHAR
sp_purth_th005,VARCHAR
sp_purth_th006,VARCHAR
sp_purth_th007,DECIMAL
sp_purth_th008,VARCHAR
sp_purth_th009,VARCHAR
sp_purth_th010,VARCHAR
sp_purth_th011,VARCHAR
sp_purth_th012,VARCHAR
sp_purth_th013,VARCHAR
sp_purth_th014,VARCHAR
sp_purth_th015,DECIMAL
sp_purth_th016,DECIMAL
sp_purth_th017,DECIMAL
sp_purth_th018,DECIMAL
sp_purth_th019,DECIMAL
sp_purth_th020,DECIMAL
sp_purth_th021,VARCHAR
sp_purth_th022,VARCHAR
sp_purth_th023,VARCHAR
sp_purth_th024,DECIMAL
sp_purth_th025,VARCHAR
sp_purth_th026,VARCHAR
sp_purth_th027,VARCHAR
sp_purth_th028,VARCHAR
sp_purth_th029,VARCHAR
sp_purth_th030,VARCHAR
sp_purth_th031,VARCHAR
sp_purth_th032,VARCHAR
sp_purth_th033,VARCHAR
sp_purth_th034,DECIMAL
sp_purth_th035,VARCHAR
sp_purth_th036,VARCHAR
sp_purth_th037,VARCHAR
sp_purth_th038,VARCHAR
sp_purth_th039,VARCHAR
sp_purth_th040,VARCHAR
sp_purth_th041,VARCHAR
sp_purth_th042,VARCHAR
sp_purth_th043,VARCHAR
sp_purth_th044,VARCHAR
sp_purth_th045,DECIMAL
sp_purth_th046,DECIMAL
sp_purth_th047,DECIMAL
sp_purth_th048,DECIMAL
sp_purth_th049,DECIMAL
sp_purth_th050,DECIMAL
sp_purth_th051,DECIMAL
sp_purth_th052,DECIMAL
sp_purth_th053,VARCHAR
sp_purth_th054,VARCHAR
sp_purth_th055,DECIMAL
sp_purth_th056,VARCHAR
sp_purth_th057,VARCHAR
sp_purth_th058,VARCHAR
sp_purth_th059,VARCHAR
sp_purth_th060,VARCHAR
sp_purth_th061,DECIMAL
sp_purth_th062,VARCHAR
sp_purth_th063,VARCHAR
sp_purth_th064,VARCHAR
sp_purth_th065,VARCHAR
sp_purth_th066,VARCHAR
sp_purth_th067,DECIMAL
sp_purth_th068,DECIMAL
sp_purth_th069,VARCHAR
sp_purth_th070,VARCHAR
sp_purth_th071,VARCHAR
sp_purth_th072,VARCHAR
sp_purth_th073,DECIMAL
sp_purth_th074,VARCHAR
sp_purth_th075,VARCHAR
sp_purth_th076,VARCHAR
sp_purth_th077,VARCHAR
sp_purth_th078,VARCHAR
sp_purth_th079,VARCHAR
sp_purth_th080,VARCHAR
sp_purth_th081,VARCHAR
sp_purth_th082,VARCHAR
sp_purth_th083,VARCHAR
sp_purth_th084,VARCHAR
sp_purth_th085,VARCHAR
sp_purth_th086,VARCHAR
sp_purth_th087,VARCHAR
sp_purth_th088,VARCHAR
sp_purth_th089,DECIMAL
sp_purth_th090,DECIMAL
sp_purth_th091,VARCHAR
sp_purth_th092,VARCHAR
sp_purth_th500,VARCHAR
sp_purth_th501,VARCHAR
sp_purth_th502,VARCHAR
sp_purth_th503,VARCHAR
sp_purth_th550,VARCHAR
sp_purth_th551,VARCHAR
sp_purth_th552,VARCHAR
sp_purth_th553,VARCHAR
sp_purth_th554,VARCHAR
sp_purth_th555,VARCHAR
sp_purth_th556,VARCHAR
sp_purth_th557,VARCHAR
sp_purth_th558,VARCHAR
sp_purti_ti001,VARCHAR
sp_purti_ti002,VARCHAR
sp_purti_ti003,VARCHAR
sp_purti_ti004,VARCHAR
sp_purti_ti005,VARCHAR
sp_purti_ti006,VARCHAR
sp_purti_ti007,DECIMAL
sp_purti_ti008,VARCHAR
sp_purti_ti009,VARCHAR
sp_purti_ti010,DECIMAL
sp_purti_ti011,DECIMAL
sp_purti_ti012,VARCHAR
sp_purti_ti013,VARCHAR
sp_purti_ti014,VARCHAR
sp_purti_ti015,DECIMAL
sp_purti_ti016,VARCHAR
sp_purti_ti017,VARCHAR
sp_purti_ti018,VARCHAR
sp_purti_ti019,VARCHAR
sp_purti_ti020,VARCHAR
sp_purti_ti021,DECIMAL
sp_purti_ti022,DECIMAL
sp_purti_ti023,VARCHAR
sp_purti_ti024,VARCHAR
sp_purti_ti025,VARCHAR
sp_purti_ti026,VARCHAR
sp_purti_ti027,DECIMAL
sp_purti_ti028,DECIMAL
sp_purti_ti029,DECIMAL
sp_purti_ti030,VARCHAR
sp_purti_ti031,DECIMAL
sp_purti_ti032,VARCHAR
sp_purti_ti033,VARCHAR
sp_purti_ti034,DECIMAL
sp_purti_ti035,VARCHAR
sp_purti_ti036,VARCHAR
sp_purti_ti037,VARCHAR
sp_purti_ti038,VARCHAR
sp_purti_ti039,VARCHAR
sp_purti_ti040,DECIMAL
sp_purti_ti041,DECIMAL
sp_purti_ti042,VARCHAR
sp_purti_ti043,VARCHAR
sp_purti_ti044,VARCHAR
sp_purti_ti045,VARCHAR
sp_purti_ti046,VARCHAR
sp_purti_ti047,VARCHAR
sp_purti_ti048,VARCHAR
sp_purti_ti049,VARCHAR
sp_purti_ti050,DECIMAL
sp_purti_ti051,DECIMAL
sp_purti_ti052,VARCHAR
sp_purti_ti053,VARCHAR
sp_purti_ti054,VARCHAR
sp_purti_ti055,DECIMAL
sp_purti_ti056,VARCHAR
sp_purti_ti057,VARCHAR
sp_purti_ti058,VARCHAR
sp_purti_ti059,VARCHAR
sp_purti_ti060,VARCHAR
sp_purti_ti061,VARCHAR
sp_purti_ti062,VARCHAR
sp_purti_ti063,VARCHAR
sp_purti_ti064,VARCHAR
sp_purti_ti065,VARCHAR
sp_purti_ti066,VARCHAR
sp_purti_ti067,VARCHAR
sp_purti_ti068,VARCHAR
sp_purti_ti069,VARCHAR
sp_purti_ti070,VARCHAR
sp_purti_ti071,VARCHAR
sp_purti_ti072,VARCHAR
sp_purti_ti073,VARCHAR
sp_purti_ti074,VARCHAR
sp_purti_ti075,VARCHAR
sp_purti_ti076,VARCHAR
sp_purti_ti077,VARCHAR
sp_purtj_tj001,VARCHAR
sp_purtj_tj002,VARCHAR
sp_purtj_tj003,VARCHAR
sp_purtj_tj004,VARCHAR
sp_purtj_tj005,VARCHAR
sp_purtj_tj006,VARCHAR
sp_purtj_tj007,VARCHAR
sp_purtj_tj008,DECIMAL
sp_purtj_tj009,DECIMAL
sp_purtj_tj010,DECIMAL
sp_purtj_tj011,VARCHAR
sp_purtj_tj012,VARCHAR
sp_purtj_tj013,VARCHAR
sp_purtj_tj014,VARCHAR
sp_purtj_tj015,VARCHAR
sp_purtj_tj016,VARCHAR
sp_purtj_tj017,VARCHAR
sp_purtj_tj018,VARCHAR
sp_purtj_tj019,VARCHAR
sp_purtj_tj020,VARCHAR
sp_purtj_tj021,VARCHAR
sp_purtj_tj022,DECIMAL
sp_purtj_tj023,VARCHAR
sp_purtj_tj024,DECIMAL
sp_purtj_tj025,VARCHAR
sp_purtj_tj026,VARCHAR
sp_purtj_tj027,VARCHAR
sp_purtj_tj028,VARCHAR
sp_purtj_tj029,VARCHAR
sp_purtj_tj030,DECIMAL
sp_purtj_tj031,DECIMAL
sp_purtj_tj032,DECIMAL
sp_purtj_tj033,DECIMAL
sp_purtj_tj034,VARCHAR
sp_purtj_tj035,VARCHAR
sp_purtj_tj036,VARCHAR
sp_purtj_tj037,DECIMAL
sp_purtj_tj038,VARCHAR
sp_purtj_tj039,VARCHAR
sp_purtj_tj040,DECIMAL
sp_purtj_tj041,VARCHAR
sp_purtj_tj042,VARCHAR
sp_purtj_tj043,VARCHAR
sp_purtj_tj044,VARCHAR
sp_purtj_tj045,VARCHAR
sp_purtj_tj046,DECIMAL
sp_purtj_tj047,DECIMAL
sp_purtj_tj048,VARCHAR
sp_purtj_tj049,VARCHAR
sp_purtj_tj050,VARCHAR
sp_purtj_tj051,VARCHAR
sp_purtj_tj052,DECIMAL
sp_purtj_tj053,VARCHAR
sp_purtj_tj054,VARCHAR
sp_purtj_tj055,VARCHAR
sp_purtj_tj056,VARCHAR
sp_purtj_tj057,VARCHAR
sp_purtj_tj058,VARCHAR
sp_purtj_tj059,VARCHAR
sp_purtj_tj060,VARCHAR
sp_purtj_tj061,DECIMAL
sp_purtj_tj062,DECIMAL
sp_purtj_tj500,VARCHAR
sp_purtj_tj501,VARCHAR
sp_qmsmh_mh001,VARCHAR
sp_qmsmh_mh002,VARCHAR
sp_qmsmh_mh003,VARCHAR
sp_qmsmh_mh004,DECIMAL
sp_qmsmh_mh005,DECIMAL
sp_qmsmh_mh006,VARCHAR
sp_qmsmh_mh007,VARCHAR
sp_qmsmh_mh008,VARCHAR
sp_qmsta_ta001,VARCHAR
sp_qmsta_ta002,VARCHAR
sp_qmsta_ta003,VARCHAR
sp_qmsta_ta004,VARCHAR
sp_qmsta_ta005,VARCHAR
sp_qmsta_ta006,VARCHAR
sp_qmsta_ta007,DECIMAL
sp_qmsta_ta008,DECIMAL
sp_qmsta_ta009,DECIMAL
sp_qmsta_ta010,VARCHAR
sp_qmsta_ta011,VARCHAR
sp_qmsta_ta012,VARCHAR
sp_qmsta_ta013,DECIMAL
sp_qmsta_ta014,VARCHAR
sp_qmsta_ta015,VARCHAR
sp_qmsta_ta016,VARCHAR
sp_qmsta_ta017,VARCHAR
sp_qmsta_ta018,VARCHAR
sp_qmsta_ta019,VARCHAR
sp_qmsta_ta020,VARCHAR
sp_qmsta_ta021,VARCHAR
sp_qmsta_ta022,DECIMAL
sp_qmsta_ta023,DECIMAL
sp_qmsta_ta024,DECIMAL
sp_qmsta_ta025,VARCHAR
sp_qmsta_ta026,DECIMAL
sp_qmsta_ta027,DECIMAL
sp_qmsta_ta028,VARCHAR
sp_qmsta_ta029,VARCHAR
sp_qmsta_ta030,VARCHAR
sp_qmstb_tb001,VARCHAR
sp_qmstb_tb002,VARCHAR
sp_qmstb_tb003,VARCHAR
sp_qmstb_tb004,VARCHAR
sp_qmstb_tb005,DECIMAL
sp_qmstb_tb006,VARCHAR
sp_qmstb_tb007,DECIMAL
sp_qmstb_tb008,DECIMAL
sp_qmstb_tb009,DECIMAL
sp_qmstb_tb010,VARCHAR
sp_qmstb_tb011,VARCHAR
sp_qmstb_tb012,VARCHAR
sp_qmstb_tb013,DECIMAL
sp_qmstb_tb014,DECIMAL
sp_qmstb_tb015,VARCHAR
sp_qmstb_tb016,VARCHAR
sp_qmstb_tb017,VARCHAR
sp_qmstc_tc001,VARCHAR
sp_qmstc_tc002,VARCHAR
sp_qmstc_tc003,VARCHAR
sp_qmstc_tc004,VARCHAR
sp_qmstc_tc005,VARCHAR
sp_qmstc_tc006,DECIMAL
sp_qmstc_tc007,VARCHAR
sp_qmstc_tc008,DECIMAL
sp_qmstc_tc009,DECIMAL
sp_qmstc_tc010,VARCHAR
sp_qmstc_tc011,VARCHAR
sp_qmstc_tc012,VARCHAR
sp_qmstd_td001,VARCHAR
sp_qmstd_td002,VARCHAR
sp_qmstd_td003,VARCHAR
sp_qmstd_td004,VARCHAR
sp_qmstd_td005,VARCHAR
sp_qmstd_td006,VARCHAR
sp_qmstd_td007,DECIMAL
sp_qmstd_td008,DECIMAL
sp_qmstd_td009,DECIMAL
sp_qmstd_td010,VARCHAR
sp_qmstd_td011,VARCHAR
sp_qmstd_td012,VARCHAR
sp_qmstd_td013,DECIMAL
sp_qmstd_td014,VARCHAR
sp_qmstd_td015,VARCHAR
sp_qmstd_td016,VARCHAR
sp_qmstd_td017,VARCHAR
sp_qmstd_td018,VARCHAR
sp_qmstd_td019,VARCHAR
sp_qmstd_td020,VARCHAR
sp_qmstd_td021,VARCHAR
sp_qmstd_td022,DECIMAL
sp_qmstd_td023,DECIMAL
sp_qmstd_td024,DECIMAL
sp_qmstd_td025,VARCHAR
sp_qmstd_td026,DECIMAL
sp_qmstd_td027,DECIMAL
sp_qmstd_td028,VARCHAR
sp_qmstd_td029,VARCHAR
sp_qmstd_td030,VARCHAR
sp_qmstd_td031,DECIMAL
sp_qmstd_td500,DECIMAL
sp_qmstd_td501,DECIMAL
sp_qmstd_td502,DECIMAL
sp_qmstd_td503,DECIMAL
sp_qmstd_td504,DECIMAL
sp_qmstd_td505,DECIMAL
sp_qmstd_td506,VARCHAR
sp_qmstd_td507,DECIMAL
sp_qmstd_td553,DECIMAL
sp_qmstd_td554,DECIMAL
sp_qmstd_td555,DECIMAL
sp_qmstd_td556,DECIMAL
sp_qmste_te001,VARCHAR
sp_qmste_te002,VARCHAR
sp_qmste_te003,VARCHAR
sp_qmste_te004,VARCHAR
sp_qmste_te005,DECIMAL
sp_qmste_te006,VARCHAR
sp_qmste_te007,DECIMAL
sp_qmste_te008,DECIMAL
sp_qmste_te009,DECIMAL
sp_qmste_te010,VARCHAR
sp_qmste_te011,VARCHAR
sp_qmste_te012,VARCHAR
sp_qmste_te013,DECIMAL
sp_qmste_te014,DECIMAL
sp_qmste_te015,VARCHAR
sp_qmste_te016,VARCHAR
sp_qmste_te017,VARCHAR
sp_qmstf_tf001,VARCHAR
sp_qmstf_tf002,VARCHAR
sp_qmstf_tf003,VARCHAR
sp_qmstf_tf004,VARCHAR
sp_qmstf_tf005,VARCHAR
sp_qmstf_tf006,DECIMAL
sp_qmstf_tf007,VARCHAR
sp_qmstf_tf008,DECIMAL
sp_qmstf_tf009,DECIMAL
sp_qmstf_tf010,VARCHAR
sp_qmstf_tf011,VARCHAR
sp_qmstf_tf012,VARCHAR
sp_qmstg_tg001,VARCHAR
sp_qmstg_tg002,VARCHAR
sp_qmstg_tg003,VARCHAR
sp_qmstg_tg004,VARCHAR
sp_qmstg_tg005,VARCHAR
sp_qmstg_tg006,VARCHAR
sp_qmstg_tg007,DECIMAL
sp_qmstg_tg008,DECIMAL
sp_qmstg_tg009,DECIMAL
sp_qmstg_tg010,VARCHAR
sp_qmstg_tg011,VARCHAR
sp_qmstg_tg012,VARCHAR
sp_qmstg_tg013,DECIMAL
sp_qmstg_tg014,VARCHAR
sp_qmstg_tg015,VARCHAR
sp_qmstg_tg016,VARCHAR
sp_qmstg_tg017,VARCHAR
sp_qmstg_tg018,VARCHAR
sp_qmstg_tg019,VARCHAR
sp_qmstg_tg020,VARCHAR
sp_qmstg_tg021,VARCHAR
sp_qmstg_tg022,DECIMAL
sp_qmstg_tg023,DECIMAL
sp_qmstg_tg024,DECIMAL
sp_qmstg_tg025,VARCHAR
sp_qmstg_tg026,DECIMAL
sp_qmstg_tg027,DECIMAL
sp_qmstg_tg028,VARCHAR
sp_qmstg_tg029,VARCHAR
sp_qmstg_tg030,VARCHAR
sp_qmstg_tg031,DECIMAL
sp_qmstg_tg500,DECIMAL
sp_qmstg_tg501,DECIMAL
sp_qmstg_tg502,DECIMAL
sp_qmstg_tg503,DECIMAL
sp_qmstg_tg504,DECIMAL
sp_qmstg_tg505,DECIMAL
sp_qmstg_tg506,VARCHAR
sp_qmstg_tg507,DECIMAL
sp_qmstg_tg553,DECIMAL
sp_qmstg_tg554,DECIMAL
sp_qmstg_tg555,DECIMAL
sp_qmstg_tg556,DECIMAL
sp_qmsth_th001,VARCHAR
sp_qmsth_th002,VARCHAR
sp_qmsth_th003,VARCHAR
sp_qmsth_th004,VARCHAR
sp_qmsth_th005,DECIMAL
sp_qmsth_th006,VARCHAR
sp_qmsth_th007,DECIMAL
sp_qmsth_th008,DECIMAL
sp_qmsth_th009,DECIMAL
sp_qmsth_th010,VARCHAR
sp_qmsth_th011,VARCHAR
sp_qmsth_th012,VARCHAR
sp_qmsth_th013,DECIMAL
sp_qmsth_th014,DECIMAL
sp_qmsth_th015,VARCHAR
sp_qmsth_th016,VARCHAR
sp_qmsth_th017,VARCHAR
sp_qmsti_ti001,VARCHAR
sp_qmsti_ti002,VARCHAR
sp_qmsti_ti003,VARCHAR
sp_qmsti_ti004,VARCHAR
sp_qmsti_ti005,VARCHAR
sp_qmsti_ti006,DECIMAL
sp_qmsti_ti007,VARCHAR
sp_qmsti_ti008,DECIMAL
sp_qmsti_ti009,DECIMAL
sp_qmsti_ti010,VARCHAR
sp_qmsti_ti011,VARCHAR
sp_qmsti_ti012,VARCHAR
sp_qmstj_tj001,VARCHAR
sp_qmstj_tj002,VARCHAR
sp_qmstj_tj003,VARCHAR
sp_qmstj_tj004,VARCHAR
sp_qmstj_tj005,VARCHAR
sp_qmstj_tj006,VARCHAR
sp_qmstj_tj007,DECIMAL
sp_qmstj_tj008,DECIMAL
sp_qmstj_tj009,DECIMAL
sp_qmstj_tj010,VARCHAR
sp_qmstj_tj011,VARCHAR
sp_qmstj_tj012,VARCHAR
sp_qmstj_tj013,DECIMAL
sp_qmstj_tj014,VARCHAR
sp_qmstj_tj015,VARCHAR
sp_qmstj_tj016,VARCHAR
sp_qmstj_tj017,VARCHAR
sp_qmstj_tj018,VARCHAR
sp_qmstj_tj019,VARCHAR
sp_qmstj_tj020,VARCHAR
sp_qmstj_tj021,VARCHAR
sp_qmstj_tj022,DECIMAL
sp_qmstj_tj023,DECIMAL
sp_qmstj_tj024,DECIMAL
sp_qmstj_tj025,VARCHAR
sp_qmstj_tj026,DECIMAL
sp_qmstj_tj027,DECIMAL
sp_qmstj_tj028,VARCHAR
sp_qmstj_tj029,VARCHAR
sp_qmstj_tj030,VARCHAR
sp_qmstj_tj031,DECIMAL
sp_qmstk_tk001,VARCHAR
sp_qmstk_tk002,VARCHAR
sp_qmstk_tk003,VARCHAR
sp_qmstk_tk004,VARCHAR
sp_qmstk_tk005,DECIMAL
sp_qmstk_tk006,VARCHAR
sp_qmstk_tk007,DECIMAL
sp_qmstk_tk008,DECIMAL
sp_qmstk_tk009,DECIMAL
sp_qmstk_tk010,VARCHAR
sp_qmstk_tk011,VARCHAR
sp_qmstk_tk012,VARCHAR
sp_qmstk_tk013,DECIMAL
sp_qmstk_tk014,DECIMAL
sp_qmstk_tk015,VARCHAR
sp_qmstk_tk016,VARCHAR
sp_qmstk_tk017,VARCHAR
sp_qmstl_tl001,VARCHAR
sp_qmstl_tl002,VARCHAR
sp_qmstl_tl003,VARCHAR
sp_qmstl_tl004,VARCHAR
sp_qmstl_tl005,VARCHAR
sp_qmstl_tl006,DECIMAL
sp_qmstl_tl007,VARCHAR
sp_qmstl_tl008,DECIMAL
sp_qmstl_tl009,DECIMAL
sp_qmstl_tl010,VARCHAR
sp_qmstl_tl011,VARCHAR
sp_qmstl_tl012,VARCHAR
sp_sfctb_tb001,VARCHAR
sp_sfctb_tb002,VARCHAR
sp_sfctb_tb003,VARCHAR
sp_sfctb_tb004,VARCHAR
sp_sfctb_tb005,VARCHAR
sp_sfctb_tb006,VARCHAR
sp_sfctb_tb007,VARCHAR
sp_sfctb_tb008,VARCHAR
sp_sfctb_tb009,VARCHAR
sp_sfctb_tb010,VARCHAR
sp_sfctb_tb011,DECIMAL
sp_sfctb_tb012,VARCHAR
sp_sfctb_tb013,VARCHAR
sp_sfctb_tb014,VARCHAR
sp_sfctb_tb015,VARCHAR
sp_sfctb_tb016,VARCHAR
sp_sfctb_tb017,VARCHAR
sp_sfctb_tb018,VARCHAR
sp_sfctb_tb019,VARCHAR
sp_sfctb_tb020,VARCHAR
sp_sfctb_tb021,VARCHAR
sp_sfctb_tb022,VARCHAR
sp_sfctb_tb023,VARCHAR
sp_sfctb_tb024,VARCHAR
sp_sfctb_tb025,VARCHAR
sp_sfctb_tb026,DECIMAL
sp_sfctb_tb027,DECIMAL
sp_sfctb_tb028,VARCHAR
sp_sfctb_tb029,DECIMAL
sp_sfctb_tb030,DECIMAL
sp_sfctb_tb031,VARCHAR
sp_sfctb_tb032,VARCHAR
sp_sfctb_tb033,VARCHAR
sp_sfctb_tb034,VARCHAR
sp_sfctb_tb035,VARCHAR
sp_sfctb_tb036,VARCHAR
sp_sfctb_tb037,DECIMAL
sp_sfctb_tb038,VARCHAR
sp_sfctb_tb039,VARCHAR
sp_sfctb_tb040,VARCHAR
sp_sfctb_tb041,VARCHAR
sp_sfctb_tb042,VARCHAR
sp_sfctc_tc001,VARCHAR
sp_sfctc_tc002,VARCHAR
sp_sfctc_tc003,VARCHAR
sp_sfctc_tc004,VARCHAR
sp_sfctc_tc005,VARCHAR
sp_sfctc_tc006,VARCHAR
sp_sfctc_tc007,VARCHAR
sp_sfctc_tc008,VARCHAR
sp_sfctc_tc009,VARCHAR
sp_sfctc_tc010,VARCHAR
sp_sfctc_tc011,VARCHAR
sp_sfctc_tc012,VARCHAR
sp_sfctc_tc013,VARCHAR
sp_sfctc_tc014,DECIMAL
sp_sfctc_tc015,DECIMAL
sp_sfctc_tc016,DECIMAL
sp_sfctc_tc017,DECIMAL
sp_sfctc_tc018,DECIMAL
sp_sfctc_tc019,DECIMAL
sp_sfctc_tc020,DECIMAL
sp_sfctc_tc021,DECIMAL
sp_sfctc_tc022,VARCHAR
sp_sfctc_tc023,VARCHAR
sp_sfctc_tc024,VARCHAR
sp_sfctc_tc025,DECIMAL
sp_sfctc_tc026,VARCHAR
sp_sfctc_tc027,VARCHAR
sp_sfctc_tc028,VARCHAR
sp_sfctc_tc029,VARCHAR
sp_sfctc_tc030,VARCHAR
sp_sfctc_tc031,VARCHAR
sp_sfctc_tc032,VARCHAR
sp_sfctc_tc033,VARCHAR
sp_sfctc_tc034,VARCHAR
sp_sfctc_tc035,VARCHAR
sp_sfctc_tc036,DECIMAL
sp_sfctc_tc037,DECIMAL
sp_sfctc_tc038,VARCHAR
sp_sfctc_tc039,VARCHAR
sp_sfctc_tc040,VARCHAR
sp_sfctc_tc041,VARCHAR
sp_sfctc_tc042,DECIMAL
sp_sfctc_tc043,DECIMAL
sp_sfctc_tc044,DECIMAL
sp_sfctc_tc045,DECIMAL
sp_sfctc_tc046,DECIMAL
sp_sfctc_tc047,VARCHAR
sp_sfctc_tc048,VARCHAR
sp_sfctc_tc049,VARCHAR
sp_sfctc_tc050,VARCHAR
sp_sfctc_tc051,DECIMAL
sp_sfctc_tc052,VARCHAR
sp_sfctc_tc053,DECIMAL
sp_sfctc_tc054,DECIMAL
sp_sfctc_tc055,VARCHAR
sp_sfctc_tc056,VARCHAR
sp_sfctc_tc057,VARCHAR
sp_sfctc_tc058,VARCHAR
sp_sfctc_tc059,VARCHAR
sp_sfctc_tc060,DECIMAL
sp_sfctc_tc061,VARCHAR
sp_sfctc_tc062,VARCHAR
sp_sfctc_tc063,VARCHAR
sp_cmsmd_modi_date,VARCHAR
sp_purtc_modi_date,VARCHAR
sp_purtd_modi_date,VARCHAR
sp_purtg_modi_date,VARCHAR
sp_purth_modi_date,VARCHAR
sp_sfctc_modi_date,VARCHAR
sp_qmsmh_modi_date,VARCHAR
sp_mocta_modi_date,VARCHAR
sp_purtj_modi_date,VARCHAR
sp_invmb_modi_date,VARCHAR
sp_sfctb_modi_date,VARCHAR
sp_qmstl_modi_date,VARCHAR
sp_purti_modi_date,VARCHAR
sp_qmstk_modi_date,VARCHAR
sp_purmc_modi_date,VARCHAR
sp_qmstj_modi_date,VARCHAR
sp_purmb_modi_date,VARCHAR
sp_qmsti_modi_date,VARCHAR
sp_mocti_modi_date,VARCHAR
sp_qmsth_modi_date,VARCHAR
sp_cmsmv_modi_date,VARCHAR
sp_qmstg_modi_date,VARCHAR
sp_qmstf_modi_date,VARCHAR
sp_mocth_modi_date,VARCHAR
sp_qmste_modi_date,VARCHAR
sp_moctg_modi_date,VARCHAR
sp_cmsmk_modi_date,VARCHAR
sp_qmstd_modi_date,VARCHAR
sp_cmsmh_modi_date,VARCHAR
sp_qmstc_modi_date,VARCHAR
sp_moctf_modi_date,VARCHAR
sp_qmstb_modi_date,VARCHAR
sp_qmsta_modi_date,VARCHAR
sp_purma_ma001,VARCHAR
sp_purma_ma002,VARCHAR
sp_purma_ma003,VARCHAR
sp_purma_ma004,VARCHAR
sp_purma_ma005,INT
sp_purma_ma006,VARCHAR
sp_purma_ma007,VARCHAR
sp_purma_ma008,VARCHAR
sp_purma_ma009,VARCHAR
sp_purma_ma010,VARCHAR
sp_purma_ma011,VARCHAR
sp_purma_ma012,VARCHAR
sp_purma_ma013,VARCHAR
sp_purma_ma014,VARCHAR
sp_purma_ma015,VARCHAR
sp_purma_ma016,VARCHAR
sp_purma_ma017,VARCHAR
sp_purma_ma018,DECIMAL
sp_purma_ma019,INT
sp_purma_ma020,VARCHAR
sp_purma_ma021,VARCHAR
sp_purma_ma022,VARCHAR
sp_purma_ma023,VARCHAR
sp_purma_ma024,VARCHAR
sp_purma_ma025,VARCHAR
sp_purma_ma026,VARCHAR
sp_purma_ma027,VARCHAR
sp_purma_ma028,VARCHAR
sp_purma_ma029,VARCHAR
sp_purma_ma030,VARCHAR
sp_purma_ma031,VARCHAR
sp_purma_ma032,VARCHAR
sp_purma_ma033,VARCHAR
sp_purma_ma034,VARCHAR
sp_purma_ma035,VARCHAR
sp_purma_ma036,VARCHAR
sp_purma_ma037,VARCHAR
sp_purma_ma038,VARCHAR
sp_purma_ma039,VARCHAR
sp_purma_ma040,VARCHAR
sp_purma_ma041,VARCHAR
sp_purma_ma042,VARCHAR
sp_purma_ma043,VARCHAR
sp_purma_ma044,VARCHAR
sp_purma_ma045,VARCHAR
sp_purma_ma046,VARCHAR
sp_purma_ma047,VARCHAR
sp_purma_ma048,VARCHAR
sp_purma_ma049,VARCHAR
sp_purma_ma050,VARCHAR
sp_purma_ma051,VARCHAR
sp_purma_ma052,VARCHAR
sp_purma_ma053,VARCHAR
sp_purma_ma054,DECIMAL
sp_purma_ma055,VARCHAR
sp_purma_ma056,VARCHAR
sp_purma_ma057,VARCHAR
sp_purma_ma058,DECIMAL
sp_purma_ma059,VARCHAR
sp_purma_modi_date,VARCHAR
ServiceCode,VARCHAR
CustomerName,VARCHAR
IsAuth,INT
diskio_reads,BOOLEAN
diskio_writes,BOOLEAN
diskio_reads_per_sec,BOOLEAN
diskio_writes_per_sec,BOOLEAN
diskio_merged_read_count,BOOLEAN
diskio_merged_write_count,BOOLEAN
diskio_merged_read_count_per_sec,BOOLEAN
diskio_read_bytes_per_sec,BOOLEAN
diskio_read_bytes,BOOLEAN
diskio_write_bytes,BOOLEAN
diskio_write_bytes_per_sec,BOOLEAN
diskio_read_time,BOOLEAN
diskio_write_time,BOOLEAN
diskio_read_await,BOOLEAN
diskio_write_await,BOOLEAN
diskio_await,BOOLEAN
diskio_io_time,BOOLEAN
diskio_io_util,BOOLEAN
diskio_name,BOOLEAN
diskio_serial,BOOLEAN
diskio__reads,INT
diskio__writes,INT
diskio__reads_per_sec,INT
diskio__writes_per_sec,INT
diskio__merged_read_count,INT
diskio__merged_write_count,INT
diskio__merged_read_count_per_sec,INT
diskio__merged_write_count_per_sec,INT
diskio__read_bytes,INT
diskio__write_bytes,INT
diskio__read_bytes_per_sec,INT
diskio__write_bytes_per_sec,INT
diskio__read_time,INT
diskio__write_time,INT
diskio__read_await,INT
diskio__write_await,INT
diskio__await,INT
diskio__io_time,INT
diskio__io_util,DECIMAL
diskio__iops_in_progress,INT
diskio__name,INT
diskio__serial,INT
table_event,VARCHAR
total_wait_time,INT
tabel_user_id,INT
table_name,INT
table_sql_text,VARCHAR
database_ver_nu,INT
diskio_ops_in_progress,BOOLEAN
os_servicepack,VARCHAR
edr_workstationsallocated,INT
edr_serversallocated,INT
edr_iotallocated,INT
edr_workstationsinuse,INT
edr_serversinuse,INT
edr_iotinuse,INT
edr_expirationdate,VARCHAR
warningId,VARCHAR
warningServerName,VARCHAR
snmp_deviceno,VARCHAR
snmp_deviceitem,VARCHAR
warningStatus,VARCHAR
warningNum,INT
warningTitle,VARCHAR
model,VARCHAR
veeam_user_name,VARCHAR
veeam_user_pwd,VARCHAR
veeam_ip,VARCHAR
port,VARCHAR
tablename,VARCHAR
num,INT
aiId,VARCHAR
aiopsInstanceName,VARCHAR
aiopsItem,VARCHAR
aiopsItemName,VARCHAR
aiopsItemType,VARCHAR
aiopsItemTypeName,VARCHAR
wCount,INT
loginId,VARCHAR
userId,VARCHAR
userName,VARCHAR
visitedPage,VARCHAR
visitedPageRoute,VARCHAR
accessTime,DATE
accessDuration,DECIMAL
leaveTime,DATE
previousPage,VARCHAR
previousPageRoute,VARCHAR
accessPlatform,VARCHAR
browser,VARCHAR
browserVersion,VARCHAR
ApiInterface,VARCHAR
ApiInterfaceType,VARCHAR
RequestDuration,DECIMAL
RequestResult,VARCHAR
NetworkSystem,VARCHAR
taskExecuteTimeValue,VARCHAR
email,VARCHAR
telephone,VARCHAR
TenantStatus,INT
contacts,VARCHAR
TenantEmail,VARCHAR
userStatus,INT
ora_instance_number,INT
ora_instance_name,VARCHAR
ora_host_name,VARCHAR
ora_version,VARCHAR
ora_startup_time,VARCHAR
ora_status,VARCHAR
ora_archiver,VARCHAR
ora_databasr_status,VARCHAR
ora_instance_role,VARCHAR
ora_instance_mode,VARCHAR
ora_log_mode,VARCHAR
ora_open_mode,VARCHAR
software_info_str,VARCHAR
ora_name,VARCHAR
ora_total,INT
ora_used,DECIMAL
ora_free,DECIMAL
ora_pctused,DECIMAL
ora_num,INT
ora_type,INT
ora_value,VARCHAR
ora_display_value,VARCHAR
ora_default_value,VARCHAR
ora_isdefault,VARCHAR
ora_isbasic,VARCHAR
ora_description,VARCHAR
ora_resource_name,VARCHAR
ora_current_utilization,INT
ora_max_utilization,INT
ora_limit_value,VARCHAR
ora_inittal_allocation,VARCHAR
ora_file_name,VARCHAR
ora_block_size,INT
ora_file_size,INT
ora_rate,DECIMAL
ora_recovery_dest_file,VARCHAR
ora_group,INT
ora_member,VARCHAR
ora_bytessm,INT
ora_blocksize,INT
ora_archived,VARCHAR
ora_space_directory,VARCHAR
ora_sapce_limit,BIGINT
ora_space_used,BIGINT
ora_sapce_reclaimable,BIGINT
ora_number_of_files,INT
ora_owner,VARCHAR
ora_tablespace_name,VARCHAR
ora_segment_type,VARCHAR
ora_segment_name,VARCHAR
ora_bytesg,DECIMAL
ora_object_name,VARCHAR
ora_object_type,VARCHAR
ora_created,VARCHAR
ora_last_ddl_time,VARCHAR
ora_datetime,VARCHAR
ora_cnt,INT
ora_logtype,VARCHAR
e10_task_createdate,VARCHAR
e10_task_lastmodifieddate,VARCHAR
e10_task_id,VARCHAR
e10_task_code,VARCHAR
e10_task_subcode,VARCHAR
e10_task_displayname,VARCHAR
e10_task_executeengine,VARCHAR
e10_task_executetypekey,VARCHAR
e10_task_executeparameter,VARCHAR
e10_task_executeparameters,VARCHAR
e10_task_executeresult,VARCHAR
e10_task_executeresultid,VARCHAR
e10_task_status,VARCHAR
e10_task_priority,INT
e10_task_description,VARCHAR
e10_task_tsktime,VARCHAR
e10_task_outtime,VARCHAR
e10_task_starttime,VARCHAR
e10_task_finishtime,VARCHAR
e10_task_jobname,VARCHAR
e10_task_isvalid,INT
e10_task_percent,INT
e10_task_period_isdayperiod,INT
e10_task_ismonthperiod,INT
e10_task_period_day,VARCHAR
e10_task_isweekperiod,INT
e10_task_period_week,VARCHAR
e10_task_period_month,VARCHAR
e10_task_period_time,VARCHAR
e10_task_period_interval,INT
e10_task_period_runtimes,INT
e10_task_period_isexistedouttime,INT
e10_task_period_weekinmonth,VARCHAR
e10_task_message_messagetype,VARCHAR
e10_task_message_title,VARCHAR
e10_task_message_content,VARCHAR
e10_task_message_sender,VARCHAR
e10_task_message_recipient,VARCHAR
e10_task_message_cc,VARCHAR
e10_task_message_bcc,VARCHAR
e10_task_message_sendtime,VARCHAR
e10_task_message_receivetime,VARCHAR
e10_task_message_status,VARCHAR
e10_task_message_importantlevel,VARCHAR
e10_task_dispatch_transmittype,VARCHAR
e10_task_dispatch_documenttype,VARCHAR
e10_task_dispatch_iskeepacopy,INT
e10_task_dispatch_receivers,VARCHAR
e10_task_programname_chs,VARCHAR
e10_task_programname_cht,VARCHAR
e10_task_tasktype,VARCHAR
e10_task_source,VARCHAR
e10_task_programname_enu,VARCHAR
e10_task_period_isminuteperiod,INT
e10_task_period_ishourperiod,INT
e10_task_executestrategy,VARCHAR
e10_task_schedulingmode,VARCHAR
tbs_type,VARCHAR
lock_cnt,INT
customerServiceCode,VARCHAR
moduleName,VARCHAR
moduleStatus,INT
startDate,DATE
endDate,DATE
className,VARCHAR
tenantSid,BIGINT
moduleId,BIGINT
sep,VARCHAR
use_dynamic_variable,BOOLEAN
characters,VARCHAR
place,ENUM
pre_sep,VARCHAR
stateSchedule,VARCHAR
scheduleCheckDetails,VARCHAR
backUpSize,VARCHAR
backUpCheck,VARCHAR
needMaintenance,BOOLEAN
username,VARCHAR
userid,INT
default_tablespace,VARCHAR
profile,VARCHAR
CrmId,VARCHAR
ProductCode,VARCHAR
SubmitWay,VARCHAR
IssueStatus,VARCHAR
SyncStatus,VARCHAR
eventId,INT
process,VARCHAR
processPath,VARCHAR
severity,VARCHAR
classification,VARCHAR
device,VARCHAR
ip,VARCHAR
macAddresses,VARCHAR
operatingSystem,VARCHAR
organization,VARCHAR
processType,VARCHAR
e10_op_log_type_key,VARCHAR
e10_op_log_status,VARCHAR
e10_op_log_id,VARCHAR
e10_op_log_operation,VARCHAR
e10_op_log_time,INT
e10_op_log_account_set_id,VARCHAR
win_perfmon_ignore_ne_counters,BOOLEAN
win_perfmon_group_measurements,BOOLEAN
enterpriseCode,INT
lastChangeDate,VARCHAR
scheduleCode,VARCHAR
scheduleDescription,VARCHAR
statusCode,VARCHAR
operationType,VARCHAR
schedulingStartTime,VARCHAR
scheduleEndTime,VARCHAR
specifyRunDate,VARCHAR
specifyRunTime,VARCHAR
typeOperationMonth,VARCHAR
operationMonth,VARCHAR
typeOperationWeek,VARCHAR
OperationWeek,VARCHAR
typeOperationDay,VARCHAR
runWeek,VARCHAR
runDay,VARCHAR
timePeriodType,VARCHAR
elapsedTime,VARCHAR
serialCode,INT
runJob,VARCHAR
win_perfmon_queries,TEXT
operatingSite,VARCHAR
taskRunnHost,VARCHAR
win_perfmon_sampling_interval,INT
win_perfmon_cumulative_sampling_times,INT
exec_cron,VARCHAR
exec_on_start,BOOLEAN
win_perfmon_add_timestamp,BOOLEAN
win_perfmon_timestamp_field_name,VARCHAR
e10_op_log_occur_time,VARCHAR
schedulingOperationCode,VARCHAR
scheduledRunningTime,VARCHAR
schedulingOperationMode,VARCHAR
schedulingOperationStatus,VARCHAR
runEndTime,VARCHAR
maximumRunningTime,VARCHAR
e10_module_id,VARCHAR
e10_module_name,VARCHAR
e10_module_concurrent_count,INT
e10_module_concurrent_count_total,INT
e10_module_concurrent_count_max,INT
tempnums,INT
objectname,VARCHAR
subobjectname,VARCHAR
objectid,BIGINT
dataobjectid,BIGINT
objecttype,VARCHAR
creattime,VARCHAR
lastddltime,VARCHAR
timstamp,VARCHAR
objectstatus,VARCHAR
namespce,INT
sessionkey,VARCHAR
usecode,VARCHAR
jobcode,VARCHAR
databasecode,VARCHAR
hostaddress,VARCHAR
starttime,VARCHAR
stoptime,VARCHAR
leavecode,VARCHAR
employeeno,VARCHAR
jobusenumber,VARCHAR
nouse,INT
initialoperatingsiteno,VARCHAR
jobpid,VARCHAR
parentranksessionkey,VARCHAR
useip,VARCHAR
agentemployeecode,VARCHAR
enterprisecode,INT
armedition,VARCHAR
rundate,VARCHAR
operationstagecode,VARCHAR
processingdata,VARCHAR
executor,VARCHAR
executioncompletiondatetime,VARCHAR
executionduration,INT
objectsize,DECIMAL
monitorid,VARCHAR
collecttime,VARCHAR
onlinenumber,INT
dateSizeMb,DECIMAL
checkMessages,VARCHAR
logsize,VARCHAR
e10_module_expiry_date,DATE
win_perfmon_refresh_category,BOOLEAN
field_key,VARCHAR
value_key,VARCHAR
merge_multi_value,BOOLEAN
e10_session_online_users,INT
e10_license_used_count,INT
instance,VARCHAR
base_key,VARCHAR
no_group_result_key,VARCHAR
process_each_data,BOOLEAN
e10_module_expiry_date_max,VARCHAR
category,VARCHAR
has_schedule,INT
reportId,VARCHAR
item,VARCHAR
unit,VARCHAR
current_value,DECIMAL
interval_days,INT
execute_error_return_content,TEXT
content,TEXT
package_return_content,TEXT
quote_content,BOOLEAN
url,VARCHAR
runtime,BIGINT
authorizUses,INT
totalNums,INT
expiresdate,VARCHAR
debitornot,VARCHAR
maintenancedate,VARCHAR
expErrCount,INT
oraErrCount,INT
fileDirectory,VARCHAR
whetherToExecute,BOOLEAN
program_name,VARCHAR
is_success,BOOLEAN
file_path,VARCHAR
PrintServer,VARCHAR
PrinterName,VARCHAR
UserName,VARCHAR
ComputerNames,VARCHAR
element_name,VARCHAR
element_value,VARCHAR
element_comment,VARCHAR
address,VARCHAR
PrinterStatus,VARCHAR
PrinterStatusText,VARCHAR
PrintDefault,VARCHAR
wf_jobid,VARCHAR
wf_subid,VARCHAR
wf_companyid,VARCHAR
wf_userid,VARCHAR
wf_usedalias,VARCHAR
wf_jobname,VARCHAR
wf_extname,VARCHAR
wf_gentype,INT
wf_status,VARCHAR
wf_progress,INT
wf_dtrequest,VARCHAR
wf_dtreceive,VARCHAR
wf_dtschedule,VARCHAR
wf_dtstart,VARCHAR
wf_dtfinish,VARCHAR
wf_style,VARCHAR
wf_processer,VARCHAR
file_ext,VARCHAR
file_version,VARCHAR
size_kb,DECIMAL
modify_time,VARCHAR
legal_copyright,VARCHAR
company,VARCHAR
spend_milliseconds,BIGINT
avgvalue,DECIMAL
maxvalue,INT
minvalue,INT
use_local_zoned_offset,BOOLEAN
ignore_layout_before_error,BOOLEAN
aiopskit_version,VARCHAR
escli_version,VARCHAR
host_name,VARCHAR
ip_address,VARCHAR
ip_v4_address,VARCHAR
ip_v6_address,VARCHAR
os_name,VARCHAR
device_type,VARCHAR
use_proxy,BOOLEAN
proxy_address,VARCHAR
proxy_port,VARCHAR
localUserName,VARCHAR
wf_priority,INT
integrate_code,VARCHAR
integrate_name,VARCHAR
integrate_item,VARCHAR
is_integrate_platform,VARCHAR
status_code,VARCHAR
dw_bl_kind,VARCHAR
dw_bl_type,VARCHAR
dw_bl_ip_address,VARCHAR
dw_bl_device,VARCHAR
dw_bl_browser,VARCHAR
dw_bl_browser_version,VARCHAR
dw_bl_os_version,VARCHAR
dw_bl_network_system,VARCHAR
dw_bl_traceid,VARCHAR
dw_bl_uid,VARCHAR
dw_bl_sid,VARCHAR
dw_bl_pv_id,VARCHAR
dw_bl_vp,VARCHAR
dw_bl_sr,VARCHAR
dw_bl_host,VARCHAR
dw_bl_hash,VARCHAR
dw_bl_protocol,VARCHAR
dw_bl_begin,DATETIME
dw_bl_custom_field,VARCHAR
dw_bl_error_type,VARCHAR
dw_bl_message,VARCHAR
dw_bl_file_name,VARCHAR
dw_bl_selector,VARCHAR
dw_bl_event_type,VARCHAR
dw_bl_url,VARCHAR
dw_bl_status,VARCHAR
dw_bl_status_text,VARCHAR
dw_bl_duration,BIGINT
dw_bl_async,VARCHAR
dw_bl_method,VARCHAR
dw_bl_params,VARCHAR
dw_bl_rtt,INT
dw_bl_empty_points,VARCHAR
dw_bl_first_paint,INT
dw_bl_first_content_paint,INT
dw_bl_first_meaningful_paint,INT
dw_bl_largest_contentful_paint,INT
dw_bl_connect_end,BIGINT
dw_bl_connect_start,BIGINT
dw_bl_dom_content_loaded_event_end,BIGINT
dw_bl_dom_content_loaded_event_start,BIGINT
dw_bl_dom_interactive,BIGINT
dw_bl_dom_loading,BIGINT
dw_bl_domain_lookup_end,BIGINT
dw_bl_domain_lookup_start,BIGINT
dw_bl_load_event_end,BIGINT
dw_bl_load_event_start,BIGINT
dw_bl_navigation_start,BIGINT
dw_bl_redirect_end,BIGINT
dw_bl_redirect_start,BIGINT
dw_bl_request_start,BIGINT
dw_bl_response_end,BIGINT
dw_bl_response_start,BIGINT
dw_bl_secure_connection_start,BIGINT
dw_bl_unload_event_end,BIGINT
dw_bl_unload_event_start,BIGINT
dw_bl_input_delay,BIGINT
dw_bl_start_time,BIGINT
dw_bl_route_type,VARCHAR
dw_bl_visited_page,VARCHAR
dw_bl_visited_page_route,VARCHAR
dw_bl_access_time,DATETIME
dw_bl_access_duration,BIGINT
dw_bl_leave_time,DATETIME
dw_bl_previous_page,VARCHAR
dw_bl_previous_page_route,VARCHAR
dw_bl_fetch_start,BIGINT
usedate,VARCHAR
sqlserver_version,VARCHAR
sessions_count,INT
BlockedProcesses,BIGINT
version_no,VARCHAR
version_level,VARCHAR
build_number,VARCHAR
num_date_system,VARCHAR
dw_bl_operate_type,VARCHAR
dw_bl_operate_content,VARCHAR
dw_bl_operate_result,VARCHAR
e10_doc_code,VARCHAR
e10_doc_name,VARCHAR
e10_doc_category,ENUM
e10_module,VARCHAR
e10_doc_no,VARCHAR
e10_doc_date,VARCHAR
e10_approve_date,VARCHAR
e10_approve_status,VARCHAR
e10_approve_period,INT
e10_is_make_up_order,INT
e10_is_not_timely_approve,INT
e10_max_last_modify_date,VARCHAR
e10_company_name,VARCHAR
e10_period,VARCHAR
e10_im_diff_date,INT
e10_ar_diff_date,INT
e10_ap_diff_date,INT
e10_cb_days,INT
e10_gl_diff_date,INT
e10_model_code,VARCHAR
e10_account_set,VARCHAR
sqlserver_biz_smp_id,VARCHAR
disk_fstype_enum,ENUM
state_desc,ENUM
user_access_desc,VARCHAR
recovery_model_desc,VARCHAR
db_unused_percent,DECIMAL
dw_bl_pid,VARCHAR
dw_bl_app,VARCHAR
dbNameValue,VARCHAR
dw_bl_eid,BIGINT
dw_bl_user_sid,BIGINT
e10_op_log_user,VARCHAR
dw_bl_dom_complete,BIGINT
delete_num,INT
num_rows,BIGINT
need_mb,DECIMAL
true_mb,DECIMAL
recover_mb,DECIMAL
object_owner,VARCHAR
info,VARCHAR
cost,BIGINT
os_bits,VARCHAR
test1,VARCHAR
test2,VARCHAR
test3,DECIMAL
t100_reality_deduction_date,VARCHAR
t100_doc_deduction_date,VARCHAR
t100_yyyy,VARCHAR
t100_mm,VARCHAR
t100_ent,INT
t100_site,VARCHAR
t100_ent_code,INT
t100_ent_language,VARCHAR
t100_ent_name,VARCHAR
t100_ent_status,VARCHAR
t100_account_set_code,VARCHAR
t100_account_set_language,VARCHAR
t100_account_set_name,VARCHAR
t100_account_set_status,VARCHAR
t100_comp,VARCHAR
t100_module,VARCHAR
t100_final_days,INT
t100_module_code,VARCHAR
t100_doc_code,INT
t100_doc_name,VARCHAR
t100_doc_no,VARCHAR
t100_create_date,VARCHAR
t100_doc_date,VARCHAR
t100_approve_status,VARCHAR
t100_is_make_up_order,INT
t100_max_last_modify_date,VARCHAR
userSid,BIGINT
t100_item,INT
t100_item_order,INT
t100_access_code,INT
t100_database,VARCHAR
tenantId,VARCHAR
tenantName,VARCHAR
taxCode,VARCHAR
tenantStatus,INT
registerPhone,VARCHAR
phone,VARCHAR
cellphone_prefix,VARCHAR
tenantModuleContractId,BIGINT
sid,BIGINT
tenantModuleContractStatus,INT
orderMethod,INT
openId,BIGINT
noticeEmail,VARCHAR
itemNo,VARCHAR
dataMonth,VARCHAR
tagId,BIGINT
tagValue,BIGINT
preValue,BIGINT
compareType,ENUM
compareValue,INT
tenantModuleContractTagValue,VARCHAR
aiopsAuthStatus,VARCHAR
deleteStatus,INT
db_display_name,VARCHAR
collecteddeviceIds,VARCHAR
servicecode,VARCHAR
customername,VARCHAR
db_unused,DECIMAL
pageId,BIGINT
firstMenu,VARCHAR
secondMenu,VARCHAR
thirdMenu,VARCHAR
pageName,VARCHAR
accessCnt,BIGINT
activeDayCnt,INT
os_boot_time,VARCHAR
os_up_time_value,BIGINT
os_boot_time_value,BIGINT
imm_system_health_stat,INT
imm_system_health_stat_name,VARCHAR
disk__free_sum,BIGINT
disk__total_sum,BIGINT
disk__used_sum,BIGINT
xcc_sys_health,VARCHAR
xcc_memory_health,VARCHAR
xcc_processors_health,VARCHAR
xcc_storage_health,VARCHAR
t100_nmck_apea_max_last_modify_date,VARCHAR
t100_apda_nmck_max_last_modify_date,VARCHAR
t100_docno,VARCHAR
t100_code,VARCHAR
t100_nmbc_max_last_modify_date,VARCHAR
t100_nmck_max_last_modify_date,VARCHAR
t100_language,VARCHAR
t100_nmch_max_last_modify_date,VARCHAR
t100_nmcq_max_last_modify_date,VARCHAR
t100_nmba_max_last_modify_date,VARCHAR
t100_apca_apea_max_last_modify_date,VARCHAR
t100_explain,VARCHAR
t100_currency,VARCHAR
t100_account_code,VARCHAR
t100_xrda_nmba_max_last_modify_date,VARCHAR
t100_nmbc_payment_max_last_modify_date,VARCHAR
t100_moddt,VARCHAR
t100_nmaa_max_last_modify_date,VARCHAR
t100_collect_last_modify_date,VARCHAR
t100_sum_days,INT
t100_doc_nums,INT
t100_is_timely_order,INT
IssueId,DECIMAL
ProductVersion,VARCHAR
contact_name,VARCHAR
contact_email,VARCHAR
contact_phone,VARCHAR
IssueDescription,VARCHAR
SubmitTime,DATETIME
ServiceId,VARCHAR
ServiceDepartment,VARCHAR
ServiceName,VARCHAR
workNo,VARCHAR
__version__,DATETIME
emergency,BOOLEAN
live_process,BOOLEAN
site,VARCHAR
machineRegion,VARCHAR
serviceRegion,VARCHAR
IssueClassification,VARCHAR
ErpSystemCode,VARCHAR
ProgramCode,VARCHAR
ProgramVersion,VARCHAR
total_work_hours,DECIMAL
OperateId,BIGINT
LoginId,VARCHAR
OperateType,VARCHAR
OperateContent,VARCHAR
OperateResult,VARCHAR
CurrentPage,VARCHAR
NextPage,VARCHAR
OperateTime,DATETIME
EndTime,DATETIME
ServiceRegion,VARCHAR
ConnectArea,VARCHAR
dataSource,VARCHAR
CustomerCode,VARCHAR
ProductCategory,VARCHAR
Sales,VARCHAR
ServiceStaffCode,VARCHAR
Consultant,VARCHAR
ContractStartDate,DATETIME
ContractExprityDate,DATETIME
ContractState,VARCHAR
IndustryCode,VARCHAR
AreaCode,VARCHAR
cust_level,VARCHAR
cust_borrowing_due_date,DATETIME
ProjectStatus,VARCHAR
service_department,VARCHAR
businessDepartmentCode,VARCHAR
businessDepartmentCodeACP,VARCHAR
serviceUnitType,VARCHAR
deliveryMode,VARCHAR
contractSource,VARCHAR
UserId,VARCHAR
wel_event_datas,VARCHAR
last_outcome_message,VARCHAR
os_install_date,VARCHAR
bios_manufacturer_version,VARCHAR
bios_release_date,VARCHAR
demo_api_method,VARCHAR
demo_api_headers,VARCHAR
demo_api_url,VARCHAR
demo_api_body_payload,VARCHAR
demo_api_response,VARCHAR
dmpPlanId,BIGINT
dmpPlanDate,DATE
triggerCount,BIGINT
completionCount,BIGINT
startTime,DATETIME
operateId,VARCHAR
operateType,VARCHAR
endTime,DATETIME
operateContent,VARCHAR
operateResult,VARCHAR
A01,VARCHAR
YYYYMM,VARCHAR
in_data,DECIMAL
disk__drive_type,INT
disk__drive_type_name,VARCHAR
disk_drive_type,BOOLEAN
event_log_clear_backlog,BOOLEAN
time_window_interval,VARCHAR
IRW_index,VARCHAR
IRW_YYYYMM,VARCHAR
IRW_data,DECIMAL
problem,VARCHAR
result,VARCHAR
help,BOOLEAN
flumeTimestamp,VARCHAR
IRW_ID,VARCHAR
param1,VARCHAR
param2,VARCHAR
param1Value,VARCHAR
param2Value,VARCHAR
source,VARCHAR
destination,VARCHAR
spend_time_ms,BIGINT
send_bytes,BIGINT
tenant_sync_source,VARCHAR
projectCode,VARCHAR
moduleCode,VARCHAR
purchaseDate,DATE
startYearMonth,VARCHAR
endYearMonth,VARCHAR
evaluation,INT
kbsearch,INT
Order_time,VARCHAR
TC001,VARCHAR
EM_Order_Time,VARCHAR
EM_Order_TC001,VARCHAR
EM_Order_TC002,VARCHAR
EM_Order_TC027,VARCHAR
EM_Order_last_modify_date,VARCHAR
EM_Order_TD001,VARCHAR
EM_Order_TD002,VARCHAR
EM_Order_TD003,VARCHAR
EM_Order_TD027,VARCHAR
EM_Order_TD013,VARCHAR
EM_Sales_Order_Time,VARCHAR
EM_Sales_Order_TG001,VARCHAR
EM_Sales_Order_TG003,VARCHAR
EM_Sales_Order_TG023,VARCHAR
EM_Sales_Order_last_modify_date,VARCHAR
EM_Sales_Order_TH001,VARCHAR
EM_Sales_Order_TH002,VARCHAR
EM_Sales_Order_TH013,VARCHAR
EM_Sales_Order_TH003,VARCHAR
EM_Sales_Order_TH015,VARCHAR
EM_Sales_Order_TH016,VARCHAR
EM_Temp_Order_Time,VARCHAR
EM_Temp_Order_TF001,VARCHAR
EM_Temp_Order_TF002,VARCHAR
EM_Temp_Order_TF003,VARCHAR
EM_Temp_Order_TF020,VARCHAR
EM_Temp_Order_last_modify_date,VARCHAR
EM_Temp_Order_TG001,VARCHAR
EM_Temp_Order_TG002,VARCHAR
EM_Temp_Order_TG003,VARCHAR
EM_Temp_Order_TG014,VARCHAR
EM_Temp_Order_TG015,VARCHAR
EM_Temp_Order_TG016,VARCHAR
EM_Temp_Order_TG018,VARCHAR
EM_Project_Time,VARCHAR
EM_Project_LD001,VARCHAR
EM_Project_LD002,VARCHAR
EM_Project_LD003,VARCHAR
EM_Project_LD004,DECIMAL
EM_Project_LD005,VARCHAR
EM_Project_LD006,VARCHAR
EM_Project_LD007,VARCHAR
EM_Project_LD008,VARCHAR
EM_Project_LD009,VARCHAR
EM_Project_LD010,DECIMAL
EM_Project_LD012,DECIMAL
customerFullNameCH,VARCHAR
customerFullNameEN,VARCHAR
EM_Sales_Order_TG002,VARCHAR
add_key_contain_special_key,BOOLEAN
SystemManufacturer,VARCHAR
SystemProductName,VARCHAR
SystemFamily,VARCHAR
ServerName,VARCHAR
dw_bl_source_url,VARCHAR
EM_Order_TC003,DECIMAL
EM_Sales_Order_TH014,VARCHAR
EM_Project_last_modify_date,VARCHAR
DATA,VARCHAR
snmp_ups_battery_status,INT
snmp_ups_output_source,DECIMAL
snmp_ups_alarms_present,INT
snmp_ups_battery_minutesremaining,INT
is_customerNum,VARCHAR
is_customerName,VARCHAR
is_mail,VARCHAR
is_invitePeople,VARCHAR
is_inviteTime,DATETIME
is_activityNum,VARCHAR
is_activityName,VARCHAR
IRW_data1,VARCHAR
IRW_data2,VARCHAR
IRW_data3,VARCHAR
IRW_data4,DECIMAL
IRW_data5,DECIMAL
IRW_data6,DECIMAL
CR_ID,VARCHAR
CR_FW,VARCHAR
CR_data,DECIMAL
CR_DATE,VARCHAR
CR_TYPE,VARCHAR
CR_data1,DECIMAL
CR_data2,DECIMAL
CR_NUM,VARCHAR
interval_occur_count,INT
xcc_collect_address,VARCHAR