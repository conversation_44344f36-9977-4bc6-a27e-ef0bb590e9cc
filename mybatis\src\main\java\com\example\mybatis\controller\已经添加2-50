2024-10-12T11:30:11.618+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:115313829524032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e61bac1ad62a52d5b7c825db69f3ea89&deviceId=528236118840194115
2024-10-12T11:30:11.741+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0d3281f780c7bbfbb090c15d3b42529d\\\",\\\"dbIdValue\\\":\\\"0d3281f780c7bbfbb090c15d3b42529d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SZJTNEW\\\",\\\"targetValue\\\":\\\"SZJTNEW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503b117c1d02ba52a5852df8c765d66a1440ff51b2c2a1d3a820964cd90482c0c45f4c9911620a27f971d7ef3a9e02cb1395882bea2ef760d4c13f68396214cfc41a115c29a717b5ccdfff470f0e372859eca62837e8ab282a7a7d38ff166addcb29447d99a2ec7ef89a88d4e2c68fe587859cfa094f34a777a7130937781c88671b2847bc0af08b3d81f57d8304b8a60dd3728c2c522950fc6a847ed6960ccc4b4dc5827e2cfd7d2aeda814bd99487e8481304dbb4bdbf9d167d3a9dfa8dc52451434965465e849f566539496038dda90fdea5f085fb46cf3d329967f20310062d630d87ca60e06fba86987d020e9a40ed527c052e31a06121","collectName":"E10附件数据采集","accId":779544910123584,"adimId":762101397520961,"id":779799008412224,"adcId":762100883616320,"execParamsVersion":"1ae13fc0808debd74f82addba69582c7","aiId":762101397361218,"isEnable":1},"paramsMap":{"deviceId":"528236118840194115","eid":115313829524032,"aiId":762101397361218,"execParamsDbAiId":762100883276352,"execParamsDbId":"0d3281f780c7bbfbb090c15d3b42529d"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799008412224 acc incomplete","batchId":779799010071104,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528236118840194115
2024-10-12T11:30:19.108+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:116736969040448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8f20452f81ffc744a4fbe6c9de203b9f&deviceId=528094943449722937
2024-10-12T11:30:19.108+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ccb530744523d4c932f2fd0344698e54\\\",\\\"dbIdValue\\\":\\\"ccb530744523d4c932f2fd0344698e54\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   528094943449722937
2024-10-12T11:30:20.466+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:129432821494336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0b0c266eaa1d9179ac779483f5946a73&deviceId=528091567722869061
2024-10-12T11:30:20.467+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b9fd385231e1ad748063f6d0be70a2fb\\\",\\\"dbIdValue\\\":\\\"b9fd385231e1ad748063f6d0be70a2fb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"terrui\\\",\\\"targetValue\\\":\\\"terrui\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503090247a793faf7d3958e3d1814478f8349375f9ecfcfbdd80bafadece35d3a83e44e2073f1860a02899824445e445880c978e91821379838d62f78efdc6addba80dabc8b50477763a5a18bb20a967818d37aacd9e1813fa6f15d029b152e66dc6bfa36e785006bd6b78f46561e46d3bba39d19a13672209d8b2e31f27e3f4049c52aaa39682d91ffbe91d0b35eee8ef462117a51c35c9338555194ebd0f163492c4341dec7ac64e061475c09fffbbc038846c4f7ba846b510c756351dc44e6c112540f7719f4559b1712743769294ec7fde17e2ca0fd7553d15e02b5c3add905aede4cc4c0a74494798cfcbbc6b1cbc1e08871565d2fddcb","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761753218589249,"id":779799027425856,"adcId":761751905247808,"execParamsVersion":"02dcf8440fb236465180112a868bf7ec","aiId":761753218470467,"isEnable":1},"paramsMap":{"deviceId":"528091567722869061","eid":129432821494336,"aiId":761753218470467,"execParamsDbAiId":761753018962496,"execParamsDbId":"b9fd385231e1ad748063f6d0be70a2fb"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799027425856 acc incomplete","batchId":779799028609600,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528091567722869061
2024-10-12T11:30:21.888+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:488620427575872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0b5b05daaa682c577042dd8219b7bfc8&deviceId=527938935842686516
2024-10-12T11:30:21.889+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"268a7cb2a37e4e4e62bd5a3b2cc2fe24\\\",\\\"dbIdValue\\\":\\\"268a7cb2a37e4e4e62bd5a3b2cc2fe24\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XAZDJ\\\",\\\"targetValue\\\":\\\"XAZDJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527938935842686516
2024-10-12T11:30:23.237+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322213179968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=51d47a3dd48e200e88ffcc1299f49a88&deviceId=527835710514804033
2024-10-12T11:30:23.238+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f7603b68cf09d9096071fcc274e33f7b\\\",\\\"dbIdValue\\\":\\\"f7603b68cf09d9096071fcc274e33f7b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AQHT\\\",\\\"targetValue\\\":\\\"AQHT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503363d1e8d995105eb79ecef3d084dbd79090902c0ee4d8204b45ae531f8e75e666ca401373cbc0cbe94fdc7a8b060c7c09da4a033f085b1fd3e6224dc46cfe31e5e330cb6b8ca2ae0e2042b8305d53c41f70147a66b4cba24bcff60156899c6e6a9a22448b43f4a9eccc47908fde00f50bc2f6c7010b99b0e94b1599ed37d257a2068ccd317a1c3875a82155e2d57e9eba0f5077389bee0421e933225e7cb1e701c0e34f8fe7d968fcd4b9eb26b7d9cd175108e0e39e2c3a3bc888b8fdde0fe37237aa609eeb3bf4957bbff9016368fd46408e9696d2b456e137a16b6b94598738d724b02f00d340105e715ac571e70b8","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761125437301314,"id":779799038800448,"adcId":761124833485376,"execParamsVersion":"8a7db592d9abb8b3597b9a60094321ed","aiId":761125436150336,"isEnable":1},"paramsMap":{"deviceId":"527835710514804033","eid":41322213179968,"aiId":761125436150336,"execParamsDbAiId":761124832461376,"execParamsDbId":"f7603b68cf09d9096071fcc274e33f7b"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799038800448 acc incomplete","batchId":779799039955520,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527835710514804033
2024-10-12T11:30:24.685+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:325015457452608 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c7909b7cfe067eedfa97306ecacae376&deviceId=527832622517204035
2024-10-12T11:30:24.687+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c48c474fea8dff8b980f5fa29fe29dc7\\\",\\\"dbIdValue\\\":\\\"c48c474fea8dff8b980f5fa29fe29dc7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"QIAOBO\\\",\\\"targetValue\\\":\\\"QIAOBO\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850388084054ba88f106c7361015657cdd49c8ac3dca64a241832247d62b74705b4983ffcc7ab08960847f0799bfb2ec044bd682925efec50171de0861057e7fcc14ebc6e3807a38c01b496e72ecd0b787123980a684d7b957dbe0132ce8962f492c06c1ba18d99181174e650abc1b7b56bcaf8395ad266dfcfa55125a3948c953c3a05bdd7c35f31610a3dc975737403ed776c346833c9ad839fa41ffde865494379bcf936962b366e761188a0ee08799595a27ddf3159e01310a47c3b5b01067dd0617c2a7b18c53c59e7e3c483effdb7600d87b3668b0be7a290123c791e490745cdfa7dad91f33bc5a3fd408163d315a6b6dbe64fea53b3e","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761121733562946,"id":779799044600384,"adcId":761116542980672,"execParamsVersion":"84dd3cab10992f76b5e2ef62af1ece46","aiId":761121733362242,"isEnable":1},"paramsMap":{"deviceId":"527832622517204035","eid":325015457452608,"aiId":761121733362242,"execParamsDbAiId":761116542571072,"execParamsDbId":"c48c474fea8dff8b980f5fa29fe29dc7"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799044600384 acc incomplete","batchId":779799045755456,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527832622517204035
2024-10-12T11:30:26.076+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:348021075505728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7a37eb3bf5cbb6e831b0016860c57ace&deviceId=509855168175551555
2024-10-12T11:30:26.077+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c1871e5c19ea22827bbe32944d136b70\\\",\\\"dbIdValue\\\":\\\"c1871e5c19ea22827bbe32944d136b70\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850345826719eabca01adfc95fa2919c6a2e27590089f461f8f7e925b44a465782ab50dd5cb6c7f6bc0a9e6c831380df5beb15b3c269946f89535672610f2f69cfebec70c9455887720a2d77fd8d8252b49b5f3d75278fdf36b9d32fe38dda43bc3ab8de1893972d6fb1f56e1fbd510021bc54f74f9f2037b8ab1f2f4a1d09aa355581a72a3186874ab9108418b53943f49abbcdb4ec437325f7db419a1ff568b64d17e8871073dc8f5cf1cad5e27f262dc084f51029e11bd9baa200a4074b2d1e2b7267ba5700804e508487cbf9ca309220979b1a623ab5dab2069946008a87d350d6abbead3228e0ff61262431fb6350a1","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761092613272129,"id":779799050351168,"adcId":717226093810240,"execParamsVersion":"56c0cdf8c0a08663a55ef6b41c0809ce","aiId":761092613132866,"isEnable":1},"paramsMap":{"deviceId":"509855168175551555","eid":348021075505728,"aiId":761092613132866,"execParamsDbAiId":761092433625664,"execParamsDbId":"c1871e5c19ea22827bbe32944d136b70"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799050351168 acc incomplete","batchId":779799051461184,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   509855168175551555
2024-10-12T11:30:27.476+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:46260654977600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=38246a66eb4eb096b0020f21140dfe32&deviceId=527812600134644803
2024-10-12T11:30:27.477+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"76e77447aae7cd0a0a70a5361a76afc1\\\",\\\"dbIdValue\\\":\\\"76e77447aae7cd0a0a70a5361a76afc1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10-test\\\",\\\"targetValue\\\":\\\"E10-test\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850372f2e9d5e0b17494c0ceae9795c31de15f52cc25a67d528504ef0aedff132f708617f7f131a4e09e63ea20fd0678033cb3f9825600e9a5f9189d12d9881bc4fc299939435d3f8946abb91e3928c62573636dc51ff09bd24ca4889b64ebf8bf6489019213f4959d51daceb13c161da0afa46d03d9264baf0a901e60970349880a89655b449081c89c4af9b928d3b8f9bb114e71771151774aebd048553057f1b4b4f826430f5b230922563db57b48fe0d7905f6f5571cbecb7c624fa865f99acb6ba3e72f16b0380a5407b213989041dbfe4e1f93324a353abb007857a6063f0ff580d2b3d2d98c11897e0c33a00a2c597479711887805dd7","collectName":"E10附件数据采集","accId":779544910123584,"adimId":761076052869698,"id":779799056065088,"adcId":761067223593536,"execParamsVersion":"c36ae2b1e4f2a5f72e86fc98742c22fc","aiId":761076052685378,"isEnable":1},"paramsMap":{"deviceId":"527812600134644803","eid":46260654977600,"aiId":761076052685378,"execParamsDbAiId":761074760180288,"execParamsDbId":"76e77447aae7cd0a0a70a5361a76afc1"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799056065088 acc incomplete","batchId":779799057150528,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527812600134644803
2024-10-12T11:30:28.860+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:432667481489984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3123e7977cf0a444525479a62cf049c0&deviceId=527690140382937923
2024-10-12T11:30:28.861+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d64665df286a6d112b0065488f74f15a\\\",\\\"dbIdValue\\\":\\\"d64665df286a6d112b0065488f74f15a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"timzuu\\\",\\\"targetValue\\\":\\\"timzuu\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503963622f6c7ccf69f7b0b6a06727e232d43bdb519dc648e08cbb12897035d8fbb5b649243e7b474c10b5049ce5cdfa7e33be672a46fd986c7b6b2c7a8c488058ceec68c8416709942eabefd9f0cc2d09c98da5d99a3ef83481811a2e14723d9b96a28d0beaea6dfcbd9d0389d5ae2874386ddb072325da146bffe9ab9b901beeb977623dfd7367862ca9c21fb6e0b8dc877cf9977c882adeaa1f0fe7df3edd299f9957648f6c3fb85edfd7565048b073229ddcc6c3fe9ed01407de6a45a8930d016095fd103d0ae72cc736c35aaaf109bae3b7db8c73301a52d62bda127826128612d0bd9c22ba8043bbde7e274e5526a655b3782cc1c7147","collectName":"E10附件数据采集","accId":779544910123584,"adimId":760768315134529,"id":779799061733952,"adcId":760768135795264,"execParamsVersion":"4ada2277f0197710e3ff693c9cb0c282","aiId":760768314909252,"isEnable":1},"paramsMap":{"deviceId":"527690140382937923","eid":432667481489984,"aiId":760768314909252,"execParamsDbAiId":760768135455296,"execParamsDbId":"d64665df286a6d112b0065488f74f15a"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799061733952 acc incomplete","batchId":779799062835776,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527690140382937923
2024-10-12T11:30:30.282+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:476322661114432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5a5c69d133e0c7c8530d29e8a08dc49c&deviceId=527679908663539525
2024-10-12T11:30:30.283+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d79a5542df86273c655155dd2345bd39\\\",\\\"dbIdValue\\\":\\\"d79a5542df86273c655155dd2345bd39\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CY_Office1\\\",\\\"targetValue\\\":\\\"CY_Office1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527679908663539525
2024-10-12T11:30:31.619+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:360167685390912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c00953531936e7c52ede2afa3b55719a&deviceId=527653307112506692
2024-10-12T11:30:31.620+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a4caecc641ddf39d09bd790cc8e32205\\\",\\\"dbIdValue\\\":\\\"a4caecc641ddf39d09bd790cc8e32205\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_LPM\\\",\\\"targetValue\\\":\\\"E10_LPM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85032cf7815b64f99ee080c67d4d85540bfbf940e565987a11b70461b26adbbccc85a43f2e10a09ca11aaed2928810f67a69f5f643b98d53c873c43a87a39a76e7eae7c53793055cc056ee4015e35d1b0e8a45175f6db86233e55492c59f693f13a87c811e3b5801e7c6dce855c367a1e06bd26bf5a6e27af3d48b2b78edff9e17979380d27a7d3b0269bff44a3cc6db4721f1fb8f538a43019ef612b626bd22190c614f4df02afee47a6b67b8a9adad3c3584a5758f17a459066f198014b5c846865a427bda3fde64d94b57ff5568f9ecb075ffaaf6f23552f1e33c36a228833e61c1f1069e97ec56fc8ac046af18790640fb955aebb3ec51d6","collectName":"E10附件数据采集","accId":779544910123584,"adimId":760679440540225,"id":779799072977472,"adcId":760678019203648,"execParamsVersion":"f50c1314105906a480947e7cd62bab72","aiId":760679440360004,"isEnable":1},"paramsMap":{"deviceId":"527653307112506692","eid":360167685390912,"aiId":760679440360004,"execParamsDbAiId":760679131849280,"execParamsDbId":"a4caecc641ddf39d09bd790cc8e32205"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799072977472 acc incomplete","batchId":779799074030144,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527653307112506692
2024-10-12T11:30:33.202+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:351206084338240 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5726b2ab62188cd7f08be80ad81c46ec&deviceId=527240226351887427
2024-10-12T11:30:33.203+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9474183255d15393b4dcffefe4b9b180\\\",\\\"dbIdValue\\\":\\\"9474183255d15393b4dcffefe4b9b180\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDMS\\\",\\\"targetValue\\\":\\\"XDMS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527240226351887427
2024-10-12T11:30:34.584+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318218551872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e126be3606a5edd96646e81e943d8c75&deviceId=527217593669398576
2024-10-12T11:30:34.585+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f4d6517bc6bca89caaa69a96e561ba4c\\\",\\\"dbIdValue\\\":\\\"f4d6517bc6bca89caaa69a96e561ba4c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LRDATA\\\",\\\"targetValue\\\":\\\"LRDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527217593669398576
2024-10-12T11:30:35.966+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:412539743334976 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=94dd589036da7c7b8eb6743c6fa3ae7f&deviceId=527211587728060984
2024-10-12T11:30:35.966+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8bf507d5e2ac522300ce34e3f7b6fdad\\\",\\\"dbIdValue\\\":\\\"8bf507d5e2ac522300ce34e3f7b6fdad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RQ_NEW\\\",\\\"targetValue\\\":\\\"RQ_NEW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85030bf2c7318d181581cf4ba2d532dda9d36df6bf23a14a967d9e9a2d38823e98474ec1c95b79fee7cb960cb48ff49f7fcfc49e98906252f7835edcc9e214be31c8664a0ad978512b894ae48a4a4733135285df590b75f36240c8b76a7591349300c055c74762144a8a2ffaff4a21dadcd2629c3b8ecb7f574298165be71c4c63029de6c8e85a563aabec36ce9837a360622c5243537ed7a0ae8f103d56038750c7b221c6e365aaa65e4da480a65850b6c0f8a414aa8f79f6dc742012c538e323036df7a2ed6025fbecb7966170acf54de3008ab1710cefff1f4efb996a065e9ff46a7d4bc249118a00856b24a529ec39d32386aa3b2a22dd4d","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759599904305729,"id":779799090942528,"adcId":759599399076416,"execParamsVersion":"dfa64794a673fa18ac46efc1d3062123","aiId":759599904072259,"isEnable":1},"paramsMap":{"deviceId":"527211587728060984","eid":412539743334976,"aiId":759599904072259,"execParamsDbAiId":759599742956096,"execParamsDbId":"8bf507d5e2ac522300ce34e3f7b6fdad"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799090942528 acc incomplete","batchId":779799092048448,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527211587728060984
2024-10-12T11:30:37.422+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:497248312607296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=86199ddec11bb0696820d640c801bf32&deviceId=527211029566863414
2024-10-12T11:30:37.423+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ebe8b55540f7c3365fa8c929453e4711\\\",\\\"dbIdValue\\\":\\\"ebe8b55540f7c3365fa8c929453e4711\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSTnew\\\",\\\"targetValue\\\":\\\"HSTnew\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503f600e8c3e13d4bfd7396e2f41f7aa1fda17930964ce976005105ec06530b4b2c2964ec76252d83bf205eecfa5aea94465ebb4b63e3a9b0767f761e6fcd44e7db7c647c0f272ff1d7ceb8abeb080f11c73698ae5270ca4931c242a38510b6426cab016b0bfdfbb0b9509b874fc80747f9b4941e193518d2ff1640df3159cdf9e81f1d683587858573469db25bfad23229730d7640eff961b5d2ffa8988c7fc383df188bf80139a7f61f804100cb02865bde91740e9b481e4e13e520583954202cf047c60a5279c0c7dac042b31b3bbc06f31dd6e05b5094e94ce9bb862a7353f697388760cd4b5575baf71a9a1bda74f6704c810c372ddb37","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759598664962625,"id":779799096808000,"adcId":759598318371392,"execParamsVersion":"09a04e83cb8cbf07489b85fd3583a982","aiId":759598664766018,"isEnable":1},"paramsMap":{"deviceId":"527211029566863414","eid":497248312607296,"aiId":759598664766018,"execParamsDbAiId":759598318084672,"execParamsDbId":"ebe8b55540f7c3365fa8c929453e4711"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799096808000 acc incomplete","batchId":779799098090048,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527211029566863414
2024-10-12T11:30:38.853+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324871377472 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4db46121baa0a83a6387d883475c75da&deviceId=418649857692025656
2024-10-12T11:30:38.854+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2b8d4ce4cf534fa828753bfb48e2b768\\\",\\\"dbIdValue\\\":\\\"2b8d4ce4cf534fa828753bfb48e2b768\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MKdata\\\",\\\"targetValue\\\":\\\"MKdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503af7387e2b04913726a86a26dc1d7bed5a882c7b067883089753843883f326148b200434a6ea23da4a151cc36647e139b01d374096a50d6fc76a6aefb4dc74797f7e5a0b4e649d17f9e4e7c1b25bfe6274f6fdd840a6aedd2e99eaa5c3688d13b3df55698ca90d15ee02fe12e0c3697cc8a0d8fd9bb88d62e79e0863c4550663642836a6814a02813f606826025e644c1d75b54b279da2715b7d109f2efb62aa3b71e371f2cdb8177219b0c0099e8fb6716b55da5b911d29546479c9ba2adaf968d4951d1d294c1b20d72a82e44fb670d0b4a71b20bbffeb03f3295d25ce8c2316d901bc757d6b136407004b58684df5109baffd049e900fb","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759361583919681,"id":779799102771776,"adcId":494557453349440,"execParamsVersion":"56ca35361bb389f5aec9b40e7484858f","aiId":759361583583810,"isEnable":1},"paramsMap":{"deviceId":"418649857692025656","eid":41324871377472,"aiId":759361583583810,"execParamsDbAiId":494557425058368,"execParamsDbId":"2b8d4ce4cf534fa828753bfb48e2b768"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799102771776 acc incomplete","batchId":779799104406080,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   418649857692025656
2024-10-12T11:30:40.394+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:192213893222976 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e8d774fd7f54e69be455fe1dbf82bb3b&deviceId=510848891554378546
2024-10-12T11:30:40.394+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8dc68ee33bfd3b8c28445b253a8ce990\\\",\\\"dbIdValue\\\":\\\"8dc68ee33bfd3b8c28445b253a8ce990\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDEEN\\\",\\\"targetValue\\\":\\\"XDEEN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503d6dad08092f0bfbbcee9c15f2bc349d79ed9bb74e44d3682db8c1fe3b4ae709338c5d8b8d558ff8f37cd80d4584424f00663b74b76f01a5be37064a1f4c37cc2fc1d7b3de980bb95abe56e585254476fb0e74e00472190527cf970942b1faed9d16c4e916b8cbd489926b9ea1b1e6f3ca05255fad78561f0f7e777542e1980a3d2ce4d87be3d70a27a0a4e4d18c7ee8db07b4cbe1d129c0dadc55f50757e4fb6a6ef02e5bb3eb1609d7ed3232c1e74ea3673f3f18bc1824412cfbb9eb4dbbaaed03aecfdfebc084472b14167b682a9636fc353ddc1620886f7823b96d5b3c0671cf875ca49a1c91dd13b90e04482f2954a59edc9134221eb","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759331808211521,"id":779799108923968,"adcId":719651823374912,"execParamsVersion":"536aeba3a54bd1c472b374dff770e4ca","aiId":759331807986244,"isEnable":1},"paramsMap":{"deviceId":"510848891554378546","eid":192213893222976,"aiId":759331807986244,"execParamsDbAiId":759331644912192,"execParamsDbId":"8dc68ee33bfd3b8c28445b253a8ce990"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799108923968 acc incomplete","batchId":779799109890624,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   510848891554378546
2024-10-12T11:30:41.730+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:421357505851968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c07aa376bd8e3c4151df1c9142993701&deviceId=527101823832568899
2024-10-12T11:30:41.731+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"05518c1b3c67b73610cfec9a4110f117\\\",\\\"dbIdValue\\\":\\\"05518c1b3c67b73610cfec9a4110f117\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XM\\\",\\\"targetValue\\\":\\\"XM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85033ebd9995d56c58701772f8d4d5629a183a6ce50275ac405b30fdf8fe1d7517d68d68f861cfde01862b2b75ffb8d3fb8acef2d51c1b100f435eafd978ebfeaa5e692cd794a9a12c6a47839bc6a42d78e7430f2122dbfff81f4a59481000cfd1378f05f7e7de44980e700e7c53684e3f85e37b9beb9a71ba9c4461f8e234ab46f6dd9b027c7506f4983e6a12bd5ab2a7f3a5b1950134c4d70c7fde4e550221dd718173b9324c6bf99f9b0f39ddf13aa9249ea25370bb2edcca9103c679d6b2022666c0bc8be54efd188f32fb29659ef3db6f79978d0ebce016d0fca82684e6caa9193755cf3d8936b04d92d2a998f7cf37","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759331738440257,"id":779799114515008,"adcId":759331543691840,"execParamsVersion":"8ccbebd901f1f20811854615f0dac7b7","aiId":759331738325569,"isEnable":1},"paramsMap":{"deviceId":"527101823832568899","eid":421357505851968,"aiId":759331738325569,"execParamsDbAiId":759326052733504,"execParamsDbId":"05518c1b3c67b73610cfec9a4110f117"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799114515008 acc incomplete","batchId":779799115559488,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527101823832568899
2024-10-12T11:30:43.118+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:501059200619072 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=09da6add23a3343c311d6d89fca39f59&deviceId=518516571275015235
2024-10-12T11:30:43.118+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d0af9d694f6d733e60af659575871e30\\\",\\\"dbIdValue\\\":\\\"d0af9d694f6d733e60af659575871e30\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MYDATA\\\",\\\"targetValue\\\":\\\"MYDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503f8e98008754dc9a793925ff0842f1a9cff84847ff3937d9068e42fd195c8eb7a5d6a7accb8e4759bcc1839af1da0c0c1fea16ca42b912ab86b679ac09b10963d89d5bce08e86051d85a0f7f64a16c37e0d8459e6316c1ce81fefa7e765cbea83c571034b9002a1ae616f8c2919fb64b6e0a781f2c7097114f7ae1783dda700a109b0523d6b3a213b02298c5031dd83cc5ce8a217c835406dd5fe4dd8d0b74fcee8be8aee23465d5327b12ae3640beee7400decddca2bb8fc17bc4a0210f98284b7637ba59c440688f426865d747d18f720f50ea00d286160d254268f6ac7d40ce3b6e5c5fa786182a388b882d0c58aeeda941a597f037933","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759320078103104,"id":779799120077376,"adcId":738372290413120,"execParamsVersion":"e3d92661c0a92b3b530fc928eecc3f23","aiId":759320077939265,"isEnable":1},"paramsMap":{"deviceId":"518516571275015235","eid":501059200619072,"aiId":759320077939265,"execParamsDbAiId":738372316738112,"execParamsDbId":"d0af9d694f6d733e60af659575871e30"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799120077376 acc incomplete","batchId":779799121162816,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   518516571275015235
2024-10-12T11:30:44.486+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:455486867960384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e1331a0defc423a613333f11477d094b&deviceId=520693830253560889
2024-10-12T11:30:44.487+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8d2549f0cd4a474f594a4d0df0fe7533\\\",\\\"dbIdValue\\\":\\\"8d2549f0cd4a474f594a4d0df0fe7533\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10DATA\\\",\\\"targetValue\\\":\\\"E10DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85030ffa4b0acb6562cbb34fc2b2deefa7eb50d5a75740785d63e7050bddd9d477d4821ef92a532113c43e5cfe61ef43ed80a63355c79bd5b51b88efaa16546e03ff152c28851f9a01fab44f9ca011f27ae133e67a0c7ef2ed3996ba439dbf5797cc0f6b8d2a4db5f93eb554a7021a9892b7296a9d67c8880182e8256cd75e9688382c62b398530d4090c47367b625beb2da5d08be42906c2218455419d6644bd3943e58bce88a5b85b2e4090874348a0056a6ded04bd051138a424a51cda05cd7414511b787165403b0b67481a9cc8377f0334f6541243a42e1189e58093a63a8220f0e1d0f08c4c4915cebf7c591251d092bad8f47c4927b0f","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759307241976384,"id":779799125774912,"adcId":743687504376384,"execParamsVersion":"29dcd3290ba9982564ffae31e0a0e692","aiId":759307241828929,"isEnable":1},"paramsMap":{"deviceId":"520693830253560889","eid":455486867960384,"aiId":759307241828929,"execParamsDbAiId":743687565267520,"execParamsDbId":"8d2549f0cd4a474f594a4d0df0fe7533"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799125774912 acc incomplete","batchId":779799126893120,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   520693830253560889
2024-10-12T11:30:45.901+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:453584565600832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=04821283571ddac38bf96237c08396ed&deviceId=527078832134439473
2024-10-12T11:30:45.902+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"52c870457b84876cea04917d2c634bcd\\\",\\\"dbIdValue\\\":\\\"52c870457b84876cea04917d2c634bcd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_gy\\\",\\\"targetValue\\\":\\\"E10_gy\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503361bb4749e5457eaec48b02bc11fd410bf48d33b9ef42f411a08328239f4bf9696b33a070f9f4860f9c8f6601696ffa4fda8e301f2aa1bb7d9e1d69c5b92aa4bd63f36f5181a80d3f675c9d68811025744a2d93f7f17f688ab491de5d37f788be96e0b6d936356bd722c2ae52b559aee76a7c1f1bc270346adf9ddc9b588c7c0e41209ff13d6e84b014f0cb7e82a55b7cd183a4b529e8f5b421c13eae3b1f589e1558bcd0bb08930b3108d999ef6d71a1a5d761070db108bdcc6736d24dbfca8e44688a9e240e71df4829a9eda0f0b3188db2b62bcb38462889f607120433fbe97a86e03c34c4adfcc005b339b523b91de6dce39fea07aed","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759276018901570,"id":779799131497024,"adcId":759275881828928,"execParamsVersion":"bd29c25e9c8138f73eef8d02a4cc0f86","aiId":759276018782785,"isEnable":1},"paramsMap":{"deviceId":"527078832134439473","eid":453584565600832,"aiId":759276018782785,"execParamsDbAiId":759274249790016,"execParamsDbId":"52c870457b84876cea04917d2c634bcd"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799131497024 acc incomplete","batchId":779799132549696,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   527078832134439473
2024-10-12T11:30:47.258+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324433769024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b976e271d331fb3b73872a19d6bafdb3&deviceId=527067902516408375
2024-10-12T11:30:47.258+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7cd1e091a32626b6fe00c273c92eb29a\\\",\\\"dbIdValue\\\":\\\"7cd1e091a32626b6fe00c273c92eb29a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KLD\\\",\\\"targetValue\\\":\\\"KLD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527067902516408375
2024-10-12T11:30:48.660+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318982160960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b18edcc01ebf3bde409382241b8b64c9&deviceId=527071971813371956
2024-10-12T11:30:48.661+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2adc4ea2e43b6501849ba0e6b6ef1de8\\\",\\\"dbIdValue\\\":\\\"2adc4ea2e43b6501849ba0e6b6ef1de8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HD\\\",\\\"targetValue\\\":\\\"HD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   527071971813371956
2024-10-12T11:30:50.016+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322677633600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dab4d1829e4a95609f5633db5915a65b&deviceId=408968408516801603
2024-10-12T11:30:50.016+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e9c0823d4c7d7553caf241836654e905\\\",\\\"dbIdValue\\\":\\\"e9c0823d4c7d7553caf241836654e905\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Tuness\\\",\\\"targetValue\\\":\\\"Tuness\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503d9d9f1773eda3e1ac64629993e3db56cde1ecadc5ee69e8ecec58357b23cc7db2f40ef300e9a3dfe9c8479f3df6c174fd73f44242c0a0857f7aec4823268968c3f2a34044092cfcf7ba8908fc88f0822d877ba07e841a51e83de28358f0683d77f85b44b1736272716acb94788b228b3225d58f1eddfae5703cd85d9c9a508e40f873c6e122131fb448bf0e24ac3bd3eec2d0d761aa6e44e6111ff2e24dadbc46d197baa33e2629460d518b14f193b5988712740871355b6afab16433c34a485ce2cfeb7d46821bdf9fda0654a9960f5e787d2fe0462aff3d4486277647fd658ba734f20293f41205bd33319976160ab6febf4cef9ca9727","collectName":"E10附件数据采集","accId":779544910123584,"adimId":759661491606081,"id":779799148360256,"adcId":470920365937216,"execParamsVersion":"3674a428b11fea9db126234d9c18479c","aiId":759254144377411,"isEnable":1},"paramsMap":{"deviceId":"408968408516801603","eid":41322677633600,"aiId":759254144377411,"execParamsDbAiId":470920150786624,"execParamsDbId":"e9c0823d4c7d7553caf241836654e905"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799148360256 acc incomplete","batchId":779799149437504,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   408968408516801603
2024-10-12T11:30:51.389+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:455000128533056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d410458dfb8d8acc7f58afe4c432fa38&deviceId=526961102970434617
2024-10-12T11:30:51.390+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d3bc9aed6c1980ba4bf641140f7218a0\\\",\\\"dbIdValue\\\":\\\"d3bc9aed6c1980ba4bf641140f7218a0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJCC\\\",\\\"targetValue\\\":\\\"ZJCC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526961102970434617
2024-10-12T11:30:52.848+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:318377744618048 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=899c4f3f999a8d9edbe0889821413cc4&deviceId=526948711033485378
2024-10-12T11:30:52.849+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ec376a3341dc8c2160c580363a8de8f4\\\",\\\"dbIdValue\\\":\\\"ec376a3341dc8c2160c580363a8de8f4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LG\\\",\\\"targetValue\\\":\\\"LG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526948711033485378
2024-10-12T11:30:54.193+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:213697382179392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=263abf30e79e6709dd7ff6c9800cc262&deviceId=526929029127746627
2024-10-12T11:30:54.193+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"db8283922d7a8555550845fe82d6bece\\\",\\\"dbIdValue\\\":\\\"db8283922d7a8555550845fe82d6bece\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TY01\\\",\\\"targetValue\\\":\\\"TY01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526929029127746627
2024-10-12T11:30:55.550+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:352728313410112 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7b937ba9171b2e0376c3e876d4bf07d2&deviceId=526814187993707587
2024-10-12T11:30:55.551+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f9a26688add64b34b227907596526929\\\",\\\"dbIdValue\\\":\\\"f9a26688add64b34b227907596526929\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1_SH\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1_SH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526814187993707587
2024-10-12T11:30:56.914+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:342800238953024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2cdfbb5b0d8c28d30c5da2358befe18c&deviceId=526808186749989955
2024-10-12T11:30:56.915+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fac7f85ca8ae67a6719cbeabec0394ba\\\",\\\"dbIdValue\\\":\\\"fac7f85ca8ae67a6719cbeabec0394ba\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HX\\\",\\\"targetValue\\\":\\\"HX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85031e1dafeee8e5ce2f77326b978b652155cd30ef9cb8d0fdcad76feb7bc65b9b99d5ce8173c5efb1aaeefb4d1386cd3968b1c790dc9f0d1170990b22400e5d9fd5302056c15cd9518e01cce516a50232fcf005ec6c3aacf794fa50846b7775dedcf965920f40293f70c010aff6fe4f0bfcb5c2f424f2bcc53456f862fad8b7a94e059c81a5ceb687d82d10cb19bd022e7ec68828a5134b0138720df1ef43992b59eae0d28becd3bbb2d9ee771d8ab7760f16a026ec05761039f68e90ff14d354634afcdba5b11141d881e32aca05a60135ea115444f9586e0c98bb5a6ec55a2ad1823fcb18e3c9c804fcf38c6feac4f9e0","collectName":"E10附件数据采集","accId":779544910123584,"adimId":758618441491009,"id":779799176684096,"adcId":758614431912512,"execParamsVersion":"f3560ab06d9c8e7615527ade8424a0b1","aiId":758618441331268,"isEnable":1},"paramsMap":{"deviceId":"526808186749989955","eid":342800238953024,"aiId":758618441331268,"execParamsDbAiId":758615591453248,"execParamsDbId":"fac7f85ca8ae67a6719cbeabec0394ba"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799176684096 acc incomplete","batchId":779799177704000,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   526808186749989955
2024-10-12T11:30:58.286+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:322624359711296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d4a954e1a6ad763ace87c88e8ef485b5&deviceId=526801462760584259
2024-10-12T11:30:58.287+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4f402096d7aab88e25e73e818f8c9b82\\\",\\\"dbIdValue\\\":\\\"4f402096d7aab88e25e73e818f8c9b82\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JIANGLANG\\\",\\\"targetValue\\\":\\\"JIANGLANG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85038b334693bcfef39479a5ebf1b39d9a0b37a38a9d47fb8b3e58bb56712d5848778a924cd2f23a30d054168dac83ee5489fc47e13d5be620ac046c6806a0f6e4fa9daee84eaf6bc4a950112bc5860f4b8c0f7b805078874e7488eb187ff872437181ce0ffb1534767411dbd946e2764e22242f563a6ac33b176cb004493b4896df7f7bb10b7c97a5787f206cc36a97871269c5d07feb62c8091ed5a145a5bae0315e9f9cb57bd79f0db535af6ba7f7f390f161a4439d3c72083265eb1934b9570978d60ebc62aafaa97e6c2ef8e03bfdacba497f3a844155c3ce0d6830948a88679c4b565fdc37d98bf6c9aba702e50b305324be65a97381c428467206c0fdbcec","collectName":"E10附件数据采集","accId":779544910123584,"adimId":758598775382594,"id":779799182275136,"adcId":758598574576192,"execParamsVersion":"823d85d96ad0d0ae4abea8cfe71d5b7d","aiId":758598775173699,"isEnable":1},"paramsMap":{"deviceId":"526801462760584259","eid":322624359711296,"aiId":758598775173699,"execParamsDbAiId":758598574219840,"execParamsDbId":"4f402096d7aab88e25e73e818f8c9b82"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799182275136 acc incomplete","batchId":779799183258176,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   526801462760584259
2024-10-12T11:30:59.637+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:432413170586176 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=610bff93e6e15d14256787c3da1735ad&deviceId=526674595349017667
2024-10-12T11:30:59.639+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"00e51d1921e7087b2bbccb227f8beb9d\\\",\\\"dbIdValue\\\":\\\"00e51d1921e7087b2bbccb227f8beb9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"sndata\\\",\\\"targetValue\\\":\\\"sndata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526674595349017667
2024-10-12T11:31:01.033+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321174889024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=29a43ee065458d42310adb9be39ee449&deviceId=526673594856518979
2024-10-12T11:31:01.033+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"888a0dfc84611c78ddeefbc2a0ccd0e1\\\",\\\"dbIdValue\\\":\\\"888a0dfc84611c78ddeefbc2a0ccd0e1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CJDATA\\\",\\\"targetValue\\\":\\\"CJDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526673594856518979
2024-10-12T11:31:02.432+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:313696285348416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b1b998db2214066db17e5ddf9d9484ba&deviceId=505359239762425155
2024-10-12T11:31:02.432+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"540101eb1fcd0c1f481f934a55580574\\\",\\\"dbIdValue\\\":\\\"540101eb1fcd0c1f481f934a55580574\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XC\\\",\\\"targetValue\\\":\\\"XC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505359239762425155
2024-10-12T11:31:03.803+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:396990726627904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eff5abd59d34700b50070fe30ea605b2&deviceId=526662749963433027
2024-10-12T11:31:03.803+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9d0a2a6b34c6f93cfbb8fc7b4a0ccbed\\\",\\\"dbIdValue\\\":\\\"9d0a2a6b34c6f93cfbb8fc7b4a0ccbed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TBTL_DATA\\\",\\\"targetValue\\\":\\\"TBTL_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526662749963433027
2024-10-12T11:31:05.143+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322050449984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=722911a60bad57b2b4fe29807a7c8851&deviceId=526660081849218370
2024-10-12T11:31:05.144+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1d83a88a154800683d8a84f10c6bb4d4\\\",\\\"dbIdValue\\\":\\\"1d83a88a154800683d8a84f10c6bb4d4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"tsmd\\\",\\\"targetValue\\\":\\\"tsmd\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526660081849218370
2024-10-12T11:31:06.475+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318931374656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=22592bddaac05548a4baa789864763c8&deviceId=526637775835903027
2024-10-12T11:31:06.476+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"796670b2354bea1f6b2f5b59c2a920c1\\\",\\\"dbIdValue\\\":\\\"796670b2354bea1f6b2f5b59c2a920c1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XM\\\",\\\"targetValue\\\":\\\"XM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526637775835903027
2024-10-12T11:31:07.856+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:556218123137600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4b0829011caa120234d49026de35836a&deviceId=526632016284763448
2024-10-12T11:31:07.857+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"403dbc44c298262427c9af6f184b9acd\\\",\\\"dbIdValue\\\":\\\"403dbc44c298262427c9af6f184b9acd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JCERP\\\",\\\"targetValue\\\":\\\"JCERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526632016284763448
2024-10-12T11:31:09.266+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:615399660900928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=531c8e3815a6b129dcb053bd10f22c56&deviceId=526239189516170547
2024-10-12T11:31:09.267+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b44bd36593bbdb026d45cd69f7532ad6\\\",\\\"dbIdValue\\\":\\\"b44bd36593bbdb026d45cd69f7532ad6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KLYB888\\\",\\\"targetValue\\\":\\\"KLYB888\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526239189516170547
2024-10-12T11:31:10.678+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322053829184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=344228dc74c643f4560639fc37051532&deviceId=526237133166360627
2024-10-12T11:31:10.678+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1fa494d84e54120729a2345a23e70b00\\\",\\\"dbIdValue\\\":\\\"1fa494d84e54120729a2345a23e70b00\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LI ZHAN\\\",\\\"targetValue\\\":\\\"LI ZHAN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526237133166360627
2024-10-12T11:31:12.016+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322087400000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c7b3d019cf593dd947f8e2e4cb452ff8&deviceId=526237970517210179
2024-10-12T11:31:12.017+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c6d93863acbfdd54c1da009c11e6aa06\\\",\\\"dbIdValue\\\":\\\"c6d93863acbfdd54c1da009c11e6aa06\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10PSL2018\\\",\\\"targetValue\\\":\\\"E10PSL2018\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526237970517210179
2024-10-12T11:31:13.354+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:198790180278848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8b1dc7d4217953869611ab7ada81c9b0&deviceId=526224438031561795
2024-10-12T11:31:13.355+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3ccc6b9f763c66e797aeb6e8cb15743e\\\",\\\"dbIdValue\\\":\\\"3ccc6b9f763c66e797aeb6e8cb15743e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GZXXKJ1\\\",\\\"targetValue\\\":\\\"GZXXKJ1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526224438031561795
2024-10-12T11:31:14.707+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:247988408816192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=12fbf5475f217a5a06d99b0e7f5c279a&deviceId=526207057624314947
2024-10-12T11:31:14.708+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bf36c9342932754bd446aa44ecf72c5d\\\",\\\"dbIdValue\\\":\\\"bf36c9342932754bd446aa44ecf72c5d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_CISTA\\\",\\\"targetValue\\\":\\\"E10_CISTA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   526207057624314947
2024-10-12T11:31:16.073+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323477541440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=07918e988a7351d397dec09336ab2604&deviceId=525829226331386681
2024-10-12T11:31:16.074+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3a69988a65a7a38a7a68aec3df027cfa\\\",\\\"dbIdValue\\\":\\\"3a69988a65a7a38a7a68aec3df027cfa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SQL Server\\\",\\\"targetValue\\\":\\\"SQL Server\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503d12e64b52d4a6c3ea7a3ce65290355d9937046fbbee728ed253525732f0a74133baac6e370dd919cc4ece40375540f01d50003edac0446e5a7a96c40c0ffe961c7b37dcccf09acc45efe97bde651740dd614195ca5b1441b128b49f72e39799f775d72188ac2976b1b60bd784351ab14ffd3eecc08b007afb2898f2a0758f92f724217c12c235d2df0ca5b27fb56d5bc650e4d0d2c09a013f3d8ff0a5b5eea20b43f82f3c38326c42915f7fb525134c5c542b163b897468968c105565b3e5bb07a7f8b55f445f1e7d6756d76d42ff63db2a05c464ed23b10ac8235e3bc41d685e28a19ecf95f035ae57b1d3c484680a571e8566e91fd38bb1f7a9fa421c0b782","collectName":"E10附件数据采集","accId":779544910123584,"adimId":756858757382721,"id":779799255147072,"adcId":756788630041152,"execParamsVersion":"cf41cee81bf814091948772f9bc954a2","aiId":756858757124673,"isEnable":1},"paramsMap":{"deviceId":"525829226331386681","eid":99990000,"aiId":756858757124673},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779799255147072 acc incomplete","batchId":779799256199744,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   525829226331386681
2024-10-12T11:31:17.443+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:247988355576384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c0b7ee8b7cf05c4cc85b5217748219f9&deviceId=525940235331191605
2024-10-12T11:31:17.444+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0a4c633df2f4b3ead1b14bb6bd83ec6c\\\",\\\"dbIdValue\\\":\\\"0a4c633df2f4b3ead1b14bb6bd83ec6c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MH\\\",\\\"targetValue\\\":\\\"MH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525940235331191605
2024-10-12T11:31:18.782+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:390561719169600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f65bfded37761e5fb40689a80671dfb9&deviceId=525936810497422390
2024-10-12T11:31:18.782+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a1d69ab3c87d21603b600ecb826500fa\\\",\\\"dbIdValue\\\":\\\"a1d69ab3c87d21603b600ecb826500fa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525936810497422390
2024-10-12T11:31:20.149+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:509150014587456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2a8b03dc79d518c4a2f69daca7e17510&deviceId=525933420358939715
2024-10-12T11:31:20.149+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b72b971d385f12d87881a8fafdf16caa\\\",\\\"dbIdValue\\\":\\\"b72b971d385f12d87881a8fafdf16caa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WMZSK\\\",\\\"targetValue\\\":\\\"WMZSK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525933420358939715
2024-10-12T11:31:21.624+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:578956607812160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7d811fc406fddad36a2daa0b4396e033&deviceId=485196600080086083
2024-10-12T11:31:21.624+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"90ec38710ff62158afe3e39d829236f8\\\",\\\"dbIdValue\\\":\\\"90ec38710ff62158afe3e39d829236f8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485196600080086083
2024-10-12T11:31:22.974+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:441578402107968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=adc31eb52431784484376bf0de26a654&deviceId=525773122717102640
2024-10-12T11:31:22.974+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bb0ea9b0e406430cb6db00dedd4f31b4\\\",\\\"dbIdValue\\\":\\\"bb0ea9b0e406430cb6db00dedd4f31b4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_TaiFu\\\",\\\"targetValue\\\":\\\"E10_TaiFu\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525773122717102640
2024-10-12T11:31:24.343+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:535692432847424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7b0c41a197efc5e2404ba24f2b2c1c73&deviceId=525763967490998585
2024-10-12T11:31:24.344+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1d4f1bae81d3aaa5f81a23f32324a300\\\",\\\"dbIdValue\\\":\\\"1d4f1bae81d3aaa5f81a23f32324a300\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KAITONGDIANZI\\\",\\\"targetValue\\\":\\\"KAITONGDIANZI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525763967490998585
2024-10-12T11:31:25.805+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:509208992485952 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1a03ee08f87a9215cfd1fb0e38047e4e&deviceId=449837968383815737
2024-10-12T11:31:25.806+08:00  INFO 26568 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e29ff85004a4420650df0491e424825a\\\",\\\"dbIdValue\\\":\\\"e29ff85004a4420650df0491e424825a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SZLQ_ZSK2\\\",\\\"targetValue\\\":\\\"SZLQ_ZSK2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   449837968383815737
deviceIds:,'528236118840194115','528094943449722937','528091567722869061','527938935842686516','527835710514804033','527832622517204035','509855168175551555','527812600134644803','527690140382937923','527679908663539525','527653307112506692','527240226351887427','527217593669398576','527211587728060984','527211029566863414','418649857692025656','510848891554378546','527101823832568899','518516571275015235','520693830253560889','527078832134439473','527067902516408375','527071971813371956','408968408516801603','526961102970434617','526948711033485378','526929029127746627','526814187993707587','526808186749989955','526801462760584259','526674595349017667','526673594856518979','505359239762425155','526662749963433027','526660081849218370','526637775835903027','526632016284763448','526239189516170547','526237133166360627','526237970517210179','526224438031561795','526207057624314947','525829226331386681','525940235331191605','525936810497422390','525933420358939715','485196600080086083','525773122717102640','525763967490998585','449837968383815737'