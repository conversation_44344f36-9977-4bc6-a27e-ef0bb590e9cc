timely:
  # ????
  switch: true
  # ?????
  offset-file-name: D://DebeziumDataTest/offsets.dat
  # ????????????
  offset-file-clean: true
  # ??????? ??ms
  offset-time: 60000
  # ????????
  history-file-name: D://DebeziumDataTest/dbhistory.dat
  # ????????
  offline:
    ip: ************
    port: 3306
    username: root
    password: 123456
    # ?????????? instance-name  logic-name ????
    # ???
    instance-name: mysql-connector
    # ???
    logic-name: mysql-connector
    # ????
    include-table: dmp_tag_weight_mapping
    # ????
    include-db: dmp
server:
  port: 8081

spring:
  main:
    allow-circular-references: true
  datasource:
    url: *************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.jdbc.Driver