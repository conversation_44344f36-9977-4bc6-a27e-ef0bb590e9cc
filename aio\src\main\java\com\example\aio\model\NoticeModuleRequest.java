package com.example.aio.model;

import lombok.Data;

/**
 * 通知模块保存请求DTO
 */
@Data
public class NoticeModuleRequest {
    private Integer nmId;
    private Boolean specialEnabled;
    private String nwIds;
    private String nrlIds;
    private String email;
    private String authUserId;
    private Long authUserSid;
    private Long eid;

    public NoticeModuleRequest() {
    }

    public NoticeModuleRequest(Integer nmId, Boolean specialEnabled, String nwIds, String nrlIds,
                              String email, String authUserId, Long authUserSid, Long eid) {
        this.nmId = nmId;
        this.specialEnabled = specialEnabled;
        this.nwIds = nwIds;
        this.nrlIds = nrlIds;
        this.email = email;
        this.authUserId = authUserId;
        this.authUserSid = authUserSid;
        this.eid = eid;
    }

}
