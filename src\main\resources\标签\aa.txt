CREATE TABLE `tbb.aiops_ability_tag_score`(
  `eid` string,
  `OrderDeliveryScore` double,
  `BasicAbilityScore` double,
  `ProductionAbilityScore` double,
  `MaterialAbilityScore` double,
  `MonthlyAbilityScore` double,
  `InventoryAbilityScore` double)
COMMENT ''
ROW FORMAT SERDE
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
  'field.delim'='\t',
  'serialization.format'='\t')
STORED AS INPUTFORMAT
  'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'


SELECT
    id AS eid,
    CASE WHEN tagId = '754698248249920' THEN '基础应用能力'
     WHEN tagId = '754699437040192' THEN '订单交付管理能力'
     WHEN tagId = '758200593494592' THEN '生产制造管理能力'
     WHEN tagId = '758211379151424' THEN '物料准备管理能力'
     WHEN tagId = '758236385014336' THEN '月结核算管理能力'
     WHEN tagId = '758245165445696' THEN '存货周转管理能力'
end  as type,
    score,
    100 as totalScore
FROM AIEOM.tenant_tag_score
WHERE tagId IN (
                '754698248249920',
                '754699437040192',
                '758200593494592',
                '758211379151424',
                '758236385014336',
                '758245165445696'
    );

CREATE TABLE `tbb.aiops_ability_tag_score_v2`(
  `eid` string,
  `type` string,
  `score` decimal(20,5),
  `totalScore` decimal(20,5))
COMMENT ''
ROW FORMAT SERDE
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
  'field.delim'='\t',
  'serialization.format'='\t')
STORED AS INPUTFORMAT
  'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'

  beeline -d "com.cloudera.impala.jdbc41.Driver" -u "****************************" -e"
  INVALIDATE METADATA tbb.aiops_ability_tag_score_v2;
  "