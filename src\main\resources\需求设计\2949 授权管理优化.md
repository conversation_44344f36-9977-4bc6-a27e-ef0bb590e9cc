# 2949 【E/T】授权管理优化

### 1. 优化授权管理页面
   ```mermaid
   flowchart TD
      id0((开始))
    -->    id1["`获取**租户数据**`"] 
    --> id2["`同步租户合约数据`"] 
    --> id3["`找出**产品模组合约设定** 的模组数据`"]
    --> id5["`保存**租户模组合约数据**`"]
    --> id6["`保存**租户模组合约明细数据**`"]
    --> id7["`发送邮件给服务人员`"]
   ```
>- 147 产品线 无IT模组 同步 起止为合约起止 状态为已注册
>- 147 产品线 有IT模组 不同步 
1) 根据入参获取租户数据
2) 同步租户合约数据 从云管家同步到企业运维云 tc表
3) `supplier_product_module_contract_sync_setting` 表新增数据为 `147产品线` ，查询`租户合约`和`租户模组合约`数据
4) 修改 `/alltenant/cn` 找出`147产品线`没有运维模组合约 
   - 添加`IT运维模组` 状态为 `3已订阅`  
   - 服务人员入参传入  根据姓名 查数据 取`updateTime`正序第一笔
5) 保存租户模组合约明细数据
6) 同步成功之后 根据`147产品线` 找到服务人员 发送邮件 **邮件内容暂无 ?**
### 2. 工程开通 
>- **合同/出货是什么?**
1) 开通授权: 开通订阅模组授权功能与现在一致
   
      ```mermaid
      flowchart TD
       id0((开始))
      -->      id1["`服务人员登录客服端`"] 
      -->   id2["`进入新的开通订阅模组入口`"]
      -->   id3["`选择运维模组`"]
      -->   id6["`保存订阅模组`"]
      ```

2) 维护客户家管理员: 可以邀请客户家员工成为MIS 功能和现在客服端一致
      ```mermaid
      flowchart TD
       id0((开始))
      -->      id1["`服务人员登录客服端`"] 
      -->   id2["`进入新的租户人员管理入口`"]
      -->   id3["`选择对应租户 邀请用户加入`"]
      -->   id6["`保存用户人员`"]
      ```

### 3. 增加首次开通日期
   ```mermaid
   flowchart TD
    id0((开始))
      -->
        id1["`新增订阅模组`"] & id2["`修改订阅模组`"]
    --> id3["`保存合约订阅模组数据`"]
    --> id4["`保存合约订阅模组历史记录数据`"]
    --> id5["`计算订阅模组历史记录数据历史状态`"]
    --> id6["`更新订阅模组历史记录数据历史状态`"]
   ```
1) 找到新增订阅模组 和 修改订阅模组 的入口
2) 保存订阅模组数据
3) 订阅模组数据保存成功之后，保存订阅模组数据历史记录表 `tenant_module_contract_history`
4) 根据 订阅模组历史记录数据表的模组id和合约状态 计算该租户下 每个模组对应的历史状态
   - 首次新增订阅模组 时间
   - 首次试用 时间
   - 首次正式订阅 时间
   - 变更授权记录
   ```sql
   select moduleId,status,min(createTime) from tenant_module_contract_history
         where eid = 99990000
         group by moduleId,status
   order by moduleId asc
   ```
5) 更新历史表数据状态
   
   ```mermaid
   erDiagram
    tmc ||--o{ tenant_module_contract_history : contains
    tmc {
        bigint id PK
        bigint sid UK
        bigint eid UK
        bigint moduleId UK
        int status "1:试用中2:试用到期3:已订阅4:订阅到期5:年维服务6:年维到期"
        date startDate
        date endDate
    }

    tenant_module_contract_history {
        bigint id PK 
        bigint moduleContractId "tmcId" 
        string operateType "历史订阅状态：1首次新增订阅模组 2首次试用 3首次正式订阅 4变更授权记录"
        bigint moduleId 
        int status "1:试用中2:试用到期3:已订阅4:订阅到期5:年维服务6:年维到期"
        date startDate
        date endDate
        
    }


   ```
### 4. 增加维护记录
1) 找到 新增订阅模组 和 修改订阅模组 入口
2) 维护记录 记录到 租户模组合约历史表 `tenant_module_contract_history`








