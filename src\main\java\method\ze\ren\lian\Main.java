package method.ze.ren.lian;

public class Main {
    public static void main(String[] args) {
        int[][] pro = new int[][]{{1, 2}, {100, 3}, {102, 4}};
        int index = 0;
        for (int[] p : pro) {
            ProcessorChain processorChain = new ProcessorChain();
            processorChain.addProcessor(new LengthProcessor());
            processorChain.addProcessor(new WidthProcessor());
            Product product = new Product(p[0], p[1]);
            boolean process = processorChain.process(product, processorChain);
            System.out.println("产品" + index + "是否合格：" + process);
            index++;
        }
    }
}
