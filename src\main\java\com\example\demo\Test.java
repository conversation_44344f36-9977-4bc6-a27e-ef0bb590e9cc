package com.example.demo;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

public class Test {
    public static void main(String[] args) {
        String csvFile = "D:\\work\\demo\\src\\main\\resources\\data.csv"; // CSV文件路径
        String line = "";
        String csvSplitBy = "\t"; // 根据实际的分隔符进行调整，这里使用制表符作为分隔符
        boolean isFirstLine = true; // 用于跳过第一行的表头

        try (BufferedReader br = new BufferedReader(new FileReader(csvFile))) {
            while ((line = br.readLine()) != null) {
                if (isFirstLine) {
                    isFirstLine = false; // 跳过表头
                    continue;
                }
                // 使用分隔符分割行
                String[] data = line.split(csvSplitBy);

                // 创建INSERT语句
                StringBuilder insertQuery = new StringBuilder("INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES (");
                for (int i = 0; i < data.length; i++) {
                    insertQuery.append("'").append(data[i].replace("'", "''")).append("'");
                    if (i < data.length - 1) {
                        insertQuery.append(", ");
                    }
                }
                insertQuery.append(");");

                // 打印INSERT语句
                System.out.println(insertQuery.toString());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
