CREATE EXTERNAL TABLE `es_customerservice_v3_external` (
  `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
  `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
  `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
  `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
  `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
  `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
  `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
  `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
  `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
  `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
  `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
  `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
  `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
  `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "",
"database" = "aio-db",
"table" = "es_customerservice_v3"
);


CREATE TABLE `es_customerservice_v3_bak_20240805` (
                                         `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=OLAP
    DUPLICATE KEY(`grp_dpt_id`, `dpt_id`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`grp_dpt_id`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_manager_contact`, `dpt_id`, `dpt_manager_contact`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "true"
);

CREATE TABLE `es_customerservice_v3` (
                                         `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=OLAP
    DUPLICATE KEY(`grp_dpt_id`, `dpt_id`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`grp_dpt_id`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_manager_contact`, `dpt_id`, `dpt_manager_contact`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "true"
);

    insert into es_customerservice_v3_bak_20240805 select * from    es_customerservice_v3
    insert into es_customerservice_v3 select * from    es_customerservice_v3_external
CREATE EXTERNAL TABLE `mars_customerservice_external` (
  `CustomerServiceCode` varchar(50) NOT NULL,
  `ProductCode` varchar(50) NOT NULL,
  `ProductCategory` varchar(20),
  `ProductVersion` varchar(30),
  `Sales` varchar(20),
  `SalesContact` varchar(100),
  `HasOwnerService` int DEFAULT '1',
  `HasTextService` int DEFAULT '0',
  `HasOwnerIssueService` int DEFAULT '1',
  `ServiceStaffCode` varchar(50),
  `ServiceStaff` varchar(50),
  `ServiceStaffContact` varchar(100),
  `ServiceStaffQQ` varchar(50),
  `Consultant` varchar(50),
  `ConsultantContact` varchar(100),
  `ContractStartDate` varchar(50),
  `ContractExprityDate` varchar(50),
  `ContractState` varchar(50),
  `IndustryCode` varchar(50),
  `AreaCode` varchar(50),
  `HostAuthNum` int(11),
  `ClientAuthNum` int(11),
  `SnmpAuthNum` int(11),
  `pmWorkno` varchar(50),
  `cust_level` varchar(50),
  `trial_expired` varchar(50),
  `service_department` varchar(50),
  `cust_borrowing_due_date` varchar(50),
  `status` varchar(2),
  `service_cc_staff_emails` varchar(2000),
  `formal` int,
  `IsTrial` int,
  `window_department` varchar(50),
  `businessDepartmentCode` varchar(50),
  `businessDepartmentCodeACP` varchar(50),
  `serviceUnitType` varchar(50),
  `deliveryMode` varchar(50),
  `contractSource` int(1),
  `salesCode` varchar(50),
  `agent_limit_issueCount` int(10),
  `__version__` datetime
)ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
  "host" = "***************",
  "port" = "4306",
  "user" = "digiwin",
  "password" = "gitlab123",  -- 请设置正确的密码
  "database" = "aio-db",
  "table" = "mars_customerservice"
);

CREATE TABLE `es_customerservice_v3` (
                                         `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=OLAP
    DUPLICATE KEY(`grp_dpt_id`, `dpt_id`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`grp_dpt_id`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_manager_contact`, `dpt_id`, `dpt_manager_contact`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "true"
);
insert into es_customerservice_v3 select * from es_customerservice_v3_external

        CREATE EXTERNAL TABLE es_customerservice_v2_external (
  eId bigint(20) NOT NULL COMMENT "",
  updateDate datetime NOT NULL COMMENT "",
  SalesContact varchar(100) NOT NULL COMMENT "",
  CustomerCode varchar(100) NOT NULL COMMENT "",
  grp_dpt_id varchar(255) NULL COMMENT "",
  grp_dpt_name varchar(255) NULL COMMENT "",
  grp_dpt_manager varchar(255) NULL COMMENT "",
  bus_dpt_id varchar(255) NULL COMMENT "",
  bus_dpt_name varchar(255) NULL COMMENT "",
  bus_dpt_manager varchar(255) NULL COMMENT "",
  dpt_id varchar(255) NULL COMMENT "",
  dpt_name varchar(255) NULL COMMENT "",
  dpt_manager varchar(255) NULL COMMENT "",
  Sales varchar(255) NULL COMMENT "",
  CustomerServiceCode varchar(255) NULL COMMENT "",
  CustomerName varchar(255) NULL COMMENT "",
  CustomerFullNameCH varchar(255) NULL COMMENT "",
  CustomerFullNameEN varchar(255) NULL COMMENT "",
  another_name varchar(255) NULL COMMENT "",
  current_valid_status char(1) NULL COMMENT "",
  t100_cust_id varchar(255) NULL COMMENT "",
  taxNo varchar(255) NULL COMMENT "",
  contacts varchar(255) NULL COMMENT "",
  tenantTelephone varchar(255) NULL COMMENT "",
  tenantEmail varchar(255) NULL COMMENT "",
  address varchar(255) NULL COMMENT "",
  __version__ datetime NULL COMMENT "",
  grp_dpt_manager_contact varchar(255) NULL COMMENT "",
  bus_dpt_manager_contact varchar(255) NULL COMMENT "",
  bus_dpt_win_name varchar(255) NULL COMMENT "",
  bus_dpt_win_contact varchar(255) NULL COMMENT "",
  dpt_manager_contact varchar(255) NULL COMMENT "",
  valueAddedConsultant varchar(255) NULL COMMENT "",
  valueAddedConsultantEmail varchar(255) NULL COMMENT "",
  IndustryCode varchar(255) NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "es_customerservice_v4_1"
);
CREATE TABLE `es_customerservice_v2` (
                                         `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultant` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultantEmail` varchar(65533) NULL COMMENT "",
                                         `IndustryCode` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eId`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eId`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);
insert into  es_customerservice_v2 select * from es_customerservice_v2_external;
insert into  es_customerservice_v2_bak_20240903 select * from es_customerservice_v2;
select count(1) from es_customerservice_v2;
INSERT INTO es_customerservice_v2 (eId,
                                   updateDate,
                                   SalesContact,
                                   CustomerCode,
                                   grp_dpt_id,
                                   grp_dpt_name,
                                   grp_dpt_manager,
                                   bus_dpt_id,
                                   bus_dpt_name,
                                   bus_dpt_manager,
                                   dpt_id,
                                   dpt_name,
                                   dpt_manager,
                                   Sales,
                                   CustomerServiceCode,
                                   CustomerName,
                                   CustomerFullNameCH,
                                   CustomerFullNameEN,
                                   another_name,
                                   current_valid_status,
                                   t100_cust_id,
                                   taxNo,
                                   contacts,
                                   tenantTelephone,
                                   tenantEmail,
                                   address,
                                   __version__,
                                   grp_dpt_manager_contact,
                                   bus_dpt_manager_contact,
                                   bus_dpt_win_name,
                                   bus_dpt_win_contact,
                                   dpt_manager_contact,
                                   valueAddedConsultant,
                                   valueAddedConsultantEmail,
                                   IndustryCode)
VALUES (99990000,
        now(),
        '<EMAIL>',
        '99990000',
        'C0001',
        '部门1',
        '7',
        '8',
        '8',
        '9',
        '10',
        '11',
        '12',
        '陈超',
        '99990000',
        '鼎捷软件',
        '鼎捷软件',
        '鼎捷软件',
        '11',
        '1',
        '20',
        '21',
        '22',
        '23',
        '24',
        '25',
        '26',
        '27',
        '28',
        '29',
        '30',
        '31',
        '32',
        '33',
        'C18');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41321126404672, '*********', '08015010', '天津科禄格', '天津科禄格通风设备有限公司', '', NULL, 'Y', NULL, '911202225503639223', '段凤玲', '', '', '天津市武清区汊沽港镇安园道168号', '2023-09-13 06:35:04', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318405489216, '*********', '08015600', '石油机械厂', '北京石油机械有限公司', '', NULL, 'Y', NULL, '91110114101929281W', '', '', '', '', '2023-06-29 17:11:58', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318150099520, '*********', '08019923', '东方国铁', '北京世纪东方国铁科技股份有限公司', '', NULL, 'Y', NULL, '911101067454558431', '牛经理', '', '', '北京市丰台区南四环西路188号十二区31楼世纪东方大厦', '2023-08-01 06:13:17', 'I9');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41324466401856, '*********', '18010800', '北京华懋', '北京华懋伟业精密电子有限公司', '', NULL, 'Y', NULL, '110222740430006', '王智臣', '', '', '北京顺义区达盛路5号', '2023-10-27 23:02:40', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 50605765960256, '*********', '18010900', '天津高丘', '高丘六和（天津）工业有限公司', '', NULL, 'Y', NULL, '911201137303720849', '巩先生', '', '', '天津北辰经济技术开发区外国中小企业双盈工业区', '2023-07-26 15:13:16', '');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318026822208, '*********', '18012300', '伍享工业', '沈阳伍享工业有限公司', '', NULL, 'Y', NULL, '91210112702047831P', '安双全', '', '', '沈阳市浑南新区新成街6号', '2023-11-06 06:40:33', 'I3');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41323754316352, '*********', '18014300', '西科盛世', '北京西科盛世通酒店会展设备制造有限公司', '', NULL, 'Y', NULL, '91110113766754543Y', '王伟', '', '', '北京天竺空港工业开发区B区安庆大街8号', '2022-12-31 01:01:01', 'I6');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41322797310528, '*********', '18015010', '实力源', '北京实力源科技开发有限责任公司', '', NULL, 'Y', NULL, '91110106633764772R', '庄潍滨', '', '', '北京市丰台区白盆窑村南558号（原电炉厂内）', '2023-08-18 06:13:03', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41325043475008, '*********', '18015700', '同一伟业', '北京同一伟业科技有限公司', '', NULL, 'Y', NULL, '110108633673707', '李焱焱', '', '', '北京市海淀区上地信息路1号国际创业园2号楼403室', '2023-12-09 06:16:15', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318475096640, '*********', '18018500', '正森木业', '正森木业（天津）有限公司', '', NULL, 'Y', NULL, '120115783338330', '梁刚', '', '', '天津是开发区洞庭路159号', '2023-09-13 06:35:00', 'I99');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41324257493568, '*********', '23017300', '老村长酒', '黑龙江省老村长酒业有限公司', '', NULL, 'Y', NULL, '912301137276970219', '', '', '', '', '2023-12-11 10:17:29', '');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41322722599488, '*********', '34017002', '新晨办公', '北京新晨办公设备有限公司', '', NULL, 'Y', NULL, '911101121024125911', '王军', '', '', '北京市梨园镇公庄村', '2023-07-17 06:13:30', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41323376194112, 'C001845653', '72000162', '三盛智慧', '三盛智慧教育科技股份有限公司', '', NULL, 'Y', NULL, '91110000754166859U', '周广道', '', '', '北京市海淀区学院路街道办事处学知园社区居委会', '2023-08-18 06:13:04', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318932529728, 'C002308254', '72000566', '航天奥祥', '北京航天奥祥通风科技股份有限公司', '', NULL, 'Y', NULL, '91110111071696199H', '赵蕾', '', '', '北京市房山区窦店镇广茂路38号', '2023-08-18 06:13:07', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41324226490944, 'C002281780', '72000813', '远东德力', '北京市远东德力电子有限公司', '', NULL, 'Y', NULL, '110108802032274', '何清晨', '', '', '北京回龙观镇国际信息产业基地', '2023-08-18 06:13:13', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41319169139264, 'C002232020', '72001066', '北京经纬', '北京经纬信息技术有限公司', '', NULL, 'Y', NULL, '911101081020143823', '郑先生', '', '', '北京市海淀区北下关街道办事处铁道科学研究院社区居委会', '2023-08-18 06:13:07', 'I99');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41324957598272, 'C000038033', '72001298', '桑宝金太阳', '三河市桑宝金太阳新能源技术有限公司', '', NULL, 'Y', NULL, '9113108267030787X7', '唐玲', '', '', '河北省三河市燕郊开发区欧伏路84号', '2023-08-18 06:13:12', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41318304522816, 'C002028290', '72001915', '永大制药', '沈阳永大制药股份有限公司', '', NULL, 'Y', NULL, '91210100604612702T', '李志伟', '', '', '沈阳市经济技术开发区沧海路20号', '2023-08-18 06:13:06', 'I99');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 444799371653696, 'C200011766', '72002116', '天津协承昌', '天津协承昌新材料科技有限公司', '', NULL, 'Y', '72002116', '91120116MA05K2DB9G', '苑淑华', '', '', '天津市经济技术开发区海清街10号A栋厂房', '2023-09-13 06:35:09', 'I6');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 41320431280704, 'C002171833', '72003308', '北京日端', '北京日端电子有限公司', '', NULL, 'Y', NULL, '911101056000420363', '周静霞', '', '', '北京市顺义区赵全营镇兆丰产业基地兆丰一街27号', '2023-10-27 06:38:22', 'I8');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 156299950580288, 'C200030504', '72005130', '北京汇冠', '北京汇冠触摸技术有限公司', '', NULL, 'Y', '72005130', '91110105777699877M', '商五昌', '', '', '北京市海淀区西北旺东路10号院东区21号楼6层101-602', '2023-08-18 06:13:08', 'I99');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 233430812213824, 'C011207461', '72005389', '山西华鑫', '山西华鑫电气有限公司', '', NULL, 'Y', '72005389', '91140300748578443L', '马晓琴', '', '', '山西省阳泉市矿区煤山路二坑', '2023-09-19 15:37:25', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 304578938188352, 'C001644867', '72006357', '平遥华兴', '山西平遥华兴电机铸造有限公司', '', NULL, 'Y', '72006357', '91140000724605750P', '', '', '', '', '2022-12-31 01:01:01', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 322624368411200, 'C200042584', '72006559', '北京建筑', '北京建筑机械化研究院有限公司', '', '建机院', 'Y', '72006559', '911101014000046175', '赵红学', '', '', '北京市东城区方家胡同21号', '2023-07-17 06:14:24', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 340868513550912, 'C200030909', '72006678', '北京灵汐', '北京灵汐科技有限公司', '', NULL, 'Y', NULL, '91110108MA019WL32P', '祝夭龙', '', '', '北京市海淀区北四环西路67号中关村前沿技术创新中心801', '2023-05-06 22:59:35', 'I4');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 485892572471872, 'C200040137', '72007870', '研博智创', '研博智创任丘科技有限公司', '', NULL, 'Y', '72007870', '91130982MA0CU05T08', '周春良', '', '', '河北任丘经济开发区', '2023-12-08 14:08:00', 'I1');
INSERT INTO `es_customerservice_v4_1`(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`, `Sales`, `SalesContact`, `eId`, `CustomerCode`, `CustomerServiceCode`, `CustomerName`, `CustomerFullNameCH`, `CustomerFullNameEN`, `another_name`, `current_valid_status`, `t100_cust_id`, `taxNo`, `contacts`, `tenantTelephone`, `tenantEmail`, `address`, `__version__`, `IndustryCode`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>', '魏婷婷', '<EMAIL>', 489816618103360, 'C200039707', '72007959', '苏州领慧', '苏州领慧立芯科技有限公司', '', NULL, 'Y', NULL, '91320594MA22MFFK2H', '卢总', '', '', '中国（江苏）自由贸易试验区苏州片区苏州工业园区金鸡湖大道88号人工智能产业园E4-045单元（该地址不得从事零售）', '2023-07-14 15:13:25', 'I4');
CREATE TABLE `IndustryPlanQuickScreening_bak_20240930` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT "",
                                              `sqSalesP` int(11) NULL COMMENT "",
                                              `sqLackInfoTools` varchar(65535) NULL COMMENT "",
                                              `f_CPOHighStockLevel0101` varchar(65533) NULL COMMENT "",
                                              `f_CPORollingDemand0104` varchar(65533) NULL COMMENT "",
                                              `f_CPODailyPlanScheduling0105` varchar(65533) NULL COMMENT "",
                                              `f_CPOToolForScheduling0106` varchar(65533) NULL COMMENT "",
                                              `f_CPOCapacityScheduling0107` varchar(65533) NULL COMMENT "",
                                              `f_CPOScheduleWithERP0110` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupplierDelivery0111` varchar(65533) NULL COMMENT "",
                                              `f_CPOCompletedCount` int(11) NULL COMMENT "",
                                              `f_CPOTotalQuantity` int(11) NULL COMMENT "",
                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOCompletedCount` int(11) NULL COMMENT "",
                                              `f_COOTotalQuantity` int(11) NULL COMMENT "",
                                              `fs_CPOSurvey` varchar(65533) NULL COMMENT "",
                                              `fs_COOSurvey` varchar(65533) NULL COMMENT "",
                                              `f_COOStackerCountWorkers` decimal64(10, 0) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueOne0112` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueTwo0113` varchar(65533) NULL COMMENT "",
                                              `f_CPOUseErp0114` varchar(65533) NULL COMMENT "",
                                              `f_CPOIndustry0115` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);


UPDATE IndustryPlanQuickScreening
SET fscfo = (
    json_set(
            json_set(fscfo, '$.sqIPODisclosureDeadline', ''),
            '$.sqCfoCompletedCount', 3
    )
    )
WHERE eid = 99990000;

select fscfo from IndustryPlanQuickScreening where eid=99990000;

select fscfo from IndustryPlanQuickScreening where eid=41319428911680;
select fscfo,sqCfoCompletedCount,sqIPODisclosureDeadline from IndustryPlanQuickScreening where eid=41319428911680;
select fscfo,sqCfoCompletedCount,sqIPODisclosureDeadline from IndustryPlanQuickScreening where salesContact='<EMAIL>';
select fscfo,sqCfoCompletedCount,sqIPODisclosureDeadline from IndustryPlanQuickScreening_bak_20240813_1 where salesContact='<EMAIL>';
alter table IndustryPlanQuickScreening add column  f_COOIntelligenceWarehousing  string;
alter table IndustryPlanQuickScreening add column  sqIPODisclosureDeadline  string;
alter table CSORoleSurvey
   add column f_CSOcommBeforeQtnTech0203 string ,
   add column f_CSOreqToQuoteDays0204 string ,
   add column f_quotingCycleLength string ,
   add column f_CSOcommBeforeOrdrTech0207 string ,
   add column f_CSOquoteToOrderDays0208 string ,
   add column fs_quotingCycleLength string ,
   add column fs_CSOorderCycle string ;

alter table CSORoleSurvey
    add column f_CSOcustomProdRatio0105 decimal ,
    add column f_CSOsaleModelsNum0106 int ,
    add column fs_CSOOptionalProdRatio string ,
    add column fs_CSOSaleProdModels string ;
ALTER TABLE CSORoleSurvey
    MODIFY COLUMN f_CSOquoteToOrderDays0208 int;

ALTER TABLE CFORoleSurvey
drop column f_complianceSatisfaction;

CREATE TABLE `IndustryPlanQuickScreening_bak_20240717_2` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT "",
                                              `sqSalesP` int(11) NULL COMMENT "",
                                              `sqLackInfoTools` varchar(65535) NULL COMMENT "",
                                              `f_CPOHighStockLevel0101` varchar(65533) NULL COMMENT "",
                                              `f_CPOComponentAssembly0102` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupportAttributes0103` varchar(65533) NULL COMMENT "",
                                              `f_CPORollingDemand0104` varchar(65533) NULL COMMENT "",
                                              `f_CPODailyPlanScheduling0105` varchar(65533) NULL COMMENT "",
                                              `f_CPOToolForScheduling0106` varchar(65533) NULL COMMENT "",
                                              `f_CPOCapacityScheduling0107` varchar(65533) NULL COMMENT "",
                                              `f_CPOExpertiseScheduling0108` varchar(65533) NULL COMMENT "",
                                              `f_CPORepetitiveWork0109` varchar(65533) NULL COMMENT "",
                                              `f_CPOScheduleWithERP0110` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupplierDelivery0111` varchar(65533) NULL COMMENT "",
                                              `f_CPOCompletedCount` int(11) NULL COMMENT "",
                                              `f_CPOTotalQuantity` int(11) NULL COMMENT "",
                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOCompletedCount` int(11) NULL COMMENT "",
                                              `f_COOTotalQuantity` int(11) NULL COMMENT "",
                                              `fs_CPOSurvey` varchar(65533) NULL COMMENT "",
                                              `fs_COOSurvey` varchar(65533) NULL COMMENT "",
                                              `f_COOStackerCountWorkers` decimal64(10, 0) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);
insert into IndustryPlanQuickScreening_bak_20240717_2 select * from IndustryPlanQuickScreening;
update IndustryPlanQuickScreening set f_COOCompletedCount =f_COOCompletedCount-1  where f_COOStackerCountWorkers is not null and f_COOCompletedCount>0 and f_COOCompletedCount <12
select fs_COOSurvey from IndustryPlanQuickScreening  order by collectedTime desc limit 5;
insert into IndustryPlanQuickScreening select `eid`,
    `SalesContact`,
    `userSid`,
    `uploadDataModelCode`,
    `collectedTime`,
    `sqSummitFreq`,
    `sqCollateRate`,
    `sqFirmCount`,
    `sqOverseasFirmCount`,
    `sqERPSysCount`,
    `sqConsolCycle`,
    `sqIPODisclosure`,
    `sqCfoCompletedCount`,
    `sqCfoTotalQuantity`,
    `sqIndustryAffil`,
    `sqBizModel`,
    `sqBizTeamSize`,
    `sqProdType`,
    `sqProdTypeRatio`,
    `sqProdModelCount`,
    `sqQuoteEfficiency`,
    `sqQuoteConfirmation`,
    `sqOrderConfirmation`,
    `sqOrderEfficiency`,
    `sqOrderingOperationMode`,
    `sqOrderProgressTracking`,
    `sqCsoCompletedCount`,
    `sqCsoTotalQuantity`,
    `sqBranchCount`,
    `sqOverseasBranches`,
    `sqEmployeeCount`,
    `sqHumanResourcesCount`,
    `sqPaperContract`,
    `sqPaperDummy`,
    `sqAttendanceSettlementDays`,
    `sqSalarySettlementDays`,
    `sqHumanRightsVerification`,
    `sqStaffingBlueprint`,
    `sqBudgetManagement`,
    `sqTalentManagement`,
    `sqEnterpriseResourceSharing`,
    `sqChoCompletedCount`,
    `sqChoTotalQuantity`,
    `serviceCode`,
    `customerName`,
    `CustomerCode`,
    `businessDepartmentCode`,
    `businessDepartmentCodeACP`,
    `businessDepartmentName`,
    `url`,
    `createTime`,
    `SalesName`,
    `userid`,
    `ThemeApplySourceCode`,
    `ThemeApplayStatus`,
    `ThemeLastUpdateTime`,
    `sqScraningState`,
    `sqScraningDesc`,
    `fscfo`,
    `fscso`,
    `fscho`,
    `sqSalesP`,
    `sqLackInfoTools`,
    `f_CPOHighStockLevel0101`,
    `f_CPOComponentAssembly0102`,
    `f_CPOSupportAttributes0103`,
    `f_CPORollingDemand0104`,
    `f_CPODailyPlanScheduling0105`,
    `f_CPOToolForScheduling0106`,
    `f_CPOCapacityScheduling0107`,
    `f_CPOExpertiseScheduling0108`,
    `f_CPORepetitiveWork0109`,
    `f_CPOScheduleWithERP0110`,
    `f_CPOSupplierDelivery0111`,
    `f_CPOCompletedCount`,
    `f_CPOTotalQuantity`,
    `f_COOGroundTugCount0101`,
    `f_COOStackerCount0102`,
    `f_COOStackerWorkers0103`,
    `f_COODailyStackerRuns0104`,
    `f_COOManualFeeding0105`,
    `f_COOWarehouseCount0106`,
    `f_COOSingleWarehouseStaff0107`,
    `f_COOWarehouseShelvingStaff0108`,
    `f_COOMaterialTurnoverTime0109`,
    `f_COOSlowMovingInventory0110`,
    `f_COOWarehouseWorkload0111`,
    `f_COOCompletedCount`,
    `f_COOTotalQuantity`,
    `fscpo`,
    `fscoo` FROM IndustryPlanQuickScreening_bak_20240711;
INSERT INTO IndustryPlanQuickScreening(sqScraningState, sqScraningDesc, sqSalesP, sqLackInfoTools,
                                       f_CPOHighStockLevel0101, f_CPOComponentAssembly0102, f_CPOSupportAttributes0103,
                                       f_CPORollingDemand0104, f_CPODailyPlanScheduling0105, f_CPOToolForScheduling0106,
                                       f_CPOCapacityScheduling0107, f_CPOExpertiseScheduling0108,
                                       f_CPORepetitiveWork0109, f_CPOScheduleWithERP0110, f_CPOSupplierDelivery0111,
                                       f_CPOCompletedCount, f_CPOTotalQuantity, f_COOGroundTugCount0101,
                                       f_COOStackerCount0102, f_COOStackerWorkers0103, f_COODailyStackerRuns0104,
                                       f_COOManualFeeding0105, f_COOWarehouseCount0106, f_COOSingleWarehouseStaff0107,
                                       f_COOWarehouseShelvingStaff0108, f_COOMaterialTurnoverTime0109,
                                       f_COOSlowMovingInventory0110, f_COOWarehouseWorkload0111, f_COOCompletedCount,
                                       f_COOTotalQuantity, fscpo, fscoo, uploadDataModelCode, collectedTime, fscfo,
                                       fscso, fscho, serviceCode, customerName, eid, CustomerCode,
                                       businessDepartmentCode, businessDepartmentCodeACP, businessDepartmentName, url,
                                       createTime, SalesName, SalesContact, userid, ThemeApplySourceCode,
                                       ThemeApplayStatus, ThemeLastUpdateTime, userSid, sqSummitFreq, sqCollateRate,
                                       sqFirmCount, sqOverseasFirmCount, sqERPSysCount, sqConsolCycle, sqIPODisclosure,
                                       sqCfoCompletedCount, sqCfoTotalQuantity, sqIndustryAffil, sqBizModel,
                                       sqBizTeamSize, sqProdType, sqProdTypeRatio, sqProdModelCount, sqQuoteEfficiency,
                                       sqQuoteConfirmation, sqOrderConfirmation, sqOrderEfficiency,
                                       sqOrderingOperationMode, sqOrderProgressTracking, sqCsoCompletedCount,
                                       sqCsoTotalQuantity, sqBranchCount, sqOverseasBranches, sqEmployeeCount,
                                       sqHumanResourcesCount, sqPaperContract, sqPaperDummy, sqAttendanceSettlementDays,
                                       sqSalarySettlementDays, sqHumanRightsVerification, sqStaffingBlueprint,
                                       sqBudgetManagement, sqTalentManagement, sqEnterpriseResourceSharing,
                                       sqChoCompletedCount, sqChoTotalQuantity)
values ('Y', null, 5 %, '3', '1', '0', '1', '1', '1', '1', '1', '1', '1', '1', '2', 11, 11, 0, 10, 20, 8, '1', 30, 5,
        40, '1', '1', '2', 11, 11,
        '{"f_CPOHighStockLevel0101":"1","f_CPOComponentAssembly0102":"0","f_CPOSupportAttributes0103":"1","f_CPORollingDemand0104":"1","f_CPODailyPlanScheduling0105":"1","f_CPOToolForScheduling0106":"1","f_CPOCapacityScheduling0107":"1","f_CPOExpertiseScheduling0108":"1","f_CPORepetitiveWork0109":"1","f_CPOScheduleWithERP0110":"1","f_CPOSupplierDelivery0111":"2","f_CPOCompletedCount":11,"f_CPOTotalQuantity":11}',
        '{"f_COOGroundTugCount0101":"0","f_COOStackerCount0102":"10","f_COOStackerWorkers0103":"20","f_COODailyStackerRuns0104":"8","f_COOManualFeeding0105":"1","f_COOWarehouseCount0106":"30","f_COOSingleWarehouseStaff0107":"5","f_COOWarehouseShelvingStaff0108":"40","f_COOMaterialTurnoverTime0109":"1","f_COOSlowMovingInventory0110":"1","f_COOWarehouseWorkload0111":"2","f_COOCompletedCount":11,"f_COOTotalQuantity":11}',
        'IndustryPlanQuickScreening', '2024-07-08 17:49:59',
        '{"sqAnnualRevenue":"2","sqSummitFreq":"1","sqCollateRate":"1","sqFirmCount":"4","sqOverseasFirmCount":"1","sqERPSysCount":"1","sqJointReportOutputMethod":"B","sqConsolCycle":"2","sqCombinedOutputDays":"1","sqIPODisclosure":"0","sqIPODisclosureDeadline":"0","sqCfoCompletedCount":7,"sqCfoTotalQuantity":7}',
        '{"sqIndustryAffil":"A","sqBizModel":"A,B,C","sqSalesP":"5%","sqLackInfoTools":"3","sqBizTeamSize":"200","sqProdType":"A,B","sqProdTypeRatio":"80","sqProdModelCount":"2000","sqQuoteEfficiency":"5","sqQuoteConfirmation":"1","sqOrderConfirmation":"1","sqOrderEfficiency":"10","sqOrderingOperationMode":"C","sqOrderProgressTracking":"C","sqCsoCompletedCount":14,"sqCsoTotalQuantity":14}',
        '{"sqBranchCount":"15","sqOverseasBranches":"1","sqEmployeeCount":"4","sqHumanResourcesCount":"4","sqPaperContract":"1","sqPaperDummy":"0","sqAttendanceSettlementDays":"2","sqSalarySettlementDays":"2","sqHumanRightsVerification":"0","sqStaffingBlueprint":"1","sqBudgetManagement":"1","sqTalentManagement":"0","sqEnterpriseResourceSharing":"0","sqChoCompletedCount":13,"sqChoTotalQuantity":13}',
        '18028410', null, '41319678427712', '100063154', null, null, null, null, null, '刘东枝', '<EMAIL>',
        0, 'hyksdy2405', 'Applayed', '2024-07-08 17:49:59', 0, '1', '1', '4', '1', '1', '2', '0', 7, 7, 'A', 'A,B,C',
        200, 'A,B', 80, 2000, 5, '1', '1', 10, 'C', 'C', 14, 14, 15, '1', '4', '4', '1', '0', '2', '2', '0', '1', '1',
        '0', '0', 13, 13);;


CREATE TABLE cxo_tag_meta_data_external (
                                     cxotype varchar(255) DEFAULT NULL,
                                     cxotag varchar(255) DEFAULT NULL,
                                     cxochildtag varchar(255) DEFAULT NULL,
                                     cxotitle varchar(255) DEFAULT NULL,
                                     cxosubject varchar(255) DEFAULT NULL
) ENGINE=MYSQL
    COMMENT "MYSQL"
    PROPERTIES (
                   "host" = "***************",
                   "port" = "4306",
                   "user" = "digiwin",
                   "password" = "gitlab123",
                   "database" = "aio-db",
                   "table" = "cxo_tag_meta_data"
               );



CREATE TABLE IF NOT EXISTS tbb.aiops_cxo_tag_v2(
                                    eid string,
                                    caldatetime timestamp,
                                    cxotype string,
                                    cxotag string,
                                    cxochildtag string,
                                    cxotitle string,
                                    cxosubject string)
    COMMENT ''
    ROW FORMAT SERDE
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe'
WITH SERDEPROPERTIES (
  'field.delim'='\t',
  'serialization.format'='\t')
STORED AS INPUTFORMAT
  'org.apache.hadoop.mapred.TextInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
  'hdfs://nameservice1/user/hive/warehouse/tbb.db/aiops_cxo_tag_v2'
TBLPROPERTIES (
  'transient_lastDdlTime'='1719900542')
;

beeline -d "com.cloudera.impala.jdbc41.Driver" -u "*********************************" -e"
CREATE TABLE IF NOT EXISTS tbb.aiops_cxo_tag
(
    eid string,
    caldatetime timestamp,
    cxotype string,
    cxotag string,
    cxochildtag string,
    cxoscore double,
    cxocontent string,
    cxosuject string
) COMMENT ''
    ROW FORMAT DELIMITED FIELDS TERMINATED BY '\t'
LOCATION '/user/hive/warehouse/tbb.db/aiops_cxo_tag/';
INVALIDATE METADATA tbb.aiops_cxo_tag;
"

beeline -d "com.cloudera.impala.jdbc41.Driver" -u "*********************************" -e"
drop table tbb.aiops_cxo_tag;

INVALIDATE METADATA tbb.aiops_cxo_tag;
"

beeline -d "com.cloudera.impala.jdbc41.Driver" -u "****************************" -e"
INVALIDATE METADATA tbb.aiops_ability_tag_score;
"

INSERT INTO tbb.aiops_cxo_tag (eid, calDateTime, cxoType, cxoTag, cxochildTag, cxoScore, cxoContent, cxoSuject) VALUES
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '总结', '', NULL, '整体的结论+初步解读', ''),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '用工风险高', '', 87, '1,纸质合同风险管控难 2,验厂机构多通过率低 3,纸质工资条保密性差', '1，引入电子合同，线上签署，高效快速；有意向认证、实名认证，规避合同签署争议； 2，通过系统搭建多个验厂账套，按需求设置不同的验厂规则，满足不同机构的验厂要求；灵活设定出勤以及加班规则，一键生成符合规则的考勤数据，薪资差异归属特定项目，高效通过人权验厂； 3，电子工资条，一键发送；员工移动端实时查看工资条，密码二次验证，安全便捷'),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '人工成本高', '', 46, '1,无编制或编制管控难 2,加班管控难 3,假勤管控难', '1，搭建全面的编制管理，强弱管控收放自如 2，加班提报时数和员工打卡比对、二者取小，严谨的加班时数取值逻辑，让加班更加规范 3，年假、育儿假等假期按法规准确计算，额度系统自动管控，高效准确；请假审批额度与已用清晰可见'),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '人资效率低', '', 86, '1，考勤结算周期长 2，人事信息以及变动难收集 3，分析报表难制作', '1，各类班次信息、出勤工时、请假、加班、调休、出差等数据，自动汇总，准确率高；考勤排班、请假、加班等全流程线上提报，支持移动使用；考勤汇总结果一键发布，线上确认，高效无忧 2，通过预入职管理、快速扫码报道，报道人员自助填写基本信息、上传证照信息，缩短入职报道时间，提升报道效率 3， 提供智能化人力统计报表与分析，一键拉出所需报表，高效快捷；透过领导决策平台，可以移动查看'),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '人工成本高', '无法合理管控加班', 38, '', ''),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '人工成本高', '预算管控难度大', 68, '', ''),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '人工成本高', '编制管控难度大', 45, '', ''),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '用工风险高', '验厂通过率低', 80, '', ''),
                                                                                                                    ('99990000', '2024-06-17', 'CHO', '用工风险高', '纸质合同有风险', 56, '', '');


select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , '' as cxochildtag, chotag01score * 0.5 + managertag01score * 0.3 + hrtag01score * 0.2 as cxoscore from (select eid , ifnull(f_highEmploymentRisk,0) * 100 as chotag01score, ifnull(f_contractRiskControlDifficult,0) * 20 + ifnull(f_multipleInspectionInstitutionsLowPassRate,0) * 60 +ifnull(f_overseasCompRisk,0) * 20 as managertag01score, ifnull(f_paperDocCtrlDiff,0) * 100 + ifnull(f_factoryAuditLow,0) * 60 + ifnull(f_paySlipConfidLow,0) * 5 as hrtag01score from servicecloud.CHORoleSurvey ) as tag001 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype,'人工成本高' as cxotag , '' as cxochildtag, chotag02score * 0.5 + managertag02score * 0.3 + hrtag02score * 0.2 as cxoscore from (select eid , ifnull(f_highLaborCost,0) * 100 as chotag02score, ifnull(f_staffControlDiff,0) * 30 + ifnull(f_budgetControlDiff,0) * 30 + ifnull(f_OTHoursVerifyDiff,0) * 40 as managertag02score, ifnull(f_leaveCtrlDiff ,0)* 50 + ifnull(f_OTHoursVerifyDiff ,0)* 50 as hrtag02score from servicecloud.CHORoleSurvey ) as tag002 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , '' as cxochildtag, chotag03score * 0.5 + managertag03score * 0.3 + hrtag03score * 0.2 as cxoscore from (select eid , ifnull(f_lowEfficiencyHR,0) * 100 as chotag03score, ifnull(f_payrollPeriodLong ,0)* 50 + ifnull(f_HRSalaryCycleLong,0) * 50 as managertag03score, ifnull(f_HRInfoCollectDiff ,0)* 10 + ifnull(f_HRChangeProcComplex,0) * 5 + ifnull(f_reportDiff,0) * 5 + + ifnull(f_attDataCollectDiff ,0) * 10 + ifnull(f_shiftChangeFreq,0) * 15 + ifnull(f_attDataCollectDiff,0) * 40 +ifnull( f_excelPRLinkDiff,0) * 10 + ifnull(f_pieceworkOutputDiff,0) * 10 + ifnull(f_taxCalcDiff,0) * 10 + ifnull(f_salaryCycleLong,0) * 15 + ifnull(f_partTimeCostAllocDiff,0) * 5 as hrtag03score from servicecloud.CHORoleSurvey ) as tag003 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745910561178176 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人工成本高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745916151251520 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745920774877760

                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT ""



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ;

SELECT
    grp_dpt_name,
    grp_dpt_manager,
    bus_dpt_name,
    bus_dpt_manager,
    dpt_name,
    dpt_manager,
    CustomerServiceCode,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN CustomerName
        ELSE ''
        END AS CustomerName_HighPotential,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN Sales
        ELSE ''
        END AS Sales_HighPotential,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN CustomerFullNameCH
        ELSE ''
        END AS CustomerFullNameCH_HighPotential
FROM (
         SELECT
             b.CustomerServiceCode,
             b.CustomerName,
             b.CustomerFullNameCH,
             b.grp_dpt_name,
             b.grp_dpt_manager,
             b.bus_dpt_name,
             b.bus_dpt_manager,
             b.dpt_name,
             b.dpt_manager,
             b.Sales,
             GROUP_CONCAT(a.tagValue) AS tagvaluestr
         FROM AIEOM.tenant_tag_string a
                  LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
         WHERE tagId = '746979687588416'
         GROUP BY
             b.CustomerServiceCode,
             b.CustomerName,
             b.CustomerFullNameCH,
             b.grp_dpt_name,
             b.grp_dpt_manager,
             b.bus_dpt_name,
             b.bus_dpt_manager,
             b.dpt_name,
             b.dpt_manager,
             b.Sales
     ) AS aa
WHERE CustomerServiceCode IS NOT NULL
ORDER BY grp_dpt_name, bus_dpt_name
LIMIT 1000;


SELECT a.grp_dpt_name,
       a.grp_dpt_manager,
       a.bus_dpt_id,
       a.bus_dpt_name,
       a.bus_dpt_win_name,
       a.bus_dpt_manager,
       a.cus_count,
       a.cus_compeleted_count,
       a.cus_screening_count,
       a.cus_nocompeleted_count,
       a.no_screening_count,
       (case when (b.allcount is NULL) THEN 0 ELSE b.allcount end) AS gqCount
FROM (SELECT a.grp_dpt_name,
             a.bus_dpt_id,
             a.grp_dpt_manager,
             a.bus_dpt_name,
             a.bus_dpt_win_name,
             a.bus_dpt_manager,
             count(distinct a.eid)                                      AS cus_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) THEN 0
                     ELSE 1 end)                                        AS cus_compeleted_count,
             sum(case
                     WHEN ((b.f_CPOCompletedCount > 0 AND b.f_CPOCompletedCount < b.f_CPOTotalQuantity)) THEN 1
                     ELSE 0 end)                                        AS cus_screening_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) AND
                          (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
                     ELSE 0 end)                                        AS cus_nocompeleted_count,
             sum(case WHEN (b.sqScraningState = 'D') THEN 1 ELSE 0 end) AS no_screening_count
      FROM servicecloud.es_customerservice_v2 a
               LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
               LEFT JOIN servicecloud.IndustryPlanQuickScreening b ON b.eid = a.eid
      WHERE a.grp_dpt_name is NOT null
      GROUP BY a.grp_dpt_name, a.bus_dpt_id, a.bus_dpt_name, a.bus_dpt_manager,a.bus_dpt_win_name,a.grp_dpt_manager) a
         LEFT JOIN (SELECT bus_dpt_id, count(distinct CustomerServiceCode) AS allcount
                    FROM (SELECT b.CustomerServiceCode, b.bus_dpt_id
                          FROM AIEOM.tenant_tag_string a
                                   LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
                          WHERE tagId = '747209582092864'
                          GROUP BY b.CustomerServiceCode, b.bus_dpt_id) AS aa
                    WHERE CustomerServiceCode is NOT null
                    GROUP BY bus_dpt_id) b ON a.bus_dpt_id = b.bus_dpt_id
ORDER BY a.bus_dpt_id
limit 1000;


SELECT a.grp_dpt_id,
       a.grp_dpt_name,
       a.grp_dpt_manager,
       a.bus_dpt_win_name,
       a.grp_dpt_manager_contact,
       a.bus_dpt_manager,
       a.bus_dpt_win_contact,
       a.cus_count,
       a.cus_compeleted_count,
       a.cus_screening_count,
       a.cus_nocompeleted_count,
       a.no_screening_count,
       (case when (b.allcount is NULL) THEN 0 ELSE b.allcount end) AS gqCount
FROM (SELECT a.grp_dpt_id,
             a.grp_dpt_name,
             c.grp_dpt_manager,
             c.bus_dpt_manager,
             c.bus_dpt_win_name,
             c.grp_dpt_manager_contact,
             c.bus_dpt_win_contact,
             count(distinct a.eid)                                      AS cus_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) THEN 0
                     ELSE 1 end)                                        AS cus_compeleted_count,
             sum(case
                     WHEN ((b.f_CPOCompletedCount > 0 AND b.f_CPOCompletedCount < b.f_CPOTotalQuantity)) THEN 1
                     ELSE 0 end)                                        AS cus_screening_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) AND
                          (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
                     ELSE 0 end)                                        AS cus_nocompeleted_count,
             sum(case WHEN (b.sqScraningState = 'D') THEN 1 ELSE 0 end) AS no_screening_count
      FROM servicecloud.es_customerservice_v2 a
               LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
               LEFT JOIN servicecloud.IndustryPlanQuickScreening b ON b.eid = a.eid
      WHERE a.grp_dpt_name is NOT null
      GROUP BY a.grp_dpt_id, a.grp_dpt_name, c.grp_dpt_manager, c.bus_dpt_win_name, c.grp_dpt_manager_contact,
               c.bus_dpt_win_contact,c.bus_dpt_manager) a
         LEFT JOIN (SELECT grp_dpt_id, count(distinct CustomerServiceCode) AS allcount
                    FROM (SELECT b.CustomerServiceCode, b.grp_dpt_id
                          FROM AIEOM.tenant_tag_string a
                                   LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
                          WHERE tagId = '747209582092864'
                          GROUP BY b.CustomerServiceCode, b.grp_dpt_id) AS aa
                    WHERE CustomerServiceCode is NOT null
                    GROUP BY grp_dpt_id) b ON a.grp_dpt_id = b.grp_dpt_id
ORDER BY a.grp_dpt_id
limit 1000;

SELECT a.grp_dpt_id,
       a.grp_dpt_name,
       a.grp_dpt_manager,
       a.bus_dpt_win_name,
       a.grp_dpt_manager_contact,
       a.bus_dpt_manager,
       a.bus_dpt_name,
       a.bus_dpt_win_contact,
       a.cus_count,
       a.cus_compeleted_count,
       a.cus_screening_count,
       a.cus_nocompeleted_count,
       a.no_screening_count,
       (case when (b.allcount is NULL) THEN 0 ELSE b.allcount end) AS gqCount
FROM (SELECT a.grp_dpt_id,
             a.grp_dpt_name,
             c.grp_dpt_manager,
             c.bus_dpt_manager,
             c.bus_dpt_name,
             c.bus_dpt_win_name,
             c.grp_dpt_manager_contact,
             c.bus_dpt_win_contact,
             count(distinct a.eid)                                      AS cus_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) THEN 0
                     ELSE 1 end)                                        AS cus_compeleted_count,
             sum(case
                     WHEN ((b.f_CPOCompletedCount > 0 AND b.f_CPOCompletedCount < b.f_CPOTotalQuantity)) THEN 1
                     ELSE 0 end)                                        AS cus_screening_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) AND
                          (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
                     ELSE 0 end)                                        AS cus_nocompeleted_count,
             sum(case WHEN (b.sqScraningState = 'D') THEN 1 ELSE 0 end) AS no_screening_count
      FROM servicecloud.es_customerservice_v2 a
               LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
               LEFT JOIN servicecloud.IndustryPlanQuickScreening b ON b.eid = a.eid
      WHERE a.grp_dpt_name is NOT null
      GROUP BY a.grp_dpt_id, a.grp_dpt_name, c.grp_dpt_manager, c.bus_dpt_win_name, c.grp_dpt_manager_contact,
               c.bus_dpt_win_contact,c.bus_dpt_manager,c.bus_dpt_name) a
         LEFT JOIN (SELECT grp_dpt_id, count(distinct CustomerServiceCode) AS allcount
                    FROM (SELECT b.CustomerServiceCode, b.grp_dpt_id
                          FROM AIEOM.tenant_tag_string a
                                   LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
                          WHERE tagId = '747209582092864'
                          GROUP BY b.CustomerServiceCode, b.grp_dpt_id) AS aa
                    WHERE CustomerServiceCode is NOT null
                    GROUP BY grp_dpt_id) b ON a.grp_dpt_id = b.grp_dpt_id
ORDER BY a.grp_dpt_id
limit 1000;


SELECT
    grp_dpt_name,
    grp_dpt_manager,
    bus_dpt_name,
    bus_dpt_manager,
    dpt_name,
    dpt_manager,
    CustomerServiceCode,
    CustomerCode,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN CustomerName
        ELSE ''
        END AS CustomerName_HighPotential,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN Sales
        ELSE ''
        END AS Sales_HighPotential,
    CASE
        WHEN tagvaluestr LIKE '%COO运营领域高潜客户%' THEN CustomerFullNameCH
        ELSE ''
        END AS CustomerFullNameCH_HighPotential
FROM (
         SELECT
             b.CustomerServiceCode,
             b.CustomerCode,
             b.CustomerName,
             b.CustomerFullNameCH,
             b.grp_dpt_name,
             b.grp_dpt_manager,
             b.bus_dpt_name,
             b.bus_dpt_manager,
             b.dpt_name,
             b.dpt_manager,
             b.Sales,
             GROUP_CONCAT(a.tagValue) AS tagvaluestr
         FROM AIEOM.tenant_tag_string a
                  LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
         WHERE tagId = '746979687588416'
         GROUP BY
             b.CustomerServiceCode,
             b.CustomerName,
             b.CustomerFullNameCH,
             b.grp_dpt_name,
             b.grp_dpt_manager,
             b.bus_dpt_name,
             b.bus_dpt_manager,
             b.dpt_name,
             b.dpt_manager,
             b.CustomerCode,
             b.Sales
     ) AS aa
WHERE CustomerServiceCode IS NOT NULL
ORDER BY grp_dpt_name, bus_dpt_name
LIMIT 1000;


alter table IndustryPlanQuickScreening
    add column f_CPOOrderDeliveryIssueOne0112 string,
    add column f_CPOOrderDeliveryIssueTwo0113 string;
alter table IndustryPlanQuickScreening
    add column f_CPOUseErp0114 string,
    add column f_CPOIndustry0115 string;


ALTER TABLE IndustryPlanQuickScreening
    drop column f_CPOComponentAssembly0102,
    drop column  f_CPOSupportAttributes0103,
    drop column f_CPOExpertiseScheduling0108,
    drop column f_CPORepetitiveWork0109;


CREATE TABLE `IndustryPlanQuickScreening_bak_20240723` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);

CREATE TABLE `IndustryPlanQuickScreening_bak_20240725` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT "",
                                              `sqSalesP` int(11) NULL COMMENT "",
                                              `sqLackInfoTools` varchar(65535) NULL COMMENT "",
                                              `f_CPOHighStockLevel0101` varchar(65533) NULL COMMENT "",
                                              `f_CPORollingDemand0104` varchar(65533) NULL COMMENT "",
                                              `f_CPODailyPlanScheduling0105` varchar(65533) NULL COMMENT "",
                                              `f_CPOToolForScheduling0106` varchar(65533) NULL COMMENT "",
                                              `f_CPOCapacityScheduling0107` varchar(65533) NULL COMMENT "",
                                              `f_CPOScheduleWithERP0110` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupplierDelivery0111` varchar(65533) NULL COMMENT "",
                                              `f_CPOCompletedCount` int(11) NULL COMMENT "",
                                              `f_CPOTotalQuantity` int(11) NULL COMMENT "",
                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOCompletedCount` int(11) NULL COMMENT "",
                                              `f_COOTotalQuantity` int(11) NULL COMMENT "",
                                              `fs_CPOSurvey` varchar(65533) NULL COMMENT "",
                                              `fs_COOSurvey` varchar(65533) NULL COMMENT "",
                                              `f_COOStackerCountWorkers` decimal64(10, 0) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueOne0112` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueTwo0113` varchar(65533) NULL COMMENT "",
                                              `f_CPOUseErp0114` varchar(65533) NULL COMMENT "",
                                              `f_CPOIndustry0115` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);
update IndustryPlanQuickScreening set f_CPOTotalQuantity = 11 where  fs_CPOSurvey IS NOT NULL;
update IndustryPlanQuickScreening set f_CPOCompletedCount = 11 where fs_CPOSurvey IS NOT NULL
                                                                 AND f_CPOHighStockLevel0101 IS NOT NULL
                                                                 AND f_CPORollingDemand0104 IS NOT NULL
                                                                 AND f_CPODailyPlanScheduling0105 IS NOT NULL
                                                                 AND f_CPOToolForScheduling0106 IS NOT NULL
                                                                 AND f_CPOCapacityScheduling0107 IS NOT NULL
                                                                 AND f_CPOScheduleWithERP0110 IS NOT NULL
                                                                 AND f_CPOSupplierDelivery0111 IS NOT NULL
;

select f_CPOCompletedCount  from IndustryPlanQuickScreening where fs_CPOSurvey IS NOT NULL
                                                                AND f_CPOHighStockLevel0101 IS NOT NULL
                                                                AND f_CPORollingDemand0104 IS NOT NULL
                                                                AND f_CPODailyPlanScheduling0105 IS NOT NULL
                                                                AND f_CPOToolForScheduling0106 IS NOT NULL
                                                                AND f_CPOCapacityScheduling0107 IS NOT NULL
                                                                AND f_CPOScheduleWithERP0110 IS NOT NULL
                                                                AND f_CPOSupplierDelivery0111 IS NOT NULL
;


SELECT a.bus_dpt_id,
       a.grp_dpt_name,
       a.bus_dpt_name,
       a.dpt_name,
       a.Sales,
       a.SalesContact,
       a.cus_count,
       a.cus_compeleted_count,
       a.cus_screening_count,
       a.cus_nocompeleted_count,
       a.no_screening_count,
       (case when (b.allcount is NULL) THEN 0 ELSE b.allcount end) AS gqCount
FROM (select a.bus_dpt_id,
             a.grp_dpt_name,
             a.bus_dpt_name,
             a.dpt_name,
             a.dpt_id,
             a.Sales,
a.SalesContact,
             count(distinct a.eid) as cus_count,
             sum(case
                     when (b.sqCfoCompletedCount is null or b.sqCfoCompletedCount < b.sqCfoTotalQuantity) then 0
                     else 1 end)   as cus_compeleted_count,
             sum(case
                     when ((b.sqCfoCompletedCount > 0 and b.sqCfoCompletedCount < b.sqCfoTotalQuantity)) then 1
                     else 0 end)   as cus_screening_count,
             sum(case
                     WHEN (b.sqCfoCompletedCount is null OR b.sqCfoCompletedCount < b.sqCfoTotalQuantity) AND
                          (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
                     ELSE 0 end)   AS cus_nocompeleted_count,
             sum(case
                     when ((b.sqCfoCompletedCount is null or b.sqCfoCompletedCount < b.sqCfoTotalQuantity) and
                           b.sqScraningState = 'D') then 1
                     else 0 end)   as no_screening_count
      from servicecloud.es_customerservice_v2 a
               inner join (select distinct CustomerServiceCode
                           from servicecloud.mars_customerservice_external
                           where (ContractState like 'B%' or ContractState like 'H%' or
                                  (ContractState like 'G%' and ContractState != 'G6'))
                             and ProductCode in
                                 ('37', '152', '08', '165', '100', '06', '164', '176', '178', 'MES', 'PLM', '137')) as c
                          on c.CustomerServiceCode = a.CustomerServiceCode
               left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid
      where a.grp_dpt_id is not null
      group by a.bus_dpt_id, a.grp_dpt_name, a.bus_dpt_name,a.dpt_name,
               a.Sales,
               a.SalesContact,a.dpt_id) as a
         left join (select a.bus_dpt_id,a.dpt_id,a.SalesContact, sum(case when tts.gqCount > 0 then 1 else 0 end) as allcount
                    from servicecloud.es_customerservice_v2 a
                             inner join (select distinct CustomerServiceCode
                                         from servicecloud.mars_customerservice_external
                                         where (ContractState like 'B%' or ContractState like 'H%' or
                                                (ContractState like 'G%' and ContractState != 'G6'))
                                           and ProductCode in
                                               ('37', '152', '08', '165', '100', '06', '164', '176', '178', 'MES',
                                                'PLM', '137')) as cc on cc.CustomerServiceCode = a.CustomerServiceCode
                             left join(select id,
                                              sum(case when (tagId in ('731424753066560')) then 1 else 0 end) as gqCount
                                       from AIEOM.tenant_tag_string
                                       group by id) tts on tts.id = a.eid
                    where a.grp_dpt_id is not null and a.bus_dpt_name like '%厦门%'
                    group by a.bus_dpt_id,a.dpt_id,a.SalesContact) b ON a.bus_dpt_id = b.bus_dpt_id AND a.dpt_id = b.dpt_id AND a.SalesContact = b.SalesContact
WHERE a.bus_dpt_name like '%厦门%'
order by a.grp_dpt_name, a.bus_dpt_name,a.dpt_name,
         a.Sales,
         a.SalesContact
limit 100;



CREATE EXTERNAL TABLE `cdp_customer_contacts_external` (
  `id` bigint NOT NULL COMMENT "",
  `eid` bigint NOT NULL COMMENT "",
  `customerCode` varchar(255) NOT NULL COMMENT "",
  `department` varchar(255) NOT NULL COMMENT "",
  `lastContactTime` date  NULL COMMENT "",
  `contactCnt` int  NULL COMMENT "",
  `createTime` datetime  NULL COMMENT "",
  `updateTime` datetime NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "escloud-db",
"table" = "cdp_customer_contacts_external"
);



update servicecloud.IndustryPlanQuickScreening set sqIPODisclosureDeadline = 0 where eid = 41319428911680 ;
update servicecloud.IndustryPlanQuickScreening set sqCfoCompletedCount = 11 where eid = 41319428911680 ;
update servicecloud.IndustryPlanQuickScreening set fscfo = '{"sqAnnualRevenue":"1","sqSummitFreq":"1","sqCollateRate":"0","sqFirmCount":"3","sqOverseasFirmCount":"1","sqERPSysCount":"1","sqJointReportOutputMethod":"A","sqConsolCycle":"2","sqCombinedOutputDays":"2","sqIPODisclosure":"1","sqIPODisclosureDeadline":"0","sqCfoCompletedCount":11,"sqCfoTotalQuantity":11}' where eid = 41319428911680 ;

select fscfo from servicecloud.IndustryPlanQuickScreening where eid = 41319428911680

CREATE TABLE `IndustryPlanQuickScreening_bak_20240930_1` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT "",
                                              `sqSalesP` int(11) NULL COMMENT "",
                                              `sqLackInfoTools` varchar(65535) NULL COMMENT "",
                                              `f_CPOHighStockLevel0101` varchar(65533) NULL COMMENT "",
                                              `f_CPORollingDemand0104` varchar(65533) NULL COMMENT "",
                                              `f_CPODailyPlanScheduling0105` varchar(65533) NULL COMMENT "",
                                              `f_CPOToolForScheduling0106` varchar(65533) NULL COMMENT "",
                                              `f_CPOCapacityScheduling0107` varchar(65533) NULL COMMENT "",
                                              `f_CPOScheduleWithERP0110` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupplierDelivery0111` varchar(65533) NULL COMMENT "",
                                              `f_CPOCompletedCount` int(11) NULL COMMENT "",
                                              `f_CPOTotalQuantity` int(11) NULL COMMENT "",
                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOCompletedCount` int(11) NULL COMMENT "",
                                              `f_COOTotalQuantity` int(11) NULL COMMENT "",
                                              `fs_CPOSurvey` varchar(65533) NULL COMMENT "",
                                              `fs_COOSurvey` varchar(65533) NULL COMMENT "",
                                              `f_COOStackerCountWorkers` decimal64(10, 0) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueOne0112` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueTwo0113` varchar(65533) NULL COMMENT "",
                                              `f_CPOUseErp0114` varchar(65533) NULL COMMENT "",
                                              `f_CPOIndustry0115` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosureDeadline` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);