INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM0500', '福州事业部', '雷汛', '<EMAIL>', '张阳', '<EMAIL>', 'BM0530', '客户增值部', '林杰', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM0600', '厦门事业部', '涂永明', '<EMAIL>', '张阳', '<EMAIL>', 'BM0640', '客户增值部', '李翠兰', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM2200', '泉州事业部', '高翔鸫', '<EMAIL>', '张阳', '<EMAIL>', 'BM2220', '客户增值部', '王明文', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM3800', '武汉事业部', '张记住', '<EMAIL>', '张阳', '<EMAIL>', 'BM3830', '客户增值部', '薛飞', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM3900', '长沙事业部', '夏建江', '<EMAIL>', '张阳', '<EMAIL>', 'BM3920', '客户增值部', '惠然然', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4200', '渝蓉事业部', '刘玉麟', '<EMAIL>', '张阳', '<EMAIL>', 'BM4230', '客户增值部', '向燕', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4810', '增值经营一部', '齐宾', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM4800', '华北增值事业部', '齐宾', '<EMAIL>', '张阳', '<EMAIL>', 'BM4820', '增值经营二部', '齐宾', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM5200', '福建战略大客事业部', '缪奇', '<EMAIL>', '张阳', '<EMAIL>', 'BM5210', '大客增值一部', '叶丽红', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM5500', '合肥事业部', '王小迪', '<EMAIL>', '张阳', '<EMAIL>', 'BM5540', '客户增值部', '罗伟', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0100', '温州事业部', '温顺冬', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0110', '客户增值一部', '谢婷婷', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0100', '温州事业部', '温顺冬', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0120', '客户增值二部', '吕明强', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0200', '宁波事业一部', '薛军松', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0230', '客户增值一部', '薛军松', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0200', '宁波事业一部', '薛军松', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0240', '客户增值二部', '童小燕', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0300', '宁波事业二部', '马凌波', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0330', '客户增值部', '刘桢群', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0400', '杭州事业部', '汪卫忠', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0430', '客户增值部', '汪卫忠', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC0600', '绍兴事业部', '许明', '<EMAIL>', '陈叶', '<EMAIL>', 'CC0620', '客户增值部', '许明', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CC0000', '浙江事业处', '叶贤盛', '<EMAIL>', 'CC1000', '金华事业部', '陈明亿', '<EMAIL>', '陈叶', '<EMAIL>', 'CC1020', '客户增值部', '陈明亿', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0300', '东莞事业部', '刘明昆', '<EMAIL>', '朱广', '<EMAIL>', 'CD0340', '客户增值部', '谭映林', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0400', '中山事业部', '陈小美', '<EMAIL>', '朱广', '<EMAIL>', 'CD0420', '客户增值部', '陈永杰', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0500', '战略大客事业部', '许嘉易', '<EMAIL>', '朱广', '<EMAIL>', 'CD0540', '大客增值一部', '张雄鹰', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0500', '战略大客事业部', '许嘉易', '<EMAIL>', '朱广', '<EMAIL>', 'CD0550', '大客增值二部', '徐敏', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0500', '战略大客事业部', '许嘉易', '<EMAIL>', '朱广', '<EMAIL>', 'CD0561', '大客增值三部-一组', '龚志宏', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0500', '战略大客事业部', '许嘉易', '<EMAIL>', '朱广', '<EMAIL>', 'CD0562', '大客增值三部-二组', '龚志宏', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0800', '广州事业部', '陈曙光', '<EMAIL>', '朱广', '<EMAIL>', 'CD0830', '客户增值部', '陈拥军', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD0900', '佛山事业部', '陈小美', '<EMAIL>', '朱广', '<EMAIL>', 'CD0930', '客户增值部', '周敬红', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD1100', '深圳增值事业部', '陈亚欣', '<EMAIL>', '朱广', '<EMAIL>', 'CD1110', '增值业务一部', '陈亚欣', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD1100', '深圳增值事业部', '陈亚欣', '<EMAIL>', '朱广', '<EMAIL>', 'CD1120', '增值业务二部', '黄桂贤', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CD0000', '广东事业处', '伍定一', '<EMAIL>', 'CD1200', '先进制造事业部', '王祥', '<EMAIL>', '朱广', '<EMAIL>', 'CD1220', '广东先进制造增值部', '王祥', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0100', '苏州事业部', '申琳', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0160', '客户增值一部', '索飞', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0100', '苏州事业部', '申琳', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0170', '客户增值二部', '申琳', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0200', '南京事业部', '沈爱华', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0250', '客户增值部', '何蕾', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0300', '常州事业部', '沈斌', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0350', '客户增值部', '赵静', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0400', '南通事业部', '唐新炎', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0430', '客户增值部', '唐新炎', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0600', '无锡事业部', '高飞', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0630', '客户增值部', '石天', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0700', '战略大客事业部', '丁玮玮', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0720', '大客增值一部', '丁玮玮', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CE0000', '江苏事业处', '黄腾达', '<EMAIL>', 'CE0700', '战略大客事业部', '丁玮玮', '<EMAIL>', '陈红伟', '<EMAIL>', 'CE0730', '大客增值二部', '朱四云', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0100', '沪东事业部', '何耀宗', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0140', '客户增值部', '何淑娟', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0200', '沪南事业部', '廖春玲', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0240', '客户增值部', '廖春玲', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0300', '沪北事业部', '吴少鸿', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0360', '客户增值部', '杨颖', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0400', '先进制造事业部', '刘日鸣', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0420', '客户增值部', '刘丽云', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0600', '沪西事业部', '王东', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0640', '客户增值部', '王东', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CJ0000', '上海事业处', '袁能飞', '<EMAIL>', 'CJ0700', '战略大客事业部', '张范范', '<EMAIL>', '薛海燕', '<EMAIL>', 'CJ0730', '客户增值部', '黄超', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CK0000', '行业方案事业处', '顾华杰', '<EMAIL>', 'CK0200', '电子半导体事业部', '王鸿谕', '<EMAIL>', '张玉慧', '<EMAIL>', 'CK0270', '客户增值二部', '李征', '<EMAIL>');
INSERT INTO servicecloud.`es_customerservice_v3` (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, dpt_id, dpt_name, dpt_manager, dpt_manager_contact) VALUES ('CK0000', '行业方案事业处', '顾华杰', '<EMAIL>', 'CK0200', '电子半导体事业部', '王鸿谕', '<EMAIL>', '张玉慧', '<EMAIL>', 'CK0280', '客户增值二部', '洪佳纯', '<EMAIL>');
truncate table servicecloud.`es_customerservice_v3`;

SELECT aiv.app_name            appName,
       aiv.check_type          checkType,
       aiv.create_time         createTime,
       aiv.cve_name            cveName,
       aiv.cve_no              cveNo,
       aiv.cvss                cvss,
       aiv.is_linux            isLinux,
       aiv.priority            priority,
       aiv.severity            severity,
       aiv.update_time         updateTime,
       aiv.vuln_classification vulnClassification,
       aiv.vuln_tag            vulnTagString,
       aiv.cve_solution        cveSolution,
       aiv.description         description,
       aiv.detection_detail    detectionDetail,
       aiv.exp_link            expLink,
       aiv.ref_link            refLink,
       aiv.last_scan_time      lastScanTime,
       aiv.device_id           deviceId
FROM servicecloud.AsiaInfoVuln as aiv
         INNER JOIN (SELECT 'DE6B4CFE532CDE6B2765' as device_id, '2024-06-17 11:14:23' as last_scan_time) as aiv2
                    ON aiv2.device_id = aiv.device_id and aiv2.last_scan_time = aiv.last_scan_time
WHERE 1 = 1
  AND aiv.device_id IN ('DE6B4CFE532CDE6B2765')
  AND aiv.eid = '131866030248512'
  AND aiv.tpTenantId = '8e376771a7fa40829d4dff183df2e914'
ORDER BY aiv.severity DESC



SELECT aiv.app_name            appName,
       aiv.check_type          checkType,
       aiv.create_time         createTime,
       aiv.cve_name            cveName,
       aiv.cve_no              cveNo,
       aiv.cvss                cvss,
       aiv.is_linux            isLinux,
       aiv.priority            priority,
       aiv.severity            severity,
       aiv.update_time         updateTime,
       aiv.vuln_classification vulnClassification,
       aiv.vuln_tag            vulnTagString,
       aiv.cve_solution        cveSolution,
       aiv.description         description,
       aiv.detection_detail    detectionDetail,
       aiv.exp_link            expLink,
       aiv.ref_link            refLink,
       aiv.last_scan_time      lastScanTime,
       aiv.device_id           deviceId
FROM servicecloud.AsiaInfoVuln as aiv
         INNER JOIN (SELECT 'DE6B4CFE532CDE6B2765' as device_id, '2024-06-17 11:14:23' as last_scan_time) as aiv2
                    ON aiv2.device_id = aiv.device_id and aiv2.last_scan_time = aiv.last_scan_time
         INNER JOIN (SELECT MAX(last_scan_time) last_scan_time, cve_no
                     FROM servicecloud.AsiaInfoVuln
                     WHERE device_id IN ('DE6B4CFE532CDE6B2765')
                     GROUP BY cve_no) as aiv3 ON aiv3.cve_no = aiv.cve_no AND aiv3.last_scan_time = aiv2.last_scan_time
WHERE 1 = 1
  AND aiv.device_id IN ('DE6B4CFE532CDE6B2765')
  AND aiv.eid = '131866030248512'
  AND aiv.tpTenantId = '8e376771a7fa40829d4dff183df2e914'
ORDER BY aiv.severity DESC


SELECT COUNT(1) as cnt
FROM (SELECT eId                        eid,
             CustomerCode               customerCode,
             CustomerServiceCode        customerServiceCode,
             CustomerName               customerName,
             CustomerFullNameCH         customerFullNameCH,
             valueAddedConsultant,
             valueAddedConsultantEmail,
             bus_dpt_win_name           busDptWinName,
             bus_dpt_win_contact        busDptWinContact,
             grp_dpt_name               grpDptName,
             grp_dpt_manager            grpDptManager,
             bus_dpt_name               busDptName,
             bus_dpt_manager            busDptManager,
             Sales                      sales,
             SalesContact               salesContact,
             dpt_name                   dptName,
             dpt_manager                dptManager,
             group_concat(tts.tagId) as tagIdString
      FROM servicecloud.es_customerservice_v2 escv2
               LEFT JOIN AIEOM.tenant_tag_string tts on escv2.eId = tts.id AND tts.tagId IN
                                                                               (729636528575040, 729646154027584,
                                                                                729520260567616, 731424753066560)
      WHERE 1 = 1
      GROUP BY eid, customerCode, customerServiceCode, customerName, customerFullNameCH, valueAddedConsultant,
               valueAddedConsultantEmail, busDptWinName, busDptWinContact, grpDptName, busDptName, sales, salesContact,
               grpDptManager, busDptManager, dptName, dptManager
      HAVING array_contains_all(CAST(concat('[', tagIdString, ']') AS ARRAY<BIGINT>),
                                CAST(concat('[', 729520260567616, ']') AS ARRAY<BIGINT>))
      ORDER BY eid) as main;
select tts.id from AIEOM.tenant_tag_string  tts
          left join servicecloud.es_customerservice_v2 v2 on v2.eId = tts.id
          where tagId = 729520260567616 and v2.eId is NUll
    INSERT INTO servicecloud.CSORoleSurvey(uploadDataModelCode, collectedTime, ThemeApplayStatus, ThemeLastUpdateTime,
                                       ThemeApplySourceCode, f_surveyCompletedCount, f_surveyTotalQuantity, eid,
                                       f_CSOmktDeptStruct0101, f_CSOsalesStaffNum0102, f_orgStructureComplexity,
                                       f_CSOcatLevels0103, f_CSOsalesProdTypes0104, f_CSOcustomProdRatio0105,
                                       f_CSOsaleModelsNum0106, f_productVarietyComplexity, f_CSOquoteOrderDiff0107,
                                       f_CSOfutureMktPlanEff0108, f_dataAnalysisEfficiency, f_CSOquoteModesProcess0201,
                                       f_CSOcurrentQtnTemplate0202, f_quotingDifficulty, f_CSOcommBeforeQtnTech0203,
                                       f_CSOreqToQuoteDays0204, f_quotingCycleLength, f_CSOquoteMultiDiscnt0205,
                                       f_quotingTraceability, f_CSOchgReqPriceMgmt0206, f_productDeliveryControl,
                                       f_CSOcommBeforeOrdrTech0207, f_CSOquoteToOrderDays0208,
                                       f_CSOorderFillingMethod0209, f_orderCycleCustomerSatisfaction,
                                       f_CSOstdPrdOrderProc0210, f_CSOecommerceSelfSrv0211, f_freeClericalWorkload,
                                       f_CSOsalesOrderTracking0212, f_CSOcommOrderStatus0213, f_salesProdFinDependency)
values ('XXX', 'XXX', 'XXX', 'XXX', 'XXX', 10, 10, '41319843664448', 'XXX', XXX, 'XXX', 'XXX', 'XXX', XXX, XXX, 'XXX',
        'XXX', 'XXX', 'XXX', 'XXX', 'XXX', 'XXX', 'XXX', XXX, 'XXX', 'XXX', 'XXX', 'XXX', 'XXX', 'XXX', XXX, 'XXX',
        'XXX', 'XXX', 'XXX', 'XXX', 'XXX', 'XXX', 'XXX');


