package com.example.mybatis.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Optional;

//@ApiModel("回覆对象")
@Data
public class BaseResponse<T extends Object> {
    //@ApiModelProperty("返回代号")
    private String code;
    //@ApiModelProperty("错误信息")
    private String errMsg;
    //@ApiModelProperty("数据对象")
    private T data;

    public void setData(T data) {
        this.data = data;
        //纪录data实际的类别
        if (data == null) {
            this.dataClass = null;
        } else {
            this.dataClass = (Class<T>) data.getClass();
        }
    }

    /**
     * 获取成功的回覆对象
     * @return 填充成功代号及信息的回覆对象
     */
    public static BaseResponse ok() {
        BaseResponse baseResponse = new BaseResponse();
        ResponseCode code = ResponseCode.SUCCESS;
        baseResponse.code = code.getCode();
        baseResponse.errMsg = code.getMsg();
        return baseResponse;
    }

    /**
     * 获取成功的回覆对象
     * @param data 数据对象
     * @param msg 信息
     * @return 填充成功代号、特殊信息及数据的回覆对象
     */
    public static BaseResponse ok(Object data, String msg) {
        BaseResponse baseResponse = new BaseResponse();
        ResponseCode code = ResponseCode.SUCCESS;
        baseResponse.code = code.getCode();
        baseResponse.errMsg = msg;
        baseResponse.setData(data);
        return baseResponse;
    }

    /**
     * 获取成功的回覆对象
     * @param response 数据对象
     * @return 填充成功代号、信息及数据的回覆对象
     */
    public static BaseResponse ok(Object response) {
        BaseResponse baseResponse = new BaseResponse();
        ResponseCode code = ResponseCode.SUCCESS;
        baseResponse.code = code.getCode();
        baseResponse.errMsg = code.getMsg();
        baseResponse.setData(response);
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param responseCode 回覆代号枚举对象
     * @return 填充回覆代号及信息的回覆对象
     */
    public static BaseResponse error(ResponseCode responseCode) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getMsg();
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param responseCode 回覆代号枚举对象
     * @param response 数据对象
     * @return 填充回覆代号、信息及数据的回覆对象
     */
    public static BaseResponse error(ResponseCode responseCode, Object response) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getMsg();
        baseResponse.setData(response);
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param responseCode 回覆代号枚举对象
     * @param ex 异常信息对象
     * @return 填充回覆代号、(信息+异常信息)及数据的回覆对象
     */
    public static BaseResponse error(ResponseCode responseCode, Exception ex) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getMsg() + "\n" + ex.getMessage();
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param response 回覆对象(为空时自动产生)
     * @param ex 异常信息对象
     * @return 回覆对象
     */
    public static BaseResponse error(BaseResponse response, Exception ex) {
        if (response == null) {
            response = new BaseResponse();
        }
        //固定是内部错误
        ResponseCode code = ResponseCode.INTERNAL_ERROR;
        response.code = code.getCode();
        response.errMsg = ex.toString();
        return response;
    }

    /**
     * 获取错误的回覆对象
     * @param ex 异常信息对象
     * @return 填充回覆代号及异常信息的回覆对象
     */
    public static BaseResponse error(Exception ex) {
        BaseResponse baseResponse = new BaseResponse();
        //固定是内部错误
        ResponseCode code = ResponseCode.INTERNAL_ERROR;
        baseResponse.code = code.getCode();
        baseResponse.errMsg = ex.toString();
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param responseCode 回覆代号枚举对象
     * @param errMsg 异常信息
     * @return 填充回覆代号及异常信息的回覆对象
     */
    public static BaseResponse error(ResponseCode responseCode, String errMsg) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getMsg() + "\n" + errMsg;
        return baseResponse;
    }

    /**
     * 获取错误的回覆对象
     * @param code 错误代号
     * @param errMsg 异常信息
     * @return 填充回覆代号及异常信息的回覆对象
     */
    public static BaseResponse error(String code, String errMsg) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = code;
        baseResponse.errMsg = errMsg;
        return baseResponse;
    }

    /**
     * 获取动态错误讯息的回覆对象
     * @param responseData 回覆数据
     * @param responseCode 回覆代号枚举对象
     * @param param 参数
     * @return 填充回覆代号、信息及数据的回覆对象
     */
    public static BaseResponse dynamicError(Object responseData, ResponseCode responseCode, Object... param) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getDynamicMsg(param);
        baseResponse.setData(responseData);
        return baseResponse;
    }

    /**
     * 获取动态错误讯息的回覆对象
     * @param responseCode 回覆代号枚举对象
     * @param param 参数
     * @return 回覆对象
     */
    public static BaseResponse dynamicError(ResponseCode responseCode, Object... param) {
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.code = responseCode.getCode();
        baseResponse.errMsg = responseCode.getDynamicMsg(param);
        return baseResponse;
    }

    //@ApiModelProperty(value = "数据类别", hidden = true)
    @JsonIgnore
    private Class<T> dataClass;

    /**
     * 获取实际数据对象(如果没有设置过data，将直接使用Object类别进行转换)
     * @return 返回T实际的对象值，如果无法传换将返回null
     */
    //@ApiModelProperty(value = "获取实际数据对象", hidden = true)
    @JsonIgnore
    public Optional<T> getCurrentData() {
        Class<T> clazz = this.dataClass;
        if (clazz == null) {
            clazz = (Class<T>) Object.class;
        }
        return getCurrentData(clazz);
    }

    /**
     * 获取实际数据对象
     * @param clazz 特定类别
     * @return 返回T实际的对象值，如果无法传换将返回null
     */
    //@ApiModelProperty(value = "获取实际数据对象", hidden = true)
    @JsonIgnore
    public Optional<T> getCurrentData(Class<T> clazz) {
        return getCurrentData(clazz, false);
    }

    /**
     * 获取实际数据对象
     * @param allowNull 是否允许返回null
     * @return 返回T实际的对象值，如果无法传换将返回null
     */
    //@ApiModelProperty(value = "获取实际数据对象", hidden = true)
    @JsonIgnore
    public Optional<T> getCurrentData(Class<T> clazz, boolean allowNull) {

        if (this.data == null) {
            if (allowNull) {
                return Optional.of(null);
            }
            return Optional.empty();
        }
        return Optional.of(as(clazz, this.data));
    }

    private <T> T as(Class<T> t, Object o) {
        return t.isInstance(o) ? t.cast(o) : null;
    }

    /**
     * 检查是否成功
     * @return 是否成功(true:成功；false:失败)
     */
    public boolean checkIsSuccess() {
        return ResponseCode.SUCCESS.isSameCode(this.code);
    }

    /**
     * 检查是否符合特定代号
     * @param code 特定代号
     * @return 是否符合
     */
    public boolean checkEqualSomeCode(ResponseCode code) {
        if (code == null) {
            return this.code == null;
        }
        return checkEqualSomeCode(code.getCode());
    }

    /**
     * 检查是否符合特定代号
     * @param code 特定代号
     * @return 是否符合
     */
    public boolean checkEqualSomeCode(String code) {
        if (code == null) {
            return this.code == null;
        }
        return code.equals(this.code);
    }

    /**
     * 检查是否成功，并取得实际数据
     * @return 未成功返回Optional.empty，成功返回实际数据
     */
    public Optional<T> checkAndGetCurrentData() {
        if (!checkIsSuccess()) {
            return Optional.empty();
        }
        return this.getCurrentData();
    }
}
