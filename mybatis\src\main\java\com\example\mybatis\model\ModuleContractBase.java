package com.example.mybatis.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 模组合约基础类
 */
@Data
public abstract class ModuleContractBase {
    //("主键")
    private Long id;
    //("运维商Id")
    private Long sid;
    //("模组Id")
    private Long moduleId;
    //("授权起始日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startDate;
    //("授权截止日期 yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;
    @Deprecated
    //("服务器U数(作废)-请改用模组合约明细")
    private int serverCount;
    @Deprecated
    //("终端U数(作废)-请改用模组合约明细")
    private int workStationCount;
    @Deprecated
    //("物联网U数(作废)-请改用模组合约明细")
    private int iotCount;

    //("模组代号")
    private String moduleCode;
    //("模组名称")
    private String moduleName;
    //("品号")
    private String itemNo;

    //("服务商ISVsid")
    private Long serviceIsvSid;
    //("服务商名称")
    private String serviceIsvName;
}
