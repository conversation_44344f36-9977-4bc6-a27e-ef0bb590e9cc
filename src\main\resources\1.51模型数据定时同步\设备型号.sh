mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "insert into NetworkSecurityAuditDevice (f_nsadevice_id,deviceModel)
select f_nsadevice_id,daiam.bios_manufacturer from NetworkSecurityAuditDevice nsad
left join DeviceAdvancedInfoAttributeModel daiam on nsad.aiopsItemId = daiam.deviceId where nsad.aiopsItemId is not null and nsad.aiopsItemId != ''"

mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "insert into NetworkSecurityAuditDevice (f_nsadevice_id,placementPoint)
select f_nsadevice_id,ad.placementPoint from NetworkSecurityAuditDevice nsad
left join AiopsDevice ad on nsad.aiopsItemId = ad.deviceId where nsad.aiopsItemId is not null and nsad.aiopsItemId != ''"


mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "
insert into NetworkSecurityAuditDB (f_nsadb_id,databaseVersion)
select f_nsadb_id,t2.sqlserver_version from NetworkSecurityAuditDB t1
left join (select concat(t1.sqlserver_version,' ',t1.edition,' ',t1.version_no) sqlserver_version,t1.source_db_id from SQLServerDatabaseVersion t1
inner join (select max(collectedTime)collectedTime,source_db_id from SQLServerDatabaseVersion group by source_db_id) t2
on t1.collectedTime = t2.collectedTime and t1.source_db_id = t2.source_db_id) t2 on t1.aiopsItemId = t2.source_db_id
where t2.sqlserver_version is not null and t1.aiopsItemId is not null and t1.aiopsItemId !='' and t2.source_db_id is not null;"

mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "
insert into NetworkSecurityAuditDB (f_nsadb_id,database_size)

WITH RankedMsSQLDbSize AS (
    SELECT
        source_db_id,
        database_name,
        database_size,
        collectedTime,
        DENSE_RANK() OVER (PARTITION BY source_db_id ORDER BY collectedTime DESC) as rn
    FROM MsSQLDbSize
),
LatestFilteredMsSQLDbSize AS (
    SELECT
        source_db_id,
        database_name,
        REPLACE(database_size, 'MB', '') AS database_size_str
    FROM RankedMsSQLDbSize
    WHERE rn = 1
      AND database_name NOT IN ('master','model','msdb','tempdb','ReportServer','ReportServerTempDB')
),
AggregatedLatestSize AS (

    SELECT
        source_db_id,
        SUM(CAST(database_size_str AS DECIMAL(18,2))) AS total_database_size
    FROM LatestFilteredMsSQLDbSize
    GROUP BY source_db_id
)

SELECT
    t1.f_nsadb_id,
    t2.total_database_size AS database_size
FROM NetworkSecurityAuditDB t1
LEFT JOIN AggregatedLatestSize t2 ON t1.aiopsItemId = t2.source_db_id
WHERE
    t2.total_database_size IS NOT NULL
    AND t1.aiopsItemId IS NOT NULL
    AND t1.aiopsItemId != '';"


mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "
insert into NetworkSecurityAuditDB (f_nsadb_id, databaseVersion)
select f_nsadb_id, t2.database_ver_nu
from NetworkSecurityAuditDB t1
left join (
    select t1.database_ver_nu, t1.source_db_id
    from T100OracleDatabaseBasicInformation_sr_duplicate t1
    inner join (
        select max(collectedTime) collectedTime, source_db_id
        from T100OracleDatabaseBasicInformation_sr_duplicate
        group by source_db_id
    ) t2
    on t1.collectedTime = t2.collectedTime and t1.source_db_id = t2.source_db_id
) t2
on t1.aiopsItemId = t2.source_db_id
where t2.database_ver_nu is not null and t1.aiopsItemId is not null and t1.aiopsItemId !='' and t2.source_db_id is not null;"

mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "
insert into NetworkSecurityAuditDB (f_nsadb_id, database_size)
select f_nsadb_id, t2.database_size
from NetworkSecurityAuditDB t1
left join (
    select sum(sum_MB) as database_size, otsc1.source_db_id
    from OracleDBTableSpaceCollected_sr_duplicate otsc1
    inner join (
        select max(collectedTime) collectedTime, source_db_id
        from OracleDBTableSpaceCollected_sr_duplicate
        group by source_db_id
    ) otsc2
    on otsc1.collectedTime = otsc2.collectedTime and otsc1.source_db_id = otsc2.source_db_id
    group by otsc1.source_db_id
) t2
on t1.aiopsItemId = t2.source_db_id
where t2.database_size is not null and t1.aiopsItemId is not null and t1.aiopsItemId !='' and t2.source_db_id is not null;"

-- E10 安装位置
mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "INSERT INTO NetworkSecurityExamInfoSystem (infoSystemId, eid, deploymentLocation)
SELECT
    target.infoSystemId,
    target.eid,
    source_data.aggregated_deviceNames AS new_deploymentLocation
FROM
    NetworkSecurityExamInfoSystem AS target
INNER JOIN
    (
        SELECT
            GROUP_CONCAT(DISTINCT ranked.deviceName ORDER BY ranked.latest_time DESC SEPARATOR ',') AS aggregated_deviceNames,
            '37' AS aiopsItem,
            ranked.eid
        FROM (
            SELECT
                deviceName,
                eid,
                latest_time,
                ROW_NUMBER() OVER (PARTITION BY eid ORDER BY latest_time DESC) AS rn
            FROM (
                SELECT
                    deviceName,
                    eid,
                    MAX(collectedTime) AS latest_time
                FROM servicecloud.process_execute_mem_info_biz_info
                WHERE collectedTime >= DATE_SUB(NOW(), INTERVAL 90 DAY) and collectedTime <= NOW()
                GROUP BY deviceName, eid
            ) AS grouped_devices
        ) AS ranked
        WHERE rn <= 10
        GROUP BY ranked.eid
    ) AS source_data
ON
    target.eid = source_data.eid AND target.aiopsItem = source_data.aiopsItem WHERE target.aiopsItem = '37'"

-- 易飞 T100安装位置
CREATE TABLE NetworkSecurityExamInfoSystemDeploymentLocation (
    eid BIGINT NOT NULL COMMENT 'Primary Key: EID, unique identifier for the entity',
    aiopsItem VARCHAR(50) NOT NULL COMMENT 'Primary Key: AIOps item identifier, e.g., "100"',
    deviceName STRING NULL COMMENT 'Comma-separated list of device names associated with the EID and AIOps item'
)
PRIMARY KEY (eid, aiopsItem)
DISTRIBUTED BY HASH(eid)
ORDER BY (eid, aiopsItem)
PROPERTIES (
    "replication_num" = "1" -- Adjust replication number as per your cluster setup
);

python /opt/soft/datax/bin/datax.py datax_NetworkSecurityExamInfoSystemDeploymentLocation.json;

mysql -h 10.100.126.132 -P 19030 -uservicecloud --database=servicecloud -pservicecloud@123 -e "
INSERT INTO NetworkSecurityExamInfoSystem (infoSystemId, eid, deploymentLocation)
SELECT
    nsis_target.infoSystemId,
    nsisdl.eid,
    nsisdl.deviceName
FROM
    NetworkSecurityExamInfoSystemDeploymentLocation AS nsisdl
LEFT JOIN
    NetworkSecurityExamInfoSystem AS nsis_target
ON
    nsisdl.eid = nsis_target.eid and nsis_target.aiopsItem = nsisdl.aiopsItem
WHERE
    nsis_target.infoSystemId IS not NULL and nsis_target.aiopsItem in ('08','100');"