 、
### 1. 查看网安稽查项目
```plantuml
@startuml
left to right direction
actor MIS as User

rectangle "网安稽查项目" {
  usecase "查看网安稽查项目类别" as UC1
  usecase "搜索网安稽查项目类别" as UC2
  usecase "查看网安稽查项目" as UC3
  usecase "查询运维实例" as UC5
  usecase "新增网安稽查项目" as UC4
 
}

User --> UC1 : 访问主界面
User --> UC2 : 使用搜索框+筛选条件
User --> UC3 : 点击类别+访问右侧稽查项目列表
User --> UC4 : 点击新增按钮
User --> UC5 : 点击下拉选择已有系统（实例）
@enduml
```
>根据上文得UI图和核心用例图，帮我生成一份活动图，生成的内容同样也是用markdown的plantuml。要求如下
>1. 活动图的泳道有，角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、后台服务（模型服务）、后台服务（运维实例服务）、Mysql、大数据平台
>2. 每个活动都要有开始和结束节点，
>3. 除去开始结束节点的每个节点应该都是动词+名词的结构
>3. 每一个用例都要有活动图，且都分开，但是第四个和第5个用例要一起
>4. 每一个服务查询都会查询对应的Mysql库
>5. 活动图需要比较细致，包括角色点击 操作，企业运维服务云页面发起请求等
>6. 根据UI图1，是网安稽查项目的UI图，对应的用例图是上述网安稽查项目用例图，第一个用例查看网安稽查类别，需要包括角色进入页面 页面查询类别 然后返回类别 。
>7. 根据第二个用例用户搜索类别 再返回类别
>8. 根据第三个用例用户点击类别 页面查询类别下的模型列表 然后返回类别下的模型列表数据,之后再点击某个具体的模型，先去查询网安稽查服务，查询项目数据 然后再去查询模型服务下具体的模型数据 此时根据项目数据 再去查询大数据平台，组装数据。
>9. 上面的用例只涉及到了角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、Mysql 这几个泳道
>10. 根据第四个用例和第五个用例 一起生成活动图，用到的UI图1 和 UI图2，涉及到的泳道有角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、后台服务（模型服务）、后台服务（运维实例服务）、Mysql、大数据平台，首先角色点击新增项目,页面可以弹出表单,表单是根据模型服务的功能渲染的动态数据,然后要去查询对应的实例服务，然后返回实例服务数据，然后在页面上的表单填写数据，之后再选择刚刚查询出的实例数据，最后要保存到网安稽查服务，同时还需要保存到大数据平台

>是这个UI图的页面，还有页面对应得后端java服务，还有对应得db。大约得流程是这样得，首先再网安稽查项目那边新建资产类得项目，然后资产类得项目下面有四个分类 信息系统、设备、数据库、网站 每个分类下面会选择对应得系统或者设备，填写完成之后，再去网安稽查记录那边新增记录，第一步维护资产清单，首先就是选择网安稽查项目那边得系统或者设备下面得资产，第二步进行分数计算（检测评估），第三步就是产出报告


```plantuml
@startuml
left to right direction
actor MIS as User

rectangle "网安稽查项目" {
  usecase "删除网安稽查项目" as UC6
  usecase "编辑网安稽查项目" as UC7
  usecase "关联产品" as UC8
 
}

User --> UC6 : 点击删除网安稽查项目
User --> UC7 : 点击编辑网安稽查项目
User --> UC8 : 点击关联产品（实例）
@enduml
```

>根据上文得UI图和核心用例图，帮我生成一份活动图，生成的内容同样也是用markdown的plantuml。要求如下
>1. 活动图的泳道有，角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、后台服务（运维实例服务）、Mysql、大数据平台
>2. 每个活动都要有开始和结束节点，
>3. 除去开始结束节点的每个节点应该都是动词+名词的结构
>3. 每一个用例都要有活动图，且都分开，但是第四个和第5个用例要一起
>4. 每一个服务查询都会查询对应的Mysql库
>5. 活动图需要比较细致，包括角色点击 操作，企业运维服务云页面发起请求等
>6. 第一个用例图删除网安稽查项目，先调用网安稽查服务 删除项目，然后再删除mysql数据，然后再删除大数据平台数据 。
>7. 第二个用例图编辑网安稽查项目，先调用网安稽查服务 更新项目，然后再更新mysql数据，然后再更新大数据平台数据。
>8. 第三个用例图点击关联产品，先调用运维实例服务获取租户下运维实例数据，然后在页面上选择实例，点击保存，之后调用网安稽查服务保存项目和实例关联至mysql。



# 网安稽查流程活动图
```plantuml
@startuml
|#pink|企业运维服务云|
|#plum|后端Java服务|
|#lightblue|数据库|

start
partition 网安稽查项目流程 {
|企业运维服务云|
:点击【+新增】资产类项目;
|后端Java服务|
:接收新建项目请求;
|数据库|
:创建项目记录;
|后端Java服务|
:返回项目ID;

    |企业运维服务云|
    repeat :为项目添加分类（信息系统/设备/数据库/网站）;
        :选择或创建系统/设备;
        |后端Java服务|
        :验证资产信息;
        |数据库|
        if (新资产?) then (是)
            :插入资产记录;
        else (否)
            :更新关联关系;
        endif
        |企业运维服务云|
    repeat while (需要继续添加?) 
    :提交完整资产项目;
}

partition 网安稽查记录流程 {
|企业运维服务云|
:进入【稽查记录】点击【新建检测】;
|后端Java服务|
:创建检测记录;
|数据库|
:初始化检测记录状态为"资产维护";

    |企业运维服务云|
    :步骤1-维护资产清单:
    选择关联的项目资产;
    |后端Java服务|
    :批量关联资产与检测记录;
    |数据库|
    :更新检测资产清单;
    
    |企业运维服务云|
    :步骤2-检测评估:
    点击【开始评估】按钮;
    |后端Java服务|
    :启动评分引擎;
    while (遍历所有资产?) is (未完成)
        :获取资产安全配置;
        |数据库|
        :查询资产详细信息;
        |后端Java服务|
        :计算安全评分;
        |数据库|
        :保存评分结果;
    endwhile
    :更新检测状态为"评估完成";
    
    |企业运维服务云|
    :步骤3-生成报告:
    点击【生成报告】按钮;
    |后端Java服务|
    :汇总评估结果;
    :生成PDF报告文件;
    |数据库|
    :存储报告文件路径;
    :更新检测状态为"报告就绪";
    
    |企业运维服务云|
    if (需要重新评估?) then (是)
        -> 重新评估;
        :点击【重新评估】按钮;
        |后端Java服务|
        :重置评估结果;
        |数据库|
        :清除历史评分;
        -> 步骤2-检测评估;
    else (否)
        :查看/下载检测报告;
    endif
}
stop
@enduml
```