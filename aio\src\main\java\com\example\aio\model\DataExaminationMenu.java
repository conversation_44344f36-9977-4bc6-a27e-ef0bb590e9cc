package com.example.aio.model;

import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class DataExaminationMenu {
    private String id;
    private String menuName;
    private String menuName_CN = "";
    private String menuName_TW = "";
    private String menuCode;
    private String parentId;
    private String menuType;
    private String menuSource;
    private String imgUrl;
    private String appCode;
    private Long eid;
    private Long sid;
    private Long menuStatus;
    private Integer menuOrder;

    private Date createDate;
    private Date updateDate;
    private List<DataExaminationMenu> children;



    private Boolean isPage;

    public String getBusKey(){
        // 使用Hutool生成MD5哈希
        String md5Hex = DigestUtil.md5Hex(menuName + menuCode + parentId + menuType + menuStatus + menuSource + appCode + eid + sid);
        BigInteger mod = new BigInteger(md5Hex, 16).mod(new BigInteger("1000000000000000"));
        // 将MD5哈希转换为一个大整数

        return mod.toString();
    }



    //菜单移动
    private List<DataExaminationMenu> menus;

    public DataExaminationMenu() {

    }

    public DataExaminationMenu(String id, String menuType) {
        this.id = id;
        this.menuType = menuType;
    }

    //服务商运维平台新增模组管理
    private List<String> moduleCodeList;
}
