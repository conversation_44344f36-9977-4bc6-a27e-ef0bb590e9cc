-- 大陆
CREATE TABLE `AiopsTenant_bak_20241204` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

insert into AiopsTenant_bak_20241204 select * from AiopsTenant
-- 测试
CREATE TABLE `AiopsTenant_bak_20241204` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "false",
"replication_num" = "1"
);

-- 台湾
CREATE TABLE `AiopsTenant_bak_20241204` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
CREATE TABLE `AiopsTenant` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

insert into AiopsTenant_bak_20241204 select * from AiopsTenant;

-- tag_tenant_int
-- tag_tenant_long
# tag_tenant_score
# tag_tenant_string
CREATE TABLE `tag_tenant_int` (
                                  `tagId` bigint(20) NULL COMMENT "id",
                                  `tagValue` int(11) NULL COMMENT "值",
                                  `tagDate` date NULL COMMENT "生成日期",
                                  `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                  `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `tag_tenant_int_bak_20241219` (
                                  `tagId` bigint(20) NULL COMMENT "id",
                                  `tagValue` int(11) NULL COMMENT "值",
                                  `tagDate` date NULL COMMENT "生成日期",
                                  `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                  `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

insert into tag_tenant_int_bak_20241219 select * from tag_tenant_int;

CREATE TABLE `tag_tenant_long` (
                                   `tagId` bigint(20) NULL COMMENT "id",
                                   `tagValue` bigint(20) NULL COMMENT "值",
                                   `tagDate` date NULL COMMENT "生成日期",
                                   `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                   `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `tag_tenant_long_bak_20241219` (
                                   `tagId` bigint(20) NULL COMMENT "id",
                                   `tagValue` bigint(20) NULL COMMENT "值",
                                   `tagDate` date NULL COMMENT "生成日期",
                                   `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                   `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into tag_tenant_long_bak_20241219 select * from tag_tenant_long;


CREATE TABLE `tag_tenant_string` (
                                     `tagId` bigint(20) NULL COMMENT "id",
                                     `tagValue` varchar(65533) NULL COMMENT "值",
                                     `tagDate` date NULL COMMENT "生成日期",
                                     `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                     `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `tag_tenant_string_bak_20241219` (
                                     `tagId` bigint(20) NULL COMMENT "id",
                                     `tagValue` varchar(65533) NULL COMMENT "值",
                                     `tagDate` date NULL COMMENT "生成日期",
                                     `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                     `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into tag_tenant_string_bak_20241219 select * from tag_tenant_string;

alter table tag_tenant_string_bak_20241219 rename tag_tenant_string;
alter table tag_tenant_string_bak_20241219 rename tag_tenant_string;

CREATE TABLE `tag_tenant_score` (
                                    `tagId` bigint(20) NULL COMMENT "id",
                                    `score` decimal(10, 0) NULL COMMENT "分数值",
                                    `tagDate` date NULL COMMENT "生成日期",
                                    `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                    `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `score`, `tagDate`)
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "2"
);

CREATE TABLE `tag_tenant_score_bak_20241213` (
                                    `tagId` bigint(20) NULL COMMENT "id",
                                    `score` decimal(10, 0) NULL COMMENT "分数值",
                                    `tagDate` date NULL COMMENT "生成日期",
                                    `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                    `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `score`, `tagDate`)
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "true",
"replication_num" = "2"
);
insert into tag_tenant_score_bak_20241213 select * from tag_tenant_score;

insert into AIEOM.tag_tenant_string(tagId,tagValue,tagDate,uv,updateTime) values(1,'2','2024-12-12',bitmap_from_string('1,2,3'),now());
-- 恢复
drop table tag_tenant_int;
alter table  tag_tenant_int_bak_20241212 rename tag_tenant_int;

select bitmap_to_string(tti.uv),bitmap_to_string(t.uv) from tag_tenant_int tti
left join tag_tenant_int_bak_20241212 t on tti.tagId = t.tagId and tti.tagValue = t.tagValue and tti.tagDate = t.tagDate
 where bitmap_to_string(tti.uv)!=bitmap_to_string(t.uv)
limit 1;

select bak.orderId,at.orderId from AiopsTenant at
INNER JOIN AiopsTenant_bak_20241204 bak ON bak.eid = at.eid
WHERE bak.orderId != at.orderId;


alter table  tag_tenant_string rename tag_tenant_string_bak_error_20241220;
alter table tag_tenant_int rename tag_tenant_int_bak_error_20241220;
alter table tag_tenant_long rename tag_tenant_long_error_20241220;

alter table tag_tenant_string_bak_20241219 rename tag_tenant_string;
alter table tag_tenant_int_bak_20241219 rename tag_tenant_int;
alter table tag_tenant_long_bak_20241219 rename tag_tenant_long;

CREATE TABLE `tag_tenant_string_bak_20241220` (
                                     `tagId` bigint(20) NULL COMMENT "id",
                                     `tagValue` varchar(65533) NULL COMMENT "值",
                                     `tagDate` date NULL COMMENT "生成日期",
                                     `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                     `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into AIEOM.tag_tenant_string_bak_20241220 select * from AIEOM.tag_tenant_string;

CREATE TABLE `tag_tenant_long_bak_20241220` (
                                   `tagId` bigint(20) NULL COMMENT "id",
                                   `tagValue` bigint(20) NULL COMMENT "值",
                                   `tagDate` date NULL COMMENT "生成日期",
                                   `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                   `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into AIEOM.tag_tenant_long_bak_20241220 select * from AIEOM.tag_tenant_long;

CREATE TABLE `tag_tenant_int_bak_20241220` (
                                  `tagId` bigint(20) NULL COMMENT "id",
                                  `tagValue` int(11) NULL COMMENT "值",
                                  `tagDate` date NULL COMMENT "生成日期",
                                  `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
                                  `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE=OLAP
    AGGREGATE KEY(`tagId`, `tagValue`, `tagDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into AIEOM.tag_tenant_int_bak_20241220 select * from AIEOM.tag_tenant_int;
