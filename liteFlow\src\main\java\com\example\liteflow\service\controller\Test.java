package com.example.liteflow.service.controller;


import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
@RequestMapping("/lite/flow/")
@RestController
public class Test {
    @Resource
    private FlowExecutor flowExecutor;

    @GetMapping("/test")
    public void testConfig() {
        LiteflowResponse response = flowExecutor.execute2Resp("chain1", "arg");
        System.out.println(response);
    }
}

