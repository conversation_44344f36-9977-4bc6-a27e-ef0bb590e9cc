spring.application.name=aiocdp
spring.cloud.nacos.discovery.metadata.name=${spring.application.name}
server.port=9223
spring.messages.basename=i18n/messages
## Management config
management.endpoints.web.exposure.include=(??????userv2????????? ??)
management.endpoints.jmx.exposure.include=(??????userv2????????? ??)
management.endpoint.health.show-details=(??????userv2????????? ??)
management.endpoints.web.base-path=(??????userv2????????? ??)
## Swagger config
swagger.headers=(??????userv2????????? ??)
swagger.host=(??????userv2????????? ??)

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8:00

spring.datasource.url=(??????userv2????????? ??)
spring.datasource.username=(??????userv2????????? ??)
spring.datasource.password=(??????userv2????????? ??)
spring.datasource.driver-class-name=(??????userv2????????? ??)
spring.datasource.tomcat.test-on-borrow=(??????userv2????????? ??)
spring.datasource.tomcat.initialSize=(??????userv2????????? ??)
spring.datasource.tomcat.validationQuery=(??????userv2????????? ??)

# Redis?????
spring.redis.host=(??????userv2????????? ??)
# Redis???????
spring.redis.port=(??????userv2????????? ??)
spring.redis.password=
spring.redis.pool.max-active=(??????userv2????????? ??)
spring.redis.pool.max-wait=(??????userv2????????? ??)
spring.redis.pool.max-idle=(??????userv2????????? ??)
spring.redis.pool.min-idle=(??????userv2????????? ??)
spring.redis.timeout=(??????userv2????????? ??)

## Rabbit config
message_server=(??????userv2????????? ??)
spring.rabbitmq.host=(??????userv2????????? ??)
spring.rabbitmq.port=(??????userv2????????? ??)
spring.rabbitmq.username=(??????userv2????????? ??)
spring.rabbitmq.password=(??????userv2????????? ??)

#??????6?
ribbon.ConnectTimeout=(??????userv2????????? ??)
ribbon.ReadTimeout=(??????userv2????????? ??)

mybatis.mapper-locations=(??????userv2????????? ??)
management.security.enabled=(??????userv2????????? ??)

service.area=(??????userv2????????? ??)
digiwin.user.defaultlanguage=(??????userv2????????? ??)
digiwin.user.defaultserviceregion=(??????userv2????????? ??)
digiwin.user.defaulttimezone=(??????userv2????????? ??)
digiwin.user.connectarea=(??????userv2????????? ??)
esc.integration.iamAddress=(??????userv2????????? ??)
esc.integration.appToken=(??????userv2????????? ??)
esc.integration.emcAddress=(??????userv2????????? ??)
esc.integration.omcAddress=(??????userv2????????? ??)
esc.integration.cacAddress=(??????userv2????????? ??)
esc.integration.pmcAddress=h(??????userv2????????? ??)
esc.integration.gmcAddress=(??????userv2????????? ??)
esc.integration.bossIamAddress=(??????userv2????????? ??)

logging.level.escloud.usercontactmapper=(??????aiouser????????? ??)

digiwin.token.user.verifyuserid=(??????userv2????????? ??)
digiwin.token.tenant.id=(??????userv2????????? ??)
dcdp.app.token=(??????userv2????????? ??)
acp.url=(??????userv2????????? ??)
dcdp.dmp.url=(??????userv2????????? ??)
dcp.product.code=(??????userv2????????? ??)
dcp.contract=(??????userv2????????? ??)
dcp.auth.user=(??????userv2????????? ??)
logging.level.com.digiwin.escloud.aiouser.service.impl=(??????aiouser????????? ??)
logging.level.com.digiwin.escloud.aiouser.dao.IUserDao=(??????aiouser????????? ??)

digiwin.supplier.defaultsid=(??????aiouser????????? ??)
digiwin.app.code=(??????aiouser????????? ??)

digiwin.escloud.dbname=(??????aioitms????????? ??)
api.bigdata.url=(??????aioitms????????? ??)

