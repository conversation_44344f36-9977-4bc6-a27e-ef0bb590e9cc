    beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_11_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
            WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
            WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
            WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
            WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
            ELSE get_json_object(model,'$.DataContent.Product_Line')
        END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Material_proportion') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Labor_proportion') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Processing_fees') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Production_costs1') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Production_costs2') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Production_costs3') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Production_costs4') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Production_costs5') AS DECIMAL(38,2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CostCompositionRatio"
CREATE TABLE tbb.dws_aiops_pec_erpindex_4_11_0 (   key STRING,   source_db_id STRING,   product_line STRING,   indicatornumber STRING,   enterprisecode INT,   account_set STRING,   account_set_name STRING,   year STRING,   month STRING,   material_proportion DECIMAL(38,2),   labor_proportion DECIMAL(38,2),   processing_fees DECIMAL(38,2),   production_costs1 DECIMAL(38,2),   production_costs2 DECIMAL(38,2),   production_costs3 DECIMAL(38,2),   production_costs4 DECIMAL(38,2),   production_costs5 DECIMAL(38,2),   sort_minus_1_field STRING,   deviceid STRING,   eid STRING,   collectedtime STRING,   collectconfigid STRING,   uploaddatamodelcode STRING,   devicecollectdetailid STRING,   aiid STRING,   aiopsitem STRING,   flumetimestamp STRING,   yearandmonth TIMESTAMP ) WITH SERDEPROPERTIES ('serialization.format'='1') STORED AS PARQUET LOCATION 'hdfs://nameservice1/user/hive/warehouse/tbb.db/dws_aiops_pec_erpindex_4_11_0' TBLPROPERTIES ('COLUMN_STATS_ACCURATE'='{\"BASIC_STATS\":\"true\"}', 'numFiles'='0', 'numFilesErasureCoded'='0', 'numRows'='0', 'rawDataSize'='0', 'totalSize'='0')


beeline -d "com.cloudera.impala.jdbc41.Driver" -u "*****************************" -n service_cloud -e "
select eid as tenantSid, collectedtime,
get_json_object(model, '$.DataContent.MA_DBID') as MA_DBID,
get_json_object(model, '$.DataContent.MA_INDCODE') as MA_INDCODE,
get_json_object(model, '$.DataContent.MA_DBID') as MA_DBID,
get_json_object(model, '$.DataContent.MA_index') as MA_index,
get_json_object(model, '$.DataContent.MA_YYYYMM') as MA_YYYYMM,
get_json_object(model, '$.DataContent.MA_data') as MA_data
from MA_Report_test LIMIT 5
"