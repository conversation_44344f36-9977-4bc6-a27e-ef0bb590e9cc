# 网安智检二期修改点

1. 网安智检记录分简式和完整两种类型
   - 简式类型：采用简式指标，创建新的体检
   - 完整类型：采用完整指标，采用现有体检（**是否使用现在的网安智检**）
   - 历史数据默认都是完整类型 1H
   - 修改`/network/security/saveNetworkSecurityExamRecord`api 支持简式、完整 2种体检保存 2H
   - 完成类型指标创建（**是否使用现在的网安智检指标？**）、简式类型指标创建（**什么时候给出？、谁来创建？**） 1H
2. 导出资产清单
   - 硬件设备
   - 信息系统
   - 数据库
   - **以上三种类型写死（根据资产类动态查询）？**
   - 硬件设备字段不匹配：**设备类别、型号、位置**
   - 信息系统字段不匹配：**系统功能、部署位置、责任人**
   - 数据库字段不匹配：**存储内容、数据库大小、责任人**
   - **以上字段是新增？正式环境旧有数据更新 2H**
   - **导出字段是否动态** 不是动态：导出数据 3H 动态：如果前端解析模型字段(会跟着模型动态变化) 3H 如果后端解析模型字段、保存ES(不会根据模型动态变化)7H
3. 网安问卷自查表
   - 除 信息系统基本情况、信息系统运营使用单位基本情况的上半部分 **其他部分数据来源？**  如果模型不是自建 14H 如果自建模型一个模型1H
   - 这里面有网站相关数据(表单)，网站如果在资产类别下，需要修改`/network/security/getProjectType?parentCode=Asset`这类api过滤网站 3H
   - 自查表保存ES 4H
4. 简式报告
   - 弱密码排查 密码必须可以获取到明文 2H
   - 安全防毒 3H
   - **备份检查？** **4H ？**
   - 环境检查 3H
5. 网安智检 门户页查询
   - 严重预警数
   - 存在严重漏洞设备数(台湾不做) **应该都是最后一次扫描的严重漏洞设备数和漏洞数？** 2H
   - 弱密码策略设备数 2H
   - **备份？** **4H ？**