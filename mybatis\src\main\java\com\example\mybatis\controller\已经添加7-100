2024-10-12T16:59:07.176+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : selectE10 count:321
2024-10-12T16:59:07.627+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321701696064 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4f87f55aa5ba096002733d86e38d0db9&deviceId=479551468039324995
2024-10-12T16:59:07.723+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a6f2918b3150c5b5f6c28e5f3296f4e7\\\",\\\"dbIdValue\\\":\\\"a6f2918b3150c5b5f6c28e5f3296f4e7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_5.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_5.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479551468039324995
2024-10-12T16:59:09.680+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319636505152 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a8efcbec163441849f721fd2e5f9d69d&deviceId=479549123540829753
2024-10-12T16:59:09.680+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d703b55ff5439bd6d36946296d8a355b\\\",\\\"dbIdValue\\\":\\\"d703b55ff5439bd6d36946296d8a355b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NBJL\\\",\\\"targetValue\\\":\\\"NBJL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479549123540829753
2024-10-12T16:59:11.020+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:261927930872384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=34c422558d8399d773947fe53257c116&deviceId=479526976760067139
2024-10-12T16:59:11.020+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c580adfb00a526b59651cb1581d5ad3a\\\",\\\"dbIdValue\\\":\\\"c580adfb00a526b59651cb1581d5ad3a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LUCKY\\\",\\\"targetValue\\\":\\\"LUCKY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479526976760067139
2024-10-12T16:59:12.348+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:122354996421184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=91b0480f3259ccac481235b7b0f7bf40&deviceId=479401383309620277
2024-10-12T16:59:12.349+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f5508bf902b4a3fce59ea51a916c522b\\\",\\\"dbIdValue\\\":\\\"f5508bf902b4a3fce59ea51a916c522b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GD\\\",\\\"targetValue\\\":\\\"GD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479401383309620277
2024-10-12T16:59:13.686+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:164111167644224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c5edaa025fb157e7453cd8412e5145d8&deviceId=479379759793452099
2024-10-12T16:59:13.688+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2db91ca1b092ae88d864cde274f29ad2\\\",\\\"dbIdValue\\\":\\\"2db91ca1b092ae88d864cde274f29ad2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZTYC\\\",\\\"targetValue\\\":\\\"ZTYC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479379759793452099
2024-10-12T16:59:15.012+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324551799360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=13cce6c4b107d1d52b2f450407793d3c&deviceId=479275659449873475
2024-10-12T16:59:15.013+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"90c4349ee75367408e6bf837dcb4cf3d\\\",\\\"dbIdValue\\\":\\\"90c4349ee75367408e6bf837dcb4cf3d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDWJ\\\",\\\"targetValue\\\":\\\"XDWJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479275659449873475
2024-10-12T16:59:16.350+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324137538112 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f2c8488d6d9c760877b7ef10d160ae6c&deviceId=479238581030764854
2024-10-12T16:59:16.350+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"102bde7582eaf3d65d307fb35c5328aa\\\",\\\"dbIdValue\\\":\\\"102bde7582eaf3d65d307fb35c5328aa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"kingdun\\\",\\\"targetValue\\\":\\\"kingdun\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479238581030764854
2024-10-12T16:59:17.720+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:334719463490112 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=40a1eebcd3921eafbe2492efca44e83b&deviceId=479128310899556661
2024-10-12T16:59:17.720+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9e4e1532ca4933d394078544c9ab0b57\\\",\\\"dbIdValue\\\":\\\"9e4e1532ca4933d394078544c9ab0b57\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHSY\\\",\\\"targetValue\\\":\\\"SHSY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479128310899556661
2024-10-12T16:59:19.043+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:236232953184832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ba32e3b1ce8bb4333b88e692bcf94523&deviceId=479125881910998083
2024-10-12T16:59:19.044+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f91d573c080d61b8101e85d4f4bdf510\\\",\\\"dbIdValue\\\":\\\"f91d573c080d61b8101e85d4f4bdf510\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479125881910998083
2024-10-12T16:59:20.369+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:610100290703936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a8a60ef8797d2b5524fbe33960494141&deviceId=479122067023742264
2024-10-12T16:59:20.370+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a7d14b4d46b30ef16bd335a57e8453bc\\\",\\\"dbIdValue\\\":\\\"a7d14b4d46b30ef16bd335a57e8453bc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MQQP\\\",\\\"targetValue\\\":\\\"MQQP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479122067023742264
2024-10-12T16:59:21.723+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323336512064 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cfddf5dd6d2718edb57f9d91ec621bb1&deviceId=479120133684478263
2024-10-12T16:59:21.723+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"53c0ae4612609192c2a99c293fbdca0e\\\",\\\"dbIdValue\\\":\\\"53c0ae4612609192c2a99c293fbdca0e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_5.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_5.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479120133684478263
2024-10-12T16:59:23.062+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:199659576951360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fa7af3cc789bab2a55f33892e5e3db9e&deviceId=478983030208218433
2024-10-12T16:59:23.063+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c4427c2842e0f7f137a427d51b9902a9\\\",\\\"dbIdValue\\\":\\\"c4427c2842e0f7f137a427d51b9902a9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HP\\\",\\\"targetValue\\\":\\\"HP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478983030208218433
2024-10-12T16:59:24.450+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:399488643158592 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=168849c0069b0704272b9d83758e234a&deviceId=478978499957241392
2024-10-12T16:59:24.451+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3055508355b68a9651bb4d5e18a56ed3\\\",\\\"dbIdValue\\\":\\\"3055508355b68a9651bb4d5e18a56ed3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_GLB\\\",\\\"targetValue\\\":\\\"E10_GLB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478978499957241392
2024-10-12T16:59:25.790+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:387745355620928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b7c5723c63ceedd6065329cdc9a3015e&deviceId=478973243420590902
2024-10-12T16:59:25.791+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d0e6f77c515f29db25d9173ae444cfb1\\\",\\\"dbIdValue\\\":\\\"d0e6f77c515f29db25d9173ae444cfb1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BTM\\\",\\\"targetValue\\\":\\\"BTM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478973243420590902
2024-10-12T16:59:27.130+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:457211955122752 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=10b54b003ee476f7b91518528bcdc634&deviceId=478971134943966275
2024-10-12T16:59:27.131+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5dfa2000875959749f18a7b5451c34d1\\\",\\\"dbIdValue\\\":\\\"5dfa2000875959749f18a7b5451c34d1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SLK\\\",\\\"targetValue\\\":\\\"SLK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478971134943966275
2024-10-12T16:59:28.456+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:163820133777984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d448e05e63df5633b5d48cd753535600&deviceId=478967545592820803
2024-10-12T16:59:28.456+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e5ce4efa4fe636558398812d40fd9baf\\\",\\\"dbIdValue\\\":\\\"e5ce4efa4fe636558398812d40fd9baf\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"volm\\\",\\\"targetValue\\\":\\\"volm\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478967545592820803
2024-10-12T16:59:29.841+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322181448256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7a04290d0044829c062ee40ec1ff5848&deviceId=478946318404825395
2024-10-12T16:59:29.841+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"36d060b83065978ec7797e953ef82a4a\\\",\\\"dbIdValue\\\":\\\"36d060b83065978ec7797e953ef82a4a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WYFJ\\\",\\\"targetValue\\\":\\\"WYFJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478946318404825395
2024-10-12T16:59:31.178+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:122001107202624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1a02af8dd7f3bea63ae556e7607006d7&deviceId=478940512515012147
2024-10-12T16:59:31.179+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"757cf3e71406ed7ddc8867b9d734bde0\\\",\\\"dbIdValue\\\":\\\"757cf3e71406ed7ddc8867b9d734bde0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WZZL\\\",\\\"targetValue\\\":\\\"WZZL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478940512515012147
2024-10-12T16:59:32.534+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:246010834440768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cb2517af85905f9c8d617f6b27dfafa1&deviceId=478540781636826179
2024-10-12T16:59:32.535+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b5794e2a8286120695cc2ad5040a5d30\\\",\\\"dbIdValue\\\":\\\"b5794e2a8286120695cc2ad5040a5d30\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"moni12\\\",\\\"targetValue\\\":\\\"moni12\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478540781636826179
2024-10-12T16:59:33.890+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:253443248448064 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7271f315df5b7307dc4e2dda08723fc8&deviceId=478533883046937394
2024-10-12T16:59:33.890+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ab5916f4a287946a5b94fd2ccd83a5ef\\\",\\\"dbIdValue\\\":\\\"ab5916f4a287946a5b94fd2ccd83a5ef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TEST\\\",\\\"targetValue\\\":\\\"TEST\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478533883046937394
2024-10-12T16:59:35.216+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322285945408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=148c6b1a1f70d97c1cf08a72663e9587&deviceId=478533658517451843
2024-10-12T16:59:35.216+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8dcdd3ec350bf04fe1a88cb6e3a77a1c\\\",\\\"dbIdValue\\\":\\\"8dcdd3ec350bf04fe1a88cb6e3a77a1c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FFZT\\\",\\\"targetValue\\\":\\\"FFZT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478533658517451843
2024-10-12T16:59:36.527+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317950317120 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=13da88d887c3bc02e86649e361004a1b&deviceId=478521709700329781
2024-10-12T16:59:36.528+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"138d2be9f9a21ee1b0c721acc2703d14\\\",\\\"dbIdValue\\\":\\\"138d2be9f9a21ee1b0c721acc2703d14\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XiangMing2021\\\",\\\"targetValue\\\":\\\"XiangMing2021\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478521709700329781
2024-10-12T16:59:37.962+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:392301681328704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0ef252faeede74058d50b7e3ad2264b0&deviceId=478513331611649604
2024-10-12T16:59:37.963+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fa74657084a4c1fa5d1f5b017e9babf4\\\",\\\"dbIdValue\\\":\\\"fa74657084a4c1fa5d1f5b017e9babf4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MHKJZS\\\",\\\"targetValue\\\":\\\"MHKJZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478513331611649604
2024-10-12T16:59:39.335+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319251944000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f6098b9fb1df248ce1642c021567a7f6&deviceId=478508242108953667
2024-10-12T16:59:39.336+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3b09f9b7bedc62de3f541e251041366e\\\",\\\"dbIdValue\\\":\\\"3b09f9b7bedc62de3f541e251041366e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SVPC\\\",\\\"targetValue\\\":\\\"SVPC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478508242108953667
2024-10-12T16:59:40.661+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:154610417263168 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0e26f9fa284ac15739fffc1af53dfb8f&deviceId=478507299917280323
2024-10-12T16:59:40.662+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6ad7a8dc3da405adc71d43ccff64be29\\\",\\\"dbIdValue\\\":\\\"6ad7a8dc3da405adc71d43ccff64be29\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CLCDzs\\\",\\\"targetValue\\\":\\\"CLCDzs\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478507299917280323
2024-10-12T16:59:42.002+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:67894717235776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=004c7fe68be679904eb6e30eb90f12e6&deviceId=478507153720620099
2024-10-12T16:59:42.002+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"dee68886c7db8974c2ec29b4d6a9a859\\\",\\\"dbIdValue\\\":\\\"dee68886c7db8974c2ec29b4d6a9a859\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SXERP\\\",\\\"targetValue\\\":\\\"SXERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478507153720620099
2024-10-12T16:59:43.353+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320591598144 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=06ad209ed6173ae26891e596b466bf9c&deviceId=478390824246196019
2024-10-12T16:59:43.353+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f9e7ac64d9021c5d23cdf5f9f69aae8\\\",\\\"dbIdValue\\\":\\\"1f9e7ac64d9021c5d23cdf5f9f69aae8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YSGD\\\",\\\"targetValue\\\":\\\"YSGD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478390824246196019
2024-10-12T16:59:44.724+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:443678715716160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=79abf3328c9b93dcf6fcdf0ecaecd198&deviceId=478374399016186947
2024-10-12T16:59:44.725+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f5da23a30a264e177481421956627e07\\\",\\\"dbIdValue\\\":\\\"f5da23a30a264e177481421956627e07\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RCXJZS2022\\\",\\\"targetValue\\\":\\\"RCXJZS2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478374399016186947
2024-10-12T16:59:46.079+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:230554779136576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=84aa509c86d1f1cee98381dc6ea17264&deviceId=478365090563765569
2024-10-12T16:59:46.080+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3c9c14adf55aa848813abb2a4890005c\\\",\\\"dbIdValue\\\":\\\"3c9c14adf55aa848813abb2a4890005c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"VSITS\\\",\\\"targetValue\\\":\\\"VSITS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478365090563765569
2024-10-12T16:59:47.420+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321628459584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b5dd8cafbd5dd3b285b43bae64d6622f&deviceId=478262649721730865
2024-10-12T16:59:47.420+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"47ffca7a0bd7af321379b82f900306f6\\\",\\\"dbIdValue\\\":\\\"47ffca7a0bd7af321379b82f900306f6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10-XYDZ\\\",\\\"targetValue\\\":\\\"E10-XYDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478262649721730865
2024-10-12T16:59:48.776+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320325419584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=05074d52c411f6b9c750b9573568eb1e&deviceId=478257128054730818
2024-10-12T16:59:48.777+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6e7b255c59d3ac932160dced7fbbb933\\\",\\\"dbIdValue\\\":\\\"6e7b255c59d3ac932160dced7fbbb933\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GBGD1\\\",\\\"targetValue\\\":\\\"GBGD1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478257128054730818
2024-10-12T16:59:50.100+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320117850688 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3f386ff86bd275469582ecfe2798b377&deviceId=478252977992255044
2024-10-12T16:59:50.100+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a15a15068344e5b737b0e5ed94149fbd\\\",\\\"dbIdValue\\\":\\\"a15a15068344e5b737b0e5ed94149fbd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_5.0.0.1BaseData\\\",\\\"targetValue\\\":\\\"E10_5.0.0.1BaseData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478252977992255044
2024-10-12T16:59:51.451+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:295988745720384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b2e663bb74a0865e60e2c0f1d1ccfbf9&deviceId=478214680540624433
2024-10-12T16:59:51.452+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ab875f4143a7696317928f96b27db405\\\",\\\"dbIdValue\\\":\\\"ab875f4143a7696317928f96b27db405\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YXY\\\",\\\"targetValue\\\":\\\"YXY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478214680540624433
2024-10-12T16:59:52.777+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:64975108530752 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=937e065523c6a10720dd94bebe7c9efc&deviceId=478109413123568707
2024-10-12T16:59:52.778+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"231ad1131c2fff556227edd26f28704f\\\",\\\"dbIdValue\\\":\\\"231ad1131c2fff556227edd26f28704f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MYdata\\\",\\\"targetValue\\\":\\\"MYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478109413123568707
2024-10-12T16:59:54.131+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:230555090899520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=edf4870cee1bd957f761b78787aa388b&deviceId=478080602180956485
2024-10-12T16:59:54.132+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b5d701dca6701748899d301d9e1bc764\\\",\\\"dbIdValue\\\":\\\"b5d701dca6701748899d301d9e1bc764\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FTR\\\",\\\"targetValue\\\":\\\"FTR\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478080602180956485
2024-10-12T16:59:55.486+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320338244160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0c2999c3671bb9d89a5396c4df1b0e3c&deviceId=464761092787155251
2024-10-12T16:59:55.487+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2538265fe5b6ce81ef11d2df88d62ab1\\\",\\\"dbIdValue\\\":\\\"2538265fe5b6ce81ef11d2df88d62ab1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YZDZ\\\",\\\"targetValue\\\":\\\"YZDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464761092787155251
2024-10-12T16:59:56.899+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320291689024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f3e3e29f3dd23884a8dc943f85c85cc4&deviceId=477967435614401603
2024-10-12T16:59:56.899+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4dbd91e1e1fb424c3a5dad1971cea595\\\",\\\"dbIdValue\\\":\\\"4dbd91e1e1fb424c3a5dad1971cea595\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SWM\\\",\\\"targetValue\\\":\\\"SWM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477967435614401603
2024-10-12T16:59:58.408+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322781499968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=846c610e7e07d5afd428b51f0212b463&deviceId=477962335105857092
2024-10-12T16:59:58.408+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7f6c87698eb2726dc9ef3ec4153c0737\\\",\\\"dbIdValue\\\":\\\"7f6c87698eb2726dc9ef3ec4153c0737\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZRKJ\\\",\\\"targetValue\\\":\\\"ZRKJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477962335105857092
2024-10-12T17:00:06.564+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:233428884505152 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=da8b40b01e49b8f98c6b1d6b2fe16f63&deviceId=477960810023371573
2024-10-12T17:00:06.565+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a7e783a31a50ebb64d3604ff62a873f1\\\",\\\"dbIdValue\\\":\\\"a7e783a31a50ebb64d3604ff62a873f1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HT\\\",\\\"targetValue\\\":\\\"HT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477960810023371573
2024-10-12T17:00:14.283+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324990718528 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9737c8fe657c06685666fed2dd952ba6&deviceId=477956588674495042
2024-10-12T17:00:14.283+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f9470b5d4746e3995b209880ae8b4ad7\\\",\\\"dbIdValue\\\":\\\"f9470b5d4746e3995b209880ae8b4ad7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CYDZDATA\\\",\\\"targetValue\\\":\\\"CYDZDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477956588674495042
2024-10-12T17:00:15.793+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:60816869777984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1cd42690b5721590f48cc407741e3813&deviceId=477952200375545904
2024-10-12T17:00:15.793+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6f40bd5569ebf0de7831774ab5d5c982\\\",\\\"dbIdValue\\\":\\\"6f40bd5569ebf0de7831774ab5d5c982\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GH\\\",\\\"targetValue\\\":\\\"GH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477952200375545904
2024-10-12T17:00:17.377+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323335455296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5bcb5a583732f0f9956efbf18def0626&deviceId=477932841062118467
2024-10-12T17:00:17.378+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5cf8f93fc0a21cea02d725007d29b377\\\",\\\"dbIdValue\\\":\\\"5cf8f93fc0a21cea02d725007d29b377\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SUNTOP\\\",\\\"targetValue\\\":\\\"SUNTOP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477932841062118467
2024-10-12T17:00:18.769+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317564789312 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=62721a2dca3d7db1f39c15a156ab6d4e&deviceId=477528641606005827
2024-10-12T17:00:18.770+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"24e7cef19634039b159c83c79e770367\\\",\\\"dbIdValue\\\":\\\"24e7cef19634039b159c83c79e770367\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SWdata\\\",\\\"targetValue\\\":\\\"SWdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477528641606005827
2024-10-12T17:00:20.156+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:286445314777664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bb5c8b72a8a44835577f6ede9f937bec&deviceId=477521360260707651
2024-10-12T17:00:20.158+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f6e60908959f8f162370f1b8726f06eb\\\",\\\"dbIdValue\\\":\\\"f6e60908959f8f162370f1b8726f06eb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"yunruidz\\\",\\\"targetValue\\\":\\\"yunruidz\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477521360260707651
2024-10-12T17:00:21.542+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319308046912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=67abf894e14f02294451d14f2f46722e&deviceId=477519181319779906
2024-10-12T17:00:21.542+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f3fdd14fa67da1ecc1f79583104f7687\\\",\\\"dbIdValue\\\":\\\"f3fdd14fa67da1ecc1f79583104f7687\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"99\\\",\\\"targetValue\\\":\\\"99\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477519181319779906
2024-10-12T17:00:22.896+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321035358784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=46d7024113f980c88a2cc580e735782a&deviceId=477493391232350273
2024-10-12T17:00:22.896+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"29b61a920f0931e6cd7f5afcd225c16c\\\",\\\"dbIdValue\\\":\\\"29b61a920f0931e6cd7f5afcd225c16c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CYJX\\\",\\\"targetValue\\\":\\\"CYJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477493391232350273
2024-10-12T17:00:24.223+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:247988480729664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e75c9029676b409ef7d4860caa9f4f8a&deviceId=477384012759838768
2024-10-12T17:00:24.223+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28c34c34d49444e77d16be82a113e9a8\\\",\\\"dbIdValue\\\":\\\"28c34c34d49444e77d16be82a113e9a8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDGY\\\",\\\"targetValue\\\":\\\"XDGY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477384012759838768
2024-10-12T17:00:25.638+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:582348383449664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6749b52620b4e695db69b63e240a1bef&deviceId=477356747116261424
2024-10-12T17:00:25.638+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"07deceef7cbce884ef8eed20da0a3e98\\\",\\\"dbIdValue\\\":\\\"07deceef7cbce884ef8eed20da0a3e98\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JA01\\\",\\\"targetValue\\\":\\\"JA01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477356747116261424
2024-10-12T17:00:26.977+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322249331264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7f9ea467d4448a1dacaec64ea98a8048&deviceId=477352846581314369
2024-10-12T17:00:26.977+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"227a6ae547b08edca599ab11fa248c15\\\",\\\"dbIdValue\\\":\\\"227a6ae547b08edca599ab11fa248c15\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ganta\\\",\\\"targetValue\\\":\\\"ganta\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477352846581314369
2024-10-12T17:00:28.317+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:214523180003904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8a0c99d0a8c97df7053c8654e325bd0f&deviceId=477346765494829872
2024-10-12T17:00:28.317+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"766598f0f95e098dcce7b2ed749d1eb7\\\",\\\"dbIdValue\\\":\\\"766598f0f95e098dcce7b2ed749d1eb7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477346765494829872
2024-10-12T17:00:29.702+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:247988480193088 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c8f5b81c6c084a7d9f9f9327d55fd11d&deviceId=477239045148128323
2024-10-12T17:00:29.703+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f9a7955598b283c0ee11581f129d47a1\\\",\\\"dbIdValue\\\":\\\"f9a7955598b283c0ee11581f129d47a1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RDZS\\\",\\\"targetValue\\\":\\\"RDZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477239045148128323
2024-10-12T17:00:31.013+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322575151680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a8f79468ce8c593f6ce9e0f8bec010c7&deviceId=477231571837924406
2024-10-12T17:00:31.013+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c711d18b0618815f97ada3afa636b787\\\",\\\"dbIdValue\\\":\\\"c711d18b0618815f97ada3afa636b787\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KSLdata\\\",\\\"targetValue\\\":\\\"KSLdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477231571837924406
2024-10-12T17:00:32.444+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324114141760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=47da8868f37280510b8d71325cd6c13f&deviceId=477226839320835139
2024-10-12T17:00:32.444+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b6b567d99b056010bc132f3f3f793530\\\",\\\"dbIdValue\\\":\\\"b6b567d99b056010bc132f3f3f793530\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HT\\\",\\\"targetValue\\\":\\\"HT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477226839320835139
2024-10-12T17:00:33.819+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323314868800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5c298227205366b9deb71595c53be6ac&deviceId=477213060730204208
2024-10-12T17:00:33.819+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"aa30c913c2132fe1ebfb1ccbf9d008c0\\\",\\\"dbIdValue\\\":\\\"aa30c913c2132fe1ebfb1ccbf9d008c0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HS_E10\\\",\\\"targetValue\\\":\\\"HS_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477213060730204208
2024-10-12T17:00:35.176+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:399061026308672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4ee1e92529fb72ee703e5b61509a99ef&deviceId=477090779555181635
2024-10-12T17:00:35.176+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4079ba5a8d74b01aeece7ca76a00c6ac\\\",\\\"dbIdValue\\\":\\\"4079ba5a8d74b01aeece7ca76a00c6ac\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"123\\\",\\\"targetValue\\\":\\\"123\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477090779555181635
2024-10-12T17:00:36.505+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321095160384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cdda4c65444fe57df5d9ab7ef862e0c0&deviceId=477089627111437363
2024-10-12T17:00:36.506+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"eb2646712c7874cf67ab5f15e172436a\\\",\\\"dbIdValue\\\":\\\"eb2646712c7874cf67ab5f15e172436a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_60JMK1\\\",\\\"targetValue\\\":\\\"E10_60JMK1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477089627111437363
2024-10-12T17:00:38.144+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:59799434785344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=af6cfef254438fca83b4db86d9f6c8cd&deviceId=433307778501589059
2024-10-12T17:00:38.145+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7c4fa82be7b7a13fc89bf23394601e5f\\\",\\\"dbIdValue\\\":\\\"7c4fa82be7b7a13fc89bf23394601e5f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AUTEC\\\",\\\"targetValue\\\":\\\"AUTEC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   433307778501589059
2024-10-12T17:00:39.511+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:195398912782912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6ccf149bfac63a9fb39f4aa2862a1ab0&deviceId=477087403811550275
2024-10-12T17:00:39.513+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"348a2f0ec8ca24249462a5af3a15ff44\\\",\\\"dbIdValue\\\":\\\"348a2f0ec8ca24249462a5af3a15ff44\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JYdata\\\",\\\"targetValue\\\":\\\"JYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477087403811550275
2024-10-12T17:00:40.910+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:313115622003264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=78b9d42af8811a06c6bd6ae5bfe9c544&deviceId=477066932672018485
2024-10-12T17:00:40.911+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d5a52d60cdf83fe67625e28fc5add6cd\\\",\\\"dbIdValue\\\":\\\"d5a52d60cdf83fe67625e28fc5add6cd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CMDZ\\\",\\\"targetValue\\\":\\\"CMDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   477066932672018485
2024-10-12T17:00:42.307+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:234802347536960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c6ea21d8ca5f28b41d3cc55125cb332c&deviceId=476947337411835449
2024-10-12T17:00:42.307+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1eac90bdc4799c54c2cbfd8b9a87dfc1\\\",\\\"dbIdValue\\\":\\\"1eac90bdc4799c54c2cbfd8b9a87dfc1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZL\\\",\\\"targetValue\\\":\\\"ZL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476947337411835449
2024-10-12T17:00:43.646+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324238172736 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cac77e1be638a784a04416959e9e2fdb&deviceId=476938623627838520
2024-10-12T17:00:43.647+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"51faef1ecb3c554bebca6d0ce30da7ca\\\",\\\"dbIdValue\\\":\\\"51faef1ecb3c554bebca6d0ce30da7ca\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"jcsj\\\",\\\"targetValue\\\":\\\"jcsj\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476938623627838520
2024-10-12T17:00:45.033+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:419572390003264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d05d16c56cb3b5d72de5c35f61611697&deviceId=400286962633687609
2024-10-12T17:00:45.033+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"992038d04f0308e630b7516cd31d66b5\\\",\\\"dbIdValue\\\":\\\"992038d04f0308e630b7516cd31d66b5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XWEBL\\\",\\\"targetValue\\\":\\\"XWEBL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   400286962633687609
2024-10-12T17:00:46.438+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319400755776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b8a06120d1bc0ab597f325cf766ee177&deviceId=476482407252374595
2024-10-12T17:00:46.438+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"97a2ab6e8e5a0385c12c5bf827821241\\\",\\\"dbIdValue\\\":\\\"97a2ab6e8e5a0385c12c5bf827821241\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DATA\\\",\\\"targetValue\\\":\\\"DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476482407252374595
2024-10-12T17:00:47.869+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324336259648 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=79c09545c83041b558bcc798a2dfc9c2&deviceId=450838558928815408
2024-10-12T17:00:47.870+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4100ba4d9383394dadd2991728995b1b\\\",\\\"dbIdValue\\\":\\\"4100ba4d9383394dadd2991728995b1b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10yimei\\\",\\\"targetValue\\\":\\\"E10yimei\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   450838558928815408
2024-10-12T17:00:49.209+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324079231552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7b913f6142fa26003f9e62ad6b41a2a4&deviceId=476361022651774771
2024-10-12T17:00:49.209+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ae0d588464531a55e9b72031cf70d3c1\\\",\\\"dbIdValue\\\":\\\"ae0d588464531a55e9b72031cf70d3c1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"reihsingData\\\",\\\"targetValue\\\":\\\"reihsingData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476361022651774771
2024-10-12T17:00:50.562+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:393363351929408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a8719076c296bb984f223f96cbce0438&deviceId=476339754208604209
2024-10-12T17:00:50.562+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"20c2d03aa4187c652de442aebac5e938\\\",\\\"dbIdValue\\\":\\\"20c2d03aa4187c652de442aebac5e938\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WBQ\\\",\\\"targetValue\\\":\\\"WBQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850343f46eeec979f1f224cceae048f2a332e155cf06eddf743e592d7dbb3b317d00b93ca1a7446ce82e3c33347f66a142359106ce6060d39e023f7c29825b937ef2048b074d7f4038b742cd60df13f45417f47b097b13df85cd356482aab00bbb865e6ccb6f7d391060c61712a9cb8a9d33f00b88bc9ab9a2bbc92d285460503863d3fe22d58d4ecba77f8ebfffb991656415eb0b8749dd5b0f5c9fa12cb4beae7eba21ca66ddd7102f9444ae2e696a9e1763b4d752dde86374551774642f9fd4065258a8de99fa158e73a3654db58a65cc39d38c046da57f39224629e43535fb015a0a37466486a0e0d3b5f1d669e99ea1","collectName":"E10附件数据采集","accId":779544910123584,"adimId":635401576862272,"id":779880254313024,"adcId":635401485509184,"execParamsVersion":"f67ee721db6c49cd15a68d21e30c9fbf","aiId":635401576833601,"isEnable":0},"paramsMap":{"deviceId":"476339754208604209","eid":393363351929408,"aiId":635401576833601,"execParamsDbAiId":635400878518848,"execParamsDbId":"20c2d03aa4187c652de442aebac5e938"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779880254313024 acc incomplete","batchId":779880255357504,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   476339754208604209
2024-10-12T17:00:51.961+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:290782426583616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=32b37df709e87bfb389f66d275cbdfb9&deviceId=476334245879493700
2024-10-12T17:00:51.961+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"85e7c18718615d1d68eb802acd58d708\\\",\\\"dbIdValue\\\":\\\"85e7c18718615d1d68eb802acd58d708\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"taifu\\\",\\\"targetValue\\\":\\\"taifu\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476334245879493700
2024-10-12T17:00:53.358+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317614637632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5a13d904a4b58706df069d374c5e1a67&deviceId=476090086383961155
2024-10-12T17:00:53.358+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9c80579a9c279c11535d0d25608267fe\\\",\\\"dbIdValue\\\":\\\"9c80579a9c279c11535d0d25608267fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BDKZ\\\",\\\"targetValue\\\":\\\"BDKZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476090086383961155
2024-10-12T17:00:54.799+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:177327185576512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61fa64beac1a39b32101a8e76f1a5ab1&deviceId=476077118619399235
2024-10-12T17:00:54.800+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"dabb1c29353224b47768cf1039841d84\\\",\\\"dbIdValue\\\":\\\"dabb1c29353224b47768cf1039841d84\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WHXY\\\",\\\"targetValue\\\":\\\"WHXY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476077118619399235
2024-10-12T17:00:56.128+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:194337220239936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=57cd12756ead71258ded812d3e1cc3da&deviceId=476071351199739959
2024-10-12T17:00:56.128+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"20dbb26e8e39f4a40999e8ff0eddae5f\\\",\\\"dbIdValue\\\":\\\"20dbb26e8e39f4a40999e8ff0eddae5f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CGDL\\\",\\\"targetValue\\\":\\\"CGDL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476071351199739959
2024-10-12T17:00:57.485+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41325084611136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dea3c218f607677939815fea7255101f&deviceId=476053686351574083
2024-10-12T17:00:57.485+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"253fa85cfd192a83c676526e3b2215aa\\\",\\\"dbIdValue\\\":\\\"253fa85cfd192a83c676526e3b2215aa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YWYQ\\\",\\\"targetValue\\\":\\\"YWYQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476053686351574083
2024-10-12T17:00:58.842+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321658937920 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e906643c67c9c26e06033359e4e8f610&deviceId=476048687143859267
2024-10-12T17:00:58.843+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4247ea2c08d1394394e574b687b8c56f\\\",\\\"dbIdValue\\\":\\\"4247ea2c08d1394394e574b687b8c56f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AYDATA2022\\\",\\\"targetValue\\\":\\\"AYDATA2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503d2f993cd87a51a9467f4ce88424f3a4978d6b5cadd536c8ecb82a335c9edadd4ec042b8de4443a82df31e418e3e309d4f2f89f9ab84fae1dc4e4007403bbb49a085a133ce31c36f1470e68ec390a3cc4b5c2eb686f89ecbea3d67a0c471069a66a7729fa2b1903624c55cb24a7fe1f0b3b5e65f1d93eb3f7f91f077317d98dc6cf087806cfb446cc4bbc29d1214a88085d7d8dd2f19d64632dfe3d7f4356ea6625c428297016f53fe152bdd329f68688c6e90cf78ffc688f2876bf4266c1a87172f920a058cd76e17a82836d18ef76d5c0158a498e80f1d788a11298c4fbce454def90599f59cb5fd1863909596b72c47742cb623403647a3b3438be8c01664a","collectName":"E10附件数据采集","accId":779544910123584,"adimId":634690228392512,"id":779880288768576,"adcId":634690126041664,"execParamsVersion":"4d9d045400b83bc1a86f1f20cb89ec31","aiId":634690228314690,"isEnable":1},"paramsMap":{"deviceId":"476048687143859267","eid":41321658937920,"aiId":634690228314690,"execParamsDbAiId":634690054132288,"execParamsDbId":"4247ea2c08d1394394e574b687b8c56f"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779880288768576 acc incomplete","batchId":779880289858112,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   476048687143859267
2024-10-12T17:01:00.340+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318755545664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=95c463e07be22993497783c7ee05a326&deviceId=476045289875058743
2024-10-12T17:01:00.340+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4ed3275c700a025376d3a10b3d544ac9\\\",\\\"dbIdValue\\\":\\\"4ed3275c700a025376d3a10b3d544ac9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GDSCKJ\\\",\\\"targetValue\\\":\\\"GDSCKJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   476045289875058743
2024-10-12T17:01:01.695+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:454026927415872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=77dcdd44a1a151223dfde5cc06fd2788&deviceId=475946992132965441
2024-10-12T17:01:01.695+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"631b17d5dcc4fd287e2192979f39db34\\\",\\\"dbIdValue\\\":\\\"631b17d5dcc4fd287e2192979f39db34\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JSAK\\\",\\\"targetValue\\\":\\\"JSAK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475946992132965441
2024-10-12T17:01:03.128+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321617400384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1f34837d487c71157856a1652db5d537&deviceId=475940644490064963
2024-10-12T17:01:03.129+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8e23acc813aeeb4ac4b4777054b211e2\\\",\\\"dbIdValue\\\":\\\"8e23acc813aeeb4ac4b4777054b211e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DDX\\\",\\\"targetValue\\\":\\\"DDX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475940644490064963
2024-10-12T17:01:04.477+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318562619968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=474e9bc042b83e1e15f312d34291c38f&deviceId=475937856385201219
2024-10-12T17:01:04.479+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"84ae5d7ef4f52b795f9acd9fbe3f0d65\\\",\\\"dbIdValue\\\":\\\"84ae5d7ef4f52b795f9acd9fbe3f0d65\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"longyun\\\",\\\"targetValue\\\":\\\"longyun\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475937856385201219
2024-10-12T17:01:05.850+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320083944000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0f1199a8f95b701cf67b547976e4db98&deviceId=475931684265538627
2024-10-12T17:01:05.851+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0bb031e548b5fa3d2018c3b41975386f\\\",\\\"dbIdValue\\\":\\\"0bb031e548b5fa3d2018c3b41975386f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WZDATA\\\",\\\"targetValue\\\":\\\"WZDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475931684265538627
2024-10-12T17:01:07.197+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:469952619377216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aca96fd51c82d10fae9d6a2db26b4f7e&deviceId=459744571673298481
2024-10-12T17:01:07.198+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f6bda691cf2ade9838fe43622151993b\\\",\\\"dbIdValue\\\":\\\"f6bda691cf2ade9838fe43622151993b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KMdata\\\",\\\"targetValue\\\":\\\"KMdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   459744571673298481
2024-10-12T17:01:08.538+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:297861233238592 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ef6e602fd050574690f323232f17cdca&deviceId=475928028812162115
2024-10-12T17:01:08.538+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f9565eb3febf8c64c6676828dcb51c41\\\",\\\"dbIdValue\\\":\\\"f9565eb3febf8c64c6676828dcb51c41\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MSTdata0604\\\",\\\"targetValue\\\":\\\"MSTdata0604\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475928028812162115
2024-10-12T17:01:09.914+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:303782652092992 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7eeaa830e04a5a4592ed40c72432c30a&deviceId=475922196129133635
2024-10-12T17:01:09.915+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7394d428a7f5453922c80c160144c5dd\\\",\\\"dbIdValue\\\":\\\"7394d428a7f5453922c80c160144c5dd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LHYX-E10\\\",\\\"targetValue\\\":\\\"LHYX-E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475922196129133635
2024-10-12T17:01:11.332+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:187965367161410 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=380071f1f368345463e8ba22bcf2b1e5&deviceId=475921518648375366
2024-10-12T17:01:11.333+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e00417d992b9a78c88d7c9fd0d9bf6eb\\\",\\\"dbIdValue\\\":\\\"e00417d992b9a78c88d7c9fd0d9bf6eb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJ_ERP\\\",\\\"targetValue\\\":\\\"ZJ_ERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475921518648375366
2024-10-12T17:01:12.716+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317823685184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ed12016259af48b7f425bc6419aefe18&deviceId=475910758362792502
2024-10-12T17:01:12.716+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5dd942793c3c8a315750eeaf188abde5\\\",\\\"dbIdValue\\\":\\\"5dd942793c3c8a315750eeaf188abde5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MTK\\\",\\\"targetValue\\\":\\\"MTK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475910758362792502
2024-10-12T17:01:14.073+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:340868524225088 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d41dc9f44cb0d0f2dd683fdff042a946&deviceId=475906541342439493
2024-10-12T17:01:14.074+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f04f0ded0c07a31f60bf9a8b037a8d5\\\",\\\"dbIdValue\\\":\\\"1f04f0ded0c07a31f60bf9a8b037a8d5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475906541342439493
2024-10-12T17:01:15.473+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:368281370612288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6bcb37bb771844e8cc9927719846236f&deviceId=475903912721791032
2024-10-12T17:01:15.473+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"232182b39a9fc80655577ab5469de5b3\\\",\\\"dbIdValue\\\":\\\"232182b39a9fc80655577ab5469de5b3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZHdata\\\",\\\"targetValue\\\":\\\"ZHdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475903912721791032
2024-10-12T17:01:16.842+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323116532288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0505ab2d9736912a06fd269683bc650e&deviceId=475900944496735299
2024-10-12T17:01:16.842+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d10b8bafd6c9d83d193b3ece4e30b670\\\",\\\"dbIdValue\\\":\\\"d10b8bafd6c9d83d193b3ece4e30b670\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"QBDZ\\\",\\\"targetValue\\\":\\\"QBDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475900944496735299
2024-10-12T17:01:18.179+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321310085696 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f273da59f7f7011cc69e54acad073cb4&deviceId=475896890265711683
2024-10-12T17:01:18.179+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"237e486be6f92549ce759799b605f3e2\\\",\\\"dbIdValue\\\":\\\"237e486be6f92549ce759799b605f3e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YFDATA\\\",\\\"targetValue\\\":\\\"YFDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475896890265711683
2024-10-12T17:01:19.517+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:134063420088896 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=82ef91e5ea834086e64ced35f9f1defd&deviceId=475507198991021123
2024-10-12T17:01:19.517+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"90ea74545ccf4512603706b2a5412682\\\",\\\"dbIdValue\\\":\\\"90ea74545ccf4512603706b2a5412682\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JMDATA\\\",\\\"targetValue\\\":\\\"JMDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475507198991021123
2024-10-12T17:01:20.870+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:233421571891776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=56d2e0225999bbffc667ab926d30671b&deviceId=475504458936103489
2024-10-12T17:01:20.870+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a8d9579a918b22df2b9a40519ded6797\\\",\\\"dbIdValue\\\":\\\"a8d9579a918b22df2b9a40519ded6797\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YMZN\\\",\\\"targetValue\\\":\\\"YMZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475504458936103489
2024-10-12T17:01:22.200+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:498883378926144 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0990ed1faade917b4d75e8f8d20579e2&deviceId=442035683163386947
2024-10-12T17:01:22.201+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4a23d2c5bb2730fe603195a4ec54fa10\\\",\\\"dbIdValue\\\":\\\"4a23d2c5bb2730fe603195a4ec54fa10\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DQdata\\\",\\\"targetValue\\\":\\\"DQdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   442035683163386947
2024-10-12T17:01:23.727+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318613389888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f50735c9e38158de1b3620d7ac84f729&deviceId=475496243485750339
2024-10-12T17:01:23.728+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e2c43aa40cdf94c83dcef15f863ebf41\\\",\\\"dbIdValue\\\":\\\"e2c43aa40cdf94c83dcef15f863ebf41\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CHF\\\",\\\"targetValue\\\":\\\"CHF\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475496243485750339
2024-10-12T17:01:25.184+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:251674150036032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3b73052c68f8538063a5d9dfaeecf71b&deviceId=475490661756318787
2024-10-12T17:01:25.184+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"882b062b9f83b8709d7b6ce26d4de1e3\\\",\\\"dbIdValue\\\":\\\"882b062b9f83b8709d7b6ce26d4de1e3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RSH_E10zs\\\",\\\"targetValue\\\":\\\"RSH_E10zs\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475490661756318787
2024-10-12T17:01:26.554+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:365094038045248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2400d4e76ed3dcb2fee6bc867298d2e7&deviceId=475489148048782403
2024-10-12T17:01:26.554+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a10072e0c4375d500cdfce7aa949dea5\\\",\\\"dbIdValue\\\":\\\"a10072e0c4375d500cdfce7aa949dea5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SATdata\\\",\\\"targetValue\\\":\\\"SATdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475489148048782403
2024-10-12T17:01:28.140+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319613325888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2ab205a0ce2b546f020f49f97a07147d&deviceId=475488466205951286
2024-10-12T17:01:28.141+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28012b71d2f7a857125258ed9ebdaddb\\\",\\\"dbIdValue\\\":\\\"28012b71d2f7a857125258ed9ebdaddb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JGDATA\\\",\\\"targetValue\\\":\\\"JGDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475488466205951286
2024-10-12T17:01:29.465+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323181273664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=089dbc46710624d05a7b1f62d9f0cf13&deviceId=475484340436878136
2024-10-12T17:01:29.467+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"97e9159d6ad1c7979b1ce15416fbb2ad\\\",\\\"dbIdValue\\\":\\\"97e9159d6ad1c7979b1ce15416fbb2ad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Xdata\\\",\\\"targetValue\\\":\\\"Xdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475484340436878136
2024-10-12T17:01:30.820+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:233427540402752 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d45fe2c0759b9b8d286356daa30b44f0&deviceId=475469309527733315
2024-10-12T17:01:30.820+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"490f647f2c6fcc1180def25791ecbbc6\\\",\\\"dbIdValue\\\":\\\"490f647f2c6fcc1180def25791ecbbc6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHENGBAO\\\",\\\"targetValue\\\":\\\"SHENGBAO\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475469309527733315
2024-10-12T17:01:32.158+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324465447488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d0b36833ce46786c98969238506a978b&deviceId=475462532304221251
2024-10-12T17:01:32.158+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"218a306f1a6e092e694ef214a82c014c\\\",\\\"dbIdValue\\\":\\\"218a306f1a6e092e694ef214a82c014c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WMDZ\\\",\\\"targetValue\\\":\\\"WMDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475462532304221251
2024-10-12T17:01:33.634+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:284041968230976 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=db0e56262602e58c318d3843ad192856&deviceId=475362276828786736
2024-10-12T17:01:33.634+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"803b93114ad9306d9509a634c47d5d9a\\\",\\\"dbIdValue\\\":\\\"803b93114ad9306d9509a634c47d5d9a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LHUI\\\",\\\"targetValue\\\":\\\"LHUI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475362276828786736
2024-10-12T17:01:35.020+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322234389056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=411831e2d4218726254d847c7845ccb0&deviceId=475327168323269699
2024-10-12T17:01:35.021+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4b72e4506df33ce4b07e6a05f03f4944\\\",\\\"dbIdValue\\\":\\\"4b72e4506df33ce4b07e6a05f03f4944\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"data02\\\",\\\"targetValue\\\":\\\"data02\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475327168323269699
2024-10-12T17:01:36.389+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317175751232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=755efaa3f828765ca4a8cdec89db5050&deviceId=475318758408205379
2024-10-12T17:01:36.390+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6d36e9f924bfaa84cb27e7e13e9a4690\\\",\\\"dbIdValue\\\":\\\"6d36e9f924bfaa84cb27e7e13e9a4690\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HLHDB\\\",\\\"targetValue\\\":\\\"HLHDB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475318758408205379
2024-10-12T17:01:38.008+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:438045934015040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=50ab0813911a275d0b036be55e9bd60c&deviceId=475316713903109187
2024-10-12T17:01:38.008+08:00  INFO 10320 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0b430a9d6639b020881609ba6a5e6fee\\\",\\\"dbIdValue\\\":\\\"0b430a9d6639b020881609ba6a5e6fee\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003_ZS\\\",\\\"targetValue\\\":\\\"E6003_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475316713903109187
deviceIds:,'479551468039324995','479549123540829753','479526976760067139','479401383309620277','479379759793452099','479275659449873475','479238581030764854','479128310899556661','479125881910998083','479122067023742264','479120133684478263','478983030208218433','478978499957241392','478973243420590902','478971134943966275','478967545592820803','478946318404825395','478940512515012147','478540781636826179','478533883046937394','478533658517451843','478521709700329781','478513331611649604','478508242108953667','478507299917280323','478507153720620099','478390824246196019','478374399016186947','478365090563765569','478262649721730865','478257128054730818','478252977992255044','478214680540624433','478109413123568707','478080602180956485','464761092787155251','477967435614401603','477962335105857092','477960810023371573','477956588674495042','477952200375545904','477932841062118467','477528641606005827','477521360260707651','477519181319779906','477493391232350273','477384012759838768','477356747116261424','477352846581314369','477346765494829872','477239045148128323','477231571837924406','477226839320835139','477213060730204208','477090779555181635','477089627111437363','433307778501589059','477087403811550275','477066932672018485','476947337411835449','476938623627838520','400286962633687609','476482407252374595','450838558928815408','476361022651774771','476339754208604209','476334245879493700','476090086383961155','476077118619399235','476071351199739959','476053686351574083','476048687143859267','476045289875058743','475946992132965441','475940644490064963','475937856385201219','475931684265538627','459744571673298481','475928028812162115','475922196129133635','475921518648375366','475910758362792502','475906541342439493','475903912721791032','475900944496735299','475896890265711683','475507198991021123','475504458936103489','442035683163386947','475496243485750339','475490661756318787','475489148048782403','475488466205951286','475484340436878136','475469309527733315','475462532304221251','475362276828786736','475327168323269699','475318758408205379','475316713903109187'