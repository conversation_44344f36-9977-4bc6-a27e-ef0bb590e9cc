# 2960 【數科】infra訂閱模組同步訂單自動化訂閱

### 1. 优化授权管理页面
   ```mermaid
   flowchart TD
      id0((开始))
    -->    id1["`获取**租户数据**`"] 
        -->    id8["`同步租户合约数据`"] 
    --> id2["`找到租户存在**163产品线** 科维合约订阅模组数据`"] 
    --> id3["`查询**科维CRM**api 过滤**MA=true** 的科维模组合约数据`"]
    --> id5["`根据**科维CRM**模组合约数据 找到 服务云订阅模组数据`"]
    --> id6["`汇总**科维CRM**模组合约数据 和 服务云订阅模组数据`"]
    --> id7["`保存订阅模组数据到服务云`"]
   ```
> - TMP_RH OPENFIND Vulnerability_Scan CLOUD_BACKUP 订阅模组同步 可用总数
> - CLOUD_BACKUP 还需同步使用数，**还未确定是否要走新的逻辑**
> - 科维修改模组code 对他们影响大吗？ 不大
> - 我们把科维的这些订阅模组都同步到企业服务云，那之后他们在企业服务云修改了这些模组的信息 还需要再同步到 科维吗？ 不需要反向同步，授权的统一管理是在数科CRM
1. 增加表 维护 自动同步订阅模组与服务人员 关系表 
   
   ```mermaid
   erDiagram

    module_staff_sync_mapping {
        bigint id PK
        bigint staffId  UK "服务人员id"
        string moduleCode   "模组code"
        string staffName  "服务人员名称"
        bigint moduleId UK "模组id"
        datetime createDate
        datetime updateDate
    }


   ```
2) 找科维同事 修改订阅模组code和企业服务云保持一致(找丽香)
3) 增加数据到`module_staff_sync_mapping`表 增加TempHum(TMP_RH)  CloudSpam(OPENFIND) CloudBackup(CLOUD_BACKUP) SecurityScanner(Vulnerability_Scan) 对应的 服务人员 1H
4) 维护表数据`aiops_esclient_contract_item_mapping` 增加TempHum(TMP_RH)  CloudSpam(OPENFIND) CloudBackup(CLOUD_BACKUP) SecurityScanner(Vulnerability_Scan)  对应的 运维项目`samdId` 和 科维订阅模组`contractTarget` 1H
5) 修改同步模组合约代码 服务人员部分为查询`module_staff_sync_mapping`表 1H
6) 保存租户模组合约数据








