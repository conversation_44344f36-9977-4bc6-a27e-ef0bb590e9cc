package method.ze.ren.lian;

public class LengthProcessor implements Processor{
    @Override
    public boolean process(Product product, ProcessorChain processorChain) {
        if (product.getLength() > 100 && product.getLength() < 200) {
            System.out.println("长度合格");
            processorChain.process(product, processorChain);
        }
        System.out.println("长度不合格");
        return false;
    }
}
