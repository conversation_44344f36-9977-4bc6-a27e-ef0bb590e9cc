CREATE TABLE `E10ListModuleDocuments_sr_primary` (
                                                     `eid` varchar(65533) NOT NULL COMMENT "",
                                                     `source_db_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_account_set` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_module` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_category` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_no` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_date` date NOT NULL COMMENT "",
                                                     `e10_doc_code` varchar(65533) NULL COMMENT "",
                                                     `e10_doc_name` varchar(65533) NULL COMMENT "",
                                                     `e10_create_date` date NULL COMMENT "",
                                                     `e10_approve_date` date NULL COMMENT "",
                                                     `e10_approve_status` varchar(65533) NULL COMMENT "",
                                                     `e10_approve_period` int(11) NULL COMMENT "",
                                                     `e10_is_make_up_order` boolean NULL COMMENT "",
                                                     `e10_is_not_timely_approve` boolean NULL COMMENT "",
                                                     `e10_max_last_modify_date` varchar(65533) NULL COMMENT "",
                                                     `e10_model_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_name` varchar(65533) NULL COMMENT "",
                                                     `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`, `source_db_id`, `e10_account_set`, `e10_module`, `e10_doc_category`, `e10_doc_id`, `e10_doc_no`, `e10_doc_date`)
COMMENT "OLAP"
PARTITION BY RANGE(`e10_doc_date`)
(PARTITION p202312 VALUES [("2023-12-01"), ("2024-01-01")),
PARTITION p202401 VALUES [("2024-01-01"), ("2024-02-01")),
PARTITION p202402 VALUES [("2024-02-01"), ("2024-03-01")),
PARTITION p202403 VALUES [("2024-03-01"), ("2024-04-01")),
PARTITION p202404 VALUES [("2024-04-01"), ("2024-05-01")),
PARTITION p202405 VALUES [("2024-05-01"), ("2024-06-01")),
PARTITION p202406 VALUES [("2024-06-01"), ("2024-07-01")),
PARTITION p202407 VALUES [("2024-07-01"), ("2024-08-01")),
PARTITION p202408 VALUES [("2024-08-01"), ("2024-09-01")),
PARTITION p202409 VALUES [("2024-09-01"), ("2024-10-01")),
PARTITION p202410 VALUES [("2024-10-01"), ("2024-11-01")),
PARTITION p202411 VALUES [("2024-11-01"), ("2024-12-01")),
PARTITION p202412 VALUES [("2024-12-01"), ("2025-01-01")),
PARTITION p202501 VALUES [("2025-01-01"), ("2025-02-01")),
PARTITION p202502 VALUES [("2025-02-01"), ("2025-03-01")))
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "MONTH",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-12",
"dynamic_partition.end" = "2",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"dynamic_partition.start_day_of_month" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `E10ListModuleDocuments_sr_primary_bak` (
                                                     `eid` varchar(65533) NOT NULL COMMENT "",
                                                     `source_db_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_account_set` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_module` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_category` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_id` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_no` varchar(65533) NOT NULL COMMENT "",
                                                     `e10_doc_date` date NOT NULL COMMENT "",
                                                     `e10_doc_code` varchar(65533) NULL COMMENT "",
                                                     `e10_doc_name` varchar(65533) NULL COMMENT "",
                                                     `e10_create_date` date NULL COMMENT "",
                                                     `e10_approve_date` date NULL COMMENT "",
                                                     `e10_approve_status` varchar(65533) NULL COMMENT "",
                                                     `e10_approve_period` int(11) NULL COMMENT "",
                                                     `e10_is_make_up_order` boolean NULL COMMENT "",
                                                     `e10_is_not_timely_approve` boolean NULL COMMENT "",
                                                     `e10_max_last_modify_date` varchar(65533) NULL COMMENT "",
                                                     `e10_model_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_code` varchar(65533) NULL COMMENT "",
                                                     `e10_company_name` varchar(65533) NULL COMMENT "",
                                                     `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
                                                     `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP
    UNIQUE KEY(`eid`, `source_db_id`, `e10_account_set`, `e10_module`, `e10_doc_category`, `e10_doc_id`, `e10_doc_no`, `e10_doc_date`)
COMMENT "OLAP"
PARTITION BY RANGE(`e10_doc_date`)
(PARTITION p202312 VALUES [("2023-12-01"), ("2024-01-01")),
PARTITION p202401 VALUES [("2024-01-01"), ("2024-02-01")),
PARTITION p202402 VALUES [("2024-02-01"), ("2024-03-01")),
PARTITION p202403 VALUES [("2024-03-01"), ("2024-04-01")),
PARTITION p202404 VALUES [("2024-04-01"), ("2024-05-01")),
PARTITION p202405 VALUES [("2024-05-01"), ("2024-06-01")),
PARTITION p202406 VALUES [("2024-06-01"), ("2024-07-01")),
PARTITION p202407 VALUES [("2024-07-01"), ("2024-08-01")),
PARTITION p202408 VALUES [("2024-08-01"), ("2024-09-01")),
PARTITION p202409 VALUES [("2024-09-01"), ("2024-10-01")),
PARTITION p202410 VALUES [("2024-10-01"), ("2024-11-01")),
PARTITION p202411 VALUES [("2024-11-01"), ("2024-12-01")),
PARTITION p202412 VALUES [("2024-12-01"), ("2025-01-01")),
PARTITION p202501 VALUES [("2025-01-01"), ("2025-02-01")),
PARTITION p202502 VALUES [("2025-02-01"), ("2025-03-01")))
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "MONTH",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-12",
"dynamic_partition.end" = "2",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"dynamic_partition.start_day_of_month" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-01-01'
  AND e10_doc_date < '2024-02-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-02-01'
  AND e10_doc_date < '2024-03-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-03-01'
  AND e10_doc_date < '2024-04-01';



INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-05-01'
  AND e10_doc_date < '2024-06-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-06-01'
  AND e10_doc_date < '2024-07-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-07-01'
  AND e10_doc_date < '2024-08-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-08-01'
  AND e10_doc_date < '2024-09-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-09-01'
  AND e10_doc_date < '2024-10-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-10-01'
  AND e10_doc_date < '2024-11-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-11-01'
  AND e10_doc_date < '2024-12-01';

INSERT INTO E10ListModuleDocuments_sr_primary_bak
SELECT *
FROM E10ListModuleDocuments_sr_primary
WHERE e10_doc_date >= '2024-12-01'
  AND e10_doc_date < '2025-01-01';


INSERT INTO E10ListModuleDocuments_sr_primary
SELECT *
FROM E10ListModuleDocuments_sr_primary_bak_20241202
WHERE e10_doc_date >= '2024-04-01'
  AND e10_doc_date < '2024-04-02';--   sss

INSERT INTO E10ListModuleDocuments_sr_primary
SELECT *
FROM E10ListModuleDocuments_sr_primary_bak_20241202
WHERE e10_doc_date >= '2024-04-06'
  AND e10_doc_date < '2024-04-07';--   sss

INSERT INTO E10ListModuleDocuments_sr_primary
SELECT *
FROM E10ListModuleDocuments_sr_primary_bak_20241202
WHERE e10_doc_date >= '2024-04-20'
  AND e10_doc_date < '2024-04-30';

alter table E10ListModuleDocuments_sr_primary rename E10ListModuleDocuments_sr_primary_bak_20241202;
alter table E10ListModuleDocuments_sr_primary_bak rename E10ListModuleDocuments_sr_primary;

UPDATE `dcp-db`.etl_engine t SET t.sinkExtend = ' UNIQUE KEY(`eid`, `source_db_id`, `e10_account_set`, `e10_module`, `e10_doc_category`, `e10_doc_id`, `e10_doc_no`, `e10_doc_date`)
COMMENT "OLAP"  PARTITION BY RANGE(e10_doc_date)(  START ("2024-01-01") END ("2025-03-23") EVERY (INTERVAL 1 DAY)  )  DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "MONTH",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-12",
"dynamic_partition.end" = "2",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"dynamic_partition.start_day_of_month" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
)' WHERE t.id = 1651;

CREATE TABLE `T100DocumentDeductionListCollected_sr_primary` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `t100_ent` int(11) NOT NULL COMMENT "",
  `t100_site` varchar(65533) NOT NULL COMMENT "",
  `t100_doc_no` varchar(65533) NOT NULL COMMENT "",
  `t100_item` int(11) NOT NULL COMMENT "",
  `t100_item_order` int(11) NOT NULL COMMENT "",
  `t100_access_code` int(11) NOT NULL COMMENT "",
  `t100_max_last_modify_date` varchar(65533) NOT NULL COMMENT "",
  `deviceId` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `collectConfigId` varchar(65533) NULL COMMENT "",
  `uploadDataModelCode` varchar(65533) NULL COMMENT "",
  `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
  `t100_reality_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_doc_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_yyyy` varchar(65533) NULL COMMENT "",
  `t100_mm` varchar(65533) NULL COMMENT "",
  `t100_module_code` varchar(65533) NULL COMMENT "",
  `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(`eid`, `source_db_id`, `t100_ent`, `t100_site`, `t100_doc_no`, `t100_item`, `t100_item_order`, `t100_access_code`, `t100_max_last_modify_date`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `T100DocumentDeductionListCollected_sr_primary_bak` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `t100_ent` int(11) NOT NULL COMMENT "",
  `t100_site` varchar(65533) NOT NULL COMMENT "",
  `t100_doc_no` varchar(65533) NOT NULL COMMENT "",
  `t100_item` int(11) NOT NULL COMMENT "",
  `t100_item_order` int(11) NOT NULL COMMENT "",
  `t100_access_code` int(11) NOT NULL COMMENT "",
  `t100_max_last_modify_date` varchar(65533) NOT NULL COMMENT "",
  `deviceId` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `collectConfigId` varchar(65533) NULL COMMENT "",
  `uploadDataModelCode` varchar(65533) NULL COMMENT "",
  `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
  `t100_reality_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_doc_deduction_date` varchar(65533) NULL COMMENT "",
  `t100_yyyy` varchar(65533) NULL COMMENT "",
  `t100_mm` varchar(65533) NULL COMMENT "",
  `t100_module_code` varchar(65533) NULL COMMENT "",
  `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP 
UNIQUE KEY(`eid`, `source_db_id`, `t100_ent`, `t100_site`, `t100_doc_no`, `t100_item`, `t100_item_order`, `t100_access_code`, `t100_max_last_modify_date`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-01-01 00:00:00' AND collectedTime < '2024-03-01 00:00:00';

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-03-01 00:00:00' AND collectedTime < '2024-05-01 00:00:00';

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-05-01 00:00:00' AND collectedTime < '2024-07-01 00:00:00';

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-07-01 00:00:00' AND collectedTime < '2024-09-01 00:00:00';

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-09-01 00:00:00' AND collectedTime < '2024-11-01 00:00:00';

INSERT INTO T100DocumentDeductionListCollected_sr_primary_bak
SELECT *
FROM T100DocumentDeductionListCollected_sr_primary
WHERE collectedTime >= '2024-11-01 00:00:00' AND collectedTime < '2024-12-01 00:00:00';

alter table T100DocumentDeductionListCollected_sr_primary rename T100DocumentDeductionListCollected_sr_primary_bak_20241202;
alter table T100DocumentDeductionListCollected_sr_primary_bak rename T100DocumentDeductionListCollected_sr_primary;

UPDATE `dcp-db`.etl_engine t SET t.sinkExtend = 'UNIQUE KEY(eid,source_db_id,t100_ent,t100_site,t100_doc_no,t100_item,t100_item_order,t100_access_code,t100_max_last_modify_date)
        DISTRIBUTED BY HASH(source_db_id) BUCKETS 10
        PROPERTIES(
            "replication_num" = "1"
        );' WHERE t.id = 1764;


CREATE TABLE `sqlserver_biz_sbh` (
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `database_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_backup_start_date` datetime NOT NULL COMMENT "",
  `eid` varchar(65533) NOT NULL COMMENT "",
  `collectedDate` date NULL COMMENT "",
  `sqlserver_biz_sbh_name` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_type` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `deviceId` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
  `sqlserver_biz_sbh_backup_finish_date` datetime NULL COMMENT "",
  `sqlserver_biz_sbh_recovery_model` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_size` bigint(20) NULL COMMENT "",
  `sqlserver_biz_sbh_physical_device_name` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_first_lsn` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_last_lsn` varchar(65533) NULL COMMENT "",
  `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(`source_db_id`, `database_name`, `sqlserver_biz_sbh_backup_start_date`, `eid`)
COMMENT "OLAP"
PARTITION BY RANGE(`sqlserver_biz_sbh_backup_start_date`)
(PARTITION p20240605 VALUES [("2024-06-05 00:00:00"), ("2024-06-06 00:00:00")),
PARTITION p20240606 VALUES [("2024-06-06 00:00:00"), ("2024-06-07 00:00:00")),
PARTITION p20240607 VALUES [("2024-06-07 00:00:00"), ("2024-06-08 00:00:00")),
PARTITION p20240608 VALUES [("2024-06-08 00:00:00"), ("2024-06-09 00:00:00")),
PARTITION p20240609 VALUES [("2024-06-09 00:00:00"), ("2024-06-10 00:00:00")),
PARTITION p20240610 VALUES [("2024-06-10 00:00:00"), ("2024-06-11 00:00:00")),
PARTITION p20240611 VALUES [("2024-06-11 00:00:00"), ("2024-06-12 00:00:00")),
PARTITION p20240612 VALUES [("2024-06-12 00:00:00"), ("2024-06-13 00:00:00")),
PARTITION p20240613 VALUES [("2024-06-13 00:00:00"), ("2024-06-14 00:00:00")),
PARTITION p20240614 VALUES [("2024-06-14 00:00:00"), ("2024-06-15 00:00:00")),
PARTITION p20240615 VALUES [("2024-06-15 00:00:00"), ("2024-06-16 00:00:00")),
PARTITION p20240616 VALUES [("2024-06-16 00:00:00"), ("2024-06-17 00:00:00")),
PARTITION p20240617 VALUES [("2024-06-17 00:00:00"), ("2024-06-18 00:00:00")),
PARTITION p20240618 VALUES [("2024-06-18 00:00:00"), ("2024-06-19 00:00:00")),
PARTITION p20240619 VALUES [("2024-06-19 00:00:00"), ("2024-06-20 00:00:00")),
PARTITION p20240620 VALUES [("2024-06-20 00:00:00"), ("2024-06-21 00:00:00")),
PARTITION p20240621 VALUES [("2024-06-21 00:00:00"), ("2024-06-22 00:00:00")),
PARTITION p20240622 VALUES [("2024-06-22 00:00:00"), ("2024-06-23 00:00:00")),
PARTITION p20240623 VALUES [("2024-06-23 00:00:00"), ("2024-06-24 00:00:00")),
PARTITION p20240624 VALUES [("2024-06-24 00:00:00"), ("2024-06-25 00:00:00")),
PARTITION p20240625 VALUES [("2024-06-25 00:00:00"), ("2024-06-26 00:00:00")),
PARTITION p20240626 VALUES [("2024-06-26 00:00:00"), ("2024-06-27 00:00:00")),
PARTITION p20240627 VALUES [("2024-06-27 00:00:00"), ("2024-06-28 00:00:00")),
PARTITION p20240628 VALUES [("2024-06-28 00:00:00"), ("2024-06-29 00:00:00")),
PARTITION p20240629 VALUES [("2024-06-29 00:00:00"), ("2024-06-30 00:00:00")),
PARTITION p20240630 VALUES [("2024-06-30 00:00:00"), ("2024-07-01 00:00:00")),
PARTITION p20240701 VALUES [("2024-07-01 00:00:00"), ("2024-07-02 00:00:00")),
PARTITION p20240702 VALUES [("2024-07-02 00:00:00"), ("2024-07-03 00:00:00")),
PARTITION p20240703 VALUES [("2024-07-03 00:00:00"), ("2024-07-04 00:00:00")),
PARTITION p20240704 VALUES [("2024-07-04 00:00:00"), ("2024-07-05 00:00:00")),
PARTITION p20240705 VALUES [("2024-07-05 00:00:00"), ("2024-07-06 00:00:00")),
PARTITION p20240706 VALUES [("2024-07-06 00:00:00"), ("2024-07-07 00:00:00")),
PARTITION p20240707 VALUES [("2024-07-07 00:00:00"), ("2024-07-08 00:00:00")),
PARTITION p20240708 VALUES [("2024-07-08 00:00:00"), ("2024-07-09 00:00:00")),
PARTITION p20240709 VALUES [("2024-07-09 00:00:00"), ("2024-07-10 00:00:00")),
PARTITION p20240710 VALUES [("2024-07-10 00:00:00"), ("2024-07-11 00:00:00")),
PARTITION p20240711 VALUES [("2024-07-11 00:00:00"), ("2024-07-12 00:00:00")),
PARTITION p20240712 VALUES [("2024-07-12 00:00:00"), ("2024-07-13 00:00:00")),
PARTITION p20240713 VALUES [("2024-07-13 00:00:00"), ("2024-07-14 00:00:00")),
PARTITION p20240714 VALUES [("2024-07-14 00:00:00"), ("2024-07-15 00:00:00")),
PARTITION p20240715 VALUES [("2024-07-15 00:00:00"), ("2024-07-16 00:00:00")),
PARTITION p20240716 VALUES [("2024-07-16 00:00:00"), ("2024-07-17 00:00:00")),
PARTITION p20240717 VALUES [("2024-07-17 00:00:00"), ("2024-07-18 00:00:00")),
PARTITION p20240718 VALUES [("2024-07-18 00:00:00"), ("2024-07-19 00:00:00")),
PARTITION p20240719 VALUES [("2024-07-19 00:00:00"), ("2024-07-20 00:00:00")),
PARTITION p20240720 VALUES [("2024-07-20 00:00:00"), ("2024-07-21 00:00:00")),
PARTITION p20240721 VALUES [("2024-07-21 00:00:00"), ("2024-07-22 00:00:00")),
PARTITION p20240722 VALUES [("2024-07-22 00:00:00"), ("2024-07-23 00:00:00")),
PARTITION p20240723 VALUES [("2024-07-23 00:00:00"), ("2024-07-24 00:00:00")),
PARTITION p20240724 VALUES [("2024-07-24 00:00:00"), ("2024-07-25 00:00:00")),
PARTITION p20240725 VALUES [("2024-07-25 00:00:00"), ("2024-07-26 00:00:00")),
PARTITION p20240726 VALUES [("2024-07-26 00:00:00"), ("2024-07-27 00:00:00")),
PARTITION p20240727 VALUES [("2024-07-27 00:00:00"), ("2024-07-28 00:00:00")),
PARTITION p20240728 VALUES [("2024-07-28 00:00:00"), ("2024-07-29 00:00:00")),
PARTITION p20240729 VALUES [("2024-07-29 00:00:00"), ("2024-07-30 00:00:00")),
PARTITION p20240730 VALUES [("2024-07-30 00:00:00"), ("2024-07-31 00:00:00")),
PARTITION p20240731 VALUES [("2024-07-31 00:00:00"), ("2024-08-01 00:00:00")),
PARTITION p20240801 VALUES [("2024-08-01 00:00:00"), ("2024-08-02 00:00:00")),
PARTITION p20240802 VALUES [("2024-08-02 00:00:00"), ("2024-08-03 00:00:00")),
PARTITION p20240803 VALUES [("2024-08-03 00:00:00"), ("2024-08-04 00:00:00")),
PARTITION p20240804 VALUES [("2024-08-04 00:00:00"), ("2024-08-05 00:00:00")),
PARTITION p20240805 VALUES [("2024-08-05 00:00:00"), ("2024-08-06 00:00:00")),
PARTITION p20240806 VALUES [("2024-08-06 00:00:00"), ("2024-08-07 00:00:00")),
PARTITION p20240807 VALUES [("2024-08-07 00:00:00"), ("2024-08-08 00:00:00")),
PARTITION p20240808 VALUES [("2024-08-08 00:00:00"), ("2024-08-09 00:00:00")),
PARTITION p20240809 VALUES [("2024-08-09 00:00:00"), ("2024-08-10 00:00:00")),
PARTITION p20240810 VALUES [("2024-08-10 00:00:00"), ("2024-08-11 00:00:00")),
PARTITION p20240811 VALUES [("2024-08-11 00:00:00"), ("2024-08-12 00:00:00")),
PARTITION p20240812 VALUES [("2024-08-12 00:00:00"), ("2024-08-13 00:00:00")),
PARTITION p20240813 VALUES [("2024-08-13 00:00:00"), ("2024-08-14 00:00:00")),
PARTITION p20240814 VALUES [("2024-08-14 00:00:00"), ("2024-08-15 00:00:00")),
PARTITION p20240815 VALUES [("2024-08-15 00:00:00"), ("2024-08-16 00:00:00")),
PARTITION p20240816 VALUES [("2024-08-16 00:00:00"), ("2024-08-17 00:00:00")),
PARTITION p20240817 VALUES [("2024-08-17 00:00:00"), ("2024-08-18 00:00:00")),
PARTITION p20240818 VALUES [("2024-08-18 00:00:00"), ("2024-08-19 00:00:00")),
PARTITION p20240819 VALUES [("2024-08-19 00:00:00"), ("2024-08-20 00:00:00")),
PARTITION p20240820 VALUES [("2024-08-20 00:00:00"), ("2024-08-21 00:00:00")),
PARTITION p20240821 VALUES [("2024-08-21 00:00:00"), ("2024-08-22 00:00:00")),
PARTITION p20240822 VALUES [("2024-08-22 00:00:00"), ("2024-08-23 00:00:00")),
PARTITION p20240823 VALUES [("2024-08-23 00:00:00"), ("2024-08-24 00:00:00")),
PARTITION p20240824 VALUES [("2024-08-24 00:00:00"), ("2024-08-25 00:00:00")),
PARTITION p20240825 VALUES [("2024-08-25 00:00:00"), ("2024-08-26 00:00:00")),
PARTITION p20240826 VALUES [("2024-08-26 00:00:00"), ("2024-08-27 00:00:00")),
PARTITION p20240827 VALUES [("2024-08-27 00:00:00"), ("2024-08-28 00:00:00")),
PARTITION p20240828 VALUES [("2024-08-28 00:00:00"), ("2024-08-29 00:00:00")),
PARTITION p20240829 VALUES [("2024-08-29 00:00:00"), ("2024-08-30 00:00:00")),
PARTITION p20240830 VALUES [("2024-08-30 00:00:00"), ("2024-08-31 00:00:00")),
PARTITION p20240831 VALUES [("2024-08-31 00:00:00"), ("2024-09-01 00:00:00")),
PARTITION p20240901 VALUES [("2024-09-01 00:00:00"), ("2024-09-02 00:00:00")),
PARTITION p20240902 VALUES [("2024-09-02 00:00:00"), ("2024-09-03 00:00:00")),
PARTITION p20240903 VALUES [("2024-09-03 00:00:00"), ("2024-09-04 00:00:00")),
PARTITION p20240904 VALUES [("2024-09-04 00:00:00"), ("2024-09-05 00:00:00")),
PARTITION p20240905 VALUES [("2024-09-05 00:00:00"), ("2024-09-06 00:00:00")),
PARTITION p20240906 VALUES [("2024-09-06 00:00:00"), ("2024-09-07 00:00:00")),
PARTITION p20240907 VALUES [("2024-09-07 00:00:00"), ("2024-09-08 00:00:00")),
PARTITION p20240908 VALUES [("2024-09-08 00:00:00"), ("2024-09-09 00:00:00")),
PARTITION p20240909 VALUES [("2024-09-09 00:00:00"), ("2024-09-10 00:00:00")),
PARTITION p20240910 VALUES [("2024-09-10 00:00:00"), ("2024-09-11 00:00:00")),
PARTITION p20240911 VALUES [("2024-09-11 00:00:00"), ("2024-09-12 00:00:00")),
PARTITION p20240912 VALUES [("2024-09-12 00:00:00"), ("2024-09-13 00:00:00")),
PARTITION p20240913 VALUES [("2024-09-13 00:00:00"), ("2024-09-14 00:00:00")),
PARTITION p20240914 VALUES [("2024-09-14 00:00:00"), ("2024-09-15 00:00:00")),
PARTITION p20240915 VALUES [("2024-09-15 00:00:00"), ("2024-09-16 00:00:00")),
PARTITION p20240916 VALUES [("2024-09-16 00:00:00"), ("2024-09-17 00:00:00")),
PARTITION p20240917 VALUES [("2024-09-17 00:00:00"), ("2024-09-18 00:00:00")),
PARTITION p20240918 VALUES [("2024-09-18 00:00:00"), ("2024-09-19 00:00:00")),
PARTITION p20240919 VALUES [("2024-09-19 00:00:00"), ("2024-09-20 00:00:00")),
PARTITION p20240920 VALUES [("2024-09-20 00:00:00"), ("2024-09-21 00:00:00")),
PARTITION p20240921 VALUES [("2024-09-21 00:00:00"), ("2024-09-22 00:00:00")),
PARTITION p20240922 VALUES [("2024-09-22 00:00:00"), ("2024-09-23 00:00:00")),
PARTITION p20240923 VALUES [("2024-09-23 00:00:00"), ("2024-09-24 00:00:00")),
PARTITION p20240924 VALUES [("2024-09-24 00:00:00"), ("2024-09-25 00:00:00")),
PARTITION p20240925 VALUES [("2024-09-25 00:00:00"), ("2024-09-26 00:00:00")),
PARTITION p20240926 VALUES [("2024-09-26 00:00:00"), ("2024-09-27 00:00:00")),
PARTITION p20240927 VALUES [("2024-09-27 00:00:00"), ("2024-09-28 00:00:00")),
PARTITION p20240928 VALUES [("2024-09-28 00:00:00"), ("2024-09-29 00:00:00")),
PARTITION p20240929 VALUES [("2024-09-29 00:00:00"), ("2024-09-30 00:00:00")),
PARTITION p20240930 VALUES [("2024-09-30 00:00:00"), ("2024-10-01 00:00:00")),
PARTITION p20241001 VALUES [("2024-10-01 00:00:00"), ("2024-10-02 00:00:00")),
PARTITION p20241002 VALUES [("2024-10-02 00:00:00"), ("2024-10-03 00:00:00")),
PARTITION p20241003 VALUES [("2024-10-03 00:00:00"), ("2024-10-04 00:00:00")),
PARTITION p20241004 VALUES [("2024-10-04 00:00:00"), ("2024-10-05 00:00:00")),
PARTITION p20241005 VALUES [("2024-10-05 00:00:00"), ("2024-10-06 00:00:00")),
PARTITION p20241006 VALUES [("2024-10-06 00:00:00"), ("2024-10-07 00:00:00")),
PARTITION p20241007 VALUES [("2024-10-07 00:00:00"), ("2024-10-08 00:00:00")),
PARTITION p20241008 VALUES [("2024-10-08 00:00:00"), ("2024-10-09 00:00:00")),
PARTITION p20241009 VALUES [("2024-10-09 00:00:00"), ("2024-10-10 00:00:00")),
PARTITION p20241010 VALUES [("2024-10-10 00:00:00"), ("2024-10-11 00:00:00")),
PARTITION p20241011 VALUES [("2024-10-11 00:00:00"), ("2024-10-12 00:00:00")),
PARTITION p20241012 VALUES [("2024-10-12 00:00:00"), ("2024-10-13 00:00:00")),
PARTITION p20241013 VALUES [("2024-10-13 00:00:00"), ("2024-10-14 00:00:00")),
PARTITION p20241014 VALUES [("2024-10-14 00:00:00"), ("2024-10-15 00:00:00")),
PARTITION p20241015 VALUES [("2024-10-15 00:00:00"), ("2024-10-16 00:00:00")),
PARTITION p20241016 VALUES [("2024-10-16 00:00:00"), ("2024-10-17 00:00:00")),
PARTITION p20241017 VALUES [("2024-10-17 00:00:00"), ("2024-10-18 00:00:00")),
PARTITION p20241018 VALUES [("2024-10-18 00:00:00"), ("2024-10-19 00:00:00")),
PARTITION p20241019 VALUES [("2024-10-19 00:00:00"), ("2024-10-20 00:00:00")),
PARTITION p20241020 VALUES [("2024-10-20 00:00:00"), ("2024-10-21 00:00:00")),
PARTITION p20241021 VALUES [("2024-10-21 00:00:00"), ("2024-10-22 00:00:00")),
PARTITION p20241022 VALUES [("2024-10-22 00:00:00"), ("2024-10-23 00:00:00")),
PARTITION p20241023 VALUES [("2024-10-23 00:00:00"), ("2024-10-24 00:00:00")),
PARTITION p20241024 VALUES [("2024-10-24 00:00:00"), ("2024-10-25 00:00:00")),
PARTITION p20241025 VALUES [("2024-10-25 00:00:00"), ("2024-10-26 00:00:00")),
PARTITION p20241026 VALUES [("2024-10-26 00:00:00"), ("2024-10-27 00:00:00")),
PARTITION p20241027 VALUES [("2024-10-27 00:00:00"), ("2024-10-28 00:00:00")),
PARTITION p20241028 VALUES [("2024-10-28 00:00:00"), ("2024-10-29 00:00:00")),
PARTITION p20241029 VALUES [("2024-10-29 00:00:00"), ("2024-10-30 00:00:00")),
PARTITION p20241030 VALUES [("2024-10-30 00:00:00"), ("2024-10-31 00:00:00")),
PARTITION p20241031 VALUES [("2024-10-31 00:00:00"), ("2024-11-01 00:00:00")),
PARTITION p20241101 VALUES [("2024-11-01 00:00:00"), ("2024-11-02 00:00:00")),
PARTITION p20241102 VALUES [("2024-11-02 00:00:00"), ("2024-11-03 00:00:00")),
PARTITION p20241103 VALUES [("2024-11-03 00:00:00"), ("2024-11-04 00:00:00")),
PARTITION p20241104 VALUES [("2024-11-04 00:00:00"), ("2024-11-05 00:00:00")),
PARTITION p20241105 VALUES [("2024-11-05 00:00:00"), ("2024-11-06 00:00:00")),
PARTITION p20241106 VALUES [("2024-11-06 00:00:00"), ("2024-11-07 00:00:00")),
PARTITION p20241107 VALUES [("2024-11-07 00:00:00"), ("2024-11-08 00:00:00")),
PARTITION p20241108 VALUES [("2024-11-08 00:00:00"), ("2024-11-09 00:00:00")),
PARTITION p20241109 VALUES [("2024-11-09 00:00:00"), ("2024-11-10 00:00:00")),
PARTITION p20241110 VALUES [("2024-11-10 00:00:00"), ("2024-11-11 00:00:00")),
PARTITION p20241111 VALUES [("2024-11-11 00:00:00"), ("2024-11-12 00:00:00")),
PARTITION p20241112 VALUES [("2024-11-12 00:00:00"), ("2024-11-13 00:00:00")),
PARTITION p20241113 VALUES [("2024-11-13 00:00:00"), ("2024-11-14 00:00:00")),
PARTITION p20241114 VALUES [("2024-11-14 00:00:00"), ("2024-11-15 00:00:00")),
PARTITION p20241115 VALUES [("2024-11-15 00:00:00"), ("2024-11-16 00:00:00")),
PARTITION p20241116 VALUES [("2024-11-16 00:00:00"), ("2024-11-17 00:00:00")),
PARTITION p20241117 VALUES [("2024-11-17 00:00:00"), ("2024-11-18 00:00:00")),
PARTITION p20241118 VALUES [("2024-11-18 00:00:00"), ("2024-11-19 00:00:00")),
PARTITION p20241119 VALUES [("2024-11-19 00:00:00"), ("2024-11-20 00:00:00")),
PARTITION p20241120 VALUES [("2024-11-20 00:00:00"), ("2024-11-21 00:00:00")),
PARTITION p20241121 VALUES [("2024-11-21 00:00:00"), ("2024-11-22 00:00:00")),
PARTITION p20241122 VALUES [("2024-11-22 00:00:00"), ("2024-11-23 00:00:00")),
PARTITION p20241123 VALUES [("2024-11-23 00:00:00"), ("2024-11-24 00:00:00")),
PARTITION p20241124 VALUES [("2024-11-24 00:00:00"), ("2024-11-25 00:00:00")),
PARTITION p20241125 VALUES [("2024-11-25 00:00:00"), ("2024-11-26 00:00:00")),
PARTITION p20241126 VALUES [("2024-11-26 00:00:00"), ("2024-11-27 00:00:00")),
PARTITION p20241127 VALUES [("2024-11-27 00:00:00"), ("2024-11-28 00:00:00")),
PARTITION p20241128 VALUES [("2024-11-28 00:00:00"), ("2024-11-29 00:00:00")),
PARTITION p20241129 VALUES [("2024-11-29 00:00:00"), ("2024-11-30 00:00:00")),
PARTITION p20241130 VALUES [("2024-11-30 00:00:00"), ("2024-12-01 00:00:00")),
PARTITION p20241201 VALUES [("2024-12-01 00:00:00"), ("2024-12-02 00:00:00")),
PARTITION p20241202 VALUES [("2024-12-02 00:00:00"), ("2024-12-03 00:00:00")),
PARTITION p20241203 VALUES [("2024-12-03 00:00:00"), ("2024-12-04 00:00:00")),
PARTITION p20241204 VALUES [("2024-12-04 00:00:00"), ("2024-12-05 00:00:00")),
PARTITION p20241205 VALUES [("2024-12-05 00:00:00"), ("2024-12-06 00:00:00")),
PARTITION p20241206 VALUES [("2024-12-06 00:00:00"), ("2024-12-07 00:00:00")),
PARTITION p20241207 VALUES [("2024-12-07 00:00:00"), ("2024-12-08 00:00:00")),
PARTITION p20241208 VALUES [("2024-12-08 00:00:00"), ("2024-12-09 00:00:00")),
PARTITION p20241209 VALUES [("2024-12-09 00:00:00"), ("2024-12-10 00:00:00")),
PARTITION p20241210 VALUES [("2024-12-10 00:00:00"), ("2024-12-11 00:00:00")),
PARTITION p20241211 VALUES [("2024-12-11 00:00:00"), ("2024-12-12 00:00:00")),
PARTITION p20241212 VALUES [("2024-12-12 00:00:00"), ("2024-12-13 00:00:00")),
PARTITION p20241213 VALUES [("2024-12-13 00:00:00"), ("2024-12-14 00:00:00")),
PARTITION p20241214 VALUES [("2024-12-14 00:00:00"), ("2024-12-15 00:00:00")),
PARTITION p20241215 VALUES [("2024-12-15 00:00:00"), ("2024-12-16 00:00:00")),
PARTITION p20241216 VALUES [("2024-12-16 00:00:00"), ("2024-12-17 00:00:00")),
PARTITION p20241217 VALUES [("2024-12-17 00:00:00"), ("2024-12-18 00:00:00")),
PARTITION p20241218 VALUES [("2024-12-18 00:00:00"), ("2024-12-19 00:00:00")),
PARTITION p20241219 VALUES [("2024-12-19 00:00:00"), ("2024-12-20 00:00:00")),
PARTITION p20241220 VALUES [("2024-12-20 00:00:00"), ("2024-12-21 00:00:00")),
PARTITION p20241221 VALUES [("2024-12-21 00:00:00"), ("2024-12-22 00:00:00")),
PARTITION p20241222 VALUES [("2024-12-22 00:00:00"), ("2024-12-23 00:00:00")),
PARTITION p20241223 VALUES [("2024-12-23 00:00:00"), ("2024-12-24 00:00:00")),
PARTITION p20241224 VALUES [("2024-12-24 00:00:00"), ("2024-12-25 00:00:00")),
PARTITION p20241225 VALUES [("2024-12-25 00:00:00"), ("2024-12-26 00:00:00")),
PARTITION p20241226 VALUES [("2024-12-26 00:00:00"), ("2024-12-27 00:00:00")),
PARTITION p20241227 VALUES [("2024-12-27 00:00:00"), ("2024-12-28 00:00:00")),
PARTITION p20241228 VALUES [("2024-12-28 00:00:00"), ("2024-12-29 00:00:00")),
PARTITION p20241229 VALUES [("2024-12-29 00:00:00"), ("2024-12-30 00:00:00")),
PARTITION p20241230 VALUES [("2024-12-30 00:00:00"), ("2024-12-31 00:00:00")),
PARTITION p20241231 VALUES [("2024-12-31 00:00:00"), ("2025-01-01 00:00:00")),
PARTITION p20250101 VALUES [("2025-01-01 00:00:00"), ("2025-01-02 00:00:00")))
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-180",
"dynamic_partition.end" = "30",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `sqlserver_biz_sbh_bak` (
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `database_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_backup_start_date` datetime NOT NULL COMMENT "",
  `eid` varchar(65533) NOT NULL COMMENT "",
  `collectedDate` date NULL COMMENT "",
  `sqlserver_biz_sbh_name` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_type` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `deviceId` varchar(65533) NULL COMMENT "",
  `aiId` varchar(65533) NULL DEFAULT "" COMMENT "",
  `aiopsItem` varchar(65533) NULL DEFAULT "" COMMENT "",
  `sqlserver_biz_sbh_backup_finish_date` datetime NULL COMMENT "",
  `sqlserver_biz_sbh_recovery_model` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_size` bigint(20) NULL COMMENT "",
  `sqlserver_biz_sbh_physical_device_name` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_first_lsn` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_last_lsn` varchar(65533) NULL COMMENT "",
  `flumeTimestamp` varchar(65536) NULL COMMENT ""
) ENGINE=OLAP 
UNIQUE KEY(`source_db_id`, `database_name`, `sqlserver_biz_sbh_backup_start_date`, `eid`)
COMMENT "OLAP"
PARTITION BY RANGE(`sqlserver_biz_sbh_backup_start_date`)
(PARTITION p20240605 VALUES [("2024-06-05 00:00:00"), ("2024-06-06 00:00:00")),
PARTITION p20240606 VALUES [("2024-06-06 00:00:00"), ("2024-06-07 00:00:00")),
PARTITION p20240607 VALUES [("2024-06-07 00:00:00"), ("2024-06-08 00:00:00")),
PARTITION p20240608 VALUES [("2024-06-08 00:00:00"), ("2024-06-09 00:00:00")),
PARTITION p20240609 VALUES [("2024-06-09 00:00:00"), ("2024-06-10 00:00:00")),
PARTITION p20240610 VALUES [("2024-06-10 00:00:00"), ("2024-06-11 00:00:00")),
PARTITION p20240611 VALUES [("2024-06-11 00:00:00"), ("2024-06-12 00:00:00")),
PARTITION p20240612 VALUES [("2024-06-12 00:00:00"), ("2024-06-13 00:00:00")),
PARTITION p20240613 VALUES [("2024-06-13 00:00:00"), ("2024-06-14 00:00:00")),
PARTITION p20240614 VALUES [("2024-06-14 00:00:00"), ("2024-06-15 00:00:00")),
PARTITION p20240615 VALUES [("2024-06-15 00:00:00"), ("2024-06-16 00:00:00")),
PARTITION p20240616 VALUES [("2024-06-16 00:00:00"), ("2024-06-17 00:00:00")),
PARTITION p20240617 VALUES [("2024-06-17 00:00:00"), ("2024-06-18 00:00:00")),
PARTITION p20240618 VALUES [("2024-06-18 00:00:00"), ("2024-06-19 00:00:00")),
PARTITION p20240619 VALUES [("2024-06-19 00:00:00"), ("2024-06-20 00:00:00")),
PARTITION p20240620 VALUES [("2024-06-20 00:00:00"), ("2024-06-21 00:00:00")),
PARTITION p20240621 VALUES [("2024-06-21 00:00:00"), ("2024-06-22 00:00:00")),
PARTITION p20240622 VALUES [("2024-06-22 00:00:00"), ("2024-06-23 00:00:00")),
PARTITION p20240623 VALUES [("2024-06-23 00:00:00"), ("2024-06-24 00:00:00")),
PARTITION p20240624 VALUES [("2024-06-24 00:00:00"), ("2024-06-25 00:00:00")),
PARTITION p20240625 VALUES [("2024-06-25 00:00:00"), ("2024-06-26 00:00:00")),
PARTITION p20240626 VALUES [("2024-06-26 00:00:00"), ("2024-06-27 00:00:00")),
PARTITION p20240627 VALUES [("2024-06-27 00:00:00"), ("2024-06-28 00:00:00")),
PARTITION p20240628 VALUES [("2024-06-28 00:00:00"), ("2024-06-29 00:00:00")),
PARTITION p20240629 VALUES [("2024-06-29 00:00:00"), ("2024-06-30 00:00:00")),
PARTITION p20240630 VALUES [("2024-06-30 00:00:00"), ("2024-07-01 00:00:00")),
PARTITION p20240701 VALUES [("2024-07-01 00:00:00"), ("2024-07-02 00:00:00")),
PARTITION p20240702 VALUES [("2024-07-02 00:00:00"), ("2024-07-03 00:00:00")),
PARTITION p20240703 VALUES [("2024-07-03 00:00:00"), ("2024-07-04 00:00:00")),
PARTITION p20240704 VALUES [("2024-07-04 00:00:00"), ("2024-07-05 00:00:00")),
PARTITION p20240705 VALUES [("2024-07-05 00:00:00"), ("2024-07-06 00:00:00")),
PARTITION p20240706 VALUES [("2024-07-06 00:00:00"), ("2024-07-07 00:00:00")),
PARTITION p20240707 VALUES [("2024-07-07 00:00:00"), ("2024-07-08 00:00:00")),
PARTITION p20240708 VALUES [("2024-07-08 00:00:00"), ("2024-07-09 00:00:00")),
PARTITION p20240709 VALUES [("2024-07-09 00:00:00"), ("2024-07-10 00:00:00")),
PARTITION p20240710 VALUES [("2024-07-10 00:00:00"), ("2024-07-11 00:00:00")),
PARTITION p20240711 VALUES [("2024-07-11 00:00:00"), ("2024-07-12 00:00:00")),
PARTITION p20240712 VALUES [("2024-07-12 00:00:00"), ("2024-07-13 00:00:00")),
PARTITION p20240713 VALUES [("2024-07-13 00:00:00"), ("2024-07-14 00:00:00")),
PARTITION p20240714 VALUES [("2024-07-14 00:00:00"), ("2024-07-15 00:00:00")),
PARTITION p20240715 VALUES [("2024-07-15 00:00:00"), ("2024-07-16 00:00:00")),
PARTITION p20240716 VALUES [("2024-07-16 00:00:00"), ("2024-07-17 00:00:00")),
PARTITION p20240717 VALUES [("2024-07-17 00:00:00"), ("2024-07-18 00:00:00")),
PARTITION p20240718 VALUES [("2024-07-18 00:00:00"), ("2024-07-19 00:00:00")),
PARTITION p20240719 VALUES [("2024-07-19 00:00:00"), ("2024-07-20 00:00:00")),
PARTITION p20240720 VALUES [("2024-07-20 00:00:00"), ("2024-07-21 00:00:00")),
PARTITION p20240721 VALUES [("2024-07-21 00:00:00"), ("2024-07-22 00:00:00")),
PARTITION p20240722 VALUES [("2024-07-22 00:00:00"), ("2024-07-23 00:00:00")),
PARTITION p20240723 VALUES [("2024-07-23 00:00:00"), ("2024-07-24 00:00:00")),
PARTITION p20240724 VALUES [("2024-07-24 00:00:00"), ("2024-07-25 00:00:00")),
PARTITION p20240725 VALUES [("2024-07-25 00:00:00"), ("2024-07-26 00:00:00")),
PARTITION p20240726 VALUES [("2024-07-26 00:00:00"), ("2024-07-27 00:00:00")),
PARTITION p20240727 VALUES [("2024-07-27 00:00:00"), ("2024-07-28 00:00:00")),
PARTITION p20240728 VALUES [("2024-07-28 00:00:00"), ("2024-07-29 00:00:00")),
PARTITION p20240729 VALUES [("2024-07-29 00:00:00"), ("2024-07-30 00:00:00")),
PARTITION p20240730 VALUES [("2024-07-30 00:00:00"), ("2024-07-31 00:00:00")),
PARTITION p20240731 VALUES [("2024-07-31 00:00:00"), ("2024-08-01 00:00:00")),
PARTITION p20240801 VALUES [("2024-08-01 00:00:00"), ("2024-08-02 00:00:00")),
PARTITION p20240802 VALUES [("2024-08-02 00:00:00"), ("2024-08-03 00:00:00")),
PARTITION p20240803 VALUES [("2024-08-03 00:00:00"), ("2024-08-04 00:00:00")),
PARTITION p20240804 VALUES [("2024-08-04 00:00:00"), ("2024-08-05 00:00:00")),
PARTITION p20240805 VALUES [("2024-08-05 00:00:00"), ("2024-08-06 00:00:00")),
PARTITION p20240806 VALUES [("2024-08-06 00:00:00"), ("2024-08-07 00:00:00")),
PARTITION p20240807 VALUES [("2024-08-07 00:00:00"), ("2024-08-08 00:00:00")),
PARTITION p20240808 VALUES [("2024-08-08 00:00:00"), ("2024-08-09 00:00:00")),
PARTITION p20240809 VALUES [("2024-08-09 00:00:00"), ("2024-08-10 00:00:00")),
PARTITION p20240810 VALUES [("2024-08-10 00:00:00"), ("2024-08-11 00:00:00")),
PARTITION p20240811 VALUES [("2024-08-11 00:00:00"), ("2024-08-12 00:00:00")),
PARTITION p20240812 VALUES [("2024-08-12 00:00:00"), ("2024-08-13 00:00:00")),
PARTITION p20240813 VALUES [("2024-08-13 00:00:00"), ("2024-08-14 00:00:00")),
PARTITION p20240814 VALUES [("2024-08-14 00:00:00"), ("2024-08-15 00:00:00")),
PARTITION p20240815 VALUES [("2024-08-15 00:00:00"), ("2024-08-16 00:00:00")),
PARTITION p20240816 VALUES [("2024-08-16 00:00:00"), ("2024-08-17 00:00:00")),
PARTITION p20240817 VALUES [("2024-08-17 00:00:00"), ("2024-08-18 00:00:00")),
PARTITION p20240818 VALUES [("2024-08-18 00:00:00"), ("2024-08-19 00:00:00")),
PARTITION p20240819 VALUES [("2024-08-19 00:00:00"), ("2024-08-20 00:00:00")),
PARTITION p20240820 VALUES [("2024-08-20 00:00:00"), ("2024-08-21 00:00:00")),
PARTITION p20240821 VALUES [("2024-08-21 00:00:00"), ("2024-08-22 00:00:00")),
PARTITION p20240822 VALUES [("2024-08-22 00:00:00"), ("2024-08-23 00:00:00")),
PARTITION p20240823 VALUES [("2024-08-23 00:00:00"), ("2024-08-24 00:00:00")),
PARTITION p20240824 VALUES [("2024-08-24 00:00:00"), ("2024-08-25 00:00:00")),
PARTITION p20240825 VALUES [("2024-08-25 00:00:00"), ("2024-08-26 00:00:00")),
PARTITION p20240826 VALUES [("2024-08-26 00:00:00"), ("2024-08-27 00:00:00")),
PARTITION p20240827 VALUES [("2024-08-27 00:00:00"), ("2024-08-28 00:00:00")),
PARTITION p20240828 VALUES [("2024-08-28 00:00:00"), ("2024-08-29 00:00:00")),
PARTITION p20240829 VALUES [("2024-08-29 00:00:00"), ("2024-08-30 00:00:00")),
PARTITION p20240830 VALUES [("2024-08-30 00:00:00"), ("2024-08-31 00:00:00")),
PARTITION p20240831 VALUES [("2024-08-31 00:00:00"), ("2024-09-01 00:00:00")),
PARTITION p20240901 VALUES [("2024-09-01 00:00:00"), ("2024-09-02 00:00:00")),
PARTITION p20240902 VALUES [("2024-09-02 00:00:00"), ("2024-09-03 00:00:00")),
PARTITION p20240903 VALUES [("2024-09-03 00:00:00"), ("2024-09-04 00:00:00")),
PARTITION p20240904 VALUES [("2024-09-04 00:00:00"), ("2024-09-05 00:00:00")),
PARTITION p20240905 VALUES [("2024-09-05 00:00:00"), ("2024-09-06 00:00:00")),
PARTITION p20240906 VALUES [("2024-09-06 00:00:00"), ("2024-09-07 00:00:00")),
PARTITION p20240907 VALUES [("2024-09-07 00:00:00"), ("2024-09-08 00:00:00")),
PARTITION p20240908 VALUES [("2024-09-08 00:00:00"), ("2024-09-09 00:00:00")),
PARTITION p20240909 VALUES [("2024-09-09 00:00:00"), ("2024-09-10 00:00:00")),
PARTITION p20240910 VALUES [("2024-09-10 00:00:00"), ("2024-09-11 00:00:00")),
PARTITION p20240911 VALUES [("2024-09-11 00:00:00"), ("2024-09-12 00:00:00")),
PARTITION p20240912 VALUES [("2024-09-12 00:00:00"), ("2024-09-13 00:00:00")),
PARTITION p20240913 VALUES [("2024-09-13 00:00:00"), ("2024-09-14 00:00:00")),
PARTITION p20240914 VALUES [("2024-09-14 00:00:00"), ("2024-09-15 00:00:00")),
PARTITION p20240915 VALUES [("2024-09-15 00:00:00"), ("2024-09-16 00:00:00")),
PARTITION p20240916 VALUES [("2024-09-16 00:00:00"), ("2024-09-17 00:00:00")),
PARTITION p20240917 VALUES [("2024-09-17 00:00:00"), ("2024-09-18 00:00:00")),
PARTITION p20240918 VALUES [("2024-09-18 00:00:00"), ("2024-09-19 00:00:00")),
PARTITION p20240919 VALUES [("2024-09-19 00:00:00"), ("2024-09-20 00:00:00")),
PARTITION p20240920 VALUES [("2024-09-20 00:00:00"), ("2024-09-21 00:00:00")),
PARTITION p20240921 VALUES [("2024-09-21 00:00:00"), ("2024-09-22 00:00:00")),
PARTITION p20240922 VALUES [("2024-09-22 00:00:00"), ("2024-09-23 00:00:00")),
PARTITION p20240923 VALUES [("2024-09-23 00:00:00"), ("2024-09-24 00:00:00")),
PARTITION p20240924 VALUES [("2024-09-24 00:00:00"), ("2024-09-25 00:00:00")),
PARTITION p20240925 VALUES [("2024-09-25 00:00:00"), ("2024-09-26 00:00:00")),
PARTITION p20240926 VALUES [("2024-09-26 00:00:00"), ("2024-09-27 00:00:00")),
PARTITION p20240927 VALUES [("2024-09-27 00:00:00"), ("2024-09-28 00:00:00")),
PARTITION p20240928 VALUES [("2024-09-28 00:00:00"), ("2024-09-29 00:00:00")),
PARTITION p20240929 VALUES [("2024-09-29 00:00:00"), ("2024-09-30 00:00:00")),
PARTITION p20240930 VALUES [("2024-09-30 00:00:00"), ("2024-10-01 00:00:00")),
PARTITION p20241001 VALUES [("2024-10-01 00:00:00"), ("2024-10-02 00:00:00")),
PARTITION p20241002 VALUES [("2024-10-02 00:00:00"), ("2024-10-03 00:00:00")),
PARTITION p20241003 VALUES [("2024-10-03 00:00:00"), ("2024-10-04 00:00:00")),
PARTITION p20241004 VALUES [("2024-10-04 00:00:00"), ("2024-10-05 00:00:00")),
PARTITION p20241005 VALUES [("2024-10-05 00:00:00"), ("2024-10-06 00:00:00")),
PARTITION p20241006 VALUES [("2024-10-06 00:00:00"), ("2024-10-07 00:00:00")),
PARTITION p20241007 VALUES [("2024-10-07 00:00:00"), ("2024-10-08 00:00:00")),
PARTITION p20241008 VALUES [("2024-10-08 00:00:00"), ("2024-10-09 00:00:00")),
PARTITION p20241009 VALUES [("2024-10-09 00:00:00"), ("2024-10-10 00:00:00")),
PARTITION p20241010 VALUES [("2024-10-10 00:00:00"), ("2024-10-11 00:00:00")),
PARTITION p20241011 VALUES [("2024-10-11 00:00:00"), ("2024-10-12 00:00:00")),
PARTITION p20241012 VALUES [("2024-10-12 00:00:00"), ("2024-10-13 00:00:00")),
PARTITION p20241013 VALUES [("2024-10-13 00:00:00"), ("2024-10-14 00:00:00")),
PARTITION p20241014 VALUES [("2024-10-14 00:00:00"), ("2024-10-15 00:00:00")),
PARTITION p20241015 VALUES [("2024-10-15 00:00:00"), ("2024-10-16 00:00:00")),
PARTITION p20241016 VALUES [("2024-10-16 00:00:00"), ("2024-10-17 00:00:00")),
PARTITION p20241017 VALUES [("2024-10-17 00:00:00"), ("2024-10-18 00:00:00")),
PARTITION p20241018 VALUES [("2024-10-18 00:00:00"), ("2024-10-19 00:00:00")),
PARTITION p20241019 VALUES [("2024-10-19 00:00:00"), ("2024-10-20 00:00:00")),
PARTITION p20241020 VALUES [("2024-10-20 00:00:00"), ("2024-10-21 00:00:00")),
PARTITION p20241021 VALUES [("2024-10-21 00:00:00"), ("2024-10-22 00:00:00")),
PARTITION p20241022 VALUES [("2024-10-22 00:00:00"), ("2024-10-23 00:00:00")),
PARTITION p20241023 VALUES [("2024-10-23 00:00:00"), ("2024-10-24 00:00:00")),
PARTITION p20241024 VALUES [("2024-10-24 00:00:00"), ("2024-10-25 00:00:00")),
PARTITION p20241025 VALUES [("2024-10-25 00:00:00"), ("2024-10-26 00:00:00")),
PARTITION p20241026 VALUES [("2024-10-26 00:00:00"), ("2024-10-27 00:00:00")),
PARTITION p20241027 VALUES [("2024-10-27 00:00:00"), ("2024-10-28 00:00:00")),
PARTITION p20241028 VALUES [("2024-10-28 00:00:00"), ("2024-10-29 00:00:00")),
PARTITION p20241029 VALUES [("2024-10-29 00:00:00"), ("2024-10-30 00:00:00")),
PARTITION p20241030 VALUES [("2024-10-30 00:00:00"), ("2024-10-31 00:00:00")),
PARTITION p20241031 VALUES [("2024-10-31 00:00:00"), ("2024-11-01 00:00:00")),
PARTITION p20241101 VALUES [("2024-11-01 00:00:00"), ("2024-11-02 00:00:00")),
PARTITION p20241102 VALUES [("2024-11-02 00:00:00"), ("2024-11-03 00:00:00")),
PARTITION p20241103 VALUES [("2024-11-03 00:00:00"), ("2024-11-04 00:00:00")),
PARTITION p20241104 VALUES [("2024-11-04 00:00:00"), ("2024-11-05 00:00:00")),
PARTITION p20241105 VALUES [("2024-11-05 00:00:00"), ("2024-11-06 00:00:00")),
PARTITION p20241106 VALUES [("2024-11-06 00:00:00"), ("2024-11-07 00:00:00")),
PARTITION p20241107 VALUES [("2024-11-07 00:00:00"), ("2024-11-08 00:00:00")),
PARTITION p20241108 VALUES [("2024-11-08 00:00:00"), ("2024-11-09 00:00:00")),
PARTITION p20241109 VALUES [("2024-11-09 00:00:00"), ("2024-11-10 00:00:00")),
PARTITION p20241110 VALUES [("2024-11-10 00:00:00"), ("2024-11-11 00:00:00")),
PARTITION p20241111 VALUES [("2024-11-11 00:00:00"), ("2024-11-12 00:00:00")),
PARTITION p20241112 VALUES [("2024-11-12 00:00:00"), ("2024-11-13 00:00:00")),
PARTITION p20241113 VALUES [("2024-11-13 00:00:00"), ("2024-11-14 00:00:00")),
PARTITION p20241114 VALUES [("2024-11-14 00:00:00"), ("2024-11-15 00:00:00")),
PARTITION p20241115 VALUES [("2024-11-15 00:00:00"), ("2024-11-16 00:00:00")),
PARTITION p20241116 VALUES [("2024-11-16 00:00:00"), ("2024-11-17 00:00:00")),
PARTITION p20241117 VALUES [("2024-11-17 00:00:00"), ("2024-11-18 00:00:00")),
PARTITION p20241118 VALUES [("2024-11-18 00:00:00"), ("2024-11-19 00:00:00")),
PARTITION p20241119 VALUES [("2024-11-19 00:00:00"), ("2024-11-20 00:00:00")),
PARTITION p20241120 VALUES [("2024-11-20 00:00:00"), ("2024-11-21 00:00:00")),
PARTITION p20241121 VALUES [("2024-11-21 00:00:00"), ("2024-11-22 00:00:00")),
PARTITION p20241122 VALUES [("2024-11-22 00:00:00"), ("2024-11-23 00:00:00")),
PARTITION p20241123 VALUES [("2024-11-23 00:00:00"), ("2024-11-24 00:00:00")),
PARTITION p20241124 VALUES [("2024-11-24 00:00:00"), ("2024-11-25 00:00:00")),
PARTITION p20241125 VALUES [("2024-11-25 00:00:00"), ("2024-11-26 00:00:00")),
PARTITION p20241126 VALUES [("2024-11-26 00:00:00"), ("2024-11-27 00:00:00")),
PARTITION p20241127 VALUES [("2024-11-27 00:00:00"), ("2024-11-28 00:00:00")),
PARTITION p20241128 VALUES [("2024-11-28 00:00:00"), ("2024-11-29 00:00:00")),
PARTITION p20241129 VALUES [("2024-11-29 00:00:00"), ("2024-11-30 00:00:00")),
PARTITION p20241130 VALUES [("2024-11-30 00:00:00"), ("2024-12-01 00:00:00")),
PARTITION p20241201 VALUES [("2024-12-01 00:00:00"), ("2024-12-02 00:00:00")),
PARTITION p20241202 VALUES [("2024-12-02 00:00:00"), ("2024-12-03 00:00:00")),
PARTITION p20241203 VALUES [("2024-12-03 00:00:00"), ("2024-12-04 00:00:00")),
PARTITION p20241204 VALUES [("2024-12-04 00:00:00"), ("2024-12-05 00:00:00")),
PARTITION p20241205 VALUES [("2024-12-05 00:00:00"), ("2024-12-06 00:00:00")),
PARTITION p20241206 VALUES [("2024-12-06 00:00:00"), ("2024-12-07 00:00:00")),
PARTITION p20241207 VALUES [("2024-12-07 00:00:00"), ("2024-12-08 00:00:00")),
PARTITION p20241208 VALUES [("2024-12-08 00:00:00"), ("2024-12-09 00:00:00")),
PARTITION p20241209 VALUES [("2024-12-09 00:00:00"), ("2024-12-10 00:00:00")),
PARTITION p20241210 VALUES [("2024-12-10 00:00:00"), ("2024-12-11 00:00:00")),
PARTITION p20241211 VALUES [("2024-12-11 00:00:00"), ("2024-12-12 00:00:00")),
PARTITION p20241212 VALUES [("2024-12-12 00:00:00"), ("2024-12-13 00:00:00")),
PARTITION p20241213 VALUES [("2024-12-13 00:00:00"), ("2024-12-14 00:00:00")),
PARTITION p20241214 VALUES [("2024-12-14 00:00:00"), ("2024-12-15 00:00:00")),
PARTITION p20241215 VALUES [("2024-12-15 00:00:00"), ("2024-12-16 00:00:00")),
PARTITION p20241216 VALUES [("2024-12-16 00:00:00"), ("2024-12-17 00:00:00")),
PARTITION p20241217 VALUES [("2024-12-17 00:00:00"), ("2024-12-18 00:00:00")),
PARTITION p20241218 VALUES [("2024-12-18 00:00:00"), ("2024-12-19 00:00:00")),
PARTITION p20241219 VALUES [("2024-12-19 00:00:00"), ("2024-12-20 00:00:00")),
PARTITION p20241220 VALUES [("2024-12-20 00:00:00"), ("2024-12-21 00:00:00")),
PARTITION p20241221 VALUES [("2024-12-21 00:00:00"), ("2024-12-22 00:00:00")),
PARTITION p20241222 VALUES [("2024-12-22 00:00:00"), ("2024-12-23 00:00:00")),
PARTITION p20241223 VALUES [("2024-12-23 00:00:00"), ("2024-12-24 00:00:00")),
PARTITION p20241224 VALUES [("2024-12-24 00:00:00"), ("2024-12-25 00:00:00")),
PARTITION p20241225 VALUES [("2024-12-25 00:00:00"), ("2024-12-26 00:00:00")),
PARTITION p20241226 VALUES [("2024-12-26 00:00:00"), ("2024-12-27 00:00:00")),
PARTITION p20241227 VALUES [("2024-12-27 00:00:00"), ("2024-12-28 00:00:00")),
PARTITION p20241228 VALUES [("2024-12-28 00:00:00"), ("2024-12-29 00:00:00")),
PARTITION p20241229 VALUES [("2024-12-29 00:00:00"), ("2024-12-30 00:00:00")),
PARTITION p20241230 VALUES [("2024-12-30 00:00:00"), ("2024-12-31 00:00:00")),
PARTITION p20241231 VALUES [("2024-12-31 00:00:00"), ("2025-01-01 00:00:00")),
PARTITION p20250101 VALUES [("2025-01-01 00:00:00"), ("2025-01-02 00:00:00")))
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"dynamic_partition.enable" = "true",
"dynamic_partition.time_unit" = "DAY",
"dynamic_partition.time_zone" = "Asia/Shanghai",
"dynamic_partition.start" = "-180",
"dynamic_partition.end" = "30",
"dynamic_partition.prefix" = "p",
"dynamic_partition.buckets" = "10",
"dynamic_partition.history_partition_num" = "0",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into sqlserver_biz_sbh_bak select * from sqlserver_biz_sbh;
alter table sqlserver_biz_sbh rename sqlserver_biz_sbh_bak_20241202;
alter table sqlserver_biz_sbh_bak rename sqlserver_biz_sbh;

UPDATE `dcp-db`.etl_engine t SET t.sinkExtend = 'UNIQUE KEY(source_db_id,database_name,sqlserver_biz_sbh_backup_start_date,eid)
        PARTITION BY RANGE(sqlserver_biz_sbh_backup_start_date)(
        START ("2023-02-10") END ("2023-06-10") EVERY (INTERVAL 1 DAY)
        )
        DISTRIBUTED BY HASH(source_db_id) BUCKETS 10
        PROPERTIES(
            "replication_num" = "1",
            "dynamic_partition.enable" = "true",
            "dynamic_partition.time_unit" = "DAY",
            "dynamic_partition.start" = "-180",
            "dynamic_partition.end" = "30",
            "dynamic_partition.prefix" = "p",
            "dynamic_partition.buckets" = "10"
        );' WHERE t.id = 2235;


CREATE TABLE `DMPSQLServerBackupEvent` (
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `database_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_backup_start_date` datetime NOT NULL COMMENT "",
  `eid` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `collectConfigId` varchar(65533) NULL COMMENT "",
  `uploadDataModelCode` varchar(65533) NULL COMMENT "",
  `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_finish_date` datetime NULL COMMENT "",
  `sqlserver_biz_sbh_type` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_recovery_model` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_size` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP 
PRIMARY KEY(`source_db_id`, `database_name`, `sqlserver_biz_sbh_name`, `sqlserver_biz_sbh_backup_start_date`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DMPSQLServerBackupEvent_bak` (
  `source_db_id` varchar(65533) NOT NULL COMMENT "",
  `database_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_name` varchar(65533) NOT NULL COMMENT "",
  `sqlserver_biz_sbh_backup_start_date` datetime NOT NULL COMMENT "",
  `eid` varchar(65533) NULL COMMENT "",
  `collectedTime` datetime NULL COMMENT "",
  `collectConfigId` varchar(65533) NULL COMMENT "",
  `uploadDataModelCode` varchar(65533) NULL COMMENT "",
  `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_finish_date` datetime NULL COMMENT "",
  `sqlserver_biz_sbh_type` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_recovery_model` varchar(65533) NULL COMMENT "",
  `sqlserver_biz_sbh_backup_size` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP 
UNIQUE KEY(`source_db_id`, `database_name`, `sqlserver_biz_sbh_name`, `sqlserver_biz_sbh_backup_start_date`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`source_db_id`) BUCKETS 10 
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);
insert into DMPSQLServerBackupEvent_bak select * from DMPSQLServerBackupEvent;
alter table DMPSQLServerBackupEvent rename DMPSQLServerBackupEvent_bak_20241202;
alter table DMPSQLServerBackupEvent_bak rename DMPSQLServerBackupEvent;

UPDATE `dcp-db`.etl_engine t SET t.sinkExtend = 'UNIQUE KEY(source_db_id,database_name,sqlserver_biz_sbh_name,sqlserver_biz_sbh_backup_start_date)
        DISTRIBUTED BY HASH(source_db_id) BUCKETS 10
        PROPERTIES(
            "replication_num" = "1"
        );' WHERE t.id = 1689;