function IpAndPort{
    return "{{http_service_address}}"
#        return "https://************:4435"
}


function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function QueryWarn {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $myArray = New-Object System.Collections.ArrayList
    $currDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd')
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20190725/alarms?page_num=$next_page_num&page_size=1000&start_time=$currDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
            $myArray.AddRange($jsonObject.data.data)
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}


$aa=QueryWarn
$result = Write-Output $aa | ConvertTo-Json
$jsonBytes = [System.Text.Encoding]::UTF8.GetBytes($result)
$jsonUTF8 = [System.Text.Encoding]::UTF8.GetString($jsonBytes)
Write-Output $jsonUTF8

function IpAndPort{
    return "{{http_service_address}}"
    #    return "https://************:4435"
}

function UserNameGet{
    #        return "admin"
    return "{{http_basic_login_id}}"
}

function PassGet{
    #        return "#1TXUo6aK6"
    return "{{http_basic_login_pwd}}"
}

function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function QueryWarn {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $myArray = New-Object System.Collections.ArrayList
    $currDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd')
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/alarms?page_num=$next_page_num&page_size=1000&begin_time=$currDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
            $myArray.AddRange($jsonObject.data.data)
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}




$aa=QueryWarn
$result = Write-Output $aa | ConvertTo-Json
$jsonBytes = [System.Text.Encoding]::UTF8.GetBytes($result)
$jsonUTF8 = [System.Text.Encoding]::UTF8.GetString($jsonBytes)
Write-Output $jsonUTF8
