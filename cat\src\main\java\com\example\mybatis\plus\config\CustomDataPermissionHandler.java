package com.example.mybatis.plus.config;

import com.baomidou.mybatisplus.extension.plugins.handler.MultiDataPermissionHandler;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;

public class CustomDataPermissionHandler implements MultiDataPermissionHandler {

    @Override
    public net.sf.jsqlparser.expression.Expression getSqlSegment(Table table, net.sf.jsqlparser.expression.Expression where, String mappedStatementId) {
        // 在此处编写自定义数据权限逻辑
        System.out.println("处理权限");
        try {
            String sqlSegment = "  "; // 数据权限相关的 SQL 片段
            return CCJSqlParserUtil.parseCondExpression(sqlSegment);
        } catch (JSQLParserException e) {
            e.printStackTrace();
            return null;
        }
    }
}