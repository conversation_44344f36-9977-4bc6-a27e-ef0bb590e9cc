package com.example.aio.asia;

import cn.hutool.core.date.DateUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.Objects;

public class AppAuthUtil {

    public static String buildAuthorization(String accessId, String appSecret, String url, long expireTime) {
        // Access=[Access Id], ExpireTime=[Expire Time], Signature=[signature]
        String sign = sign(appSecret, url, expireTime);
        return "Access=" + accessId + ", ExpireTime=" + expireTime + ", Signature=" + sign;
    }

    public static String buildAuthorization(String accessId, String appSecret, String url, long expireTime, String body) {
        // Access=[Access Id], ExpireTime=[Expire Time], Signature=[signature]
        String sign = sign(appSecret, url, expireTime, body);
        return "Access=" + accessId + ", ExpireTime=" + expireTime + ", Signature=" + sign;
    }

    public static String sign(String appSecret, String url, long expireTime) {
        return sign(appSecret, url, expireTime, null);
    }

    public static String sign(String appSecret, String url, long expireTime, String body) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isBlank(body)) {
            // signature = HexEncode(sha1([Secret Key]+[Expire Time]+[URL]))
            sb.append(appSecret).append(expireTime).append(url);
        } else {
            // signature = HexEncode(sha1([Secret Key]+[Expire Time]+[URL]+[POST BODY]))
            sb.append(appSecret).append(expireTime).append(url).append(body);
        }

        return DigestUtils.sha1Hex(sb.toString());
    }

    private static long getExpireTime() {
        // [Expire Time] 是以毫秒为单位的时间戳字符串，当前系统时间加10分钟。
        return System.currentTimeMillis() + 10 * 60 * 1000;
    }

    //测试区
    public static void main(String[] args) {
        long expireTime = getExpireTime();
//        System.out.println(buildAuthorization("ak-a00ff28a7f84426a8b9d712a322c4da6", "sk-bf1a59d910f14f268f6627f6e9f9ff28", "/umc/api/v1/tenant/get_agent_download_url/1be3cbc16df94d1c9b50bb2c937da674", expireTime, null));
//        System.out.println(buildAuthorization("ak-d13a4bba016511efb18aacde48001122", "sk-f7300fe4016511ef8326acde48001122", "/umc/api/v1/tenant/alias/ninbojinshan.dingjie", expireTime, null));
        Date date = new Date(1718294399 * 1000L);
        System.out.println(DateUtil.format(date, "yyyy-MM-dd HH:mm:ss.SSS"));
        pro(expireTime);
    }

    static String ak = "ak-87de0c260e334e57b02a87b59ab60a11";
    static String sk = "sk-40d9d4c25e944d0f8a76f51dc649ad12";
    static RestTemplate restTemplate = new RestTemplate();

    public static void per(long expireTime) {
        String asiaInfoUrl = "https://open-staging.io.asiainfo-sec.com";
        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/get_agent_download_url/58cd3d292cd14eff8dfb0452387a2671";
        HttpEntity request = buildHeader(apiUrl, true, ak, sk, null, asiaInfoUrl, expireTime);

        try {

            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
            System.out.println(exchange);
        } catch (Exception e) {
            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
        }
    }

    static String testurl = "https://open-test.io.asiainfo-sec.com";
    static String testaccessId = "ak-a00ff28a7f84426a8b9d712a322c4da6";
    static String testappSecret = "sk-bf1a59d910f14f268f6627f6e9f9ff28";

    public static void test(long expireTime) {
        String apiUrl = testurl + "/umc/api/v1/tenant/alias/ASIA_99990000.dingjieapi2";
        HttpEntity request = buildHeader(apiUrl, true, testaccessId, testappSecret, null, testurl, expireTime);
        try {
            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
            System.out.println(exchange);
        } catch (Exception e) {
            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
        }
    }

    public static void testDevice(long expireTime) {
        String apiUrl = testurl + "/client/api/v1/devices?page_num=1&page_size=500&tenant_uuid=2f82ad43db534a189aed230fedb69c97";
        HttpEntity request = buildHeader(apiUrl, true, testaccessId, testappSecret, null, testurl, expireTime);
        try {
            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
            System.out.println(exchange);
        } catch (Exception e) {
            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
        }
    }

    public static void testDeviceVuln(long expireTime) {
        String url = "/hrm/api/v2/vuln/host_vuln_result/C1FE4F5CBD86C1FE6E95"  + "?page_num=" + 1 + "&page_size=" + 1000;
        String apiUrl = testurl + url;
        HttpEntity request = buildHeader(apiUrl, true, testaccessId, testappSecret, null, testurl, expireTime);
        try {
            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
            System.out.println(exchange);
        } catch (Exception e) {
            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
        }
    }

    static String proUrl = "https://open.io.asiainfo-sec.com";
    static String proAk = "ak-d13a4bba016511efb18aacde48001122";
    static String proSk = "sk-f7300fe4016511ef8326acde48001122";

    /**
     * (tenant_name=鼎捷41323477541440, alias=ASIA_41323477541440.dingjie, email=<EMAIL>, description=null, license=AsiaInfoTenant.License(desktop=AsiaInfoTenant.LicenseDetails(vulnerability=false, total=6, start_time=**********, expiration_time=**********), server=AsiaInfoTenant.LicenseDetails(vulnerability=false, total=5, start_time=**********, expiration_time=**********)))
     *
     * @param expireTime
     */
//    public static  void  proSave(long expireTime){
//        String apiUrl = proUrl + "/umc/api/v1/tenant/5a119d10e3dc46479a4ff0fe0d743074";
//        HttpEntity request = buildHeader(apiUrl, false, proAk, proSk, null,proUrl,expireTime);
//        AsiaInfoTenant asiaInfoTenant = new AsiaInfoTenant();
//        AsiaInfoTenant
//        try {
//
//            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.PUT, request, String.class);
//            System.out.println(exchange);
//        } catch (Exception e) {
//            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
//        }
//    }
    public static void pro(long expireTime) {
        String apiUrl = proUrl + "/umc/api/v1/tenant/e5251864783a4728b2ccebcfb41d5263";
        HttpEntity request = buildHeader(apiUrl, false, proAk, proSk, null, proUrl, expireTime);

        try {

            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
            System.out.println(exchange);
        } catch (Exception e) {
            throw new RuntimeException("[AsiaDeviceProcessor] 请求发生异常", e);
        }
    }

//    public static void aa(String[] args) {
//        String asiaInfoUrl = "https://open-test.io.asiainfo-sec.com";
//        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/get_agent_download_url/" + uuid;
//        HttpEntity request = buildHeader(apiUrl, false, ak, sk,null,asiaInfoUrl);
//        RestTemplate restTemplate = new RestTemplate();
//        ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.GET, request, String.class);
//        exchange.getBody().get
//        if (AsiaInfoResponse.isSuccess(asiaInfoResponse) && AsiaInfoResponse.dataExists(asiaInfoResponse.getBody())) {
//            return asiaInfoResponse.getBody().getData().get("windows_download_url").toString();
//        }
//    }

    public static HttpEntity<String> buildHeader(String url, boolean flag, String asiaInfoAccessId, String asiaInfoAppSecret, String body, String asiaInfoUrl, long expireTime) {
        String replaceUrl = url.replace(asiaInfoUrl, "");
        String authorization;
        if (Objects.nonNull(body)) {
            authorization = buildAuthorization(asiaInfoAccessId, asiaInfoAppSecret, replaceUrl, expireTime, body);
        } else {
            authorization = buildAuthorization(asiaInfoAccessId, asiaInfoAppSecret, replaceUrl, expireTime);
        }
        HttpHeaders headers = new HttpHeaders();
        System.out.println(authorization);
        headers.add("Authorization", authorization);
        if (flag) {
            headers.add("Content-Type", "application/json; charset=utf-8");
        }
        return new HttpEntity<String>(body, headers);
    }

//    public static void main(String[] args) {
//        long expireTime = getExpireTime();
//    }

}
