package com.example.aio.model;

import java.util.List;

public class ResultModel {
    private User user;
    private List<Mapping> mappingInfo;
    private List<Role> roleInfo;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }


    public List<Mapping> getMappingInfo() {
        return mappingInfo;
    }

    public void setMappingInfo(List<Mapping> mappingInfo) {
        this.mappingInfo = mappingInfo;
    }

    public List<Role> getRoleInfo() {
        return roleInfo;
    }

    public void setRoleInfo(List<Role> roleInfo) {
        this.roleInfo = roleInfo;
    }
}
