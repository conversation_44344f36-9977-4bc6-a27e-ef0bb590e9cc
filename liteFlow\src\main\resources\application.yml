spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************
    username: root
    password: 123456
liteflow:
  rule-source-ext-data-map:
    applicationName: demo
    #???chain???????????
    chainTableName: liteflow_chain
    chainApplicationNameField: application_name
    chainNameField: chain_name
    elDataField: el_data
    routeField: route
    namespaceField: namespace
    chainEnableField: enable
    #???script??????????????????????
    scriptTableName: liteflow_script
    scriptApplicationNameField: application_name
    scriptIdField: script_id
    scriptNameField: script_name
    scriptDataField: script_data
    scriptTypeField: script_type
    scriptLanguageField: language
    scriptEnableField: enable
    #??????????
    pollingEnabled: true
    pollingStartSeconds: 0
    pollingIntervalSeconds: 5
  parse-mode: parse_one_on_first_exec