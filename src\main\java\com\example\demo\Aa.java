package com.example.demo;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class Aa {
   static String json = " [\n" +
            "        {\n" +
            "            \"executor_type\": \"operator\",\n" +
            "            \"node_name\": \"node.xxxx\",\n" +
            "            \"operator_type\": \"loop,join,additional,quit\",\n" +
            "            \"parent_name\": \"\",\n" +
            "            \"executors\": [{\n" +
            "                    \"executorType\": \"operator\",\n" +
            "                    \"node_name\": \"node.yyyy\",\n" +
            "                    \"parent_name\": \"node.xxxx\",\n" +
            "                    \"operator_type\": \"foreach\",\n" +
            "                    \"executors\": [{}\n" +
            "                    ]\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        {\n" +
            "            \"executor_type\": \"sender\",\n" +
            "            \"node_name\": \"node.xxxx\",\n" +
            "            \"runner_name\": \"MsSQLInstanceInfo.369989277565504\",\n" +
            "            \"sender_type\": \"http_v2\",\n" +
            "            \"http_sender_url\": \"{{digiwin_upload_url}}\",\n" +
            "            \"http_sender_protocol\": \"body_json\",\n" +
            "            \"http_sender_escape_html\": \"true\",\n" +
            "            \"http_sender_csv_split\": \",\",\n" +
            "            \"http_sender_gzip\": \"false\",\n" +
            "            \"http_sender_other_headers\": \"{\\\"eid\\\":\\\"{{eid}}\\\",\\\"token\\\":\\\"{{token}}\\\"}\",\n" +
            "            \"http_sender_collect_config_id\": \"369989277565504\",\n" +
            "            \"http_sender_upload_data_model_code\": \"MsSQLInstanceInfo\",\n" +
            "            \"http_sender_package_template\": \"{\\\"deviceId\\\":\\\"{{device_id}}\\\",\\\"eid\\\":\\\"{{eid}}\\\",\\\"collectedTime\\\":\\\"{{collected_time}}\\\",\\\"collectConfigId\\\":\\\"{{__collectConfigId__}}\\\",\\\"deviceCollectDetailId\\\":\\\"{{__deviceCollectDetailId__}}\\\",\\\"uploadDataModelCode\\\":\\\"{{__uploadDataModelCode__}}\\\",\\\"dataContent\\\":{{__selfData__}}}\",\n" +
            "            \"http_sender_send_data_to_string\": \"true\",\n" +
            "            \"http_sender_timeout\": \"30s\",\n" +
            "            \"ft_strategy\": \"backup_only\",\n" +
            "            \"ft_discard_failed_data\": \"false\",\n" +
            "            \"ft_retry_count_before_discard\": \"6\",\n" +
            "            \"ft_memory_channel\": \"false\",\n" +
            "            \"ft_long_data_discard\": \"false\",\n" +
            "            \"max_disk_used_bytes\": \"524288000\",\n" +
            "            \"max_size_per_file\": \"104857600\",\n" +
            "            \"http_sender_quote_package_data\": \"true\",\n" +
            "            \"http_sender_parent_package_template\": \"[{\\\"headers\\\": {\\\"namenode\\\":\\\"namenode.example.com\\\",\\\"datanode\\\":\\\"random_datanode.example.com\\\",\\\"SourceType\\\":\\\"JSON\\\"},\\\"body\\\":{{__packageData__}}}]\",\n" +
            "            \"http_sender_encrypt_self_data\": \"false\",\n" +
            "            \"http_sender_use_proxy\": \"true\",\n" +
            "            \"http_sender_data_batch_send_size\": \"300\",\n" +
            "            \"http_sender_fixed_sending_data\": \"{\\\"collected_time\\\":\\\"{{collected_time}}\\\"}\"\n" +
            "        }\n" +
            "    ]";
    public static List<Map<String, Object>> flattenContentList(List<Map<String, Object>> contentList) {
        List<Map<String, Object>> flattenedList = new ArrayList<>();
        if (contentList != null) {
            for (Map<String, Object> content : contentList) {
                flattenedList.add(content);
                Object parentName = content.get("executors");
                if (parentName != null && !((List<Map<String, Object>>)parentName).isEmpty()&&!((List<Map<String, Object>>)parentName).get(0).isEmpty()) {
                    List<Map<String, Object>> child = (List<Map<String, Object>>) parentName;
                    flattenedList.addAll(flattenContentList(child));
                }
            }
        }
        return flattenedList;
    }

    public static List<Map<String, Object>> flattenContentListRecursive(Map<String, Object> content) {
        List<Map<String, Object>> flattenedList = new ArrayList<>();
        flattenedList.add(content);
        List<Map<String, Object>> child = (List<Map<String, Object>>) content.get("executors");
        if (child != null && !child.isEmpty() && !child.get(0).isEmpty()) {
            for (Map<String, Object> childContent : child) {
                flattenedList.addAll(flattenContentListRecursive(childContent));
            }
        }
        return flattenedList;
    }

    public static void main(String[] args) {
        List<Map<String, Object>> mapList = JSON.parseObject(json, List.class);
        for (Map<String, Object> map:mapList) {
            List<Map<String, Object>> maps = flattenContentListRecursive(map);
            System.out.println(maps);
        }
        System.out.println();


    }
}
