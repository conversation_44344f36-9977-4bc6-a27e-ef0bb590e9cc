-- cfo
SELECT
    IF(count(distinct ecv2.eid) = 0,
       '分母不能为0', -- 防止除以0的情况
       SUM(CASE WHEN sqCfoCompletedCount = sqCfoTotalQuantity THEN 1 ELSE 0 END)  /
       (count(distinct ecv2.eid))
    ) AS ratio
FROM servicecloud.es_customerservice_v2  ecv2
         LEFT JOIN  servicecloud.IndustryPlanQuickScreening ipqs on ecv2.eid = ipqs.eid and ecv2.SalesContact = ipqs.SalesContact
WHERE ipqs.eid NOT LIKE '8888%'
  AND ipqs.eid NOT LIKE '9999%'
LIMIT 8000;
-- cso
SELECT
    IF(count(distinct ecv2.eid) = 0,
       '分母不能为0', -- 防止除以0的情况
       SUM(CASE WHEN sqCsoCompletedCount = sqCsoTotalQuantity THEN 1 ELSE 0 END)  /
       (count(distinct ecv2.eid))
    ) AS ratio
FROM servicecloud.es_customerservice_v2  ecv2
         LEFT JOIN  servicecloud.IndustryPlanQuickScreening ipqs on ecv2.eid = ipqs.eid and ecv2.SalesContact = ipqs.SalesContact
WHERE ipqs.eid NOT LIKE '8888%'
  AND ipqs.eid NOT LIKE '9999%'
LIMIT 8000;

-- cho
SELECT
    IF(count(distinct ecv2.eid) = 0,
       '分母不能为0', -- 防止除以0的情况
       SUM(CASE WHEN sqChoCompletedCount = sqChoTotalQuantity THEN 1 ELSE 0 END)  /
       (count(distinct ecv2.eid))
    ) AS ratio
FROM servicecloud.es_customerservice_v2  ecv2
         LEFT JOIN  servicecloud.IndustryPlanQuickScreening ipqs on ecv2.eid = ipqs.eid and ecv2.SalesContact = ipqs.SalesContact
WHERE ipqs.eid NOT LIKE '8888%'
  AND ipqs.eid NOT LIKE '9999%'
LIMIT 8000;


-- CFO 各事业部完成情况
select a.grp_dpt_name,
       a.bus_dpt_name,
       count(distinct a.eid)                                      as cus_count,
       sum(case
               when (b.sqCfoCompletedCount is null or b.sqCfoCompletedCount < b.sqCfoTotalQuantity) then 0
               else 1 end)                                        as cus_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact

where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name
ORDER BY  cus_compeleted_count/cus_count
limit 8000;


-- CFO 业务完成清单
select a.grp_dpt_name,
       a.bus_dpt_name,
       a.dpt_name,
       a.Sales,
       count(distinct a.eid)                                      as cus_count,
       count(distinct tts.id)                                      as cfo_high_potential_count,
       sum(case
               when (b.sqCfoCompletedCount is null or b.sqCfoCompletedCount < b.sqCfoTotalQuantity) then 0
               else 1 end)                                        as cus_cfo_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts on tts.id = b.eid and  tts.tagId = '731424753066560'
where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
order by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
limit 8000;



-- CHO 各事业部完成情况
select a.grp_dpt_name,
       a.bus_dpt_name,
       count(distinct a.eid)                                      as cus_count,
       sum(case
               when (b.sqChoCompletedCount is null or b.sqChoCompletedCount < b.sqChoTotalQuantity) then 0
               else 1 end)                                        as cus_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name
ORDER BY  cus_compeleted_count/cus_count
limit 8000;


-- CHO 业务完成清单
select a.grp_dpt_name,
       a.bus_dpt_name,
       a.dpt_name,
       a.Sales,
       count(distinct a.eid)                                      as cus_count,
       count(distinct tts.id)                                      as cho_high_potential_count,
       sum(case
               when (b.sqChoCompletedCount is null or b.sqChoCompletedCount < b.sqChoTotalQuantity) then 0
               else 1 end)                                        as cus_cho_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts on tts.id = b.eid and  tts.tagId = '729520260567616'

where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
order by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
limit 8000;



-- CSO 各事业部完成情况
select a.grp_dpt_name,
       a.bus_dpt_name,
       count(distinct a.eid)                                      as cus_count,
       sum(case
               when (b.sqCsoCompletedCount is null or b.sqCsoCompletedCount < b.sqCsoTotalQuantity) then 0
               else 1 end)                                        as cus_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name
ORDER BY  cus_compeleted_count/cus_count
limit 8000;


-- CSO 业务完成清单

select a.grp_dpt_name,
       a.bus_dpt_name,
       a.dpt_name,
       a.Sales,
       count(distinct a.eid)                                      as cus_count,
       count(distinct tts_1.id)  + count(distinct tts_2.id)                                    as cso_high_potential_count,

       sum(case
               when (b.sqCsoCompletedCount is null or b.sqCsoCompletedCount < b.sqCsoTotalQuantity) then 0
               else 1 end)                                        as cus_cso_compeleted_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts_1 on tts_1.id = b.eid and  tts_1.tagId = '729636528575040'
         left join AIEOM.tenant_tag_string tts_2 on tts_2.id = b.eid and  tts_2.tagId = '729646154027584'
where grp_dpt_name is not null
group by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
order by a.grp_dpt_name, a.bus_dpt_name, a.dpt_name, a.Sales
limit 8000;




-- ---- -- 三合一
-- cfo cso cho 都完成 完成比率

SELECT
    IF(count(distinct ecv2.eid) = 0,
       '分母不能为0', -- 防止除以0的情况
       SUM(CASE WHEN sqCfoCompletedCount = sqCfoTotalQuantity AND sqChoCompletedCount = sqChoTotalQuantity AND sqCsoCompletedCount = sqCsoTotalQuantity THEN 1 ELSE 0 END)  /
       (count(distinct ecv2.eid))
    ) AS ratio
FROM servicecloud.es_customerservice_v2  ecv2
         LEFT JOIN  servicecloud.IndustryPlanQuickScreening ipqs on ecv2.eid = ipqs.eid and ecv2.SalesContact = ipqs.SalesContact
WHERE ipqs.eid NOT LIKE '8888%'
  AND ipqs.eid NOT LIKE '9999%'
LIMIT 8000;

-- cso高潜
select
    count(distinct tts_1.id) as cso_quotation_high_potential_count,
    count(distinct tts_2.id) as  cso_goods_high_potential_count

from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts_1 on tts_1.id = b.eid and  tts_1.tagId = '729636528575040'
         left join AIEOM.tenant_tag_string tts_2 on tts_2.id = b.eid and  tts_2.tagId = '729646154027584'
limit 8000;

-- cho 高潜
select
    count(distinct tts.id)                                      as cho_high_potential_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts on tts.id = b.eid and  tts.tagId = '729520260567616'
limit 8000;


-- cfo 高潜
select
    count(distinct tts.id)                                      as cfo_high_potential_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
         left join AIEOM.tenant_tag_string tts on tts.id = b.eid and  tts.tagId = '731424753066560'
limit 8000;

-- 汇总清单

select
    a.grp_dpt_name,
    a.grp_dpt_manager,
    count(distinct a.eid) as cus_count,
    sum(case when (b.sqChoCompletedCount = b.sqChoTotalQuantity) then 1 else 0 end) as cho_count,
    sum(case when tts.cho_screening_count > 0 then 1 else 0 end) as cho_screening_count,
    sum(case when (b.sqCsoCompletedCount = b.sqCsoTotalQuantity) then 1 else 0 end) as cso_count,
    sum(case when tts.cso_screening_count > 0 then 1 else 0 end) as cso_screening_count,
    sum(case when (b.sqCfoCompletedCount = b.sqCfoTotalQuantity) then 1 else 0 end) as cfo_count,
    sum(case when tts.cfo_screening_count > 0 then 1 else 0 end) as cfo_screening_count
from
    servicecloud.es_customerservice_v2 a
        left join
    servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
        left join
    (select
         id,
         sum(case when (tagId = '729520260567616') then 1 else 0 end) as cho_screening_count,
         sum(case when (tagId in ('729636528575040','729646154027584')) then 1 else 0 end) as cso_screening_count,
         sum(case when (tagId = '731424753066560') then 1 else 0 end) as cfo_screening_count
     from
         AIEOM.tenant_tag_string
     group by
         id
    ) tts on tts.id = b.eid
where
    a.grp_dpt_name is not null
group by
    a.grp_dpt_name, a.grp_dpt_manager
order by
    a.grp_dpt_name, a.grp_dpt_manager
limit 8000;

