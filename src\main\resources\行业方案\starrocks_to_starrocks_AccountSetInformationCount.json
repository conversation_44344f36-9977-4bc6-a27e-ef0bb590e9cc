{"job": {"content": [{"writer": {"name": "mysqlwriter", "parameter": {"writeMode": "insert", "username": "servicecloud", "password": "servicecloud@123", "column": ["eid", "Product_Line", "aiopsItem", "IndicatorNumber", "Account_Set_Count", "collectedTime"], "connection": [{"jdbcUrl": "*************************************************************************************************", "table": ["AccountSetInformationCount"]}]}}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["select eid, Product_Line, aiopsItem, IndicatorNumber,count(Account_Set) as Account_Set_Count, max(collectedTime) as collectedTime from servicecloud.AccountSetInformation group by eid, Product_Line, aiopsItem, IndicatorNumber "], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}