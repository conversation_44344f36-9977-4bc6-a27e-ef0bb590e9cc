package com.example.demo;

import cn.hutool.core.date.ChineseDate;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.map.BiMap;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.system.RuntimeInfo;
import cn.hutool.system.SystemUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.example.SnowFlake;
import com.example.demo.model.*;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@RestController
public class ModelController {

    @Value("${datasource.password11}")
    private String pass;
    static String aa = "task_id              \n" +
            "collectedTime        \n" +
            "deviceId             \n" +
            "eid                  \n" +
            "collectConfigId      \n" +
            "uploadDataModelCode  \n" +
            "deviceCollectDetailId\n" +
            "aiId                 \n" +
            "aiopsItem            \n" +
            "flumeTimestamp       \n" +
            "status               \n" +
            "user_id              \n" +
            "description          \n" +
            "object_type          \n" +
            "object_id            \n" +
            "user_ip              \n" +
            "creator_id           \n" +
            "end_time             \n" +
            "begin_time           \n" +
            "action               \n" +
            "progress             \n" +
            "project_id           \n" +
            "az_id                ";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @GetMapping("hello20")
    public void hello20() {
        try (BufferedReader reader = new BufferedReader(new FileReader("D:\\work\\demo\\src\\main\\resources\\model\\vv.json"))) {
            String line;

            while ((line = reader.readLine()) != null) {
                parseJson(line, aa);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @GetMapping("hello21")
    public void hello21() {
        PriorityQueue<Menu> menuListQ = new PriorityQueue<>(Comparator.comparingInt(m -> Integer.parseInt(m.getId())));
        EasyExcel.read("D:\\doc\\Tencent Files\\562098412\\FileRecv\\客服端-台湾菜单(3).xlsx", Menu.class, new PageReadListener<>(menuListQ::addAll)).sheet().doRead();
        //todo 插入菜单表
        String maxId = "6";
        List<Menu> menuList = menuListQ.stream().filter(m -> !m.getId().equals("0")).collect(Collectors.toList());
        Integer maxOrder = 0;
        int i = 0;
        for (Menu menu : menuList) {
            String id = "";
            if (!menu.getId().equals("0")) {
                id = menu.getId();
                maxId = String.valueOf(Math.max(Integer.parseInt(id), Integer.parseInt(maxId)));

            } else {
                maxId = String.valueOf(Integer.parseInt(maxId) + 1);
                id = maxId;
            }
//            if (Objects.nonNull(menu.getMenuOrder())) {
//                maxOrder = Math.max(maxOrder, menu.getMenuOrder());
//            }else {
//                maxOrder++;
//            }
//            menu.setMenuOrder(maxOrder);
            menu.setId(id);
            String format = String.format("insert into data_examination_menu (id,menuName,menuName_CN,menuName_TW,menuCode,parentId,menuStatus,menuType,menuSource,imgUrl,menuOrder,isPage,eid,appCode,sid) values ('%s','%s','%s','%s','%s','%s',%d,'%s','%s','%s',%d,%s,%d,'%s',%d);",
                    menu.getId(), menu.getMenuName(), menu.getMenuName_CN(), menu.getMenuName_TW(), menu.getMenuCode(), menu.getParentId(), 2, menu.getMenuType(), menu.getMenuSource(), menu.getImgUrl(), menu.getMenuOrder(), "是".equals(menu.getIsPage()), 99990000, "AIEOM", 241199971893824L);
            System.out.println(format.replace("'null'", "null"));
            i++;


        }


    }

    @GetMapping("hello22")
    public void hello22() {
        String filePath = "D:\\dow\\aa.xlsx";

// 获取ExcelWriter对象
        ExcelWriter excelWriter = EasyExcel.write(filePath).build();

// 写入第一个sheet
        WriteSheet writeSheet1 = EasyExcel.writerSheet("data_examination_component").head(DataExaminationComponent.class).build();
        excelWriter.write(new ArrayList<>(), writeSheet1);

// 写入第二个sheet
        WriteSheet writeSheet2 = EasyExcel.writerSheet("data_examination_component_base_params").head(DataExaminationComponentBaseParams.class).build();
        excelWriter.write(new ArrayList<>(), writeSheet2);

// 写入第三个sheet
        WriteSheet writeSheet3 = EasyExcel.writerSheet("data_examination_component_dynamics_params").head(DataExaminationComponentDynamicsParams.class).build();
        excelWriter.write(new ArrayList<>(), writeSheet3);

// 写入第四个sheet
        WriteSheet writeSheet4 = EasyExcel.writerSheet("data_examination_component_params").head(DataExaminationComponentParams.class).build();
        excelWriter.write(new ArrayList<>(), writeSheet4);

// 必须调用finish来关闭ExcelWriter，否则可能会导致文件损坏
        excelWriter.finish();


    }

    @Transactional
    @GetMapping("hello23")
    public void hello23() {
        String filePath = "D:\\doc\\Tencent Files\\562098412\\FileRecv\\aa.xlsx";
        List<String> cid = new ArrayList<>();
        List<String> pid = new ArrayList<>();

// 获取ExcelWriter对象
        // 读取Excel文件
        String fSql = "INSERT INTO data_examination_component (id, imgUrl, componentName, componentCode, componentType, aiopsItem, layerIndex, rows, cols, componentSource, appCode, eid, sid, createUser, createMail) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        EasyExcel.read(filePath, DataExaminationComponent.class, new PageReadListener<DataExaminationComponent>(dataList -> {
            for (DataExaminationComponent data : dataList) {
                String id = SnowFlake.getInstance().newIdStr();
                cid.add(id);
                data.setId(id);
                data.setAiopsItem("HOST");
                jdbcTemplate.update(fSql, data.getId(), data.getImgUrl(), data.getComponentName(), data.getComponentCode(), data.getComponentType(), data.getAiopsItem(), data.getLayerIndex(), data.getRows(), data.getCols(), data.getComponentSource(), data.getAppCode(), data.getEid(), data.getSid(), data.getCreateUser(), data.getCreateMail());
            }
        })).sheet("data_examination_component").doRead();

        for (String id : cid) {
            String Pid = SnowFlake.getInstance().newIdStr();
            pid.add(Pid);
            String pSql = "INSERT INTO data_examination_component_params (id, componentId, paramType) VALUES (?, ?, ?)";
            jdbcTemplate.update(pSql, Pid, id, "DYNAMICS");
        }
        String cdpSql = "INSERT INTO data_examination_component_dynamics_params (id, componentParamId, fieldCode, fieldName, fieldType, fieldDefault, bindFieldSource, bindFieldCode, bindFieldName, parentEscServiceColumnCode, isMultiple, isShow) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);";
        EasyExcel.read(filePath, DataExaminationComponentDynamicsParams.class, new PageReadListener<DataExaminationComponentDynamicsParams>(dataList -> {
            for (String Pid : pid) {
                for (DataExaminationComponentDynamicsParams data : dataList) {

                    data.setId(SnowFlake.getInstance().newIdStr());
                    jdbcTemplate.update(cdpSql, data.getId(), Pid, data.getFieldCode(), data.getFieldName(), data.getFieldType(), data.getFieldDefault(), data.getBindFieldSource(), data.getBindFieldCode(), data.getBindFieldName(), data.getParentEscServiceColumnCode(), data.getIsMultiple(), data.getIsShow());
                }
            }

        })).sheet("data_examination_component_dyna").doRead();


    }

    @GetMapping("hello24")
    public void hello24() {
        // 假设这是你的CSV数据，其中包含了JSON格式的数据
        List<String> csvDataList = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader("D:\\work\\demo\\src\\main\\resources\\model\\DocumentReplenishmentRate.txt"))) {
            String line;

            while ((line = reader.readLine()) != null) {
                csvDataList.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        for (String csvData : csvDataList) {
            // 使用正则表达式来匹配JSON，并将其视为一个整体
            String[] split = csvData.split("N,");
            List<String> values = new ArrayList<>();
            String[] split1 = split[0].split(",");
            for (String s : split1) {
                if (s.equals("\\")) {
                    values.add("NULL");
                } else {
                    values.add(s);
                }

            }

            Collections.addAll(values, split[1]);


            // 构建INSERT语句
            String insertStatement = buildInsertStatement("default.documentreplenishmentrate", values.toArray(new String[0]));
            System.out.println(insertStatement);
        }

    }




    static String deviceId =
            "467381982158926915\n" +
                    "487227426695295284\n" +
                    "492428908411503671\n" +
                    "474914953770330990\n" +
                    "473868791575491651\n" +
                    "485509922759390275\n" +
                    "454593725591467075\n" +
                    "454744193730887993\n" +
                    "463892416408072756\n" +
                    "482031766639493939\n" +
                    "424762814867977525\n" +
                    "475318758408205379\n" +
                    "459950992717263681\n" +
                    "472299007838794819\n" +
                    "465746440828760112\n" +
                    "483591487687243063\n" +
                    "418359004536779843\n" +
                    "434765535784219715\n" +
                    "462736197655999555\n" +
                    "478076757379920963\n" +
                    "483181017746649667\n" +
                    "468952098352936249\n" +
                    "475910758362792502\n" +
                    "468935265990227011\n" +
                    "478508164531106883\n" +
                    "481986395980706883\n" +
                    "454595298489021766\n" +
                    "477493391232350273\n" +
                    "451573999500013878\n" +
                    "454040209600820291\n" +
                    "463728595903202098\n" +
                    "470257102556776006\n" +
                    "455023995482289219\n" +
                    "475897256394896451\n" +
                    "492613956372805445\n" +
                    "368492459031803459\n" +
                    "396051120528569411\n" +
                    "395896971585405238\n" +
                    "471614916772508739\n" +
                    "435914263559681091\n" +
                    "465206343490352195\n" +
                    "470280375675925571\n" +
                    "475213460859274041\n" +
                    "478940512515012147\n" +
                    "419234115380851510\n" +
                    "467784017387733811\n" +
                    "483043458534094146\n" +
                    "476090086383961155\n" +
                    "477090364788848196\n" +
                    "476482407252374595\n" +
                    "469384266820629571\n" +
                    "492460890935800644\n" +
                    "470256996256330309\n" +
                    "465175169325872195\n" +
                    "448658403665918262\n" +
                    "476051725799671344\n" +
                    "493295313038098998\n" +
                    "454038557716137030\n" +
                    "450132715547673410\n" +
                    "479263625790375217\n" +
                    "481996653503465011\n" +
                    "424442984356918339\n" +
                    "447512815628988980\n" +
                    "395896922780483895\n" +
                    "475903765786933062\n" +
                    "419514983492105283\n" +
                    "479097542894696518\n" +
                    "452573065046410550\n" +
                    "465772240495981381\n" +
                    "470130706903744825\n" +
                    "470135271145354307\n" +
                    "453602946043622467\n" +
                    "462873080545425732\n" +
                    "475496243485750339\n" +
                    "449991466656216131\n" +
                    "462712032660042819\n" +
                    "474025426985961030\n" +
                    "478967545592820803\n" +
                    "489128027167405123\n" +
                    "479982478325463362\n" +
                    "474205219148805187\n" +
                    "465178163001963332\n" +
                    "466618914378363971\n" +
                    "492598807100404803\n" +
                    "474924370184122416\n" +
                    "469996732613145667\n" +
                    "424582775039870513\n" +
                    "399253820497998644\n" +
                    "493588196018369603\n" +
                    "475176239162737988\n" +
                    "474308042041606967\n" +
                    "418649857692025656\n" +
                    "475905868861290292\n" +
                    "449113785358103604\n" +
                    "462880471244616771\n" +
                    "464054269784372535\n" +
                    "486076279745951811\n" +
                    "493147090696810805\n" +
                    "465900881779373123\n" +
                    "453865684980483654\n" +
                    "474914862334489143\n" +
                    "478114164666025027\n" +
                    "478366553201455671\n" +
                    "484462191643210305\n" +
                    "469256851062536248\n" +
                    "451152115247624240\n" +
                    "459744571673298481\n" +
                    "474199829283614787\n" +
                    "477245406665782339\n" +
                    "478406053361365040\n" +
                    "473296918290641987\n" +
                    "468964765922768962\n" +
                    "477356044352237616\n" +
                    "474190285128090689\n" +
                    "483468864642888771\n" +
                    "479384202618352707\n" +
                    "421279876587210054\n" +
                    "478545432734086211\n" +
                    "469841132390465843\n" +
                    "476515670666785078\n" +
                    "482279551641076787\n" +
                    "479122067023742264\n" +
                    "493752323361749315\n" +
                    "435753043204523060\n" +
                    "456797311595459398\n" +
                    "442035683163386947\n" +
                    "454038149828457780\n" +
                    "461871917461156405\n" +
                    "422156403763852850\n" +
                    "469406578521419830\n" +
                    "478537126183450417\n" +
                    "484332243867677763\n" +
                    "482137641827972163\n" +
                    "466049530714928195\n" +
                    "452026012897658165\n" +
                    "487215078647542851\n" +
                    "493608245764567858\n" +
                    "466628426338415683\n" +
                    "410982922074469475\n" +
                    "456797506076948291\n" +
                    "475484340436878136\n" +
                    "478094896201216322\n" +
                    "482590251622085953\n" +
                    "484461571758637377\n" +
                    "478507153720620099\n" +
                    "486668115569030211\n" +
                    "474336122068021559\n" +
                    "478263561328538416\n" +
                    "464908671436993603\n" +
                    "485494740821750851\n" +
                    "476361022651774771\n" +
                    "479125881910998083\n" +
                    "476334538339924272\n" +
                    "474203790635985774\n" +
                    "493756973049721923\n" +
                    "459226488709855544\n" +
                    "464883337320739907\n" +
                    "460698498002596931\n" +
                    "479416025658438962\n" +
                    "481872930997810243\n" +
                    "475923946949067318\n" +
                    "416189970017956931\n" +
                    "462165595966682179\n" +
                    "475494125613887536\n" +
                    "418679504072868260\n" +
                    "493324022864425780\n" +
                    "451000140296107075\n" +
                    "457060692361490481\n" +
                    "479128071572567876\n" +
                    "472441597078811715\n" +
                    "453746928681759032\n" +
                    "489665660704534833\n" +
                    "467807319330141493\n" +
                    "468935744124105795\n" +
                    "437348320055146032\n" +
                    "478252977992255044\n" +
                    "465890807128273217\n" +
                    "469963947903037765\n" +
                    "437515205186957620\n" +
                    "484184545478849603\n" +
                    "478392960522662980\n" +
                    "434756979722831171\n" +
                    "469386978069070902\n" +
                    "493479135809648944\n" +
                    "474492280149918257\n" +
                    "464348168524411971\n" +
                    "490103570218103875\n" +
                    "450098317423035460\n" +
                    "489122840121984048\n" +
                    "493725649802903876\n" +
                    "483290156908950595\n" +
                    "478103584903283779\n" +
                    "478533658517451843\n" +
                    "461859028180416838\n" +
                    "469839919515186243\n" +
                    "479990468977898563\n" +
                    "470140050672202549\n" +
                    "483608397443249219\n" +
                    "492719319369664048\n" +
                    "469377426649003075\n" +
                    "450975154877510470\n" +
                    "479234464237503800\n" +
                    "459226971793012023\n" +
                    "472298839496209968\n" +
                    "448648599765530936\n" +
                    "474327060660106307\n" +
                    "469992393135900213\n" +
                    "452743872422884921\n" +
                    "491696240141025857\n" +
                    "484165751960253507\n" +
                    "473879242958975554\n" +
                    "480290018233955889\n" +
                    "474345696825197113\n" +
                    "465763606437311028\n" +
                    "484190989406188611\n" +
                    "493464126559892547\n" +
                    "484347273703081017\n" +
                    "451728461237597251\n" +
                    "482172782780625972\n" +
                    "470243813659522115\n" +
                    "478374399016186947\n" +
                    "451146238776062000\n" +
                    "474335984863948850\n" +
                    "421131257833865777\n" +
                    "475066866981680196\n" +
                    "408968408516801603\n" +
                    "475507198991021123\n" +
                    "474013838392439619\n" +
                    "469256648125329730\n" +
                    "471389759890010932\n" +
                    "478101404704060726\n" +
                    "474205344491385923\n" +
                    "465342868739868230\n" +
                    "461120118315824449\n" +
                    "469409124044519234\n" +
                    "463151602128138288\n" +
                    "463025207011980355\n" +
                    "469237203499758659";

    @GetMapping("hello26")
    public void hello26() throws InterruptedException {
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求的URL

        String[] split = deviceId.split("\n");
        for (int i = 0; i < split.length; i++) {
            String s = split[i];
            String url = "http://aiops.digiwincloud.com.cn/aiogateway/aioitms/collect/config/device/{{deviceId}}/clear";
            url = url.replace("{{deviceId}}", s);
            HttpHeaders headers = new HttpHeaders();
            // 设置请求头，例如：
            headers.add("Content-Type", "application/json");
            headers.add("token", "f4efe601-d9fb-4d69-acce-6a5c518a8530");

            // 创建HttpEntity对象，传入请求头，因为是DELETE请求，所以这里body为null
            HttpEntity<String> entity = new HttpEntity<>(null, headers);

            // 发送DELETE请求
            ResponseEntity<BaseResponse> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, BaseResponse.class);
            // 打印响应体
            if (!Objects.equals(response.getBody().getData(), 1)) {
                System.out.println(s);
            } else {
                System.out.println(i + ":" + url);
                TimeUnit.MINUTES.sleep(1);
            }
        }


    }

    private String buildInsertStatement(String tableName, String[] values) {
        // 使用StringBuilder来构建值列表
        StringBuilder valueBuilder = new StringBuilder("INSERT INTO " + tableName + " VALUES (");

        for (int i = 0; i < values.length; i++) {
            // 对于空值\\N，将其转换为SQL的NULL
            if ("\\N".equals(values[i])) {
                valueBuilder.append("NULL");
            } else {
                // 对于Hive来说，需要将单引号转义
                String escapedValue = values[i].replace("'", "\\'");
                // 为所有值添加单引号
                valueBuilder.append("'").append(escapedValue).append("'");
            }

            if (i < values.length - 1) {
                valueBuilder.append(", ");
            }
        }

        valueBuilder.append(");");
        return valueBuilder.toString();
    }


    private static void parseJson(String jsonData, String aa) {
        Gson gson = new Gson();
        JsonArray jsonArray = gson.fromJson(jsonData, JsonArray.class);
        String[] split = aa.split("\n");
        JsonArray ret = new JsonArray();

        for (String s : split) {
            String trimmedS = s.trim();
            for (JsonElement element : jsonArray) {
                JsonObject object = element.getAsJsonObject();
                if (object.get("fieldCode").getAsString().equals(trimmedS)) {
                    ret.add(object);
                }
            }
        }

        System.out.println(gson.toJson(ret));
    }

    /**
     * 读取excel的多个sheet 数据
     *
     * @throws InterruptedException
     */

    @GetMapping("hello27")
    public void hello27() {
        String fileName = "D:\\des\\testSheet.xlsx";
        DemoDataListener demoDataListener = new DemoDataListener();
        ExcelReader excelReader = EasyExcel.read(fileName, Menu.class, demoDataListener).build();

        //所有列表
        List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
        for (ReadSheet readSheet : sheets) {
            // 一个个开始读取里面的数据
            excelReader.read(readSheet);
        }
        Map<String, List<Menu>> cachedDataMap = demoDataListener.getCachedDataMap();
        excelReader.finish();

    }

    public static void main(String[] args) {

        //通过农历构建
//        ChineseDate date = new ChineseDate(DateUtil.parseDate("1995-10-27"));
//// 一月
//        System.out.println(date.toStringNormal());
//        DateTime date = DateUtil.date(1677651621*1000L);

//        try {
//            FileInputStream is = new FileInputStream("D:\\des\\新建 DOCX 文档.docx");
//            // 读取 docx 后缀文件，获取表单迭代器
//            XWPFDocument doc = new XWPFDocument(is);
//            Iterator<XWPFTable> it = doc.getTablesIterator();
//            // 遍历表格
//            while (it.hasNext()) {
//                XWPFTable table = it.next();
//                // 遍历行
//                for (XWPFTableRow tr : table.getRows()) {
//                    // 遍历列
//                    StringBuilder sb = new StringBuilder("INSERT INTO `aio-db`.asia_vulnerability_kb (id, vulnTag, hazardousScene, hazardousExample, vulnTagRank, repairSuggestion, createTime, updateTime) VALUES (");
//                    for (XWPFTableCell tableCell : tr.getTableCells()) {
//                        // 遍历单元格中的数据
//                        for (XWPFParagraph paragraph : tableCell.getParagraphs()) {
//                            // 获取单元格中的数据
//                            String text = paragraph.getParagraphText();
//                            if (StringUtils.isNotBlank(text)) {
//                                sb.append("'").append(text).append("',");
//                            }
//
//                        }
//                    }
//                    sb.append(")");
//                    System.out.println(sb.toString());
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        // HTML文件路径
        String filePath = "D:\\work\\demo\\src\\main\\resources\\aa.html";

        try {
            // 从HTML文件中读取内容
            File input = new File(filePath);
            Document doc = Jsoup.parse(input); // 使用Jsoup解析HTML文件

            // 将HTML内容转换为字符串
            String htmlString = doc.outerHtml();

            // 打印转换后的字符串
            System.out.println(htmlString);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }




}
