# %(#df0c0c)[PowerToys] 功能介绍及安装
>PowerToys 是由微软开发的一套免费实用程序集合，旨在增强 Windows 操作系统的功能和用户体验。这些工具可以帮助用户%(#b90909)[提高生产力、优化系统设置]，并提供各种便捷的功能。比如下图中类似与mac的快速搜索，可以搜索应用程序等等...
![Snipaste_2025-01-10_10-41-06.png](https://i.imgur.com/63hbbJX.png)

## 主要功能
1. FancyZones
   功能：允许用户通过拖放窗口到预定义的区域来快速调整窗口布局。
   用途：提高多任务处理效率，使桌面组织更加有序。
2. File Explorer Add-ons
   功能：扩展文件资源管理器的功能，包括显示隐藏文件夹图标、复制路径等。
   用途：简化文件管理和导航操作。
3. Image Resizer
   功能：右键菜单选项，用于快速调整图像大小。
   用途：方便地批量或单个调整图片尺寸，无需打开专门的图像编辑软件。
4. Color Picker
   功能：从屏幕上的任何位置选择颜色，并将其复制为不同的格式（如 HEX、RGB）。
   用途：设计和开发过程中获取精确的颜色值。
5. Keyboard Manager
   功能：重新映射键盘快捷键，创建自定义热键。
   用途：个性化键盘布局，提高工作效率。
6. Mouse Utilities
   功能：改善鼠标体验，包括鼠标按钮重新映射、双击速度调整等。
   用途：适应个人使用习惯，提升舒适度。
7. PowerRename
   功能：高级重命名工具，支持正则表达式和模板。
   用途：批量重命名文件时更灵活和高效。
8. Run
   功能：类似 macOS 的 Spotlight 功能，可以通过快捷键快速启动应用程序和执行命令。
   用途：加快启动应用程序的速度，提高操作效率。
9. Shortcut Guide
   功能：显示所有可用的快捷键组合。
   用途：帮助用户发现和记忆常用的快捷键。
10. Text Shaper
    功能：在输入法中插入特殊字符和表情符号。
    用途：方便输入复杂的文本内容。
11. Video Conference Mute
    功能：一键静音麦克风和摄像头。
    用途：在视频会议中快速切换音频和视频状态。
12. Awake
    功能：防止计算机进入睡眠状态。
    用途：确保长时间运行的任务不会因为计算机休眠而中断。
13. Clipboard History
    功能：保存剪贴板历史记录，便于重复使用之前的粘贴内容。
    用途：提高复制粘贴操作的效率。
14. Dictation
    功能：语音转文字工具，支持多种语言。
    用途：方便用户通过语音输入文本。
15. PDF Preview
    功能：在文件资源管理器中预览 PDF 文件。
    用途：无需打开专用软件即可查看 PDF 内容。
16. Quick Accent
    功能：轻松输入带有重音符号的字符。
    用途：方便在文档中输入特殊字符。
17. System Information Viewer
    功能：显示详细的系统信息，包括硬件和软件配置。
    用途：帮助诊断系统问题和了解硬件规格。
18. Utility Manager
    功能：管理 PowerToys 中各个模块的启用和禁用状态。
    用途：根据需要定制 PowerToys 的功能集。
## 安装和使用
### 安装 PowerToys
1. 下载安装包：
    - [访问 PowerToys Github网站 下载最新版本的安装包](https://github.com/microsoft/PowerToys/releases/tag/v0.87.1)。
2. 运行安装程序：
    - 按照自己CPU架构 选择 x64 或者 arm exe的安装包即可。