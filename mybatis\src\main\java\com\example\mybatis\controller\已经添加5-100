2024-10-12T15:39:19.568+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : selectE10 count:521
2024-10-12T15:39:19.948+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320004592192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6d93b57e516cfcd60318713a8bf507f8&deviceId=510714916794020931
2024-10-12T15:39:20.056+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f12ff26c2c25f994c53ee567b7fd07c3\\\",\\\"dbIdValue\\\":\\\"f12ff26c2c25f994c53ee567b7fd07c3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BT\\\",\\\"targetValue\\\":\\\"BT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510714916794020931
2024-10-12T15:39:21.938+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:250967529763392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=be1a44b23c1be921b365e1a922389678&deviceId=510711975597061431
2024-10-12T15:39:21.939+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a388c0c704b8223621f82eba73232762\\\",\\\"dbIdValue\\\":\\\"a388c0c704b8223621f82eba73232762\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"mastermaster\\\",\\\"targetValue\\\":\\\"mastermaster\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510711975597061431
2024-10-12T15:39:23.293+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:521978760274496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f1646a588beaa5238bd75a4d8f7ac316&deviceId=510693885832348465
2024-10-12T15:39:23.294+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"788dad9ffd24c0b08ce81a50ba22cbad\\\",\\\"dbIdValue\\\":\\\"788dad9ffd24c0b08ce81a50ba22cbad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Data\\\",\\\"targetValue\\\":\\\"E10_Data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510693885832348465
2024-10-12T15:39:24.635+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:348021078823488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=95de45e1d2dde983df567acb84895dfc&deviceId=510595932761376312
2024-10-12T15:39:24.635+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"88b3dc5af0a3e9e0a0ee5bf7e3dc5c6b\\\",\\\"dbIdValue\\\":\\\"88b3dc5af0a3e9e0a0ee5bf7e3dc5c6b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HFSXW\\\",\\\"targetValue\\\":\\\"HFSXW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510595932761376312
2024-10-12T15:39:26.118+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:597271745753664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=874b74113837eb47d47462a2406e53f1&deviceId=510585985633891651
2024-10-12T15:39:26.119+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"09fbadacd39956ab9743ae8f024ac2c6\\\",\\\"dbIdValue\\\":\\\"09fbadacd39956ab9743ae8f024ac2c6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SD_ZS\\\",\\\"targetValue\\\":\\\"SD_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510585985633891651
2024-10-12T15:39:27.440+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:340868584743488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3d5c3f6d15c70a3a3fd736142da005bb&deviceId=510568744108438595
2024-10-12T15:39:27.441+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bb312292fc1a887dd5c4f6ef25b32389\\\",\\\"dbIdValue\\\":\\\"bb312292fc1a887dd5c4f6ef25b32389\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XKNERP\\\",\\\"targetValue\\\":\\\"XKNERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510568744108438595
2024-10-12T15:39:28.845+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:208506891498048 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e54518ed8ddc3ff7cd7074fa4a03854c&deviceId=510576732126852163
2024-10-12T15:39:28.845+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"85899f7251484971d637bb3d78c74c2f\\\",\\\"dbIdValue\\\":\\\"85899f7251484971d637bb3d78c74c2f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510576732126852163
2024-10-12T15:39:30.175+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:189279379227200 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7f3b13170ea86c59378920b53f00f004&deviceId=510575467695194931
2024-10-12T15:39:30.176+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"601bcbc300a190cd39b7bbd19fcb01fc\\\",\\\"dbIdValue\\\":\\\"601bcbc300a190cd39b7bbd19fcb01fc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_AZERP\\\",\\\"targetValue\\\":\\\"E10_AZERP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510575467695194931
2024-10-12T15:39:31.576+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323027825216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f0a31c248b07fc0cd65dfead2c8225be&deviceId=510572635013268534
2024-10-12T15:39:31.577+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c757eeb54524b22983bc1e3bbe4387a0\\\",\\\"dbIdValue\\\":\\\"c757eeb54524b22983bc1e3bbe4387a0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MT50\\\",\\\"targetValue\\\":\\\"MT50\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510572635013268534
2024-10-12T15:39:32.898+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323101102656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5ffc04fcc97da1088e8343d299eebd53&deviceId=510547800992269360
2024-10-12T15:39:32.898+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bbe4f09b2d186f72a008b67643416866\\\",\\\"dbIdValue\\\":\\\"bbe4f09b2d186f72a008b67643416866\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"data\\\",\\\"targetValue\\\":\\\"data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510547800992269360
2024-10-12T15:39:34.225+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319306822208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=788f4806ebe27ea32d6c189f15a4e4ce&deviceId=510542161364660272
2024-10-12T15:39:34.226+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"46034be982f5f6f9ae10a4304bba7b9f\\\",\\\"dbIdValue\\\":\\\"46034be982f5f6f9ae10a4304bba7b9f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10hbgroup\\\",\\\"targetValue\\\":\\\"E10hbgroup\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510542161364660272
2024-10-12T15:39:35.568+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:626421319938624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b72e8b47990cc09bc4aa7933347ffc1b&deviceId=510540822307943476
2024-10-12T15:39:35.569+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f5f9c4248d7126d341e5b8a39c17e9d3\\\",\\\"dbIdValue\\\":\\\"f5f9c4248d7126d341e5b8a39c17e9d3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_wenxuan\\\",\\\"targetValue\\\":\\\"E10_wenxuan\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510540822307943476
2024-10-12T15:39:36.950+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321764598336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=38be33a98099f3c64167510d488ecc76&deviceId=510441763836539205
2024-10-12T15:39:36.950+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"08cec999da6938c18cdb02103990afad\\\",\\\"dbIdValue\\\":\\\"08cec999da6938c18cdb02103990afad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZSKMNCS\\\",\\\"targetValue\\\":\\\"ZSKMNCS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510441763836539205
2024-10-12T15:39:38.304+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:488266530263616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0a7551e52188819a7dda37d5d9754e63&deviceId=510440770524689475
2024-10-12T15:39:38.305+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5100aa5cba805a873eac4e949455263a\\\",\\\"dbIdValue\\\":\\\"5100aa5cba805a873eac4e949455263a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510440770524689475
2024-10-12T15:39:39.661+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:469952652808768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b19799a00c47478e5074d7dc54a10b5f&deviceId=510435507361038660
2024-10-12T15:39:39.662+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a79742f56e48b83f530728081e941503\\\",\\\"dbIdValue\\\":\\\"a79742f56e48b83f530728081e941503\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Lemei\\\",\\\"targetValue\\\":\\\"Lemei\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510435507361038660
2024-10-12T15:39:41.035+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:360167733420608 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6a5f6ad888b77f2950c97a90215221f4&deviceId=510433777445516355
2024-10-12T15:39:41.035+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"322e920744c8a88a89bd99e4233629ed\\\",\\\"dbIdValue\\\":\\\"322e920744c8a88a89bd99e4233629ed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510433777445516355
2024-10-12T15:39:42.433+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:261927974150720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2f49ddfa468ab1ef50cfaa7af3bd9fbd&deviceId=510433957800587829
2024-10-12T15:39:42.433+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0d645702a090c201638e9c632265f533\\\",\\\"dbIdValue\\\":\\\"0d645702a090c201638e9c632265f533\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SJE10_ZS\\\",\\\"targetValue\\\":\\\"SJE10_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510433957800587829
2024-10-12T15:39:43.776+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:340868513550912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cd0152fdce5ca25207c573f7856167ef&deviceId=510414015445349443
2024-10-12T15:39:43.776+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f90abebcd40aa78500a698f6945194ad\\\",\\\"dbIdValue\\\":\\\"f90abebcd40aa78500a698f6945194ad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"mastermaster\\\",\\\"targetValue\\\":\\\"mastermaster\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510414015445349443
2024-10-12T15:39:45.135+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:389500048945728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=715ee95ef8a2d354cbfba2a9703acc49&deviceId=510404600105283890
2024-10-12T15:39:45.135+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8fc2d76e027ec3eaeef23c8cb8c7ec96\\\",\\\"dbIdValue\\\":\\\"8fc2d76e027ec3eaeef23c8cb8c7ec96\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SS\\\",\\\"targetValue\\\":\\\"SS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510404600105283890
2024-10-12T15:39:46.506+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:435214809743936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=59ba440ad42fcddb72575d16199bdd70&deviceId=510136401593906243
2024-10-12T15:39:46.507+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6c6a35a3e11067e0ea1cf963e377986b\\\",\\\"dbIdValue\\\":\\\"6c6a35a3e11067e0ea1cf963e377986b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JFZS\\\",\\\"targetValue\\\":\\\"JFZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510136401593906243
2024-10-12T15:39:47.905+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319181681216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1ae32da1229be8bf7af66e15629b0079&deviceId=509993827403052099
2024-10-12T15:39:47.906+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2c088534a559995ecf90105c859c1f37\\\",\\\"dbIdValue\\\":\\\"2c088534a559995ecf90105c859c1f37\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DS\\\",\\\"targetValue\\\":\\\"DS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509993827403052099
2024-10-12T15:39:49.265+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322696757824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=be463267b7039f6e68c58d1b160c1fda&deviceId=509979080414738500
2024-10-12T15:39:49.265+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"24499904775de5011b38961b07b0c119\\\",\\\"dbIdValue\\\":\\\"24499904775de5011b38961b07b0c119\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"QDJG\\\",\\\"targetValue\\\":\\\"QDJG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509979080414738500
2024-10-12T15:39:50.613+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:497602308100672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f1db5ae6e48a6c9f697e912aaaad5114&deviceId=509978363557856069
2024-10-12T15:39:50.614+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3154dd3d641e557fc8f5229532fbbd8e\\\",\\\"dbIdValue\\\":\\\"3154dd3d641e557fc8f5229532fbbd8e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XLCS2\\\",\\\"targetValue\\\":\\\"XLCS2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509978363557856069
2024-10-12T15:39:52.043+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:223980329071168 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0ba8daaf6050d512669a68e7342c390b&deviceId=509972515825665073
2024-10-12T15:39:52.044+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5fe7b20a556efe8f3877e5489f573fbc\\\",\\\"dbIdValue\\\":\\\"5fe7b20a556efe8f3877e5489f573fbc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509972515825665073
2024-10-12T15:39:53.407+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:485215211934272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9f49feb6cc207ae8e68d18f7cfe509b8&deviceId=509867286627107124
2024-10-12T15:39:53.408+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5f02e8c7b924da93574a98d02527df8b\\\",\\\"dbIdValue\\\":\\\"5f02e8c7b924da93574a98d02527df8b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FM_data\\\",\\\"targetValue\\\":\\\"FM_data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509867286627107124
2024-10-12T15:39:54.763+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:313115622003264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0f5a9187632bf0a0335a72f434220af0&deviceId=509862925574354229
2024-10-12T15:39:54.764+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c0cc9e2e7538033dd718af7b30c505c2\\\",\\\"dbIdValue\\\":\\\"c0cc9e2e7538033dd718af7b30c505c2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CMDZ\\\",\\\"targetValue\\\":\\\"CMDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509862925574354229
2024-10-12T15:39:56.331+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:424984914928192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=df1142b1c511398ff7bb1df2c0dea9af&deviceId=509850095550739523
2024-10-12T15:39:56.331+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7de9ce6c0ed25b19b13408f11ce773dc\\\",\\\"dbIdValue\\\":\\\"7de9ce6c0ed25b19b13408f11ce773dc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Inbisen6003\\\",\\\"targetValue\\\":\\\"Inbisen6003\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509850095550739523
2024-10-12T15:39:57.673+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:467415813964352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d94983522cd76fc21ac8eac73559f8d3&deviceId=509846927894918211
2024-10-12T15:39:57.675+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"437625e14c2f36ae5313bb9c443dada1\\\",\\\"dbIdValue\\\":\\\"437625e14c2f36ae5313bb9c443dada1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDYdata\\\",\\\"targetValue\\\":\\\"XDYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509846927894918211
2024-10-12T15:40:00.448+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:326430962590272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5a7631f9bcf6d9c77ae4a23d06ca3119&deviceId=509843644560193860
2024-10-12T15:40:00.449+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5ff27a38f6a969a98b87e69454696ce3\\\",\\\"dbIdValue\\\":\\\"5ff27a38f6a969a98b87e69454696ce3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HuaHan_01\\\",\\\"targetValue\\\":\\\"HuaHan_01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509843644560193860
2024-10-12T15:40:04.196+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318113616448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b6e9f261f057eb2ce80485a20e816430&deviceId=504628021244736579
2024-10-12T15:40:04.196+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6612c34b1913c0d65d83b6717874d658\\\",\\\"dbIdValue\\\":\\\"6612c34b1913c0d65d83b6717874d658\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504628021244736579
2024-10-12T15:40:12.511+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321345724992 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4f9cc858e785dcb09aca0f8af44265ab&deviceId=478512298604905010
2024-10-12T15:40:12.512+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ed2409923400b68d963ebcdccf5fce25\\\",\\\"dbIdValue\\\":\\\"ed2409923400b68d963ebcdccf5fce25\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RONGTA\\\",\\\"targetValue\\\":\\\"RONGTA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   478512298604905010
2024-10-12T15:40:13.867+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319320646208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e4305ace79004a30eb9127f673f255dd&deviceId=509531956183054392
2024-10-12T15:40:13.867+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f229faeaf1728f9b22ef21659a95f153\\\",\\\"dbIdValue\\\":\\\"f229faeaf1728f9b22ef21659a95f153\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AOK\\\",\\\"targetValue\\\":\\\"AOK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509531956183054392
2024-10-12T15:40:15.238+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:539231335817792 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a9a400b2b1cf25914a34f346dd88de4c&deviceId=509531053585609795
2024-10-12T15:40:15.239+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"be94f8ffa1a1eef251b7b8252714fb44\\\",\\\"dbIdValue\\\":\\\"be94f8ffa1a1eef251b7b8252714fb44\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_DH\\\",\\\"targetValue\\\":\\\"E10_DH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   509531053585609795
2024-10-12T15:40:16.600+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322364281408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e398856e7450452bf1030883027eb05b&deviceId=508979271000142389
2024-10-12T15:40:16.600+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"330e59893219f996a0cc4bb400983f34\\\",\\\"dbIdValue\\\":\\\"330e59893219f996a0cc4bb400983f34\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508979271000142389
2024-10-12T15:40:17.973+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:410504879104576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0107eaec9664695bf65390f6034b6b04&deviceId=508956040042132547
2024-10-12T15:40:17.974+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"34265fcf2c6ef74b88cf7d8b2fb449e6\\\",\\\"dbIdValue\\\":\\\"34265fcf2c6ef74b88cf7d8b2fb449e6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508956040042132547
2024-10-12T15:40:21.060+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:259812733194816 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=68274a8b355d1367fdb09843333ab1f7&deviceId=508862318520582961
2024-10-12T15:40:21.061+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"76403e2fef4d2916a8bb123535751ee0\\\",\\\"dbIdValue\\\":\\\"76403e2fef4d2916a8bb123535751ee0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"mastermaster\\\",\\\"targetValue\\\":\\\"mastermaster\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508862318520582961
2024-10-12T15:40:22.427+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:194337219666496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4fe5e74bb33e2ac40aed77ef090e2f3e&deviceId=508833106115966019
2024-10-12T15:40:22.427+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"951d9bf027dc5f17437064fdfcfdc625\\\",\\\"dbIdValue\\\":\\\"951d9bf027dc5f17437064fdfcfdc625\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ERP_ASFJ\\\",\\\"targetValue\\\":\\\"ERP_ASFJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508833106115966019
2024-10-12T15:40:23.798+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:575417358832192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3b8258b98c5ce967d01983c55fc5de1e&deviceId=508814708791324977
2024-10-12T15:40:23.800+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9da2d8cfcdcaba1f3009d3eebe878c6b\\\",\\\"dbIdValue\\\":\\\"9da2d8cfcdcaba1f3009d3eebe878c6b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10DATA\\\",\\\"targetValue\\\":\\\"E10DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508814708791324977
2024-10-12T15:40:25.168+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:262891332649536 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9642929dcd9bd284e0afe157a7c7d101&deviceId=508812690022483526
2024-10-12T15:40:25.169+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3b4c85494ae4e7d5456fad9778c79cad\\\",\\\"dbIdValue\\\":\\\"3b4c85494ae4e7d5456fad9778c79cad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"alcs\\\",\\\"targetValue\\\":\\\"alcs\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508812690022483526
2024-10-12T15:40:26.628+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:164852305904192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fdc570b7f0ac98d784e5c8b92f96bdba&deviceId=506949555003081012
2024-10-12T15:40:26.630+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2e24a0b9c7bf2cceda7c32f6b425d534\\\",\\\"dbIdValue\\\":\\\"2e24a0b9c7bf2cceda7c32f6b425d534\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_YXHG\\\",\\\"targetValue\\\":\\\"E10_YXHG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506949555003081012
2024-10-12T15:40:28.428+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:553033072747072 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dfd36c8586f62ec233385fc0cab437c2&deviceId=508677031383281989
2024-10-12T15:40:28.429+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"78db49349ee90aa22bc061991c53ad67\\\",\\\"dbIdValue\\\":\\\"78db49349ee90aa22bc061991c53ad67\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_KEMI\\\",\\\"targetValue\\\":\\\"E10_KEMI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508677031383281989
2024-10-12T15:40:29.768+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233427568456256 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1c78ca53c95c0b6907bd607f87c1ea7f&deviceId=508400877988495681
2024-10-12T15:40:29.770+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0fcad3d6ad6fa8725d8cf9b1d0c9feb1\\\",\\\"dbIdValue\\\":\\\"0fcad3d6ad6fa8725d8cf9b1d0c9feb1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Actec\\\",\\\"targetValue\\\":\\\"E10_Actec\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508400877988495681
2024-10-12T15:40:31.259+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:493975825920576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=75b62330022bca3b13311964b58989dd&deviceId=508393089115636272
2024-10-12T15:40:31.260+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bddecfdac0bb7b1b23c156105f10ce7a\\\",\\\"dbIdValue\\\":\\\"bddecfdac0bb7b1b23c156105f10ce7a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NGdata\\\",\\\"targetValue\\\":\\\"NGdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508393089115636272
2024-10-12T15:40:32.603+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:132913094545984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7dc9f5f4e39e9df47185ceee3aed00bc&deviceId=506529464453182531
2024-10-12T15:40:32.603+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b46164c48e5aa3e98d9d9b5da661db5b\\\",\\\"dbIdValue\\\":\\\"b46164c48e5aa3e98d9d9b5da661db5b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506529464453182531
2024-10-12T15:40:33.971+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321792459328 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=27b52fef0e3b89c3968f34a37974cd11&deviceId=508262539306480432
2024-10-12T15:40:33.972+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2df3b5820219b380eb46884755e3fe5d\\\",\\\"dbIdValue\\\":\\\"2df3b5820219b380eb46884755e3fe5d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JHJC\\\",\\\"targetValue\\\":\\\"JHJC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508262539306480432
2024-10-12T15:40:35.384+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:328200458031680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=54a7a82eb8d84a05341cfc76ac7eb9a0&deviceId=508256658640618307
2024-10-12T15:40:35.384+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7f965d5cb59786352ffda7cec5f22436\\\",\\\"dbIdValue\\\":\\\"7f965d5cb59786352ffda7cec5f22436\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HANTIAN\\\",\\\"targetValue\\\":\\\"HANTIAN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508256658640618307
2024-10-12T15:40:36.721+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:130229077115456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=104127a9c54df5e052dd886736e938f3&deviceId=508232656484316227
2024-10-12T15:40:36.721+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bcf4e2970ff761b3af6c622f014477e1\\\",\\\"dbIdValue\\\":\\\"bcf4e2970ff761b3af6c622f014477e1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10Prod\\\",\\\"targetValue\\\":\\\"E10Prod\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   508232656484316227
2024-10-12T15:40:38.321+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:489108120646208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=36da8808bfbaee6d27f4c4f8a666e92d&deviceId=507683014889714228
2024-10-12T15:40:38.321+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"06127ddbc2472e8ba389f8c99a8e19d9\\\",\\\"dbIdValue\\\":\\\"06127ddbc2472e8ba389f8c99a8e19d9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DCDATA\\\",\\\"targetValue\\\":\\\"DCDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507683014889714228
2024-10-12T15:40:39.692+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320334541376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2fded930655757c05b0315ad923d1483&deviceId=507654922515264836
2024-10-12T15:40:39.692+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"99cda0d37a8a7d7adb48c2e428d15352\\\",\\\"dbIdValue\\\":\\\"99cda0d37a8a7d7adb48c2e428d15352\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SQL SERVER\\\",\\\"targetValue\\\":\\\"SQL SERVER\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507654922515264836
2024-10-12T15:40:41.062+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:67496588833344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=061b9b59ce924fde9d9d2b3ee1cf5993&deviceId=507642256824480835
2024-10-12T15:40:41.063+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"365b7ac4bbf7dc27fbbd200647d6812f\\\",\\\"dbIdValue\\\":\\\"365b7ac4bbf7dc27fbbd200647d6812f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10PROA\\\",\\\"targetValue\\\":\\\"E10PROA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507642256824480835
2024-10-12T15:40:42.411+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:607623054156352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61b56caca668d1e9e74542c513f25873&deviceId=507530966537875769
2024-10-12T15:40:42.413+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cae5fefe88fa34325a4b991548224fc5\\\",\\\"dbIdValue\\\":\\\"cae5fefe88fa34325a4b991548224fc5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003\\\",\\\"targetValue\\\":\\\"E6003\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507530966537875769
2024-10-12T15:40:43.786+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:353064054231616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=53d5b575b721d695a50ca5ea4d5474b8&deviceId=507527085934261315
2024-10-12T15:40:43.786+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"479ce6c35604590c70967e3fa4d80f75\\\",\\\"dbIdValue\\\":\\\"479ce6c35604590c70967e3fa4d80f75\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507527085934261315
2024-10-12T15:40:45.143+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:477782838903360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3f575a26f26007de797a080ee0bc74fa&deviceId=507510018791192385
2024-10-12T15:40:45.143+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"773ae33ad673d4398f8b3deae5543020\\\",\\\"dbIdValue\\\":\\\"773ae33ad673d4398f8b3deae5543020\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_JLL_ZS\\\",\\\"targetValue\\\":\\\"E10_JLL_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507510018791192385
2024-10-12T15:40:46.483+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323068940864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=67924598ec0a63d4763f1f97160b5ff2&deviceId=506926871921831984
2024-10-12T15:40:46.484+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8ec4387a961c7a015e5ec50ff1335191\\\",\\\"dbIdValue\\\":\\\"8ec4387a961c7a015e5ec50ff1335191\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SQL SERVER\\\",\\\"targetValue\\\":\\\"SQL SERVER\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506926871921831984
2024-10-12T15:40:47.853+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:602933996749376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f20c8f357063b3f51c4c45d2e5b4b223&deviceId=506810017404434483
2024-10-12T15:40:47.853+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d5f002b4636dd5505119b3df92bfd399\\\",\\\"dbIdValue\\\":\\\"d5f002b4636dd5505119b3df92bfd399\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NTCS\\\",\\\"targetValue\\\":\\\"NTCS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506810017404434483
2024-10-12T15:40:49.760+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:551394987508288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c630ce56b7d4ec7071958796f523c906&deviceId=463730649971307074
2024-10-12T15:40:49.761+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"203878a72eea9b7a8dc522c5392fe6cb\\\",\\\"dbIdValue\\\":\\\"203878a72eea9b7a8dc522c5392fe6cb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SQL server\\\",\\\"targetValue\\\":\\\"SQL server\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   463730649971307074
2024-10-12T15:40:51.818+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:437751024063040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f28775081003b8b6fecaeb5ea2348dd2&deviceId=506780315910418754
2024-10-12T15:40:51.818+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5b39670846944ab6e41598ee19125113\\\",\\\"dbIdValue\\\":\\\"5b39670846944ab6e41598ee19125113\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KSDDATA\\\",\\\"targetValue\\\":\\\"KSDDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506780315910418754
2024-10-12T15:40:53.267+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320340333120 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6c393e6e9d808c56321e5d9cd35d7b5d&deviceId=454777089321616181
2024-10-12T15:40:53.267+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7600da502358eae02ee9093f2be56587\\\",\\\"dbIdValue\\\":\\\"7600da502358eae02ee9093f2be56587\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SQL SERVER\\\",\\\"targetValue\\\":\\\"SQL SERVER\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   454777089321616181
2024-10-12T15:40:54.863+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:304578938188352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5309965eb551ad28b035f0160a94aeb1&deviceId=456064190960906561
2024-10-12T15:40:54.863+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1b4ef431f757457689c8173945da0510\\\",\\\"dbIdValue\\\":\\\"1b4ef431f757457689c8173945da0510\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_demo\\\",\\\"targetValue\\\":\\\"E10_demo\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   456064190960906561
2024-10-12T15:40:56.513+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320223654464 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=660e61f0bedc288c8f53bc65e053d0f7&deviceId=506525310750049842
2024-10-12T15:40:56.513+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1030c4d3337f5dca9cec8bfbdca7b274\\\",\\\"dbIdValue\\\":\\\"1030c4d3337f5dca9cec8bfbdca7b274\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_VIEW\\\",\\\"targetValue\\\":\\\"E10_VIEW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506525310750049842
2024-10-12T15:40:58.034+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322374013504 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bc8f5b803fa2a137217191ab4aa9d03d&deviceId=506512900945360185
2024-10-12T15:40:58.035+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9f50bc31bc2e16ab4dcd43db4f7c5b56\\\",\\\"dbIdValue\\\":\\\"9f50bc31bc2e16ab4dcd43db4f7c5b56\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Fanski\\\",\\\"targetValue\\\":\\\"Fanski\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850333627530603787aa3b44a6e280756fb92c70037facf82921b1ca92c36d76bd6077a1be8aaabaafbeabb41df713e49285dfbddbe3137868e0c8a6bf78548a8c6cf3682bb5d3a2e24698f5bdca7f2ec19c7604298a3a2da3c5dfc75723e57d2efce194eca5775ed32f297b4396b91a933b6e5db4f03ef6ae9207de8bba05b28aee315ef784056dd58b02e9c72656db54fb83408206067aac2fb91ee5f4f016fc6982cf5db264eccb02bb51e88847d4a0965fc69e3161a1e5827b74d19a7eb7db930b38ac426f109104f88bc1eaf05bce84f1209ce8ac47d395716c1fdf880ea3d9c3618998e9d163046d6bb6e646fd885dac339481330dcdc3","collectName":"E10附件数据采集","accId":779544910123584,"adimId":709067510006337,"id":779860623516224,"adcId":709067427443264,"execParamsVersion":"4600edaeb3f0fc72c80fc07350356a1a","aiId":709067509998146,"isEnable":1},"paramsMap":{"deviceId":"506512900945360185","eid":41322374013504,"aiId":709067509998146,"execParamsDbAiId":709067369796160,"execParamsDbId":"9f50bc31bc2e16ab4dcd43db4f7c5b56"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779860623516224 acc incomplete","batchId":779860629115456,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   506512900945360185
2024-10-12T15:41:00.624+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41319224107584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=050aa901d39116ef82555e5660b44ca3&deviceId=506384983800164663
2024-10-12T15:41:00.624+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"95a325c145265865dbb03f78fba12442\\\",\\\"dbIdValue\\\":\\\"95a325c145265865dbb03f78fba12442\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZKDZ\\\",\\\"targetValue\\\":\\\"ZKDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506384983800164663
2024-10-12T15:41:02.681+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320430076480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=64d248a52e4dca355dbfd8fb2f936f60&deviceId=506377886064718644
2024-10-12T15:41:02.681+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"31cdf9ce4243acfb0aaf5e3ab1b255d5\\\",\\\"dbIdValue\\\":\\\"31cdf9ce4243acfb0aaf5e3ab1b255d5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YJdata\\\",\\\"targetValue\\\":\\\"YJdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506377886064718644
2024-10-12T15:41:04.315+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:457123484901952 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0dc8db2be096ad500bd7ae77a5223a9f&deviceId=506370546351486019
2024-10-12T15:41:04.315+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"34e6bf010a0a1634db6127db98fd092c\\\",\\\"dbIdValue\\\":\\\"34e6bf010a0a1634db6127db98fd092c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YASUN\\\",\\\"targetValue\\\":\\\"YASUN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506370546351486019
2024-10-12T15:41:05.790+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321568645696 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=97dfcda542409a2b2859d85667a8885b&deviceId=506367309707753523
2024-10-12T15:41:05.792+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"08e3fb8b9653fd4766ae12d939f6ad34\\\",\\\"dbIdValue\\\":\\\"08e3fb8b9653fd4766ae12d939f6ad34\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZHDQ\\\",\\\"targetValue\\\":\\\"ZHDQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506367309707753523
2024-10-12T15:41:07.344+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:522376625373760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61ac44dd76e19d7f607d83fc43df81b1&deviceId=506364645401966384
2024-10-12T15:41:07.344+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"34cd8a9077656c3dbdc1ea8b99e8b610\\\",\\\"dbIdValue\\\":\\\"34cd8a9077656c3dbdc1ea8b99e8b610\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XQDATA\\\",\\\"targetValue\\\":\\\"XQDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   506364645401966384
2024-10-12T15:41:09.240+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:380932993311296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2d21de1b770586cf027b22ff95dc71ef&deviceId=505951767310120003
2024-10-12T15:41:09.240+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9828111a9822f658ea73368169868150\\\",\\\"dbIdValue\\\":\\\"9828111a9822f658ea73368169868150\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"[E10_JCZS]\\\",\\\"targetValue\\\":\\\"[E10_JCZS]\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505951767310120003
2024-10-12T15:41:11.128+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:629909921043008 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f7a1d89820fc5391e234c3f081670434&deviceId=504934069574841411
2024-10-12T15:41:11.129+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2ecc4e3deaaaeb9d37eafa77d999d1bc\\\",\\\"dbIdValue\\\":\\\"2ecc4e3deaaaeb9d37eafa77d999d1bc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504934069574841411
2024-10-12T15:41:12.786+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323022328384 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3f90e4a062a3143cc851be6d5da80ce5&deviceId=475337180328244021
2024-10-12T15:41:12.786+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"82f8112336ae7034ef8e4a4f8c99fbb5\\\",\\\"dbIdValue\\\":\\\"82f8112336ae7034ef8e4a4f8c99fbb5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LG\\\",\\\"targetValue\\\":\\\"LG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   475337180328244021
2024-10-12T15:41:14.303+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:577894864429632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=040918afdf78b66dffcb8fc65c505bd6&deviceId=505913562653275953
2024-10-12T15:41:14.303+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a1f5985bcfb62dddb00a0e8ba63b704e\\\",\\\"dbIdValue\\\":\\\"a1f5985bcfb62dddb00a0e8ba63b704e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505913562653275953
2024-10-12T15:41:16.255+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324430283328 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e1228e3e6d74042d223620ed8e4a4656&deviceId=505903633091539000
2024-10-12T15:41:16.256+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1aabe9d70dc4ba891c95957ba1878576\\\",\\\"dbIdValue\\\":\\\"1aabe9d70dc4ba891c95957ba1878576\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSTHCS\\\",\\\"targetValue\\\":\\\"XSTHCS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505903633091539000
2024-10-12T15:41:17.819+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320306061888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9a412256aa3023f33e30b6fe189f4f0f&deviceId=505765274192983091
2024-10-12T15:41:17.819+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4bc6f350af5a7477c2e9035bfb65389b\\\",\\\"dbIdValue\\\":\\\"4bc6f350af5a7477c2e9035bfb65389b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Longtech_Official\\\",\\\"targetValue\\\":\\\"Longtech_Official\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505765274192983091
2024-10-12T15:41:20.505+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:501263976161856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2731c2b8cbb791147c0a0ae8e312cd96&deviceId=505771241328624184
2024-10-12T15:41:20.505+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d80c65dd20d86b62b7e796dfa94814e5\\\",\\\"dbIdValue\\\":\\\"d80c65dd20d86b62b7e796dfa94814e5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YUPAI\\\",\\\"targetValue\\\":\\\"YUPAI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505771241328624184
2024-10-12T15:41:23.407+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:516935624127040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f4057186fd8222bac01e91951378d134&deviceId=505768360865182787
2024-10-12T15:41:23.407+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2b20722c2100797f4eb0bf0a744724bb\\\",\\\"dbIdValue\\\":\\\"2b20722c2100797f4eb0bf0a744724bb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505768360865182787
2024-10-12T15:41:24.930+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:346251581313600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9dc348a62bbc88b44c59bcfff1d0e008&deviceId=505499228164867123
2024-10-12T15:41:24.930+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f2c924544f4fd117564eab4b22ae102\\\",\\\"dbIdValue\\\":\\\"1f2c924544f4fd117564eab4b22ae102\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HFXGW_V1.0\\\",\\\"targetValue\\\":\\\"HFXGW_V1.0\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505499228164867123
2024-10-12T15:41:26.617+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:245465265631808 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0480cd82ce0d65d9ea20b56b1e01b4dd&deviceId=505494476622409784
2024-10-12T15:41:26.618+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9fa66105234d974e571628ea512421d2\\\",\\\"dbIdValue\\\":\\\"9fa66105234d974e571628ea512421d2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10demo\\\",\\\"targetValue\\\":\\\"E10demo\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505494476622409784
2024-10-12T15:41:28.199+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:448928763204160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8301a914094dd2303cb13a0b06917866&deviceId=505468617714840888
2024-10-12T15:41:28.199+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ee0ac777e09b89d0d22e79bafd813a4c\\\",\\\"dbIdValue\\\":\\\"ee0ac777e09b89d0d22e79bafd813a4c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_QZ\\\",\\\"targetValue\\\":\\\"E10_QZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505468617714840888
2024-10-12T15:41:29.804+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:607269164126784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3e20de4b70285bfba05915c8bc7e5676&deviceId=505357395761836853
2024-10-12T15:41:29.804+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3758edc3419d4ba1d76aa29b621b5dde\\\",\\\"dbIdValue\\\":\\\"3758edc3419d4ba1d76aa29b621b5dde\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DHWDATA\\\",\\\"targetValue\\\":\\\"DHWDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505357395761836853
2024-10-12T15:41:31.596+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324480377408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4086e36f36352c8824c4309e2ee16add&deviceId=505335755703338052
2024-10-12T15:41:31.596+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"36c883acac6df86f1b35177a2d9dd9ca\\\",\\\"dbIdValue\\\":\\\"36c883acac6df86f1b35177a2d9dd9ca\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZXXD\\\",\\\"targetValue\\\":\\\"ZXXD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   505335755703338052
2024-10-12T15:41:33.267+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41320251548224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c3beda048bb51d01498eba6478c22031&deviceId=419514983492105283
2024-10-12T15:41:33.268+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"871473b44d9c52c20a3990720ae31d1f\\\",\\\"dbIdValue\\\":\\\"871473b44d9c52c20a3990720ae31d1f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ARK\\\",\\\"targetValue\\\":\\\"ARK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   419514983492105283
2024-10-12T15:41:34.665+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41322695619136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8b493e6b5651c8ffbe3439f84eabfda6&deviceId=504934363931096131
2024-10-12T15:41:34.666+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"46cff66340f759aa5bdeb5648db2803b\\\",\\\"dbIdValue\\\":\\\"46cff66340f759aa5bdeb5648db2803b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JNJX\\\",\\\"targetValue\\\":\\\"JNJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504934363931096131
2024-10-12T15:41:36.018+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:484108292354624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bcafc9ab1ee8ba31ff388d907aad5c18&deviceId=504930565183517763
2024-10-12T15:41:36.018+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4247a3204bc577101e79fcba7729c5c5\\\",\\\"dbIdValue\\\":\\\"4247a3204bc577101e79fcba7729c5c5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504930565183517763
2024-10-12T15:41:37.481+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:603672525316672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0488110e0674f54d39ac6f7f88fd726c&deviceId=504916714064983107
2024-10-12T15:41:37.482+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1e8372f6329194cb4796ff635aab20a2\\\",\\\"dbIdValue\\\":\\\"1e8372f6329194cb4796ff635aab20a2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XZdata\\\",\\\"targetValue\\\":\\\"XZdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504916714064983107
2024-10-12T15:41:38.846+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:323706643280448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f752c21d4fd091685267f0d0c5b780a3&deviceId=504898733536719939
2024-10-12T15:41:38.846+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9b1d2e9ef3500f11974636f6ae818102\\\",\\\"dbIdValue\\\":\\\"9b1d2e9ef3500f11974636f6ae818102\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DS\\\",\\\"targetValue\\\":\\\"DS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504898733536719939
2024-10-12T15:41:40.188+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:388010759959104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3e9c6026ad924d91096953232d45f68f&deviceId=504746961740510275
2024-10-12T15:41:40.188+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"91797269e9568850819098055d1daa72\\\",\\\"dbIdValue\\\":\\\"91797269e9568850819098055d1daa72\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_zhengshi\\\",\\\"targetValue\\\":\\\"E10_zhengshi\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504746961740510275
2024-10-12T15:41:41.636+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323788218944 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3d3f1324ab55312516bbefd0bcef3517&deviceId=504743247097115715
2024-10-12T15:41:41.637+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d78abe7655c976ef3107a087c7828998\\\",\\\"dbIdValue\\\":\\\"d78abe7655c976ef3107a087c7828998\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JSXN\\\",\\\"targetValue\\\":\\\"JSXN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504743247097115715
2024-10-12T15:41:43.032+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41321237959232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=722e40e70db393978422bf1506389796&deviceId=504625531656876099
2024-10-12T15:41:43.032+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"db2ee2c0a4c9feb296e56f63eb975017\\\",\\\"dbIdValue\\\":\\\"db2ee2c0a4c9feb296e56f63eb975017\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YSZM\\\",\\\"targetValue\\\":\\\"YSZM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504625531656876099
2024-10-12T15:41:44.421+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:233425559212608 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=da557c6d1681a9111adba53ad669ee33&deviceId=504604295610512708
2024-10-12T15:41:44.422+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"35bddbb216fd05223bf58a828ba67f73\\\",\\\"dbIdValue\\\":\\\"35bddbb216fd05223bf58a828ba67f73\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MSYX\\\",\\\"targetValue\\\":\\\"MSYX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504604295610512708
2024-10-12T15:41:45.745+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:553164490994240 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8479dd9c86584d63f8006c2cae2f4dcb&deviceId=504506161093947443
2024-10-12T15:41:45.745+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1e3a694156394124c90875506443ebdf\\\",\\\"dbIdValue\\\":\\\"1e3a694156394124c90875506443ebdf\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SBEP\\\",\\\"targetValue\\\":\\\"SBEP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504506161093947443
2024-10-12T15:41:47.104+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:389942413550144 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=137bf7fe9e9cd9f9d0a695e11d4c1106&deviceId=504462298102903620
2024-10-12T15:41:47.105+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0ca8f410e9244749c5acc64d39952923\\\",\\\"dbIdValue\\\":\\\"0ca8f410e9244749c5acc64d39952923\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZYBZ\\\",\\\"targetValue\\\":\\\"ZYBZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504462298102903620
2024-10-12T15:41:48.521+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:423893721678400 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=29cf4bff610fbe163a26b60cddb1f34e&deviceId=504315209247110211
2024-10-12T15:41:48.521+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4721d352e499b8a14321e2c499c648d9\\\",\\\"dbIdValue\\\":\\\"4721d352e499b8a14321e2c499c648d9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003\\\",\\\"targetValue\\\":\\\"E6003\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   504315209247110211
2024-10-12T15:41:50.055+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:499725648634432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f66129c1d7ca8ff799d17ef63221f133&deviceId=503340471016240195
2024-10-12T15:41:50.055+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"437a1d16bca3cd255795e3e56b9b1f45\\\",\\\"dbIdValue\\\":\\\"437a1d16bca3cd255795e3e56b9b1f45\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZTZS\\\",\\\"targetValue\\\":\\\"ZTZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   503340471016240195
2024-10-12T15:41:51.439+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41323427910208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=837d2538d2e427ac1e576766557a3093&deviceId=501414868952298563
2024-10-12T15:41:51.439+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0c8d482c2bb5b63fc4003868f9d6e9e7\\\",\\\"dbIdValue\\\":\\\"0c8d482c2bb5b63fc4003868f9d6e9e7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MXGY001\\\",\\\"targetValue\\\":\\\"MXGY001\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   501414868952298563
2024-10-12T15:41:52.840+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41324012126784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d631da6c02f6f78f0e3318f5f5da65fa&deviceId=501411597630059587
2024-10-12T15:41:52.840+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7693b358fc9169fbc02938900b0d20ad\\\",\\\"dbIdValue\\\":\\\"7693b358fc9169fbc02938900b0d20ad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FARGO\\\",\\\"targetValue\\\":\\\"FARGO\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   501411597630059587
2024-10-12T15:41:54.211+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:295354470220352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=23d41f88135940efb92bd6ad4685b05b&deviceId=496202195507627317
2024-10-12T15:41:54.211+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0bcabe8a70a215887bcf5174dee4eb04\\\",\\\"dbIdValue\\\":\\\"0bcabe8a70a215887bcf5174dee4eb04\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ALZK\\\",\\\"targetValue\\\":\\\"ALZK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   496202195507627317
2024-10-12T15:41:55.599+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:252030863036992 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=30bb5e8cfc662eb1e2cb20f458778798&deviceId=497501395180599349
2024-10-12T15:41:55.599+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"507abb348055d328599d8c18e3eaee0a\\\",\\\"dbIdValue\\\":\\\"507abb348055d328599d8c18e3eaee0a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HSWL\\\",\\\"targetValue\\\":\\\"HSWL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   497501395180599349
2024-10-12T15:41:56.955+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:259456055169600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2f3ca15209fad1ed0ce67364268205fe&deviceId=497794353423467312
2024-10-12T15:41:56.956+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b68d78f37f09349cdcd96f6a7a099b8b\\\",\\\"dbIdValue\\\":\\\"b68d78f37f09349cdcd96f6a7a099b8b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ERP_TCGG\\\",\\\"targetValue\\\":\\\"ERP_TCGG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   497794353423467312
2024-10-12T15:41:58.306+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:41318092898880 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4a3f7796c0d1bc5d0790d8649d45620c&deviceId=497669897300489283
2024-10-12T15:41:58.307+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"262ea3ceae9eda11e93c5d218932cab1\\\",\\\"dbIdValue\\\":\\\"262ea3ceae9eda11e93c5d218932cab1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XDJJ\\\",\\\"targetValue\\\":\\\"XDJJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   497669897300489283
2024-10-12T15:41:59.643+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:173965004743232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=82e4c63c277cf13be0b578a0d9030de1&deviceId=497646751654626373
2024-10-12T15:41:59.643+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b32e756db495162edc55bfd33524afd4\\\",\\\"dbIdValue\\\":\\\"b32e756db495162edc55bfd33524afd4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RLIKZS\\\",\\\"targetValue\\\":\\\"RLIKZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   497646751654626373
2024-10-12T15:42:01.000+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:348728838316608 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c57bbdd1068e7e1e5fc1023abdef0593&deviceId=497523297550874163
2024-10-12T15:42:01.000+08:00  INFO 32288 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"038ea939a6892ecfcaa53fe73173d59b\\\",\\\"dbIdValue\\\":\\\"038ea939a6892ecfcaa53fe73173d59b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BLDTCSK\\\",\\\"targetValue\\\":\\\"BLDTCSK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   497523297550874163
deviceIds:,'510714916794020931','510711975597061431','510693885832348465','510595932761376312','510585985633891651','510568744108438595','510576732126852163','510575467695194931','510572635013268534','510547800992269360','510542161364660272','510540822307943476','510441763836539205','510440770524689475','510435507361038660','510433777445516355','510433957800587829','510414015445349443','510404600105283890','510136401593906243','509993827403052099','509979080414738500','509978363557856069','509972515825665073','509867286627107124','509862925574354229','509850095550739523','509846927894918211','509843644560193860','504628021244736579','478512298604905010','509531956183054392','509531053585609795','508979271000142389','508956040042132547','508862318520582961','508833106115966019','508814708791324977','508812690022483526','506949555003081012','508677031383281989','508400877988495681','508393089115636272','506529464453182531','508262539306480432','508256658640618307','508232656484316227','507683014889714228','507654922515264836','507642256824480835','507530966537875769','507527085934261315','507510018791192385','506926871921831984','506810017404434483','463730649971307074','506780315910418754','454777089321616181','456064190960906561','506525310750049842','506512900945360185','506384983800164663','506377886064718644','506370546351486019','506367309707753523','506364645401966384','505951767310120003','504934069574841411','475337180328244021','505913562653275953','505903633091539000','505765274192983091','505771241328624184','505768360865182787','505499228164867123','505494476622409784','505468617714840888','505357395761836853','505335755703338052','419514983492105283','504934363931096131','504930565183517763','504916714064983107','504898733536719939','504746961740510275','504743247097115715','504625531656876099','504604295610512708','504506161093947443','504462298102903620','504315209247110211','503340471016240195','501414868952298563','501411597630059587','496202195507627317','497501395180599349','497794353423467312','497669897300489283','497646751654626373','497523297550874163'