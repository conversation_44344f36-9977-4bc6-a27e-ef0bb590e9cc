package com.example.aio.controller;

import com.example.aio.model.CsvUserData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class IamControllerTest {

    @Test
    public void testCsvParsing() throws IOException {
        IamController controller = new IamController();
        
        // 创建模拟CSV文件
        String csvContent = "41338904412736,<EMAIL>,<EMAIL>,99990000\n" +
                           "41338982142528,<EMAIL>,<EMAIL>,99990000";
        
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.csv", 
            "text/csv", 
            csvContent.getBytes()
        );
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = IamController.class.getDeclaredMethod("parseCsvFile", org.springframework.web.multipart.MultipartFile.class);
            method.setAccessible(true);
            
            @SuppressWarnings("unchecked")
            List<CsvUserData> result = (List<CsvUserData>) method.invoke(controller, file);
            
            // 验证结果
            assertEquals(2, result.size());
            
            CsvUserData first = result.get(0);
            assertEquals(Long.valueOf(41338904412736L), first.getAuthUserSid());
            assertEquals("<EMAIL>", first.getAuthUserId());
            assertEquals("<EMAIL>", first.getEmail());
            assertEquals(Long.valueOf(99990000L), first.getEid());
            
            CsvUserData second = result.get(1);
            assertEquals(Long.valueOf(41338982142528L), second.getAuthUserSid());
            assertEquals("<EMAIL>", second.getAuthUserId());
            assertEquals("<EMAIL>", second.getEmail());
            assertEquals(Long.valueOf(99990000L), second.getEid());
            
        } catch (Exception e) {
            fail("测试失败: " + e.getMessage());
        }
    }

    @Test
    public void testCsvParseEndpoint() {
        IamController controller = new IamController();
        List<CsvUserData> result = controller.testCsvParse();
        
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证第一条记录
        CsvUserData first = result.get(0);
        assertEquals(Long.valueOf(41338904412736L), first.getAuthUserSid());
        assertEquals("<EMAIL>", first.getAuthUserId());
        assertEquals("<EMAIL>", first.getEmail());
        assertEquals(Long.valueOf(99990000L), first.getEid());
    }
}
