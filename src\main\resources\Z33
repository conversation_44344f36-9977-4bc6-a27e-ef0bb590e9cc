beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_29_2 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.SemiFinishCycle') AS DECIMAL(38,2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Total_production_time') AS DECIMAL(38,2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SemiFinishProductProductionCycle"


CREATE TABLE tbb.dws_aiops_pec_erpindex_2_29_2 (   key STRING,   source_db_id STRING,   product_line STRING,   indicatornumber STRING,   enterprisecode INT,   account_set STRING,   account_set_name STRING,   year STRING,   month STRING,   semifinishcycle DECIMAL(38,2),   total_number_work INT,   total_production_time DECIMAL(38,2),   deviceid STRING,   eid STRING,   collectedtime STRING,   collectconfigid STRING,   uploaddatamodelcode STRING,   devicecollectdetailid STRING,   yearandmonth TIMESTAMP ) WITH SERDEPROPERTIES ('serialization.format'='1') STORED AS PARQUET LOCATION 'hdfs://nameservice1/user/hive/warehouse/tbb.db/dws_aiops_pec_erpindex_2_29_2' TBLPROPERTIES ('COLUMN_STATS_ACCURATE'='{\"BASIC_STATS\":\"true\"}', 'numFiles'='0', 'numFilesErasureCoded'='0', 'numRows'='0', 'rawDataSize'='0', 'totalSize'='0')
