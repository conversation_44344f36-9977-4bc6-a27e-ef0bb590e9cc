async def main(args: Args) -> Output:
    params = args.params
    # 构建输出对象
    input_str = params['input']
    # 去除首尾的大括号和引号
    input_str = input_str.strip('{}').strip('"')
    # 分割成行并返回
    lines = input_str.split('\\n')

    ret: Output = {
        "key0": params['input'] + params['input'], # 拼接两次入参 input 的值
        "key1": ["hello", "world"],  # 输出一个数组
        "key2": { # 输出一个Object 
            "key21": "hi"
        },
        "key3": "",
        "key4": "",
    }
    for i, line in enumerate(lines):
        # 去除每行的换行符和引号
        cleaned_line = line.strip().strip('"').strip('\\n')
        if cleaned_line:  # 只处理非空行
            ret[f"key{i}"] = cleaned_line
            
    return ret