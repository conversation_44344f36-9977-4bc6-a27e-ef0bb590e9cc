INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (1, 241199971893824, 99990000, 'AIEOM', 'CCTestError1', 'CCTestError1(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'PUBLISHED', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2025-03-25 10:36:39', '2024-07-09 13:25:57', '2025-03-25 10:36:38', 0, 0, 0, false);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (4, 241199971893824, 99990000, 'AIEOM', 'CCTestError2', 'CCTestError2(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (5, 241199971893824, 99990000, 'AIEOM', 'CCTestError3', 'CCTestError3(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (6, 241199971893824, 99990000, 'AIEOM', 'CCTestError4', 'CCTestError4(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (7, 241199971893824, 99990000, 'AIEOM', 'CCTestError5', 'CCTestError5(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (8, 241199971893824, 99990000, 'AIEOM', 'CCTestError6', 'CCTestError6(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (9, 241199971893824, 99990000, 'AIEOM', 'CCTestError7', 'CCTestError7(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (10, 241199971893824, 99990000, 'AIEOM', 'CCTestError8', 'CCTestError8(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (11, 241199971893824, 99990000, 'AIEOM', 'CCTestError9', 'CCTestError9(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2025-03-25 10:17:38', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (12, 241199971893824, 99990000, 'AIEOM', 'CCTestError10', 'CCTestError10(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2025-03-25 10:17:38', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (13, 241199971893824, 99990000, 'AIEOM', 'CCTestError11', 'CCTestError11(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (14, 241199971893824, 99990000, 'AIEOM', 'CCTestError12', 'CCTestError12(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (15, 241199971893824, 99990000, 'AIEOM', 'CCTestError13', 'CCTestError13(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2024-07-09 13:29:30', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (16, 241199971893824, 99990000, 'AIEOM', 'CCTestError14', 'CCTestError14(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2025-03-25 10:17:38', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);
INSERT INTO dmp.dmp_tag (id, sid, eid, appCode, code, name, dimensionId, catalogueId, remark, valueType, updateMode, intervalValue, unit, timingTime, dataUpdateTime, tagAuth, tagStatus, ruleType, layerType, schedulerId, creator, creatorName, publishTime, createTime, updateTime, updateStatus, important, resultNeedZero, weightFlag) VALUES (17, 241199971893824, 99990000, 'AIEOM', 'CCTestError15', 'CCTestError15(只用于一键体检不可更新标签)', 469112550261312, 649595610600000, '', 'STRING', 'MANUAL', 0, 'DAY', '13:23:13', '2024-09-27 18:25:11', 'PRIVATE', 'OFFLINE', 'CUSTOM', 'CUSTOM', 2536, 345339736150592, '陈莹莹', '2025-03-25 10:17:38', '2024-07-09 13:25:57', '2025-03-28 18:07:55', 0, 0, 0, true);


INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (738999772140096, 830444118364736, '08_Type01_01', '08_Type01_01', 830442862367296, 1, 'AIEOM', 30.00, '', '2025-03-28 18:18:49', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000003240512, 830444118364736, '08_Type01_02', '08_Type01_02', 830442862367296, 4, 'AIEOM', 40.00, '', '2025-03-28 18:19:45', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000084308544, 830444118364736, '08_Type01_03', '08_Type01_03', 830442862367296, 5, 'AIEOM', 30.00, '', '2025-03-28 18:20:05', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000278893120, 830444118467136, '08_Type02_01', '08_Type02_01', 830442862367296, 6, 'AIEOM', 30.00, '', '2025-03-28 18:20:52', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000359424576, 830444118467136, '08_Type02_02', '08_Type02_02', 830442862367296, 7, 'AIEOM', 30.00, '', '2025-03-28 18:21:12', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000430105152, 830444118467136, '08_Type02_03', '08_Type02_03', 830442862367296, 8, 'AIEOM', 40.00, '', '2025-03-28 18:21:29', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000563585600, 830444118577728, '08_Type03_01', '08_Type03_01', 830442862367296, 9, 'AIEOM', 20.00, '', '2025-03-28 18:22:02', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000631214656, 830444118577728, '08_Type03_02', '08_Type03_02', 830442862367296, 10, 'AIEOM', 20.00, '', '2025-03-28 18:22:18', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000695669312, 830444118577728, '08_Type03_03', '08_Type03_03', 830442862367296, 11, 'AIEOM', 60.00, '', '2025-03-28 18:22:34', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000814735936, 830444118667840, '08_Type04_01', '08_Type04_01', 830442862367296, 12, 'AIEOM', 10.00, '', '2025-03-28 18:23:03', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000873402944, 830444118667840, '08_Type04_02', '08_Type04_02', 830442862367296, 13, 'AIEOM', 40.00, '', '2025-03-28 18:23:18', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739000922108480, 830444118667840, '08_Type04_03', '08_Type04_03', 830442862367296, 14, 'AIEOM', 50.00, '', '2025-03-28 18:23:29', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739001046164032, 830444118757952, '08_Type05_01', '08_Type05_01', 830442862367296, 15, 'AIEOM', 10.50, '', '2025-03-28 18:24:00', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739001104855616, 830444118757952, '08_Type05_02', '08_Type05_02', 830442862367296, 16, 'AIEOM', 30.50, '', '2025-03-28 18:24:14', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (739001156420160, 830444118757952, '08_Type05_03', '08_Type05_03', 830442862367296, 17, 'AIEOM', 59.00, '', '2025-03-28 18:24:27', '2025-03-28 18:24:53');


INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (638999772140096, 830444118364736, 'HOST_Type01_01', 'HOST_Type01_01', 830442930668096, 1, 'AIEOM', 30.00, '', '2025-03-28 18:18:49', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000003240512, 830444118364736, 'HOST_Type01_02', 'HOST_Type01_02', 830442930668096, 4, 'AIEOM', 40.00, '', '2025-03-28 18:19:45', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000084308544, 830444118364736, 'HOST_Type01_03', 'HOST_Type01_03', 830442930668096, 5, 'AIEOM', 30.00, '', '2025-03-28 18:20:05', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000278893120, 830444118467136, 'HOST_Type02_01', 'HOST_Type02_01', 830442930668096, 6, 'AIEOM', 30.00, '', '2025-03-28 18:20:52', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000359424576, 830444118467136, 'HOST_Type02_02', 'HOST_Type02_02', 830442930668096, 7, 'AIEOM', 30.00, '', '2025-03-28 18:21:12', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000430105152, 830444118467136, 'HOST_Type02_03', 'HOST_Type02_03', 830442930668096, 8, 'AIEOM', 40.00, '', '2025-03-28 18:21:29', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000563585600, 830444118577728, 'HOST_Type03_01', 'HOST_Type03_01', 830442930668096, 9, 'AIEOM', 20.00, '', '2025-03-28 18:22:02', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000631214656, 830444118577728, 'HOST_Type03_02', 'HOST_Type03_02', 830442930668096, 10, 'AIEOM', 20.00, '', '2025-03-28 18:22:18', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000695669312, 830444118577728, 'HOST_Type03_03', 'HOST_Type03_03', 830442930668096, 11, 'AIEOM', 60.00, '', '2025-03-28 18:22:34', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000814735936, 830444118667840, 'HOST_Type04_01', 'HOST_Type04_01', 830442930668096, 12, 'AIEOM', 10.00, '', '2025-03-28 18:23:03', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000873402944, 830444118667840, 'HOST_Type04_02', 'HOST_Type04_02', 830442930668096, 13, 'AIEOM', 40.00, '', '2025-03-28 18:23:18', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639000922108480, 830444118667840, 'HOST_Type04_03', 'HOST_Type04_03', 830442930668096, 14, 'AIEOM', 50.00, '', '2025-03-28 18:23:29', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639001046164032, 830444118757952, 'HOST_Type05_01', 'HOST_Type05_01', 830442930668096, 15, 'AIEOM', 10.50, '', '2025-03-28 18:24:00', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639001104855616, 830444118757952, 'HOST_Type05_02', 'HOST_Type05_02', 830442930668096, 16, 'AIEOM', 30.50, '', '2025-03-28 18:24:14', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (639001156420160, 830444118757952, 'HOST_Type05_03', 'HOST_Type05_03', 830442930668096, 17, 'AIEOM', 59.00, '', '2025-03-28 18:24:27', '2025-03-28 18:24:53');

INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (538999772140096, 830444118364736, 'CLIENT_Type01_01', 'CLIENT_Type01_01', 830442930770496, 1, 'AIEOM', 30.00, '', '2025-03-28 18:18:49', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000003240512, 830444118364736, 'CLIENT_Type01_02', 'CLIENT_Type01_02', 830442930770496, 4, 'AIEOM', 40.00, '', '2025-03-28 18:19:45', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000084308544, 830444118364736, 'CLIENT_Type01_03', 'CLIENT_Type01_03', 830442930770496, 5, 'AIEOM', 30.00, '', '2025-03-28 18:20:05', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000278893120, 830444118467136, 'CLIENT_Type02_01', 'CLIENT_Type02_01', 830442930770496, 6, 'AIEOM', 30.00, '', '2025-03-28 18:20:52', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000359424576, 830444118467136, 'CLIENT_Type02_02', 'CLIENT_Type02_02', 830442930770496, 7, 'AIEOM', 30.00, '', '2025-03-28 18:21:12', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000430105152, 830444118467136, 'CLIENT_Type02_03', 'CLIENT_Type02_03', 830442930770496, 8, 'AIEOM', 40.00, '', '2025-03-28 18:21:29', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000563585600, 830444118577728, 'CLIENT_Type03_01', 'CLIENT_Type03_01', 830442930770496, 9, 'AIEOM', 20.00, '', '2025-03-28 18:22:02', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000631214656, 830444118577728, 'CLIENT_Type03_02', 'CLIENT_Type03_02', 830442930770496, 10, 'AIEOM', 20.00, '', '2025-03-28 18:22:18', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000695669312, 830444118577728, 'CLIENT_Type03_03', 'CLIENT_Type03_03', 830442930770496, 11, 'AIEOM', 60.00, '', '2025-03-28 18:22:34', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000814735936, 830444118667840, 'CLIENT_Type04_01', 'CLIENT_Type04_01', 830442930770496, 12, 'AIEOM', 10.00, '', '2025-03-28 18:23:03', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000873402944, 830444118667840, 'CLIENT_Type04_02', 'CLIENT_Type04_02', 830442930770496, 13, 'AIEOM', 40.00, '', '2025-03-28 18:23:18', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539000922108480, 830444118667840, 'CLIENT_Type04_03', 'CLIENT_Type04_03', 830442930770496, 14, 'AIEOM', 50.00, '', '2025-03-28 18:23:29', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539001046164032, 830444118757952, 'CLIENT_Type05_01', 'CLIENT_Type05_01', 830442930770496, 15, 'AIEOM', 10.50, '', '2025-03-28 18:24:00', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539001104855616, 830444118757952, 'CLIENT_Type05_02', 'CLIENT_Type05_02', 830442930770496, 16, 'AIEOM', 30.50, '', '2025-03-28 18:24:14', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (539001156420160, 830444118757952, 'CLIENT_Type05_03', 'CLIENT_Type05_03', 830442930770496, 17, 'AIEOM', 59.00, '', '2025-03-28 18:24:27', '2025-03-28 18:24:53');

INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (438999772140096, 830444118364736, 'MSSQL_Type01_01', 'MSSQL_Type01_01', 830443123356224, 1, 'AIEOM', 30.00, '', '2025-03-28 18:18:49', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000003240512, 830444118364736, 'MSSQL_Type01_02', 'MSSQL_Type01_02', 830443123356224, 4, 'AIEOM', 40.00, '', '2025-03-28 18:19:45', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000084308544, 830444118364736, 'MSSQL_Type01_03', 'MSSQL_Type01_03', 830443123356224, 5, 'AIEOM', 30.00, '', '2025-03-28 18:20:05', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000278893120, 830444118467136, 'MSSQL_Type02_01', 'MSSQL_Type02_01', 830443123356224, 6, 'AIEOM', 30.00, '', '2025-03-28 18:20:52', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000359424576, 830444118467136, 'MSSQL_Type02_02', 'MSSQL_Type02_02', 830443123356224, 7, 'AIEOM', 30.00, '', '2025-03-28 18:21:12', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000430105152, 830444118467136, 'MSSQL_Type02_03', 'MSSQL_Type02_03', 830443123356224, 8, 'AIEOM', 40.00, '', '2025-03-28 18:21:29', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000563585600, 830444118577728, 'MSSQL_Type03_01', 'MSSQL_Type03_01', 830443123356224, 9, 'AIEOM', 20.00, '', '2025-03-28 18:22:02', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000631214656, 830444118577728, 'MSSQL_Type03_02', 'MSSQL_Type03_02', 830443123356224, 10, 'AIEOM', 20.00, '', '2025-03-28 18:22:18', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000695669312, 830444118577728, 'MSSQL_Type03_03', 'MSSQL_Type03_03', 830443123356224, 11, 'AIEOM', 60.00, '', '2025-03-28 18:22:34', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000814735936, 830444118667840, 'MSSQL_Type04_01', 'MSSQL_Type04_01', 830443123356224, 12, 'AIEOM', 10.00, '', '2025-03-28 18:23:03', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000873402944, 830444118667840, 'MSSQL_Type04_02', 'MSSQL_Type04_02', 830443123356224, 13, 'AIEOM', 40.00, '', '2025-03-28 18:23:18', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439000922108480, 830444118667840, 'MSSQL_Type04_03', 'MSSQL_Type04_03', 830443123356224, 14, 'AIEOM', 50.00, '', '2025-03-28 18:23:29', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439001046164032, 830444118757952, 'MSSQL_Type05_01', 'MSSQL_Type05_01', 830443123356224, 15, 'AIEOM', 10.50, '', '2025-03-28 18:24:00', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439001104855616, 830444118757952, 'MSSQL_Type05_02', 'MSSQL_Type05_02', 830443123356224, 16, 'AIEOM', 30.50, '', '2025-03-28 18:24:14', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (439001156420160, 830444118757952, 'MSSQL_Type05_03', 'MSSQL_Type05_03', 830443123356224, 17, 'AIEOM', 59.00, '', '2025-03-28 18:24:27', '2025-03-28 18:24:53');

INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (338999772140096, 830444118364736, 'ORACLE_Type01_01', 'ORACLE_Type01_01', 830443123470912, 1, 'AIEOM', 30.00, '', '2025-03-28 18:18:49', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000003240512, 830444118364736, 'ORACLE_Type01_02', 'ORACLE_Type01_02', 830443123470912, 4, 'AIEOM', 40.00, '', '2025-03-28 18:19:45', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000084308544, 830444118364736, 'ORACLE_Type01_03', 'ORACLE_Type01_03', 830443123470912, 5, 'AIEOM', 30.00, '', '2025-03-28 18:20:05', '2025-03-28 18:20:12');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000278893120, 830444118467136, 'ORACLE_Type02_01', 'ORACLE_Type02_01', 830443123470912, 6, 'AIEOM', 30.00, '', '2025-03-28 18:20:52', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000359424576, 830444118467136, 'ORACLE_Type02_02', 'ORACLE_Type02_02', 830443123470912, 7, 'AIEOM', 30.00, '', '2025-03-28 18:21:12', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000430105152, 830444118467136, 'ORACLE_Type02_03', 'ORACLE_Type02_03', 830443123470912, 8, 'AIEOM', 40.00, '', '2025-03-28 18:21:29', '2025-03-28 18:21:39');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000563585600, 830444118577728, 'ORACLE_Type03_01', 'ORACLE_Type03_01', 830443123470912, 9, 'AIEOM', 20.00, '', '2025-03-28 18:22:02', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000631214656, 830444118577728, 'ORACLE_Type03_02', 'ORACLE_Type03_02', 830443123470912, 10, 'AIEOM', 20.00, '', '2025-03-28 18:22:18', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000695669312, 830444118577728, 'ORACLE_Type03_03', 'ORACLE_Type03_03', 830443123470912, 11, 'AIEOM', 60.00, '', '2025-03-28 18:22:34', '2025-03-28 18:22:43');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000814735936, 830444118667840, 'ORACLE_Type04_01', 'ORACLE_Type04_01', 830443123470912, 12, 'AIEOM', 10.00, '', '2025-03-28 18:23:03', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000873402944, 830444118667840, 'ORACLE_Type04_02', 'ORACLE_Type04_02', 830443123470912, 13, 'AIEOM', 40.00, '', '2025-03-28 18:23:18', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339000922108480, 830444118667840, 'ORACLE_Type04_03', 'ORACLE_Type04_03', 830443123470912, 14, 'AIEOM', 50.00, '', '2025-03-28 18:23:29', '2025-03-28 18:23:38');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339001046164032, 830444118757952, 'ORACLE_Type05_01', 'ORACLE_Type05_01', 830443123470912, 15, 'AIEOM', 10.50, '', '2025-03-28 18:24:00', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339001104855616, 830444118757952, 'ORACLE_Type05_02', 'ORACLE_Type05_02', 830443123470912, 16, 'AIEOM', 30.50, '', '2025-03-28 18:24:14', '2025-03-28 18:24:53');
INSERT INTO `aio-db`.aiops_exam_index (id, aeitId, code, name, aeimId, tagId, appCode, weight, remark, createDate, updateDate) VALUES (339001156420160, 830444118757952, 'ORACLE_Type05_03', 'ORACLE_Type05_03', 830443123470912, 17, 'AIEOM', 59.00, '', '2025-03-28 18:24:27', '2025-03-28 18:24:53');

-- E10
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('E10_99990000',17,85,'2025-03-28 11:11:11','2025-03-28');
-- 易飞
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('08_99990000',17,85,'2025-03-28 11:11:11','2025-03-28');
-- 主机
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('516496010621501746',17,85,'2025-03-28 11:11:11','2025-03-28');
-- 终端
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('539242866677924930',17,85,'2025-03-28 11:11:11','2025-03-28');
-- MSSQL 172.16.2.240 (172.16.2.240)
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('273131ace29ac0778f793901f61e2121',17,85,'2025-03-28 11:11:11','2025-03-28');

-- ORACLE 172.16.100.19:1521\\topprd (172.16.100.19)
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',1,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',4,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',5,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',6,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',7,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',8,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',9,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',10,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',11,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',12,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',13,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',14,85,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',15,90,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',16,70,'2025-03-28 11:11:11','2025-03-28');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('ee57629c71b4ce20c240af843929cc41',17,85,'2025-03-28 11:11:11','2025-03-28');

-- simple 主机 533166597448156471
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',863408527925824,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',22,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',23,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',24,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',25,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',26,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',27,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',28,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',29,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',30,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('533166597448156471',31,95,'2025-06-03 11:11:11','2025-06-03');

insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',863408527925824,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',22,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',23,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',24,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',25,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',26,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',27,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',28,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',29,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',30,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('533166597448156471',31,95,'2025-06-03 11:11:11','2025-06-03');


-- simple 主机 557388974721609776
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',863408527925824,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',22,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',23,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',24,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',25,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',26,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',27,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',28,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',29,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',30,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_score (id,tagId,score,updateTime,tagDate) values ('557388974721609776',31,95,'2025-06-03 11:11:11','2025-06-03');

insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',863408527925824,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',22,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',23,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',24,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',25,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',26,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',27,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',28,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',29,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',30,95,'2025-06-03 11:11:11','2025-06-03');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('557388974721609776',31,95,'2025-06-03 11:11:11','2025-06-03');
