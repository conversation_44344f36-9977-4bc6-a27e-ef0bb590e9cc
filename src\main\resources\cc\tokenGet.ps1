function IpAndPort{
#    return "{{http_service_address}}"
        return "https://*************"
#         return "https://************:4435"
}

function UserNameGet{
        return "admin"
#    return "{{http_basic_login_id}}"
}

function PassGet{
#         return "#1TXUo6aK6"
        return "xydz@2024"
#    return "{{http_basic_login_pwd}}"
}

function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function PublicKeyGet {

    $ipAndPort = IpAndPort
    $response = Invoke-WebRequest -Uri $ipAndPort/janus/public-key -Method GET -ContentType "application/json" -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.public_key
    } else {
        Write-Output $response
    }
}

function RASGet {
    $kitUrl = KitUrlGet
    $publicKey = PublicKeyGet
    $publicKey = $publicKey -replace "\n$", ""
    $pass = PassGet
    $payload = @{
        sourceValue = $pass
        hexPublicKey = $publicKey
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri http://$kitUrl/aiopskit/rsa/encrypt/by/hex/public/key?customerESize=65537 -Method POST -ContentType "application/json" -Body $jsonBody
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data
    } else {
        Write-Output $response
    }
}

function TokenGet {
    $ipAndPort = IpAndPort
    $userNameGet = UserNameGet
    $pass = RASGet


    $payload = @{
        auth = @{
            passwordCredentials = @{
                username = $userNameGet
                password = $pass
            }
        }
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri $ipAndPort/janus/authenticate -Method POST -ContentType "application/json" -Body $jsonBody -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json

        $payload = @{
            token = $jsonObject.data.access.token.id
        }
        return $payload
    } else {
        Write-Output $response
    }
}

$aa=TokenGet
$result=Write-Output $aa | ConvertTo-Json
Write-Output $result
