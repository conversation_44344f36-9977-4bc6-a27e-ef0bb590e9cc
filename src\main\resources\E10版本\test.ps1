$ErrorActionPreference = "stop";
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8;

$DefaultCenterIp = "127.0.0.1";
$DefaultCenterPort = "8047";
$E10BasePath = $Env:E10_APS_Server;
$ToolExeName = "GetE10LicenseUseTool.exe";

$CheckPaths = (
    # 授权服务器
    "\LicenseCenter\System",
    # AP服务器
    "\Server\Control\System",
    "\Server\Application\System"
);

# 取得E10实际的dll目录
function Get-CurrentSystemDllPaths() {
    $Paths = @()
    foreach ($CheckPath in $CheckPaths) {
        $CurPath = Join-Path -Path "$E10BasePath" -ChildPath "$CheckPath";
        if (Test-Path -Path "$CurPath") {
            $Paths += $CurPath;
        }
    }
    return $Paths;
}

# 取得E10控制中心地址
function Get-ControlCenterAddress() {
    $ResultIp = "$DefaultCenterIp";
    $ResultPort = "$DefaultCenterPort";
    $LicenseConfigPath = $E10BasePath + "\LicenseCenter\Digiwin.Mars.License.Management.exe.Config";
    $ApConfigPath = $E10BasePath + "\Server\Control\AccountSetsConfiguration.xml";
    if (Test-Path -Path $LicenseConfigPath) {
        # 是授权主机
        $AppSettings = (Select-Xml -Path "$LicenseConfigPath" -XPath "/configuration/appSettings/add") | Select-Object -ExpandProperty Node;
        if ($AppSettings) {
            foreach ($Setting in $AppSettings) {
                if ("LicenseCenterIP" -eq $Setting.key) {
                    $ResultIp = $Setting.value;
                } elseIf ("LicenseCenterPort" -eq $Setting.key) {
                    $ResultPort = $Setting.value;
                }
            }
        }
    } elseIf (Test-Path -Path $ApConfigPath) {
        # 是AP主机
        $AppSettings = (Select-Xml -Path "$ApConfigPath" -XPath "/AccountSetsConfiguration/LicenseCenter/LicenseCenter") | Select-Object -ExpandProperty Node;
        if ($AppSettings) {
            foreach ($Setting in $AppSettings) {
                $BindToIP = $AppSettings.BindToIP;
                if ($BindToIP) {
                    $ResultIp = $BindToIP;
                }
                $ControlCenterPort = $AppSettings.ControlCenterPort;
                if ($ControlCenterPort) {
                    $ResultPort = $ControlCenterPort;
                }
            }
        }
    }
    return ("$ResultIp" + ":" + "$ResultPort");
}

# 主入口
function Main-Function() {
    $CurUrl = Get-ControlCenterAddress;
    $DllBasePaths = Get-CurrentSystemDllPaths;
    $Now = (Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff");
    if (-not($DllBasePaths)) {
        return "{""IsSuccess"":false,""ErrMsg"":""E10 system path not exist"",""Timestamp"":""" + $Now + """}";
    }
    $ToolFullPath = "";
    $NotExistPaths = @();
    foreach ($DllBasePath in $DllBasePaths) {
        $ToolFullPath = (Join-Path -Path "$DllBasePath" -ChildPath "$ToolExeName");
        if (Test-Path "$ToolFullPath") {
            break;
        }
        $NotExistPaths += ("$ToolFullPath".Replace('"', '\"').Replace('\','\\'));
        $ToolFullPath = "";
    }
    if (-not($ToolFullPath)) {
        $ErrMsg = "check paths: " + ($NotExistPaths -join ', ') + " not any tool exist";
        return "{""IsSuccess"":false,""ErrMsg"":""" + "$ErrMsg" + """,""Timestamp"":""" + $Now + """}";
    }
    try {
        # $DllBasePath
        $CurArgs = @(
                "-methodName",
                "GetSessionInfos",
                "-address",
                "$CurUrl"
            );
            Write-Host "aa    : $ToolFullPath"
            Write-Host "bb    : $CurArgs"
        return (Start-Process -FilePath "$ToolFullPath" -ArgumentList $CurArgs -NoNewWindow);
    } catch {
        $ErrMsg = ($_ | Out-String -Width 250);
        return "{""IsSuccess"":false,""ErrMsg"":""" + "$ErrMsg" + """, ""Timestamp"":""" + $Now + """}";
    }
}

# 运行主入口，并输出json字串
$Result = Main-Function;
Write-Host $Result;
# Write-Host (ConvertTo-Json -Compress $Result);

