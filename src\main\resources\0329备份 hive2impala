beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_13_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Items_add_Num') AS INT),CAST(get_json_object(model,'$.DataContent.BOM_add_Num') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  IncreaseInMaterials"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_27_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.ECNchange_num') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ECNReplaceNumber"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_3_8_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.ProductCategories'),CAST(get_json_object(model,'$.DataContent.InventoryTurnoverRate') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.CostofGoods') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.BeginningInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.EndingInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.AverageInventory') AS DECIMAL(15 ,2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  InventoryTurnoverProduct"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_12_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.RateOfMaterialPreparation') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.NumberOfPensSet') AS INT),CAST(get_json_object(model,'$.DataContent.NumOfNeedtoSet') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  RateOfPreparationLeadTime"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  Financialreplenishmentrate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_34_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Procurement_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_of_purchases') AS INT),CAST(get_json_object(model,'$.DataContent.Total_procurement_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProcurementCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_5_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Procurement_delivery_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Zhunjiao_Bishu') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_of_purchases') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DeliveryRateForProcurement"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_11_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Material_proportion') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Labor_proportion') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Processing_fees') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Production_costs1') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Production_costs2') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Production_costs3') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Production_costs4') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Production_costs5') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CostCompositionRatio"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_3_2_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.ProductCategories'),CAST(get_json_object(model,'$.DataContent.InventoryTurnoverRate') AS DECIMAL(15,2)),CAST(get_json_object(model,'$.DataContent.CostofGoods') AS DECIMAL(15,2)),CAST(get_json_object(model,'$.DataContent.BeginningInventory') AS DECIMAL(15,2)),CAST(get_json_object(model,'$.DataContent.EndingInventory') AS DECIMAL(15,2)),CAST(get_json_object(model,'$.DataContent.AverageInventory') AS DECIMAL(15,2)),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductInventoryTurnover"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_29_1 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Product_production_cycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Total_production_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductProductionCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_3_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.InventoryTurnoverRate') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.CostofGoods') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.BeginningInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.EndingInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.AverageInventory') AS DECIMAL(15 ,2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  InventoryTurnover"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_33_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Num_timely_deductions') AS INT),CAST(get_json_object(model,'$.DataContent.Num_timely_nodeductions') AS INT),CAST(get_json_object(model,'$.DataContent.Proportion_of_timely') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  DocumentDeductionOnTime"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_9_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Timely_deduction_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Num_timely_deductions') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DocumentDeductionTimely"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_27_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Increase_orders') AS INT),CAST(get_json_object(model,'$.DataContent.Increase_shipment') AS INT),CAST(get_json_object(model,'$.DataContent.No_order_shipment') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderIncrease"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_33_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Order_lead_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.Delivery_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderLeadTime"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_44_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.OrdertoWork') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.OrdertoWorkTime') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrdertoWork"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Order_delivery_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Zhunjiao_Bishu') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DeliveryRateOfOrders"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.SettlementCycle') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Calculateyear'),CAST(get_json_object(model,'$.DataContent.Last_change_date') AS timestamp),CAST(get_json_object(model,'$.DataContent.SettlementDays') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ModuleMonthlyDays"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_43_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Timely_completion_work') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  TimelyCompletionOfWork"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_21_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Start_work_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.OnTimeNum') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  StartWorkRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_18_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.CompleteOnTimeRate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.OnTimeNum') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CompleteOnTimeRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_40_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.InvoicingVolume') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  InvoicingVolume"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_50_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.CoverageRate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.TransferRequestsNum') AS INT),CAST(get_json_object(model,'$.DataContent.PurchaseRequestsNum') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  ProcurementSourcesRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_41_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Material_Issuance_Cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Issuance_total_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MaterialIssuanceCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_51_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.CoverageRate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Transferred_Work_Num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  ProductionSourceRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_28_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.LeadTimesRate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.NumberOfPensSet') AS INT),CAST(get_json_object(model,'$.DataContent.NumOfNeedtoSet') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  LeadTimesRate"
 beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_29_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.production_cycle') AS DECIMAL(15,2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Total_production_time') AS DECIMAL(15,2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductionCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_36_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Accounts_payable_rate') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DaysPayableOutstanding"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_35_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Receivable_turnover_rate') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ReceivableTurnoverRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_57_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.LedgerAccount'),get_json_object(model,'$.DataContent.AccountSubjectName'),CAST(get_json_object(model,'$.DataContent.TransactionNum') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  MISCAccountSubjects"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_56_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.ReasonCode'),get_json_object(model,'$.DataContent.ReasonCodeName'),CAST(get_json_object(model,'$.DataContent.TransactionNum') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  MISCReasonCode"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_2_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  DocumentReplenishmentRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_a1_2_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  DocumentQuantity"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_29_2 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.SemiFinishCycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Total_production_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SemiFinishProductProductionCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_15_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Missing_work_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Unreported_number') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MissingworkRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_16_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Missingrate_WorkHour') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Missingnum_WorkHour') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_items') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MissingrateOfWorkHour"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_20_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.MonthlyincreaseInpurchase') AS INT),CAST(get_json_object(model,'$.DataContent.MonthlyincreaseInreceipt') AS INT),CAST(get_json_object(model,'$.DataContent.Monthly_increase_In_inventory') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MonthlyIncreaseInPurchase"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_22_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Deviation_Rate_Purchase_Price') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Differences_in_procurement_prices') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.unit_price') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DeviationRatePurchasePrice"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_21_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.zero_unit_price_ratio') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.zero_unit_price_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PurchaseZeroUnitPriceRatio"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_13_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Purchase_quality_defect_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.NumberofDefects') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Received_quantity') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PurchaseQualityDefectRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_6_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Doc_category'),CAST(get_json_object(model,'$.DataContent.Procurement_delivery_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Zhunjiao_Bishu') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  ProcurementTypeDeliveryRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_46_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.order_change_num') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_change_orders') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_change') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  NumberOfOrderChanges"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_47_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.changes_covered_num') AS INT),CAST(get_json_object(model,'$.DataContent.Change_num') AS INT),CAST(get_json_object(model,'$.DataContent.Change_frequency') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.CoverageRate') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  OrderChangeCoverageRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_28_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Deviation_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Difference_between_order_price') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Evaluated_unit_price') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  DeviationRateBetweenOrderPrice"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_2_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Payment_verification_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.payment_verification_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.payment_verification_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PaymentVerificationCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Payment_approval_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Payment_approval_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Payment_approval_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PaymentApprovalCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_25_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.reporting_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.reporting_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  WorkOrderReportRateSingle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_24_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.reporting_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.reporting_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  WorkOrderReportRateBatch"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_22_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Work_Type'),get_json_object(model,'$.DataContent.title_name'),CAST(get_json_object(model,'$.DataContent.Start_work_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.OnTimeNum') AS INT),CAST(get_json_object(model,'$.DataContent.delay_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  StartRateOfWork"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_19_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Work_Type'),get_json_object(model,'$.DataContent.title_name'),CAST(get_json_object(model,'$.DataContent.CompleteOnTimeRate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.OnTimeNum') AS INT),CAST(get_json_object(model,'$.DataContent.delay_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  CompleteRateOfWork"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_23_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.rate_purchase_invoice') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.diff_purchase_invoice') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Purchase_price') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  RateBetweenInvoiceAndPurchase"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_29_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.rate_invoice_order') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.diff_invoice_order') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.order_price') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  RateBetweenInvoiceOrder"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_3_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Num_BankPay') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  NumOfBankPay"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_4_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Number_BillsPay') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  NumberOfBillsPay"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_5_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.TransNumOfBillsPay') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  TransactionOfBillsPay"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_42_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.production_yield') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.production_quantity') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.quantity_good_products') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductionYield"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_8_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Collect_verification_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CollectVerificationRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_9_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Collect_verification_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Write_off_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Write_off_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CollectVerificationCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_10_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.bank_receipts_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BankReceiptsNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_7_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BankIncomeRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_7_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Account_type'),get_json_object(model,'$.DataContent.currency'),CAST(get_json_object(model,'$.DataContent.accounts_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),CAST(
    CONCAT(
        SUBSTR(get_json_object(model,'$.DataContent.StartDate'), 1, 4), '-',
        SUBSTR(get_json_object(model,'$.DataContent.StartDate'), 5, 2), '-',
        SUBSTR(get_json_object(model,'$.DataContent.StartDate'), 7, 2)
    ) AS TIMESTAMP
) from  BankAccounts"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_8_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BankExpenseRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_31_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PayableReconciliationRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_3_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsPayableRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_1_1 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.SettlementCycle') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Calculateyear'),CAST(get_json_object(model,'$.DataContent.Last_change_date') AS timestamp),CAST(get_json_object(model,'$.DataContent.SettlementDays') AS DECIMAL(10, 2)),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsPayCloseCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_32_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsReceiveRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_11_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.bills_receive_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BillsReceiveNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_12_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Transaction_of_notes_receive') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  TransactionsOfNotesReceive"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_5_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsReceivableRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_39_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.overdue_accounts_pay') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.overdue_pay_num') AS INT),CAST(get_json_object(model,'$.DataContent.accounts_pay_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OverdueAccountsPayable"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_38_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.overdue_receive_rate') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.overdue_receive_num') AS INT),CAST(get_json_object(model,'$.DataContent.accounts_receive_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OverdueAccountsReceiveRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_6_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.advance_payment_period') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Prepaid_time') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Prepaid_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AdvancePaymentPeriod"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_3_4_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.ProductCategories'),CAST(get_json_object(model,'$.DataContent.InventoryTurnoverRate') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.CostofGoods') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.BeginningInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.EndingInventory') AS DECIMAL(15 ,2)),CAST(get_json_object(model,'$.DataContent.AverageInventory') AS DECIMAL(15 ,2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MaterialInventoryTurnoverDay"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_36_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.RateOfAudit') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BOMRateOfaudit"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_37_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BOMAuditcycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_52_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.RateOfAudit') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ECNRateOfaudit"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_32_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(10, 2)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(10, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ECNAuditcycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_3_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Order_change_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Change_num') AS INT),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  RateOfOrderChange"