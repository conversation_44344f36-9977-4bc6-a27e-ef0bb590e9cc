## 网安稽查


>网安稽查系统
>1. 做检测只是检测 资产吗？
>2. 资产和我们的设备的关系
>3. 计算分数的时候 对应的资产也要和我们的设备维度有关系，对应维度标签则是改资产的分数
>4. 资产分类、系统名称和我们现在的体系怎么对应
>5. 组织类、监控预警、国产化 这些都是做什么
>6. 检测逻辑和我们得指标分类、指标得分计算逻辑一样吗
### 用例图






### 活动图
```plantuml
@startuml
|企业运维服务云|
:用户访问网安稽查项目页面;
:点击【新增】按钮;
:选择模型名称;
note right: 资产模型名称：信息系统、设备、数据库、网站
:填写模型字段内容;
:选择实例;
:提交新建资产系统请求;

|后端Java服务|
:接收新建资产系统请求;
:验证输入数据;
:保存资产系统信息;

|DB|
:存储资产系统数据\n(系统名称/分类/安全等级/备案信息);

|后端Java服务|
:返回新建资产系统成功响应;

|企业运维服务云|
:显示新建资产系统成功;
:用户访问网安稽查记录页面;
:点击【新建检测】;
:选择参与分数计算的网安项目\n(信息系统/设备/数据库/网站);
:选择检测时间;
:提交新建检测请求;

|后端Java服务|
:接收新建检测请求;
:验证资产选择和检测时间;
:创建稽查记录;
:创建体检记录;
:创建稽查记录与参与计算来源数据关联;
:创建体检记录与参与计算来源数据关联;
|DB|
:存储稽查记录\n(核心系统名称/检测时间/资产关联);
:存储体检记录;
:存储查记录与参与计算来源数据关联;
:存储体检记录与参与计算来源数据关联;
|后端Java服务|
:返回新建检测成功响应;

|企业运维服务云|
:显示新建检测成功;
:点击【维护资产清单】;
:选择参与分数计算项目下的数据;
:更新资产清单;
:提交资产清单更新请求;

|后端Java服务|
:接收资产清单更新请求;
:更新稽查记录与参与计算来源数据关联;
:更新体检记录与参与计算来源数据关联;

|DB|
:存储更新后的资产清单;
:更新计算来源数据关联;

|后端Java服务|
:返回资产清单更新成功响应;

|企业运维服务云|
:显示资产清单更新成功;
:点击【检测评估】;
note right: 一键体检是前端触发，\n此检测应是后端批量计算，\n没有暂停按钮，只可以取消评估，\n修改一键体检记录状态
:触发分数计算;

|后端Java服务|
:接收检测评估请求;
:计算评估分数; 
note right: 根据一键体检的规则，计算每个资产的分数，\n新增计算指标分数。\n（实例对应指标分数求算每个指标下得平均分数）
|DB|
:存储评估分数;

|后端Java服务|
:返回评估分数;

|企业运维服务云|
:显示评估完成或分数;
:点击【生成报告】;

|后端Java服务|
:接收生成报告请求;
:查询该检测分数、明细详情;

|DB|
:存储生成检测报告记录;

|后端Java服务|
:返回检测报告记录和报告数据详情;

|企业运维服务云|
:显示检测报告;
@enduml
```

```mermaid
classDiagram
    class 体检实例aiops_exam_item_instance_score {
        + id: long
        + 体检记录ID: long
        + aiopsItem: string
        + aiopsItemId: string
        + 是否体检完成: boolean
        + 设备得分: decimal
        + 分类生成时间: datetime
        + 体检等级编号: string
    }

    class 体检设备指标得分aiops_exam_instance_index_score {
        + id: long
        + 体检实例id: long
        + 指标id: long
        + 得分: decimal
        + 标签值: string
        + 分数生成时间: datetime
        + 体检等级编号: string
    }

    class 体征实例分类得分aiops_exam_instance_index_type_score {
        + id: long
        + 体检设备id: long
        + 指标分类id: long
        + 得分: decimal
        + 分数生成时间: datetime
        + 分数等级编号: string
    }

    class 体检记录aiops_exam_record {
        + id: long
        + 体检id: long
        + sid: long
        + eid: long
        + serviceCode: string
        + customerName: string
        + customerFullName: string
        + examBeginTime: datetime
        + examEndTime: datetime
        + examStatus: string
        + examScore: decimal
        + userId: string
        + userName: string
    }
    
    class  aiops_exam_index_score~体检指标得分~{
        + id: long
        + 体检记录id: long
        + 指标id: long
        + 得分: decimal
        + 分数生成时间: datetime
        + 分数等级编号: string
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style aiops_exam_index_score fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5

    class  network_security_examination_records~网安稽查记录~{
        + id: long
        + sid: long
        + eid: long
        + 体检id: long
        + 体检记录id: long
        + serviceCode: string
        + customerName: string
        + customerFullName: string
        + 状态: string
        + 检查人: string
        + 检查时间: datetime
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_records fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5

    class  network_security_examination_records_source_data~网安稽查记录检测数据~{
        + id: long
        + 网安稽查记录Id: long
        + 网安稽查来源数据与实例映射Id: long
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_records_source_data fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5


    class  network_security_examination_source_data_instance_mapping~网安稽查来源数据与实例映射~{
        + id: long
        + 网安稽查项目模型mappingId: long
        + 来源数据主键id: long
        + 来源Code: string
        + 来源类型: string
        + aiId: long
        + aiopsItemId: string
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_source_data_instance_mapping fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5

   

    class status{
        <<enumeration>>
        评估中
        取消评估
        评估完成
    }
    style status fill:#f9f,stroke:#333,stroke-width:4px

    class  network_security_examination_project~网安稽查项目~ {
        + id: long
        + sid: long
        + eid: long
        + projectCode: string
        + projectName: string
        + 是否参与分数计算: boolean
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_project fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5


    class  network_security_examination_project_model_mapping~网安稽查项目模型mapping~{
        + id: long
        + 网安稽查项目Id: long
        + 数据来源类型: string
        + 来源名称: string
        + 来源Code: string
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_project_model_mapping fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5

    class sourceType~数据来源类型~{
        <<enumeration>>
        mysql
        starrocks
    }
    style sourceType fill:#f9f,stroke:#333,stroke-width:4px

    class sourceName~来源名称~{
        <<enumeration>>
        信息系统
        设备数据库
        设备
        网站
    }
    style sourceName fill:#f9f,stroke:#333,stroke-width:4px

    class  network_security_examination_records_report_records~网安稽查记录报告记录~{
        + id: long
        + sid: long
        + eid: long
        + 网安稽核记录Id: long
        + serviceCode: string
        + customerName: string
        + customerFullName: string
        + 状态(生成中 未发送 已发送): string
        + 报告生成人: string
        + 报告时间: date
        + 生成时间: datetime
        + 创建时间: datetime
        + 更新时间: datetime
    }
    style network_security_examination_records_report_records fill:#bbf,stroke:#f66,stroke-width:2px,color:#fff,stroke-dasharray: 5 5


%% 定义关联关系
    network_security_examination_project "1" -- "*" network_security_examination_project_model_mapping
    network_security_examination_project_model_mapping "1" -- "*" network_security_examination_source_data_instance_mapping
    network_security_examination_project_model_mapping .. sourceName
    network_security_examination_project_model_mapping .. sourceType
    network_security_examination_source_data_instance_mapping .. sourceType
    network_security_examination_records "1" -- "*" network_security_examination_records_report_records
    network_security_examination_records .. status
    network_security_examination_records_source_data <.. network_security_examination_source_data_instance_mapping
    network_security_examination_records "1" -- "1" 体检记录aiops_exam_record
    network_security_examination_records "1" -- "*" network_security_examination_records_source_data
    体检记录aiops_exam_record "1" -- "*" 体检实例aiops_exam_item_instance_score
    体检记录aiops_exam_record "1" -- "*" aiops_exam_index_score
    体检实例aiops_exam_item_instance_score "1" -- "*" 体检设备指标得分aiops_exam_instance_index_score
    体检实例aiops_exam_item_instance_score "1" -- "*" 体征实例分类得分aiops_exam_instance_index_type_score 
```
### 任务工时 38H
>1. 网安稽查项目查询、数据内置，提供接口支持修改、除参与计算分数的网安稽查项目数据CRUD 7H
>2. 参与计算分数的网安稽查项目模型创建 2H 模型保存（已有） 模型查询（已有）增加与实例管理数据2H 客户家实例查询2H 模型数据与实例数据关联保存2H
>3. 网安稽查记录查询(携带资产数据与检测数据与报告数据) 3H
>4. 新增网安稽查记录保存（支持更新网安稽查资产清单）4H 查询参与计算分数的网安稽查项目2H
>5. 检测评估api，由后端批量触发2H,修改原有计算分数api（增加体检记录判断） 1H 
>6. 计算逻辑新增指标单独计算逻辑2H
>7. 新增api修改网安稽查记录状态（支持一键体检暂停）2H
>8. 新增api根据稽查记录查询体检记录 查询体检分数 查询非参与分数计算网安稽核项目数据 查询预警数据 组成报告想要数据、查询报告记录 7H
