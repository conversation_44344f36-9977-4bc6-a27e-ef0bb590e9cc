package com.digiwin.escloud.aiouser.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 模组服务人员mapping 表 在同步模组合约时用到
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@TableName("module_staff_sync_mapping")
@ApiModel(value = "ModuleStaffSyncMapping对象", description = "模组服务人员mapping 表 在同步模组合约时用到")
public class ModuleStaffSyncMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("员工id")
    private Long staffId;

    @ApiModelProperty("模组code")
    private String moduleCode;

    @ApiModelProperty("员工姓名")
    private String staffName;

    @ApiModelProperty("模组id")
    private Long moduleId;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public String getStaffName() {
        return staffName;
    }

    public void setStaffName(String staffName) {
        this.staffName = staffName;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "ModuleStaffSyncMapping{" +
            "id = " + id +
            ", staffId = " + staffId +
            ", moduleCode = " + moduleCode +
            ", staffName = " + staffName +
            ", moduleId = " + moduleId +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
