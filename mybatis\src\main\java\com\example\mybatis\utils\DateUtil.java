package com.example.mybatis.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * 日期实用类
 */
public final class DateUtil {
    //region 常量

    /**
     * 时区日期T时间格式器(时区为+08:00等格式)
     */
    public final static DateTimeFormatter ZONED_ALL_DATE_T_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ZONED_ALL_DATE_T_TIME);
    /**
     * 时区日期T时间格式器
     */
    public final static DateTimeFormatter ZONED_DATE_T_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ZONED_DATE_T_TIME);
    /**
     * 日期T时间毫秒格式器
     */
    public final static DateTimeFormatter DATE_T_TIME_MIL_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.DATE_T_TIME_MIL);
    /**
     * 日期T时间格式器
     */
    public final static DateTimeFormatter DATE_T_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.DATE_T_TIME);
    /**
     * 时区日期时间格式器(时区为+08:00等格式)
     */
    public final static DateTimeFormatter ZONED_ALL_DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ZONED_ALL_DATE_TIME);
    /**
     * 时区日期时间格式器
     */
    public final static DateTimeFormatter ZONED_DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ZONED_DATE_TIME);
    /**
     * 日期时间毫秒格式器
     */
    public final static DateTimeFormatter DATE_TIME_MIL_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.DATE_TIME_MIL);
    /**
     * 日期时间格式器
     */
    public final static DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.DATE_TIME);
    /**
     * 日期格式器
     */
    public final static DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ONLY_DATE);
    /**
     * 时间毫秒格式器
     */
    public final static DateTimeFormatter TIME_MIL_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ONLY_TIME_MIL);
    /**
     * 时间格式器
     */
    public final static DateTimeFormatter TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.ONLY_TIME);
    /**
     * 无符号日期时间毫秒格式器
     */
    public final static DateTimeFormatter UNSIGNED_DATE_TIME_MIL_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.UNSIGNED_DATE_TIME_MIL);
    /**
     * 无符号日期时间格式器
     */
    public final static DateTimeFormatter UNSIGNED_DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.UNSIGNED_DATE_TIME);
    /**
     * 无符号日期格式器
     */
    public final static DateTimeFormatter UNSIGNED_DATE_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.UNSIGNED_DATE);
    /**
     * 无符号时间时分格式器
     */
    public final static DateTimeFormatter UNSIGNED_TIME_HOUR_MINUTE_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.UNSIGNED_TIME_HOUR_MINUTE);
    /**
     * 无符号时间时分格式器
     */
    public final static DateTimeFormatter DATE_TIME_HOUR_MINUTE_FORMATTER =
            DateTimeFormatter.ofPattern(DateTimePatternConstant.DATE_TIME_HOUR_MINUTE);

    /**
     * SimpleDateFormat日期格式器
     */
    public final static SimpleDateFormat SIMPLE_DATE_TIME_FORMAT = new SimpleDateFormat(DateTimePatternConstant.DATE_TIME);
    /**
     * 默认最小本地日期
     */
    public final static LocalDate DEFAULT_MIN_LOCAL_DATE = LocalDate.of(1970, 1, 1);
    /**
     * 时区名称
     */
    public final static String ZONED_NAME = DateTimePatternConstant.ZONED_NAME;
    /**
     * 时区Id
     */
    public final static ZoneId ZONE_ID = ZoneId.of(ZONED_NAME);

    //endregion
    //region 日期时区获取相关

    /**
     * 获取时区Id(默认使用ZONED_NAME时区)
     * @return 时区Id
     */
    public static ZoneId getZoneId() {
        return getZoneId(ZONED_NAME);
    }

    /**
     * 获取指定时区的时区Id
     * @param zonedName 指定时区名
     * @return 时区Id
     */
    public static ZoneId getZoneId(String zonedName) {
        return ZoneId.of(zonedName);
    }

    /**
     * 获取当前时区的日期时间
     * @return 含时区日期时间
     */
    public static ZonedDateTime getZonedNow() {
        return getZonedNow(ZONED_NAME);
    }

    /**
     * 获取特定时区的日期时间
     * @param zonedName 时区名称
     * @return 含时区日期时间
     */
    public static ZonedDateTime getZonedNow(String zonedName) {
        return ZonedDateTime.now(getZoneId(zonedName));
    }

    /**
     * 获取当前时区的日期
     * @return 含时区日期
     */
    public static ZonedDateTime getZonedToday() {
        return getZonedToday(ZONED_NAME);
    }

    /**
     * 获取当前时区的日期
     * @param zonedName 时区名称
     * @return 含时区日期
     */
    public static ZonedDateTime getZonedToday(String zonedName) {
        ZoneId zoneId = getZoneId(zonedName);
        return ZonedDateTime.now(zoneId).toLocalDate().atStartOfDay().atZone(zoneId);
    }

    /**
     * 获取当前时区日期时间字串
     * @return 日期时间字串
     */
    public static String getZonedNowString() {
        return getZonedNowString(ZONED_NAME);
    }

    /**
     * 获取特定时区的日期时间字串
     * @param zoneName 时区名称
     * @return 日期时间字串
     */
    public static String getZonedNowString(String zoneName) {
        return getZonedNow(zoneName).format(ZONED_DATE_T_TIME_FORMATTER);
    }

    /**
     * 获取特定样式当前时区日期时间字串
     * @param formatPattern 特定样式字串
     * @return 日期时间字串
     */
    public static String getNowFormatString(String formatPattern) {
        return getNowFormatString(DateTimeFormatter.ofPattern(formatPattern));
    }

    /**
     * 获取特定格式当前时区日期时间字串
     * @param formatter 特定格式
     * @return 日期时间字串
     */
    public static String getNowFormatString(DateTimeFormatter formatter) {
        return getNowFormatString(ZONED_NAME, formatter);
    }

    /**
     * 获取特定格式特定时区日期时间字串
     * @param zoneName 时区名称
     * @param formatter 特定格式
     * @return 日期时间字串
     */
    public static String getNowFormatString(String zoneName, DateTimeFormatter formatter) {
        return getZonedNow(zoneName).format(formatter);
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param localDate 本地日期
     * @param formatter 特定格式
     * @return 特定格式日期字串
     */
    public static String getSomeDateFormatString(LocalDate localDate, DateTimeFormatter formatter) {
        if (localDate == null) {
            return null;
        }
        return getSomeDateFormatString(localDate.atStartOfDay(), formatter);
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param localDate 本地日期
     * @param formatPattern 格式字串
     * @return 特定格式日期字串
     */
    public static String getSomeDateFormatString(LocalDate localDate, String formatPattern) {
        if (localDate == null || StringUtils.isBlank(formatPattern)) {
            return null;
        }
        return getSomeDateFormatString(localDate.atStartOfDay(), DateTimeFormatter.ofPattern(formatPattern));
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param localDateTime 本地日期时间
     * @param formatPattern 格式字串
     * @return 特定格式日期时间字串
     */
    public static String getSomeDateFormatString(LocalDateTime localDateTime, String formatPattern) {
        if (localDateTime == null || StringUtils.isBlank(formatPattern)) {
            return null;
        }
        return getSomeDateFormatString(localDateTime, DateTimeFormatter.ofPattern(formatPattern));
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param localDateTime 本地日期时间
     * @param formatter 特定格式
     * @return 特定格式日期时间字串
     */
    public static String getSomeDateFormatString(LocalDateTime localDateTime, DateTimeFormatter formatter) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.format(formatter);
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param zonedDateTime 含时区日期时间
     * @param formatPattern 格式字串
     * @return 特定格式日期时间字串
     */
    public static String getSomeDateFormatString(ZonedDateTime zonedDateTime, String formatPattern) {
        if (zonedDateTime == null || StringUtils.isBlank(formatPattern)) {
            return null;
        }
        return getSomeDateFormatString(zonedDateTime, DateTimeFormatter.ofPattern(formatPattern));
    }

    /**
     * 将特定日期对象转为特定格式字串
     * @param zonedDateTime 含时区日期时间
     * @param formatter 特定格式
     * @return 特定格式日期时间字串
     */
    public static String getSomeDateFormatString(ZonedDateTime zonedDateTime, DateTimeFormatter formatter) {
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.format(formatter);
    }

    /**
     * 获取本地日期时间
     * @return 本地日期时间
     */
    public static LocalDateTime getLocalNow() {
        return getZonedNow().toLocalDateTime();
    }

    /**
     * 获取特定时区本地日期时间
     * @param zonedName 时区名称
     * @return 本地日期时间
     */
    public static LocalDateTime getLocalNow(String zonedName) {
        return getZonedNow(zonedName).toLocalDateTime();
    }

    /**
     * 获取本地日期
     * @return 本地日期
     */
    public static LocalDate getLocalToday() {
        return getLocalToday(ZONED_NAME);
    }

    /**
     * 获取特定时区本地日期
     * @param zonedName 时区名称
     * @return 本地日期
     */
    public static LocalDate getLocalToday(String zonedName) {
        return getLocalNow(zonedName).toLocalDate();
    }

    /**
     * 获取日期
     * @return 日期
     */
    public static Date getDate() {
        return getDate(ZONED_NAME);
    }

    /**
     * 获取特定时区日期
     * @param zonedName 时区名称
     * @return 日期
     */
    public static Date getDate(String zonedName) {
        return Date.from(getZonedNow(zonedName).toInstant());
    }

    //endregion
    //region 时区转换相关

    /**
     * 时区转换
     * @param sourceDateTimeValue 来源值
     * @param targetZoneIdString 目标时区名称
     * @return 转换时区完毕的时间
     */
    public static ZonedDateTime timeZoneTransfer(ZonedDateTime sourceDateTimeValue, String targetZoneIdString) {
        return timeZoneTransfer(sourceDateTimeValue, ZoneId.of(targetZoneIdString));
    }

    /**
     * 时区转换
     * @param sourceDateTimeValue 来源值
     * @param targetZoneId 目标时区Id
     * @return 转换时区完毕的时间
     */
    public static ZonedDateTime timeZoneTransfer(ZonedDateTime sourceDateTimeValue, ZoneId targetZoneId) {
        return sourceDateTimeValue.withZoneSameInstant(targetZoneId);
    }

    /**
     * 时区转换
     * @param sourceDateTimeValue 来源值
     * @param sourceZoneIdString 来源时区名称
     * @param targetZoneIdString 目标时区名称
     * @return 转换时区完毕的时间
     */
    public static LocalDateTime timeZoneTransfer(LocalDateTime sourceDateTimeValue, String sourceZoneIdString, String targetZoneIdString) {
        return timeZoneTransfer(sourceDateTimeValue, ZoneId.of(sourceZoneIdString), ZoneId.of(targetZoneIdString));
    }

    /**
     * 时区转换
     * @param sourceDateTimeValue 来源值
     * @param sourceZoneId 来源时区Id
     * @param targetZoneId 目标时区Id
     * @return 转换时区完毕的时间
     */
    public static LocalDateTime timeZoneTransfer(LocalDateTime sourceDateTimeValue, ZoneId sourceZoneId, ZoneId targetZoneId) {
        return sourceDateTimeValue.atZone(sourceZoneId)
                .withZoneSameInstant(targetZoneId)
                .toLocalDateTime();
    }

    /**
     * 时区转换
     * @param sourceDateValue 来源值
     * @param sourceZoneId 来源时区Id
     * @param targetZoneId 目标时区Id
     * @return 转换时区完毕的时间
     */
    public static LocalDate timeZoneTransfer(LocalDate sourceDateValue, ZoneId sourceZoneId, ZoneId targetZoneId) {
        return sourceDateValue.atStartOfDay(sourceZoneId)
                .withZoneSameInstant(targetZoneId)
                .toLocalDate();
    }

    /**
     * 时区转换
     * @param sourceDateValue 来源值
     * @param sourceZoneIdString 来源时区名称
     * @param targetZoneIdString 目标时区名称
     * @return 转换时区完毕的时间
     */
    public static LocalDate timeZoneTransfer(LocalDate sourceDateValue, String sourceZoneIdString, String targetZoneIdString) {
        return timeZoneTransfer(sourceDateValue, ZoneId.of(sourceZoneIdString), ZoneId.of(targetZoneIdString));
    }

    //endregion
    //region 字串转日期相关

    /**
     * 尝试将字串转换为时区日期时间
     * @param dateTimeString 日期时间字串
     * @return 时区日期时间
     */
    public static Optional<ZonedDateTime> tryParseZonedDateTime(String dateTimeString) {
        return tryParseZonedDateTime(dateTimeString, getFormatter(dateTimeString, ZONED_DATE_T_TIME_FORMATTER));
    }

    private static DateTimeFormatter getFormatter(String dateTimeString, DateTimeFormatter defaultFormatter) {
        if (StringUtils.isBlank(dateTimeString)) {
            return defaultFormatter;
        }
        char tenChar;
        dateTimeString = dateTimeString.trim();
        int length = dateTimeString.length();
        if (length == 10) {
            return DATE_FORMATTER;
        } else if (length == 19) {
            tenChar = dateTimeString.charAt(10);
            if (tenChar == ' ') {
                return DATE_TIME_FORMATTER;
            } else {
                return DATE_T_TIME_FORMATTER;
            }
        } else if (length == 23) {
            tenChar = dateTimeString.charAt(10);
            if (tenChar == ' ') {
                return DATE_TIME_MIL_FORMATTER;
            } else {
                return DATE_T_TIME_MIL_FORMATTER;
            }
        } else if (length > 23) {
            return ZONED_DATE_T_TIME_FORMATTER;
        } else {
            return defaultFormatter;
        }
    }

    /**
     * 尝试将特定格式字串转换为时区日期时间
     * @param dateTimeString 日期时间字串
     * @param dateTimeFormatter 日期时间格式器
     * @return 时区日期时间
     */
    public static Optional<ZonedDateTime> tryParseZonedDateTime(String dateTimeString,
                                                                DateTimeFormatter dateTimeFormatter) {
        try {
            if (checkDateOnly(dateTimeString)) {
                return tryParseLocalDate(dateTimeString, dateTimeFormatter)
                        .map(localDate -> localDate.atStartOfDay(ZONE_ID));
            }
            return Optional.of(ZonedDateTime.parse(dateTimeString, dateTimeFormatter));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    private static boolean checkDateOnly(String value) {
        return StringUtils.isBlank(value) || value.length() <= 10;
    }

    /**
     * 尝试将特定格式字串转换为本地日期时间
     * @param dateTimeString 日期时间字串
     * @return 本地日期时间
     */
    public static Optional<LocalDateTime> tryParseLocalDateTime(String dateTimeString) {
        return tryParseLocalDateTime(dateTimeString, getFormatter(dateTimeString, DATE_TIME_FORMATTER));
    }

    /**
     * 尝试将特定格式字串转换为本地日期时间
     * @param dateTimeString 日期时间字串
     * @return 本地日期时间
     */
    public static Optional<LocalDateTime> tryParseLocalDateTime(String dateTimeString,
                                                                DateTimeFormatter dateTimeFormatter) {
        try {
            if (checkDateOnly(dateTimeString)) {
                return tryParseLocalDate(dateTimeString, dateTimeFormatter).map(LocalDate::atStartOfDay);
            }
            return Optional.of(LocalDateTime.parse(dateTimeString, dateTimeFormatter));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 尝试将特定格式字串转换为本地日期
     * @param dateString 日期字串
     * @return 本地日期时间
     */
    public static Optional<LocalDate> tryParseLocalDate(String dateString) {
        return tryParseLocalDate(dateString, getFormatter(dateString, DATE_FORMATTER));
    }

    /**
     * 尝试将特定格式字串转换为本地日期
     * @param dateString 日期字串
     * @return 本地日期时间
     */
    public static Optional<LocalDate> tryParseLocalDate(String dateString, DateTimeFormatter dateTimeFormatter) {
        try {
            return Optional.of(LocalDate.parse(dateString, dateTimeFormatter));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 尝试将特定格式字串转换为本地时间
     * @param timeString 时间字串
     * @return 本地日期时间
     */
    public static Optional<LocalDate> tryParseLocalTime(String timeString) {
        return tryParseLocalDate(timeString, getFormatter(timeString, TIME_FORMATTER));
    }

    /**
     * 尝试将特定格式字串转换为本地时间
     * @param timeString 时间字串
     * @return 本地时间
     */
    public static Optional<LocalTime> tryParseLocalTime(String timeString, DateTimeFormatter dateTimeFormatter) {
        try {
            return Optional.of(LocalTime.parse(timeString, dateTimeFormatter));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 尝试将特定格式字串转换为日期
     * @param dateTimeString 日期时间字串
     * @return 日期
     */
    public static Optional<Date> tryParseDate(String dateTimeString, DateTimeFormatter dateTimeFormatter) {
        return tryParseLocalDateTime(dateTimeString, dateTimeFormatter).map(DateUtil::parseToDate);
    }

    //endregion
    //region 日期格式通转相关

    /**
     * 整理任何字串成特定格式日期时间字串
     * @param source 来源字串
     * @param defaultValue 默认值
     * @param isMillis 是否为毫秒值(true:将直接透过数值转换成日期时间；false:会认为是特殊的无符号日期时间格式)
     * @param dateTimeFormatter 特定格式(为空时认定转换为millis)
     * @param useDefaultValueCallback 使用默认值时的回调(用来写日志等处理)
     * @return 整理完成的日期格式字串
     */
    public static String tidyAnyStringToDateTimeFormat(String source, ZonedDateTime defaultValue,
                                                       boolean isMillis, DateTimeFormatter dateTimeFormatter,
                                                       Consumer<String> useDefaultValueCallback) {
        ZonedDateTime result = parseAnyStringToZonedDateTime(source, defaultValue, isMillis, useDefaultValueCallback);
        if (dateTimeFormatter == null) {
            return LongUtil.safeToString(result.toInstant().toEpochMilli());
        }
        return result.format(dateTimeFormatter);
    }

    /**
     * 整理任何字串成时区日期时间
     * @param source 来源字串
     * @param defaultValue 默认值
     * @param isMillis 是否为毫秒值(true:将直接透过数值转换成日期时间；false:会认为是特殊的无符号日期时间格式)
     * @param useDefaultValueCallback 使用默认值时的回调(用来写日志等处理)
     * @return 整理完成的日期格式字串
     */
    public static ZonedDateTime parseAnyStringToZonedDateTime(String source, ZonedDateTime defaultValue,
                                                              boolean isMillis, Consumer<String> useDefaultValueCallback) {
        if (StringUtils.isBlank(source)) {
            if (useDefaultValueCallback != null) {
                useDefaultValueCallback.accept(source);
            }
            return defaultValue;
        }
        /** 需要处理的格式
         * yyyy-MM-dd'T'HH:mm:ss.SSSZ ex:2022-09-20T09:30:00.123+08:00 -> 29，包含T
         * yyyy-MM-dd'T'HH:mm:ss.SSSZ ex:2022-09-20T09:30:00.123+0800  -> 28，包含T
         * yyyy-MM-dd'T'HH:mm:ss.SSS  ex:2022-09-20T09:30:00.123       -> 23，包含T
         * yyyy-MM-dd'T'HH:mm:ss      ex:2022-09-20T09:30:00           -> 19，包含T
         *
         * yyyy-MM-dd HH:mm:ss.SSSZ   ex:2022-09-20 09:30:00.123+08:00 -> 29
         * yyyy-MM-dd HH:mm:ss.SSSZ   ex:2022-09-20 09:30:00.123+0800  -> 28
         * yyyy-MM-dd HH:mm:ss.SSS    ex:2022-09-20 09:30:00.123       -> 23
         * yyyy-MM-dd HH:mm:ss        ex:2022-09-20 09:30:00           -> 19
         *
         * yyyy-MM-dd                 ex:2022-09-20                    -> 10
         * HH:mm:ss.SSS               ex:09:30:00.123                  -> 12
         * HH:mm:ss                   ex:09:30:00                      -> 8，包含冒号
         *
         * (实测下来不支持)yyyyMMddHHmmssSSS          ex:20220920093000123             -> 17
         * yyyyMMddHHmmss             ex:20220920093000                -> 14
         * yyyyMMdd                   ex:20220930                      -> 8
         *
         * allLong                    ex:1663637798505                 -> 13 0~Long.MaxValue
         */
        Optional<ZonedDateTime> optZdt;
        Optional<LocalDateTime> optLdt;
        Optional<LocalDate> optLd;
        Optional<LocalTime> optLt;
        int length = source.length();
        //区分全数字或包含符号的内容
        boolean isAllNumbers = source.matches("^-?[0-9]+");
        if (isAllNumbers) {
            if (isMillis) {
                Optional<Long> optLong = LongUtil.tryParseLongByString(source);
                if (optLong.isPresent()) {
                    return Instant.ofEpochMilli(optLong.get()).atZone(ZONE_ID);
                }
                if (useDefaultValueCallback != null) {
                    useDefaultValueCallback.accept(source);
                }
                return defaultValue;
            }
            switch (length) {
                case 17:
                    //(实测下来不支持)yyyyMMddHHmmssSSS          ex:20220920093000123             -> 17
                    optLdt = tryParseLocalDateTime(source, UNSIGNED_DATE_TIME_MIL_FORMATTER);
                    if (optLdt.isPresent()) {
                        return parseToZonedDateTime(optLdt.get());
                    }
                    break;
                case 14:
                    //yyyyMMddHHmmss             ex:20220920093000                -> 14
                    optLdt = tryParseLocalDateTime(source, UNSIGNED_DATE_TIME_FORMATTER);
                    if (optLdt.isPresent()) {
                        return parseToZonedDateTime(optLdt.get());
                    }
                    break;
                case 8:
                    //yyyyMMdd                   ex:20220930                      -> 8
                    optLd = tryParseLocalDate(source, UNSIGNED_DATE_FORMATTER);
                    if (optLd.isPresent()) {
                        return parseToZonedDateTime(optLd.get());
                    }
                    break;
            }
        } else {
            switch (length) {
                case 29:
                    //检查是否包含T
                    if (source.indexOf('T') > 0) {
                        //yyyy-MM-dd'T'HH:mm:ss.SSSZ ex:2022-09-20T09:30:00.123+08:00 -> 29，包含T
                        optZdt = tryParseZonedDateTime(source, ZONED_ALL_DATE_T_TIME_FORMATTER);
                        if (optZdt.isPresent()) {
                            return optZdt.get();
                        }
                        break;
                    }
                    //yyyy-MM-dd HH:mm:ss.SSSZ   ex:2022-09-20 09:30:00.123+08:00 -> 29
                    optZdt = tryParseZonedDateTime(source, ZONED_ALL_DATE_TIME_FORMATTER);
                    if (optZdt.isPresent()) {
                        return optZdt.get();
                    }
                    break;
                case 28:
                    //检查是否包含T
                    if (source.indexOf('T') > 0) {
                        //yyyy-MM-dd'T'HH:mm:ss.SSSZ ex:2022-09-20T09:30:00.123+0800  -> 28，包含T
                        optZdt = tryParseZonedDateTime(source, ZONED_DATE_T_TIME_FORMATTER);
                        if (optZdt.isPresent()) {
                            return optZdt.get();
                        }
                        break;
                    }
                    //yyyy-MM-dd HH:mm:ss.SSSZ   ex:2022-09-20 09:30:00.123+0800  -> 28
                    optZdt = tryParseZonedDateTime(source, ZONED_DATE_TIME_FORMATTER);
                    if (optZdt.isPresent()) {
                        return optZdt.get();
                    }
                    break;
                case 23:
                    //检查是否包含T
                    if (source.indexOf('T') > 0) {
                        //yyyy-MM-dd'T'HH:mm:ss.SSS  ex:2022-09-20T09:30:00.123       -> 23，包含T
                        optLdt = tryParseLocalDateTime(source, DATE_T_TIME_MIL_FORMATTER);
                        if (optLdt.isPresent()) {
                            return parseToZonedDateTime(optLdt.get());
                        }
                        break;
                    }
                    //yyyy-MM-dd HH:mm:ss.SSS    ex:2022-09-20 09:30:00.123       -> 23
                    optLdt = tryParseLocalDateTime(source, DATE_TIME_MIL_FORMATTER);
                    if (optLdt.isPresent()) {
                        return parseToZonedDateTime(optLdt.get());
                    }
                    break;
                case 19:
                    //检查是否包含T
                    if (source.indexOf('T') > 0) {
                        //yyyy-MM-dd'T'HH:mm:ss      ex:2022-09-20T09:30:00           -> 19，包含T
                        optLdt = tryParseLocalDateTime(source, DATE_T_TIME_FORMATTER);
                        if (optLdt.isPresent()) {
                            return parseToZonedDateTime(optLdt.get());
                        }
                        break;
                    }
                    //yyyy-MM-dd HH:mm:ss        ex:2022-09-20 09:30:00           -> 19
                    optLdt = tryParseLocalDateTime(source, DATE_TIME_FORMATTER);
                    if (optLdt.isPresent()) {
                        return parseToZonedDateTime(optLdt.get());
                    }
                    break;
                case 12:
                    // HH:mm:ss.SSS               ex:09:30:00.123                  -> 12
                    optLt = tryParseLocalTime(source, TIME_MIL_FORMATTER);
                    if (optLt.isPresent()) {
                        return parseToZonedDateTime(optLt.get());
                    }
                    break;
                case 10:
                    // yyyy-MM-dd                 ex:2022-09-20                    -> 10
                    optLd = tryParseLocalDate(source);
                    if (optLd.isPresent()) {
                        return parseToZonedDateTime(optLd.get());
                    }
                    break;
                case 8:
                    // HH:mm:ss                   ex:09:30:00                      -> 8，包含冒号
                    optLt = tryParseLocalTime(source, TIME_FORMATTER);
                    if (optLt.isPresent()) {
                        return parseToZonedDateTime(optLt.get());
                    }
                    break;
            }
        }
        if (useDefaultValueCallback != null) {
            useDefaultValueCallback.accept(source);
        }
        return defaultValue;
    }

    //endregion
    //region 日期类型转换相关

    /**
     * 转换日期
     * @param localDate 本地日期
     * @return 日期值
     */
    public static Date parseToDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return parseToDate(localDate.atStartOfDay());
    }

    /**
     * 转换日期
     * @param localDateTime 本地日期时间
     * @return 日期值
     */
    public static Date parseToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return parseToDate(localDateTime.atZone(getZoneId()));
    }

    /**
     * 转换日期
     * @param zonedDateTime 时区日期时间
     * @return 日期值
     */
    public static Date parseToDate(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return Date.from(zonedDateTime.toInstant());
    }

    /**
     * 转换本地日期时间
     * @param date 日期
     * @return 本地日期时间
     */
    public static LocalDateTime parseToLocalDateTime(Date date) {
        return parseToLocalDateTime(date, ZONE_ID);
    }

    /**
     * 转换本地日期时间
     * @param date 日期
     * @param zoneId 时区Id
     * @return 本地日期时间
     */
    public static LocalDateTime parseToLocalDateTime(Date date, ZoneId zoneId) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(zoneId).toLocalDateTime();
    }

    /**
     * 转换本地日期时间
     * @param localDate 本地日期
     * @return 本地日期时间
     */
    public static LocalDateTime parseToLocalDateTime(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        return localDate.atStartOfDay();
    }

    /**
     * 转换本地日期时间
     * @param zonedDateTime 时区日期时间
     * @return 本地日期时间
     */
    public static LocalDateTime parseToLocalDateTime(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.toLocalDateTime();
    }

    /**
     * 转换本地日期
     * @param date 日期
     * @return 本地日期
     */
    public static LocalDate parseToLocalDate(Date date) {
        return parseToLocalDate(date, ZONE_ID);
    }

    /**
     * 转换本地日期
     * @param date 日期
     * @param zoneId 时区Id
     * @return 本地日期
     */
    public static LocalDate parseToLocalDate(Date date, ZoneId zoneId) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(zoneId).toLocalDate();
    }

    /**
     * 转换本地日期
     * @param localDateTime 本地日期时间
     * @return 本地日期
     */
    public static LocalDate parseToLocalDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toLocalDate();
    }

    /**
     * 转换本地日期
     * @param zonedDateTime 时区日期时间
     * @return 本地日期
     */
    public static LocalDate parseToLocalDate(ZonedDateTime zonedDateTime) {
        if (zonedDateTime == null) {
            return null;
        }
        return zonedDateTime.toLocalDate();
    }

    /**
     * 转换时区日期时间
     * @param date 日期
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(Date date) {
        return parseToZonedDateTime(date, ZONE_ID);
    }

    /**
     * 转换时区日期时间
     * @param date 日期
     * @param zoneId 时区Id
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(Date date, ZoneId zoneId) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(zoneId);
    }

    /**
     * 转换时区日期时间
     * @param localTime 本地时间
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalTime localTime) {
        return parseToZonedDateTime(DEFAULT_MIN_LOCAL_DATE, localTime, ZONE_ID);
    }

    /**
     * 转换时区日期时间
     * @param localDate 本地日期
     * @param localTime 本地时间
     * @param zoneId 时区Id
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalDate localDate, LocalTime localTime, ZoneId zoneId) {
        if (localTime == null) {
            return null;
        }
        if (localDate == null) {
            localDate = DEFAULT_MIN_LOCAL_DATE;
        }
        return localTime.atDate(localDate).atZone(zoneId);
    }

    /**
     * 转换时区日期时间
     * @param localDate 本地日期
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalDate localDate) {
        return parseToZonedDateTime(localDate, ZONE_ID);
    }

    /**
     * 转换时区日期时间
     * @param localDate 本地日期
     * @param zoneId 时区Id
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalDate localDate, ZoneId zoneId) {
        if (localDate == null) {
            return null;
        }
        return localDate.atStartOfDay(zoneId);
    }

    /**
     * 转换时区日期时间
     * @param localDateTime 本地日期时间
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalDateTime localDateTime) {
        return parseToZonedDateTime(localDateTime, ZONE_ID);
    }

    /**
     * 转换时区日期时间
     * @param localDateTime 本地日期时间
     * @param zoneId 时区Id
     * @return 时区日期时间
     */
    public static ZonedDateTime parseToZonedDateTime(LocalDateTime localDateTime, ZoneId zoneId) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atZone(zoneId);
    }

    //endregion

    /**
     * 获取当前时间的几天后的日期
     */
    public static Date GetDateAfterDays(int afterdays) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_YEAR, afterdays);
        return calendar.getTime();
    }

    public static Long localDateTime2Timestamp(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault(); // 可以修改为你所需的时区
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        return zonedDateTime.toEpochSecond();
    }

    public static Long localDateTime2MaxLocalDateTime(LocalDateTime localDateTime) {
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = endOfDay.atZone(zoneId);
        return zonedDateTime.toEpochSecond();
    }

}
