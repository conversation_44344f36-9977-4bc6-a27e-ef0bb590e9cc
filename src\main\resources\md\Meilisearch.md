# Meilisearch docker安装

### 1. 安装 meilisearch 和 meilisearch-ui，准备 docker-compose.yml 文件
```yaml
services:
  meilisearch:
    image: getmeili/meilisearch:v1.12
    ports:
      - "7700:7700"
    volumes:
      - meili_data:/meili_data
    environment:
      - MEILI_MASTER_KEY=123456
    networks:
      - nodebb-net

  meilisearch-ui:
    image: riccoxie/meilisearch-ui:latest
    container_name: meilisearch-ui
    restart: on-failure
    ports:
      - "24900:24900"
    networks:
      - nodebb-net
volumes:
  meili_data:
networks:
  nodebb-net: 
```
### 2. 执行以下命令启动服务：`docker compose up -d`




