$serviceName = "Digiwin.E10.APService"
$service = Get-CimInstance -Class Win32_Service -Filter "Name='$serviceName'"

$result = @{
    e10_version = ""
    e10_pfversion = ""
    errmsg = ""
}

if ($service) {
    $exePath = $service.PathName.Trim('"')
    $directory = Split-Path -Path $exePath -Parent
    Write-Host $directory
    if ($directory -match '^([a-zA-Z]:\\[^\\]+)') {
        $rootDir = $matches[1]
        Write-Host $directory
        Write-Host $rootDir
        $newPath = Join-Path -Path $rootDir -ChildPath "Server"
        $newPath = Join-Path -Path $newPath -ChildPath "Application"
        Write-Host $newPath
        $versionFilePath = Join-Path -Path $newPath -ChildPath "Version.xml"

        if (Test-Path $versionFilePath) {
            [xml]$xmlContent = Get-Content -Path $versionFilePath
            $versionValue = $xmlContent.SelectSingleNode("//Version").InnerText
            $pfVersionValue = $xmlContent.SelectSingleNode("//PFVersion").InnerText

            $result.e10_version = $versionValue
            $result.e10_pfversion = $pfVersionValue
        }
    } else {
        $result.errmsg = "Unable to resolve service path."
    }
} else {
    $result.errmsg = "No service found with service name: $serviceName"
}

$result | ConvertTo-Json -Depth 3