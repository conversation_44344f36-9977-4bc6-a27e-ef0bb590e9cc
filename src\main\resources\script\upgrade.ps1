
function TokenGet {

    $payload = @{
        auth = @{
            passwordCredentials = @{
                username = 'admin'
                password = '6f66e437d3d7bdcab50e39698eac9c62a61fa576aa43c40e3a23ca029db1bc37f921fc22c6748ff123d91c311fa3e1fa15c235054f7784977068be4f1b409a4cbf231149de75a8045f70848ba277845b84c27b2bb94f9e346631a54e2413a2b933b6016525c7d5ff0ba4dea42a07206c951886ce158b4c5acee3992ef7cb93111c885cf3919d5c2a81c660354f24b4144cdfafcb67a8aa8e823a134c222ed320c4752a91fc6ed95bd81f334f3a5b4ac47cca6ef295eba49f8281f554b1b7118d2b00113dceac85bf8b39ede39dd0717afa4772fd71674baadb56949b4f7273a66d04086d10390cc4b19e9fb0b20f162e294bccbedd717ad5921f5c09b2ce806a'
            }
        }
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri https://************:4435/janus/authenticate -Method POST -ContentType "application/json" -Body $jsonBody -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.access.token.id
    } else {
        Write-Output $response
    }
}

function QueryResource {
    # 调用函数并获取返回值
    $token = TokenGet
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
        $response = Invoke-WebRequest -Uri "https://************:4435/janus/20180725/azs" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck
        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            return $jsonObject.data.data
        }
}

function QueryDetail {
    # 调用函数并获取返回值
    $token = TokenGet
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $array=QueryResource
    $myArray = New-Object System.Collections.ArrayList
    foreach ($item in $array) {
        $rid =  $item.id
        $response = Invoke-WebRequest -Uri "https://************:4435/janus/20180725/overview?az_id=$rid" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck
        if ($response.StatusCode -eq 200) {
            $obj = New-Object -TypeName PSObject
            $obj | Add-Member -MemberType NoteProperty -Name "az_id" -Value $item.id
            $jsonObject = $response.Content | ConvertFrom-Json

            foreach($d in $jsonObject.data.physical_resources){
                if ($d.name -eq "storage")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "storage_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "storage_used" -Value $d.used
                }
                if ($d.name -eq "cpu")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "cpu_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "cpu_used" -Value $d.used
                }
                if ($d.name -eq "memory")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "memory_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "memory_used" -Value $d.used
                }
            }

            foreach($d in $jsonObject.data.virtual_resources){
                if ($d.name -eq "CPU")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_allocated" -Value $d.allocated
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_occupied" -Value $d.occupied
                }
                if ($d.name -eq "内存")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_allocated" -Value $d.allocated
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_occupied" -Value $d.occupied
                }

            }

            $obj | Add-Member -MemberType NoteProperty -Name "server_running_count" -Value $jsonObject.data.server.running_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_offline_count" -Value $jsonObject.data.server.offline_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_total" -Value $jsonObject.data.server.total
            $obj | Add-Member -MemberType NoteProperty -Name "server_alarm_count" -Value $jsonObject.data.server.alarm_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_error_count" -Value $jsonObject.data.server.error_count

            $obj | Add-Member -MemberType NoteProperty -Name "host_error_count" -Value $jsonObject.data.host.offline_count
            $obj | Add-Member -MemberType NoteProperty -Name "host_total" -Value $jsonObject.data.host.total
            $obj | Add-Member -MemberType NoteProperty -Name "host_alarm_count" -Value $jsonObject.data.host.alarm_count
            $obj | Add-Member -MemberType NoteProperty -Name "host_online_count" -Value $jsonObject.data.host.online_count
            $myArray.Add($obj)
        }
    }
    return $myArray

}
$aa=QueryDetail
$result = $aa | Select-Object -ExcludeProperty "PS*"
$result = $result | ConvertTo-Json
Write-Output $result
