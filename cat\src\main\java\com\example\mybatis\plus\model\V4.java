package com.example.mybatis.plus.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;
@TableName("es_customerservice_v4_1_bak_20240729")
@Data
public class V4 {
    private Long eId;
    private Date updateDate;
    private String SalesContact;
    private String CustomerCode;
    private String grp_dpt_id;
    private String grp_dpt_name;
    private String grp_dpt_manager;
    private String bus_dpt_id;
    private String bus_dpt_name;
    private String bus_dpt_manager;
    private String dpt_id;
    private String dpt_name;
    private String dpt_manager;
    private String Sales;
    private String CustomerServiceCode;
    private String CustomerName;
    private String CustomerFullNameCH;
    private String CustomerFullNameEN;
    private String another_name;
    private String current_valid_status;
    private String t100_cust_id;
    private String taxNo;
    private String contacts;
    private String tenantTelephone;
    private String tenantEmail;
    private String address;
    private String __version__;
    private String grp_dpt_manager_contact;
    private String bus_dpt_manager_contact;
    private String bus_dpt_win_name;
    private String bus_dpt_win_contact;
    private String dpt_manager_contact;
    private String valueAddedConsultant;
    private String valueAddedConsultantEmail;
    private String IndustryCode;
}
