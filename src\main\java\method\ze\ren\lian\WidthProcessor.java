package method.ze.ren.lian;

public class WidthProcessor implements Processor{
    @Override
    public boolean process(Product product, ProcessorChain processorChain) {
        if (product.getWidth() < 4 && product.getWidth() > 1) {
            System.out.println("宽度合格");
           return processorChain.process(product, processorChain);
        }
        System.out.println("宽度不 合格");
        return false;
    }
}
