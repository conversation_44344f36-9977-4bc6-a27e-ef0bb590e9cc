CREATE EXTERNAL TABLE `table_tmp` (
   `customerCode` varchar(255) DEFAULT NULL,
  `serviceCode` varchar(255) DEFAULT NULL,
  `product` varchar(255) DEFAULT NULL,
  `mouth` varchar(255) DEFAULT NULL,
  `REC` int(255) DEFAULT NULL,
  `REC_D` int(255) DEFAULT NULL,
  `PAY` int(255) DEFAULT NULL,
  `PAY_D` int(255) DEFAULT NULL,
  `eid` bigint(11) DEFAULT NULL
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "table_tmp"
);

CREATE TABLE `InvoiceIssuanceNum_bak_20241212_1` (
                                      `eid` varchar(65533) NOT NULL COMMENT "",
                                      `<PERSON><PERSON><PERSON>h` varchar(65533) NOT NULL COMMENT "",
                                      `IndicatorNumber` varchar(65533) NOT NULL COMMENT "",
                                      `product_line` varchar(65533) NOT NULL COMMENT "",
                                      `InvoiceCountType` varchar(65533) NOT NULL COMMENT "",
                                      `deviceId` varchar(65533) NULL COMMENT "",
                                      `ID` varchar(65533) NULL COMMENT "",
                                      `collectedTime` varchar(65533) NULL COMMENT "",
                                      `collectConfigId` varchar(65533) NULL COMMENT "",
                                      `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                      `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
                                      `aiId` varchar(65533) NULL COMMENT "",
                                      `aiopsItem` varchar(65533) NULL COMMENT "",
                                      `flumeTimestamp` varchar(65533) NULL COMMENT "",
                                      `PERIOD_COUNT` int(11) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`, `YearMonth`, `IndicatorNumber`, `product_line`, `InvoiceCountType`)
DISTRIBUTED BY HASH(`eid`, `YearMonth`, `IndicatorNumber`, `product_line`, `InvoiceCountType`)
PROPERTIES (
"replication_num" = "3",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);

insert into InvoiceIssuanceNum_bak_20241212_1 select * from InvoiceIssuanceNum;
insert into InvoiceIssuanceNum
select * from (
                  select eid,mouth,'IMPORT',case
                                                when product = '37' then 'E10'
                                                when product = '06' then 'TOPGP'
                                                when product = '100' then 'T100'
                                                when product = '165' then 'E10'
                                                when product = '164' then 'TOPGP'
                      end
                          ,
                         'REC' AS InvoiceCountType,
                         '' as deviceId,
                         '' as ID,
                         '' as collectedTime,
                         '' as collectConfigId,
                         '' as uploadDataModelCode,
                         '' as deviceCollectDetailId,
                         '' as aiId,
                         '' as aiopsItem,
                         '' as flumeTimestamp,
                         REC AS PERIOD_COUNT

                  from table_tmp
                  WHERE REC IS NOT NULL AND REC > 0
                  union all
                  select eid,mouth,'IMPORT',case
                                                when product = '37' then 'E10'
                                                when product = '06' then 'TOPGP'
                                                when product = '100' then 'T100'
                                                when product = '165' then 'E10'
                                                when product = '164' then 'TOPGP'
                      end
                          ,
                         'REC_D' AS InvoiceCountType,
                         '' as deviceId,
                         '' as ID,
                         '' as collectedTime,
                         '' as collectConfigId,
                         '' as uploadDataModelCode,
                         '' as deviceCollectDetailId,
                         '' as aiId,
                         '' as aiopsItem,
                         '' as flumeTimestamp,
                         REC_D AS PERIOD_COUNT

                  from table_tmp
                  WHERE REC_D IS NOT NULL AND REC_D > 0
                  union all
                  select eid,mouth,'IMPORT',case
                                                when product = '37' then 'E10'
                                                when product = '06' then 'TOPGP'
                                                when product = '100' then 'T100'
                                                when product = '165' then 'E10'
                                                when product = '164' then 'TOPGP'
                      end
                          ,
                         'PAY' AS InvoiceCountType,
                         '' as deviceId,
                         '' as ID,
                         '' as collectedTime,
                         '' as collectConfigId,
                         '' as uploadDataModelCode,
                         '' as deviceCollectDetailId,
                         '' as aiId,
                         '' as aiopsItem,
                         '' as flumeTimestamp,
                         PAY AS PERIOD_COUNT

                  from table_tmp
                  WHERE PAY IS NOT NULL AND PAY > 0
                  union all
                  select eid,mouth,'IMPORT',case
                                                when product = '37' then 'E10'
                                                when product = '06' then 'TOPGP'
                                                when product = '100' then 'T100'
                                                when product = '165' then 'E10'
                                                when product = '164' then 'TOPGP'
                      end
                          ,
                         'PAY_D' AS InvoiceCountType,
                         '' as deviceId,
                         '' as ID,
                         '' as collectedTime,
                         '' as collectConfigId,
                         '' as uploadDataModelCode,
                         '' as deviceCollectDetailId,
                         '' as aiId,
                         '' as aiopsItem,
                         '' as flumeTimestamp,
                         PAY_D AS PERIOD_COUNT

                  from table_tmp
                  WHERE PAY_D IS NOT NULL AND PAY_D > 0
              ) as aa
              where eid not in (177710698418752 , 41324987613760 , 41321310085696 , 538169702163008 , 204825305580096 , 177327185748544 , 41319155941952 , 50606264996416 , 187979961795136 , 41318619652672 , 121286649041472 , 114859134292544 , 50606267871808 , 237176374104640 , 204820094431808 , 221232517599808 , 41321568645696 , 41322568458816 , 41319251944000 , 251518906839616 , 290782426583616 , 41320358371904 , 371024020644416 , 50606954054208 , 50606937645633 , 253800651649600 , 122738551022144 , 189603487023680 , 41319224107584 , 576479149167168 , 261927991145024 , 303428765745728 , 539231335817792 , 365094038045248 , 419572390003264 , 385179649442368 , 50605885956672 , 582348383449664 , 201605970743872 , 114860885103168 , 41318298124864 , 41318092898880 , 503974458245696 , 223709738615360 , 117444752433728 , 41322364281408 , 41322626425408 , 497602308100672 , 41318227792448 , 247988480729664 , 455486867960384 , 190444434641472 , 247988504801856 , 348728838316608 , 136555735216704 , 50606272569920 , 463493506511424 , 41324664308288 , 608242413912640 , 251317069603392 , 489062791406144 , 41322175803968 , 41319487783488 , 41319950901824 , 284041968230976 , 204819471577664 , 639828144009792 , 114857808200256 , 41319652053568 , 50606729339456 , 603568055308864 , 41323158536768 , 41324249039424 , 199998862271040 , 626421319938624 , 241069755806272 , 204827290509888 , 41323693920832 , 296342366966336 , 41323027825216 , 553121541141056 , 41324280517184 , 50606713328192 , 41320209658432 , 50606172987968 , 254857038058048 , 50606478004800 , 121632247059008 , 454026927415872 , 625309336937024 , 233431069491776 , 495727855137344 , 41319818691136 , 242116736836160 , 41318761796160 , 41320004592192 , 348728859038272 , 204437148615232 , 41324550558272 , 247988408816192 , 204820449485376 , 50606662427200 , 177312440308288 , 41323782079040 , 501263976161856 , 216631934124608 , 41317564789312 , 497248312607296 , 380623461671488 , 41320223654464 , 587362557395520 , 41324940997184 , 333508411421248 , 54722996597312 , 41323956580928 , 41317684970048 , 41320002822720 , 251317040927296 , 132706661843520 , 580726202552896 , 41324484985408 , 472002262684224 , 410504879104576 , 41324279341632 , 41322720313920 , 50606769095232 , 387745355620928 , 41318662427200 , 451107336372800 , 50606468403776 , 474764427522624 , 204823356801600 , 407555796488768 , 443645629964864 , 511243862917696 , 41324071080512 , 41321658937920 , 50606540653120 , 262891332649536 , 414663089590848 , 340868538053184 , 50606492414529 , 360167810081344 , 219345104949824 , 377394042643008 , 41321181426240 , 50606568575552 , 233427568456256 , 360167713915457 , 586580960662080 , 50606300013120 , 216278042899008 , 41317244518976 , 230909193376320 , 41321237959232 , 233425559212608 , 347667184403008 , 503572430520896 , 498971851244096 , 41320823841344 , 322624359711296 , 41323392590400 , 230555090899520 , 41324748821056 , 204826048877120 , 50606178951744 , 489800169996864 , 348021078823488 , 434801960948288 , 612922186703424 , 50606645936704 , 457049776116288 , 198790180278848 , 463817907868224 , 50606037709376 , 248681166471744 , 388099235238464 , 642659641553473 , 68204371202624 , 41324383269440 , 50606236435008 , 615045754454592 , 41318789538368 , 54722625737280 , 451541722235456 , 41324336259648 , 41320117850688 , 41317642646080 , 204820054135360 , 41324871377472 , 50606750081600 , 130184840688192 , 223979887858240 , 493975825920576 , 521624895054400 , 553164490994240 , 41319683080768 , 41320334541376 , 388010759995968 , 521978760274496 , 486865749467712 , 551394987508288 , 50606753284672 , 397727998583360 , 231263463055936 , 419676528308800 , 41323181273664 , 41321894355520 , 41322049294912 , 41317915910720 , 50605811593792 , 41324430283328 , 94335369769536 , 87358799786560 , 41323764019776 , 50606514307648 , 50605933494848 , 41321826452032 , 607269161337409 , 380932993311296 , 233427540402752 , 204822128263744 , 612553577706048 , 41322695619136 , 41320325419584 , 401538256974401 , 342446291751488 , 124829400437312 , 456740097024576 , 41323314868800 , 41324285932096 , 41323255009856 , 295354470220352 , 360167733420608 , 41323276161600 , 41321224741440 , 332468446376512 , 41323141268032 , 441580142301760 , 41324930519616 , 50606968828480 , 518705264656960 , 313427192324672 , 41324653830720 , 41323788218944 , 422743909794368 , 387479924552256 , 439083762618944 , 172578934809152 , 204821027000896 , 54724169318977 , 67452354056768 , 254857038070336 , 41317884629568 , 516581742760512 , 41321628459584 , 340868545000000 , 204827848184384 , 50606187057728 , 590193878413888 , 360167609414208 , 518439693906496 , 50606188360256 , 41322481824320 , 480834780418624 , 453584565600832 , 41323967918656 , 519899044131392 , 473756949385792 , 246010834440768 , 50605934428736 , 493663498678848 , 521434355974720 , 41324763423296 , 54723824173632 , 41322053829184 , 533568709153344 , 41321707291200 , 509150014587456 , 50605737734720 , 41321566327360 , 574974972121664 , 41324290028096 , 501262484800064 , 114618385830464 , 477384334389824 , 69973831074368 , 444686950593088 , 478446008427072 , 50606704407104 , 379924160918080 , 367485118304832 , 64975108530752 , 173965004743232 , 41318029238848 , 211529701704256 , 499237270835776 , 41323093819968 , 535692432847424 , 572585994568256 , 41324256326208 , 308825583903296 , 204821091447360 , 456150271787584 , 78555737862720 , 41320388624960 , 230554989212224 , 41324433769024 , 327507393045056 , 41321617400384 , 478644751626816 , 50605759930944 , 204826620342848 , 204821114077760 , 204830232322624 , 334719463490112 , 230909315793472 , 90457300812352 , 41320291689024 , 204825667830336 , 41318968259136 , 41318572851776 , 216985820115520 , 457477368783424 , 41324681814592 , 336002505069120 , 41321503699520 , 318377744618048 , 390561719169600 , 399388194947648 , 41320190227008 , 41324154901056 , 41319555539520 , 254500381585984 , 41320588763712 , 350498238710336 , 41323340292672 , 41319306822208 , 328200458031680 , 41324436939328 , 93023019741760 , 41317175751232 , 607623054156352 , 458539044737600 , 385268106555968 , 41322381623872 , 166621762024000 , 478645417009728 , 368192898683456 , 604125632815680 , 239300343095872 , 510683554583104 , 41318777201216 , 50606316077632 , 618142528959040 , 340868513550912 , 132382187176512 , 41321719181888 , 41322115412544 , 41318613389888 , 520960780313152 , 204828745249344 , 41317950317120 , 212739006276160 , 114860811002432 , 603287887430208 , 501264037851712 , 50606180991552 , 41319771353664 , 233427996840512 , 50605795250752 , 284041950188096 , 380298921230912 , 348021075505728 , 41322102293056 , 516227840713280 , 648233227125312 , 114860468224576 , 389942413550144 , 50605997613632 , 41322040336960 , 462520306000448 , 41319181681216 , 41321213911616 , 41319713583680 , 418998267236928 , 121288621666880 , 50606512431680 , 41321156989504 , 41322312503872 , 501262673281600 , 158865659040320 , 41319701307968 , 390679682499136 , 295988745720384 , 129432821494336 , 247988480193088 , 204820410065472 , 114857407476288 , 236291753914944 , 41324173070912 , 196814478627392 , 189279379227200 , 586300764594752 , 456769586786880 , 505935502217792 , 327861234983488 , 41324750455360 , 575063448322624 , 427063988019776 , 117444752458304 , 41324238172736 , 41323448189504 , 41318755545664 , 41317406003776 , 284042037015104 , 537815772021312 , 41320605258304 , 50605943272000 , 603672525316672 , 192430447481408 , 50606105670208 , 204828348736064 , 198790180704832 , 50606108697152 , 50607034094144 , 114857019339328 , 50606388019776 , 93023016075840 , 293157326680640 , 132573906776640 , 67452354024000 , 478357534323264 , 602933996749376 , 50606263067200 , 41320121475648 , 203789821596224 , 164111167644224 , 369962354995776 , 334924905103936 , 41318518768192 , 90189940150848 , 260869387493952 , 41321318765120 , 41322412257856 , 41324496147008 , 236232953184832 , 572939932451392 , 41324244402752 , 284042105225792 , 50606419563072 , 453938456482368 , 345469152653888 , 501263597011520 , 192430400258624 , 342865257542208 , 41323575718464 , 485215211934272 , 630856389501504 , 384914216768064 , 50606545691200 , 360167762076224 , 412952614392384 , 186375285858880 , 233422065754688 , 67540825760320 , 41321768223296 , 99990000 , 255912587641408 , 499725588902464 , 65417473073728 , 230554899939904 , 566480992961088 , 184641471373888 , 204827358044736 , 50605918159424 , 62542094783040 , 50605953909312 , 315201547784768 , 41321537102400 , 41317478605376 , 50606452617792 , 409354731921984 , 72097211044416 , 266690249364032 , 259812733194816 , 325015470891584 , 50605892850240 , 295988743856704 , 434566009889344 , 645085297521216 , 185151402283584 , 130229077115456 , 627078768742976 , 41322483081792 , 50606395888192 , 438045934015040 , 332468448977472 , 607269161148992 , 41320645079616 , 380638048039488 , 41317822399040 , 204819609248320 , 41322026639936 , 214523180003904 , 380932977644096 , 160238043284032 , 233423418528320 , 464201288077888 , 231263220101696 , 464643651781184 , 41323101102656 , 222087583756864 , 587804985209408 , 41320613888576 , 41320671105600 , 50606500241984 , 41325075677760 , 615399660900928 , 230554779136576 , 41322385666624 , 360403414884928 , 330330196791872 , 474597793284672 , 41318614385216 , 466059209773632 , 539319808000576 , 41322985468480 , 41320212140608 , 515608534049344 , 114841137869376 , 364399986467392 , 322624368411200 , 41324307321408 , 424984904491584 , 41319590519360 , 122354996421184 , 50607050621504 , 293614496084544 , 198495271817792 , 566923353879104 , 74530223714880 , 313115622003264 , 412952614519360 , 468846098281024 , 469952619377216 , 50605942358592 , 41319804686912 , 389588521386560 , 642659627991616 , 50606428643904 , 582849790263872 , 543404304392768 , 247988355576384 , 41320902824512 , 41317388866112 , 424984914928192 , 194337225056832 , 385179646104128 , 50606005969472 , 340868565811776 , 516005580067392 , 389499230868032 , 41322718769728 , 313696285348416 , 41317728899648 , 41317716877888 , 444421498384960 , 498927615689280 , 41320807998016 , 204820871844416 , 73557019451968 , 134653318820416 , 251674150036032 , 41319511151168 , 41324079231552 , 315201539281472 , 393363351929408 , 41321525678656 , 342800235729472 , 68690971624000 , 231351674753600 , 302720957129280 , 41324480377408 , 159214592426560 , 171517263884864 , 570373987119680 , 114856102429248 , 41323977646656 , 41322365366848 , 340868580823616 , 41320489001536 , 247988266467904 , 546751513403968 , 216292631134784 , 124876472386112 , 41318701253184 , 41318489813568 , 233423735304768 , 509592374870592 , 169423411847744 , 485892572471872 , 340868584743488 , 261927930872384 , 65373234971200 , 41321731273280 , 199659576951360 , 284042127524416 , 119568097980992 , 329725743227456 , 41318785737280 , 136555711713856 , 50606029783616 , 242116719120960 , 450061819605568 , 233428884505152 , 192213893222976 , 114860013761088 , 41322374013504 , 41323187597888 , 233422613656128 , 134063420088896 , 379517405790784 , 215216377344577 , 392301681328704 , 41325122130496 , 519147656208960 , 319704657121856 , 575417358832192 , 323068879934016 , 187260129948224 , 655664919216704 , 122753130824256 , 597271742374464 , 861071148580864 , 50606337299008 , 41321166283328 , 261928004145728 , 364650092290624 , 196814478242368 , 348728872600128 , 41324053422656 , 41324922126912 , 501263345320512 , 474818648347200 , 41323227099712 , 41324671570496 , 498891071644224 , 331141344563776 , 183945496863296 , 432413170586176 , 500652839682624 , 389573776323136 , 41322325762624 , 164852305904192 , 493355424842304 , 41322315895360 , 261927914103360 , 388010759959104 , 41317450314304 , 603672552329792 , 154610417263168 , 73158892323392 , 518439672132160 , 41321749434944 , 387745343267392 , 41322452652608 , 303782652092992 , 41323086492224 , 41317614637632 , 41319165755968 , 444092121653824 , 233424411116096 , 41318636196416 , 325015457477184 , 501262391939648 , 41318400123456 , 597271745753664 , 41324388213312 , 478844560548416 , 50606195204672 , 400096111391296 , 248135430009408 , 180364760650304 , 50606814192192 , 519413053198913 , 509208992485952 , 367485137883712 , 401538256974400 , 41324380463680 , 327242104042048 , 204821273834048 , 446126438838848 , 578956607812160 , 572851455496768 , 41323354231360 , 41319320646208 , 521270978855488 , 41320306061888 , 41323058442816 , 551617508053568 , 50606304375360 , 41321359086144 , 348728838312512 , 41322788934208 , 121288121721408 , 50605846024768 , 50606800777792 , 41317599695424 , 41324633666112 , 50606067618368 , 459689181651520 , 389234632823360 , 315201536967232 , 642659639235136 , 50606887821888 , 50605902148161 , 466766990156352 , 457610618241600 , 41319193178688 , 41323408364096 , 50606462771776 , 365359466730048 , 50606345499200 , 41319689413184 , 131954512548416 , 243886175658560 , 184944967295553 , 41322900587072 , 41322189500992 , 41317343949376 , 328341160268352 , 432667481489984 , 41324603888192 , 625309321724480 , 646150054736448 , 41324612391488 , 399488643158592 , 45906735563328 , 478844790739520 , 343345727775296 , 41324948202048 , 50606502519360 , 41322799837760 , 212237481988672 , 67496588833344 , 489816618103360 , 41317413282368 , 325383969579584 , 586431121928768 , 41320091927104 , 208506891498048 , 41319400755776 , 230554922226240 , 41322742776384 , 54724125483584 , 41318011171392 , 367485118313024 , 41323811078720 , 50605997650496 , 204827573948992 , 50606316151360 , 360167685390912 , 41317582430784 , 41319467602496 , 41319243784768 , 600102870835776 , 67894717309504 , 50606121923137 , 506407357104704 , 640086339437120 , 41318987629120 , 131866030248512 , 317575541457472 , 616018971894336 , 288569818747456 , 469952652808768 , 41323582566976 , 50606840529472 , 499017744728640 , 41321792459328 , 333170163036736 , 122664650465856 , 628051978367552 , 67894717235776 , 369608465556032 , 41322400137792 , 455000128533056 , 50606371508800 , 41323544035904 , 273524040573504 , 41321172668992 , 642659579781696 , 50605887390272 , 41323336512064 , 41323741962816 , 380298940912192 , 177327185576512 , 233423898145344 , 50606186963520 , 199217797415488 , 179273588060736 , 204829814993472 , 347313295761984 , 499709125120576 , 223650602676800 , 499679635259968 , 335648583275072 , 136555720016448 , 41323121381952 , 489108120646208 , 294219007550016 , 410504877793856 , 586389233205824 , 610100290703936 , 425265043907136 , 317575518921280 , 378101846323776 , 577894864429632 , 50605879108160 , 484108292354624 , 462166436569664 , 123107013939776 , 41319562818112 , 173729077674560 , 346251581313600 , 50605718098496 , 848097733406720 , 41320083944000 , 41319362187840 , 41322880807488 , 41323653050944 , 390325792657984 , 375713087840832 , 41318982160960 , 328908562862656 , 41320695378496 , 467415813964352 , 333863177056832 , 41321161302592 , 443678715716160 , 41318261473856 , 235863621919296 , 41325084611136 , 423893721678400 , 247988360983104 , 348021061038656 , 435214809743936 , 50606678184512 , 114859928830528 , 50606774489665 , 517024096117312 , 41322659148352 , 219345114653248 , 204826029904448 , 392419644908096 , 133679980597824 , 41323570225728 , 41322213179968 , 41323303621184 , 41320112595520 , 41321965204032 , 331141344461376 , 315452111704640 , 389912946221632 , 252030863036992 , 204819241439808 , 233421571891776 , 54724175880768 , 223979241730624 , 41319000040000 , 431675918303808 , 195398912782913 , 114859271508544 , 41317561127488 , 522376625373760 , 452257474581056 , 41325033984576 , 41324444557888 , 114855481930304 , 204829582398016 , 459736935354944 , 516670205751872 , 41321095160384 , 41324576633408 , 41320340333120 , 94438584697408 , 209568561267264 , 437692042146368 , 410445902672448 , 41321155469888 , 41322027614784 , 259456055169600 , 378101824160320 , 603672464724544 , 301659362316864 , 489770684957248 , 41321409221184 , 407909687452224 , 469290930094656 , 41320512987712 , 284042072216128 , 198480526672448 , 41318453105216 , 642659534275136 , 284042089181760 , 204822109966912 , 348505635279424 , 50606164742720 , 41318586892864 , 231263544070720 , 358014793908800 , 41317946573376 , 632033851953728 , 41322573828672 , 115313829524032 , 570505339941440 , 413984793621056 , 41322677633600 , 114859712553536 , 578160294715968 , 342800238953024 , 41323654804032 , 457610470806080 , 286445314777664 , 457211955122752 , 41317823685184 , 203980043735616 , 50606976844352 , 400211168641600 , 586300763992640 , 298215400993344 , 453584565674560 , 388364651024960 , 233427091132992 , 50606870225472 , 41320206037568 , 239300322370112 , 300686308401728 , 399120009327168 , 50606524416576 , 41323609940544 , 247987963273792 , 204822069076544 , 41318562619968 , 41322608947776)

select eid from InvoiceIssuanceNum group by eid;