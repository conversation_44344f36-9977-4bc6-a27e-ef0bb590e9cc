# 开源流媒体下载工具
## 如何运行一个 cobalt 实例 使用 docker compose
机器上安装 docker 和 docker-compose。

## 进入命令行工具
1. 创建目录:
    ```sh
    mkdir cobalt
    ```

2. 创建文件docker-compose.yml:
```yaml
  services:
    cobalt-api:
      image: ghcr.io/imputnet/cobalt:10
      init: true
      read_only: true
      restart: unless-stopped
      container_name: cobalt-api
      ports:
        - 9000:9000
        # if you use a reverse proxy (such as nginx),
        # uncomment the next line and remove the one above (9000:9000/tcp):
        # - 127.0.0.1:9000:9000

      environment:
        # replace https://api.url.example/ with your instance's url
        # or else tunneling functionality won't work properly
        API_URL: "http://www.520128.xyz/"

        # if you want to use cookies for fetching data from services,
        # uncomment the next line & volumes section
        # COOKIE_PATH: "/cookies.json"

        # it's recommended to configure bot protection or api keys if the instance is public,
        # see /docs/protect-an-instance.md for more info

        # see /docs/run-an-instance.md for more variables that you can use here

      labels:
        - com.centurylinklabs.watchtower.scope=cobalt

      # uncomment only if you use the COOKIE_PATH variable
      # volumes:
      #   - ./cookies.json:/cookies.json

      # watchtower updates the cobalt image automatically
    watchtower:
      image: ghcr.io/containrrr/watchtower
      restart: unless-stopped
      command: --cleanup --scope cobalt --interval 900 --include-restarting
      volumes:
        - /var/run/docker.sock:/var/run/docker.sock
```

3. 在当前目录运行:
    ```sh
    docker compose up -d
    ```

如果你想让你的实例支持需要身份验证才能查看公共内容的服务，请在同一目录下创建 cookies.json 文件（与 docker-compose.yml 文件位于同一目录）。你可以参考示例 cookies 文件，示例 cookies 文件可以在这里找到。

由于 watchtower 的存在，cobalt 包会自动更新。

如果你希望你的实例能够面向公网提供服务，强烈建议使用反向代理（例如 nginx）。你可以在网上查找相关教程。
```json
{
  "instagram": [
    "mid=<replace>; ig_did=<with>; csrftoken=<your>; ds_user_id=<own>; sessionid=<cookies>"
  ],
  "instagram_bearer": [
    "token=<token_with_no_bearer_in_front>", "token=IGT:2:<looks_like_this>"
  ],
  "reddit": [
    "client_id=<replace_this>; client_secret=<replace_this>; refresh_token=<replace_this>"
  ],
  "twitter": [
    "auth_token=<replace_this>; ct0=<replace_this>"
  ],
  "youtube_oauth": [
    "<output from running `pnpm run token:youtube` in `api` folder goes here>"
  ]
}
```