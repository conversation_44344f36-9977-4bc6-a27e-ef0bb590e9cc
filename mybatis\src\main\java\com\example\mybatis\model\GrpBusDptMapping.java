package com.example.mybatis.model;

import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@Data
public class GrpBusDptMapping {
    private String grp_dpt_id;
    private String grp_dpt_name;
    private String grp_dpt_manager;
    private String grp_dpt_manager_contact;
    private String bus_dpt_id;
    private String bus_dpt_name;
    private String bus_dpt_manager;
    private String bus_dpt_manager_contact;
    private String bus_dpt_win_name;
    private String bus_dpt_win_contact;
    private String dpt_id;
    private String dpt_name;
    private String dpt_manager;
    private String dpt_manager_contact;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        GrpBusDptMapping that = (GrpBusDptMapping) o;

        return new EqualsBuilder().append(grp_dpt_id, that.grp_dpt_id).append(bus_dpt_id, that.bus_dpt_id).append(dpt_id, that.dpt_id).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(grp_dpt_id).append(bus_dpt_id).append(dpt_id).toHashCode();
    }
}
