SELECT
    deviceId,
    -- 最终状态判断
    CASE
        -- 规则1: 任何一个读或写速率低于100
        WHEN MIN(diskio__read_bytes_per_sec) < 100 OR MIN(diskio__write_bytes_per_sec) < 100
            THEN '异常'
        -- 规则2: 读和写都小于300的磁盘数量超过总数的一半
        WHEN SUM(CASE WHEN diskio__read_bytes_per_sec < 300 AND diskio__write_bytes_per_sec < 300 THEN 1 ELSE 0 END) > (COUNT(*) / 2.0)
            THEN '异常'
        -- 其他情况均为正常
        ELSE '正常'
        END AS device_status,

    -- 以下为辅助诊断列（可选）
    COUNT(*) AS total_disks,
    MIN(diskio__read_bytes_per_sec) AS min_read_speed,
    MIN(diskio__write_bytes_per_sec) AS min_write_speed,
    SUM(CASE WHEN diskio__read_bytes_per_sec < 300 AND diskio__write_bytes_per_sec < 300 THEN 1 ELSE 0 END) AS low_speed_disk_count
FROM
    DiskioCollected
GROUP BY
    deviceId;


SELECT
    deviceId as objId,
    CASE
        WHEN MIN(diskio__read_bytes_per_sec) < 100 OR MIN(diskio__write_bytes_per_sec) < 100
            THEN '异常'
        WHEN SUM(CASE WHEN diskio__read_bytes_per_sec < 300 AND diskio__write_bytes_per_sec < 300 THEN 1 ELSE 0 END) > (COUNT(*) / 2.0)
            THEN '异常'
        ELSE '正常'
        END as tagValue

FROM
    DiskioCollected
GROUP BY
    deviceId;