2024-09-09 15:09:44.591 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.IUserDao.getUserByEmail      : ==>  Preparing: select sid,id,name,email,telephone,phone,defaultEid,defaultEidSid,defaultSid,defaultSidEid,status from user where email=? or id=? limit 1
2024-09-09 15:09:44.591 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.IUserDao.getUserByEmail      : ==> Parameters: <EMAIL>(String), <EMAIL>(String)
2024-09-09 15:09:44.599 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.IUserDao.getUserByEmail      : <==      Total: 1
2024-09-09 15:09:44.719 DEBUG 1 --- [      Thread-70] c.d.e.a.dao.ITenantDao.updateTenantIsv   : ==>  Preparing: UPDATE tenant SET isv = ? WHERE sid = ?
2024-09-09 15:09:44.719 DEBUG 1 --- [      Thread-70] c.d.e.a.dao.ITenantDao.updateTenantIsv   : ==> Parameters: false(Boolean), 41317858755136(Long)
2024-09-09 15:09:44.720 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.ITenantDao.getTenantV2       : ==>  Preparing: select a.sid,a.id,a.name,a.customer_id,a.taxCode,a.status,a.registerPhone,a.address, a.contacts,a.email,a.phone,a.cellphone_prefix,a.telephone,a.isv,s.sid as supplier_sid,s.supplierType as supplierType from tenant a left join supplier s on s.eid = a.sid where a.sid=?
2024-09-09 15:09:44.721 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.ITenantDao.getTenantV2       : ==> Parameters: 41317858755136(Long)
2024-09-09 15:09:44.728 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.dao.ITenantDao.getTenantV2       : <==      Total: 1
2024-09-09 15:09:44.731 DEBUG 1 --- [      Thread-70] c.d.e.a.dao.ITenantDao.updateTenantIsv   : <==    Updates: 1
2024-09-09 15:09:44.813 DEBUG 1 --- [nio-9190-exec-3] c.d.e.aiouser.dao.IUserDao.getOrgSid     : ==>  Preparing: select orgSid from supplier_employee where sid=? and eid=? and userSid=? limit 1;
2024-09-09 15:09:44.814 DEBUG 1 --- [nio-9190-exec-3] c.d.e.aiouser.dao.IUserDao.getOrgSid     : ==> Parameters: 241199971893824(Long), 41317858755136(Long), 41339749962304(Long)
2024-09-09 15:09:44.821 DEBUG 1 --- [nio-9190-exec-3] c.d.e.aiouser.dao.IUserDao.getOrgSid     : <==      Total: 0
java.lang.NullPointerException
        at com.digiwin.escloud.aiouser.service.impl.UserService.lambda$getUserOrg$24(UserService.java:1343)
        at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
        at java.util.ArrayList$ArrayListSpliterator.tryAdvance(ArrayList.java:1359)
        at java.util.stream.ReferencePipeline.forEachWithCancel(ReferencePipeline.java:126)
        at java.util.stream.AbstractPipeline.copyIntoWithCancel(AbstractPipeline.java:498)
        at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:485)
        at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
        at java.util.stream.FindOps$FindOp.evaluateSequential(FindOps.java:152)
        at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        at java.util.stream.ReferencePipeline.findAny(ReferencePipeline.java:469)
        at com.digiwin.escloud.aiouser.service.impl.UserService.getUserOrg(UserService.java:1344)
        at com.digiwin.escloud.aiouser.service.impl.UserService$$FastClassBySpringCGLIB$$154aef7e.invoke(<generated>)
        at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
        at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
        at com.digiwin.escloud.aiouser.service.impl.UserService$$EnhancerBySpringCGLIB$$d568c255.getUserOrg(<generated>)
        at sun.reflect.GeneratedMethodAccessor1095.invoke(Unknown Source)
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.lang.reflect.Method.invoke(Method.java:498)
        at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
        at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:499)
        at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
        at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
        at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:691)
        at com.digiwin.escloud.aiouser.service.impl.UserService$$EnhancerBySpringCGLIB$$53e4155.getUserOrg(<generated>)
        at com.digiwin.escloud.aiouser.controller.UserController.getUserOrg(UserController.java:401)
        at sun.reflect.GeneratedMethodAccessor1094.invoke(Unknown Source)
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.lang.reflect.Method.invoke(Method.java:498)
        at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
        at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
        at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
        at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
        at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
        at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
        at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
        at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
        at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
        at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
        at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
        at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
        at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93)
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
        at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
        at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
        at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
        at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
        at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
        at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
        at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:747)
        at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
        at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373)
        at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
        at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
        at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1589)
        at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
        at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
        at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
        at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
        at java.lang.Thread.run(Thread.java:748)
2024-09-09 15:09:44.894 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==>  Preparing: select pr.*,prdm.id prdm_id,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission from permission_role pr LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId where eid = ? AND sid = ? AND roleCode IN ( ? , ? , ? )
2024-09-09 15:09:44.894 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==> Parameters: 41317858755136(Long), 241199971893824(Long), customerService(String), endUser(String), superManager(String)
2024-09-09 15:09:44.902 DEBUG 1 --- [io-9190-exec-11] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====>  Preparing: SELECT pd.code code , pd.name name , pd.id id , pd.tableName tableName , pd.alias alias , pd.dimensionColumn dimensionColumn , pda.pdId pda_pdId , pda.id pda_id , pda.attributeCode pda_attributeCode , pda.attributeName pda_attributeName , pda.dynamicValueLogin pda_dynamicValueLogin FROM permission_dimension pd LEFT JOIN permission_dimension_attribute pda on pd.id = pda.pdId WHERE pd.id = ?
2024-09-09 15:09:44.902 DEBUG 1 --- [io-9190-exec-11] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====> Parameters: 1(Long)
2024-09-09 15:09:44.911 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:44.912 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 3(Long)
2024-09-09 15:09:44.918 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : <======      Total: 8
2024-09-09 15:09:44.919 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:44.920 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 4(Long)
2024-09-09 15:09:44.926 DEBUG 1 --- [io-9190-exec-11] d.I.selectAttributeColumnByAttributeCode : <======      Total: 3
2024-09-09 15:09:44.927 DEBUG 1 --- [io-9190-exec-11] .d.e.a.d.I.selectDataAuthDimensionByPdId : <====      Total: 2
2024-09-09 15:09:44.927 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====>  Preparing: SELECT prr.prdmId , prr.pdId , prr.pdaId , prr.id , prr.pdacId , prr.operator , prr.sourceCode , prr.dynamicValueCode , prr.value , prr.leftValue , prr.rightValue , prr.logicalOperator , prr.leftParenthesis , prr.rightParenthesis , prr.sequence , padv.id padv_id , padv.sourceCode padv_sourceCode , padv.dynamicValueCode padv_dynamicValueCode , padv.dynamicValueName padv_dynamicValueName , padv.dynamicValueType padv_dynamicValueType FROM permission_role_rule prr LEFT JOIN permission_attribute_dynamic_val padv on padv.sourceCode = prr.sourceCode WHERE prr.prdmId = ? ORDER BY prr.sequence
2024-09-09 15:09:44.927 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====> Parameters: 768144855372352(Long)
2024-09-09 15:09:44.935 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : <====      Total: 8
2024-09-09 15:09:44.935 DEBUG 1 --- [io-9190-exec-11] c.d.e.a.dao.IUserDao.selectDataAuthRole  : <==      Total: 1
2024-09-09 15:09:45.088 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==>  Preparing: select pr.*,prdm.id prdm_id,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission from permission_role pr LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId where eid = ? AND sid = ? AND roleCode IN ( ? , ? , ? )
2024-09-09 15:09:45.088 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==> Parameters: 41317858755136(Long), 241199971893824(Long), customerService(String), endUser(String), superManager(String)
2024-09-09 15:09:45.122 DEBUG 1 --- [nio-9190-exec-8] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====>  Preparing: SELECT pd.code code , pd.name name , pd.id id , pd.tableName tableName , pd.alias alias , pd.dimensionColumn dimensionColumn , pda.pdId pda_pdId , pda.id pda_id , pda.attributeCode pda_attributeCode , pda.attributeName pda_attributeName , pda.dynamicValueLogin pda_dynamicValueLogin FROM permission_dimension pd LEFT JOIN permission_dimension_attribute pda on pd.id = pda.pdId WHERE pd.id = ?
2024-09-09 15:09:45.122 DEBUG 1 --- [nio-9190-exec-8] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====> Parameters: 1(Long)
2024-09-09 15:09:45.130 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.131 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 3(Long)
2024-09-09 15:09:45.138 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : <======      Total: 8
2024-09-09 15:09:45.139 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.139 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 4(Long)
2024-09-09 15:09:45.146 DEBUG 1 --- [nio-9190-exec-8] d.I.selectAttributeColumnByAttributeCode : <======      Total: 3
2024-09-09 15:09:45.147 DEBUG 1 --- [nio-9190-exec-8] .d.e.a.d.I.selectDataAuthDimensionByPdId : <====      Total: 2
2024-09-09 15:09:45.147 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====>  Preparing: SELECT prr.prdmId , prr.pdId , prr.pdaId , prr.id , prr.pdacId , prr.operator , prr.sourceCode , prr.dynamicValueCode , prr.value , prr.leftValue , prr.rightValue , prr.logicalOperator , prr.leftParenthesis , prr.rightParenthesis , prr.sequence , padv.id padv_id , padv.sourceCode padv_sourceCode , padv.dynamicValueCode padv_dynamicValueCode , padv.dynamicValueName padv_dynamicValueName , padv.dynamicValueType padv_dynamicValueType FROM permission_role_rule prr LEFT JOIN permission_attribute_dynamic_val padv on padv.sourceCode = prr.sourceCode WHERE prr.prdmId = ? ORDER BY prr.sequence
2024-09-09 15:09:45.147 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====> Parameters: 768144855372352(Long)
2024-09-09 15:09:45.155 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : <====      Total: 8
2024-09-09 15:09:45.155 DEBUG 1 --- [nio-9190-exec-8] c.d.e.a.dao.IUserDao.selectDataAuthRole  : <==      Total: 1
2024-09-09 15:09:45.170 ERROR 1 --- [nio-9190-exec-1] c.d.e.a.controller.ProductController     : getCustomerServiceProduct
java.lang.NullPointerException: null
        at com.digiwin.escloud.aiouser.service.impl.ProductService.getCustomerServiceProduct(ProductService.java:101) ~[classes!/:na]
        at com.digiwin.escloud.aiouser.controller.ProductController.getCustomerServiceProduct(ProductController.java:132) ~[classes!/:na]
        at sun.reflect.GeneratedMethodAccessor1096.invoke(Unknown Source) ~[na:na]
        at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_181]
        at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_181]
        at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at javax.servlet.http.HttpServlet.service(HttpServlet.java:626) [tomcat-embed-core-9.0.37.jar!/:4.0.FR]
        at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at javax.servlet.http.HttpServlet.service(HttpServlet.java:733) [tomcat-embed-core-9.0.37.jar!/:4.0.FR]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:93) [spring-boot-actuator-2.3.3.RELEASE.jar!/:2.3.3.RELEASE]
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.2.8.RELEASE.jar!/:5.2.8.RELEASE]
        at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.valves.RemoteIpValve.invoke(RemoteIpValve.java:747) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:373) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1589) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) [na:1.8.0_181]
        at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) [na:1.8.0_181]
        at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.37.jar!/:9.0.37]
        at java.lang.Thread.run(Thread.java:748) [na:1.8.0_181]
2024-09-09 15:09:45.891 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==>  Preparing: select pr.*,prdm.id prdm_id,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission from permission_role pr LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId where eid = ? AND sid = ? AND roleCode IN ( ? , ? , ? )
2024-09-09 15:09:45.892 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.dao.IUserDao.selectDataAuthRole  : ==> Parameters: 41317858755136(Long), 241199971893824(Long), customerService(String), endUser(String), superManager(String)
2024-09-09 15:09:45.899 DEBUG 1 --- [nio-9190-exec-2] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====>  Preparing: SELECT pd.code code , pd.name name , pd.id id , pd.tableName tableName , pd.alias alias , pd.dimensionColumn dimensionColumn , pda.pdId pda_pdId , pda.id pda_id , pda.attributeCode pda_attributeCode , pda.attributeName pda_attributeName , pda.dynamicValueLogin pda_dynamicValueLogin FROM permission_dimension pd LEFT JOIN permission_dimension_attribute pda on pd.id = pda.pdId WHERE pd.id = ?
2024-09-09 15:09:45.900 DEBUG 1 --- [nio-9190-exec-2] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====> Parameters: 1(Long)
2024-09-09 15:09:45.907 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.907 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 3(Long)
2024-09-09 15:09:45.921 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : <======      Total: 8
2024-09-09 15:09:45.922 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.922 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 4(Long)
2024-09-09 15:09:45.928 DEBUG 1 --- [nio-9190-exec-2] d.I.selectAttributeColumnByAttributeCode : <======      Total: 3
2024-09-09 15:09:45.929 DEBUG 1 --- [nio-9190-exec-2] .d.e.a.d.I.selectDataAuthDimensionByPdId : <====      Total: 2
2024-09-09 15:09:45.929 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====>  Preparing: SELECT prr.prdmId , prr.pdId , prr.pdaId , prr.id , prr.pdacId , prr.operator , prr.sourceCode , prr.dynamicValueCode , prr.value , prr.leftValue , prr.rightValue , prr.logicalOperator , prr.leftParenthesis , prr.rightParenthesis , prr.sequence , padv.id padv_id , padv.sourceCode padv_sourceCode , padv.dynamicValueCode padv_dynamicValueCode , padv.dynamicValueName padv_dynamicValueName , padv.dynamicValueType padv_dynamicValueType FROM permission_role_rule prr LEFT JOIN permission_attribute_dynamic_val padv on padv.sourceCode = prr.sourceCode WHERE prr.prdmId = ? ORDER BY prr.sequence
2024-09-09 15:09:45.929 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : ====> Parameters: 768144855372352(Long)
2024-09-09 15:09:45.936 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.d.I.selectDataAuthRuleByPrdmId   : <====      Total: 8
2024-09-09 15:09:45.936 DEBUG 1 --- [nio-9190-exec-2] c.d.e.a.dao.IUserDao.selectDataAuthRole  : <==      Total: 1
2024-09-09 15:09:45.942  INFO 1 --- [nio-9190-exec-6] c.d.e.m.p.DataPermissionInterceptor      : [DataPermissionInterceptor] one:0.126748282
2024-09-09 15:09:45.945  INFO 1 --- [nio-9190-exec-6] c.d.e.m.p.DataPermissionInterceptor      : [DataPermissionInterceptor] SQL Change before:SELECT dp_t_main.*, sam.moduleCode, sam.moduleName FROM tenant_module_contract dp_t_main LEFT JOIN supplier_aiops_module sam ON dp_t_main.moduleId = sam.id LEFT JOIN supplier_tenant_map stm ON dp_t_main.eid = stm.eid WHERE serviceIsvSid = ? AND stm.sid = ?
2024-09-09 15:09:45.965 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.d.I.selectDataAuthRoleDetail     : ==>  Preparing: select pr.* ,prdm.id prdm_id ,prdm.prId prdm_prId,prdm.pdId prdm_pdId,prdm.useDataPermission prdm_useDataPermission from permission_role pr LEFT JOIN permission_role_dimension_map prdm on pr.id = prdm.prId where eid = ? AND sid=? AND roleCode IN ( ? )
2024-09-09 15:09:45.965 DEBUG 1 --- [io-9190-exec-10] c.d.e.a.d.I.selectDataAuthRoleDetail     : ==> Parameters: 41317858755136(Long), 241199971893824(Long), endUser(String)
2024-09-09 15:09:45.974 DEBUG 1 --- [io-9190-exec-10] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====>  Preparing: SELECT pd.code code , pd.name name , pd.id id , pd.tableName tableName , pd.alias alias , pd.dimensionColumn dimensionColumn , pda.pdId pda_pdId , pda.id pda_id , pda.attributeCode pda_attributeCode , pda.attributeName pda_attributeName , pda.dynamicValueLogin pda_dynamicValueLogin FROM permission_dimension pd LEFT JOIN permission_dimension_attribute pda on pd.id = pda.pdId WHERE pd.id = ?
2024-09-09 15:09:45.974 DEBUG 1 --- [io-9190-exec-10] .d.e.a.d.I.selectDataAuthDimensionByPdId : ====> Parameters: 1(Long)
2024-09-09 15:09:45.981 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.982 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 3(Long)
2024-09-09 15:09:45.989 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : <======      Total: 8
2024-09-09 15:09:45.989 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : ======>  Preparing: select id,pdaId,attributeColumnCode,attributeColumnName,attributeColumnType,attributeColumnAlias FROM permission_dimension_attribute_column where pdaId = ?
2024-09-09 15:09:45.990 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : ======> Parameters: 4(Long)
2024-09-09 15:09:45.996 DEBUG 1 --- [io-9190-exec-10] d.I.selectAttributeColumnByAttributeCode : <======      Total: 3
2024-09-09 15:09:45.996 DEBUG 1 --- [io-9190-exec-10] .d.e.a.d.I.selectDataAuthDimensionByPdId : <====      Total: 2