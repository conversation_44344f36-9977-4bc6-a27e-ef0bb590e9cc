def process_string_to_dict(input_string):
    """
    将带有换行符的字符串切割成数组，并存入字典中
    
    参数:
        input_string (str): 输入的字符串，以\n分隔
        
    返回:
        dict: 包含分割后字符串的字典，key为索引，value为字符串
    """
    # 将字符串按换行符分割成数组
    string_array = input_string.split('\n')
    
    # 创建字典，将数组元素存入字典中
    result_dict = {i: item for i, item in enumerate(string_array) if item}  # 只存储非空字符串
    
    return result_dict

# 测试代码
if __name__ == "__main__":
    # 测试样例
    test_string = """第一行
第二行
第三行
第四行"""
    
    result = process_string_to_dict(test_string)
    print("处理结果：")
    for key, value in result.items():
        print(f"键: {key}, 值: {value}") 