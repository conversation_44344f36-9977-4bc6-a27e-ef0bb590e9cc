INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('DIGIWIN', '165', 'E10半导体', null, '樊丽香', '<EMAIL>', 1, null, 0, null, null, null, null, null, null, null, '20220228', '20240828', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02100000', '06', 'TIPTOP ERP', '2.6', '樊丽香', '<EMAIL>', 1, 0, 1, '08627', '樊丽香', '<EMAIL>', '', '', '', 'G', '20100101', '20261231', '電子', '1', '2024-07-30 10:26:07', null, null, null, '', 'A', '', 'BP0271', '', '', '<EMAIL>;<EMAIL>', true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02100000', '164', 'TIPTOP半导体', '', '樊丽香', '<EMAIL>', 1, 0, 1, '08627', '樊丽香', '<EMAIL>                   ', null, null, null, 'A', '20220324', '20280521', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02400000', '02', 'WF-ERP', '6.2', '樊丽香', '<EMAIL>', 0, 1, 0, '06436', '張偉勝', '<EMAIL>', '', '', '', 'B', '20151101', '20240630', '機械', '7', '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, true, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02412202', '02', null, null, '樊丽香', '<EMAIL>', 1, 1, 0, '13733', '藍郁昕', '<EMAIL>', '', null, null, 'B', '20211031', '20261030', '機械', '4', '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, false, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('88881010', '163', 'Infra', '9.0', '樊丽香', '<EMAIL>', 0, 0, 0, '08627', '樊丽香', '<EMAIL>', '', '', '', '', '20240501', '20251231', '', '', '2024-07-30 10:26:07', null, null, null, null, '', null, null, null, '', null, true, null, null, null, null, null, '', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99990000', '06', 'TIPTOPERP', null, '樊丽香', '<EMAIL>', 1, 0, 1, '08627', '樊丽香', '<EMAIL>', null, null, null, 'F0', '20230318', '20300101', null, null, '2024-07-30 10:26:07', null, null, null, null, 'E', '', null, null, '0', null, true, null, 'BV04A0', null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99990000', '100', '鼎捷T100管理软件          ', '', '樊丽香', '<EMAIL>', 1, null, 1, '08627', '樊丽香', '<EMAIL>', null, null, null, 'G6', null, '20990101', null, null, '2024-07-30 10:26:07', null, null, null, '11441', 'VIP', '2020-01-31', 'BM2010', null, '1', null, true, null, 'BW0310', null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99990000', '163', 'Infra', '', '樊丽香', '<EMAIL>', 1, 0, 1, '08627', '樊丽香', '<EMAIL>', null, null, null, 'C', null, '20280521', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, true, null, null, null, null, '2', 3);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99990000', '165', 'E10半导体管理软件', null, '樊丽香', '<EMAIL>', 1, 0, 1, '08627', '樊丽香', '<EMAIL>', null, null, null, null, '20240324', '20280324', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99998888', '06', 'TIPTOP ERP', '', '樊丽香', '<EMAIL>', 1, 0, 0, '', '', '<EMAIL>', null, null, null, 'B0', '20240406', '20260521', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99998888', '08', '易飞', '', '樊丽香', '<EMAIL>', 1, 0, 0, '01436', '张慧娟', '<EMAIL>', null, null, null, 'B0', null, '20280524', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99998888', '37', 'E10', '', '樊丽香', '<EMAIL>', 1, 0, 0, '04637', 'daixy1', '<EMAIL>', null, null, null, 'B0', null, '20280521', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('99998888', '100', 'T100', '', '樊丽香', '<EMAIL>', 1, 0, 0, '', '', '<EMAIL>', null, null, null, 'B0', '20230201', '20290521', null, null, '2024-07-30 10:26:07', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02100000', '05', 'e-B Chain', '', '02100000', '<EMAIL>', 0, 0, 0, '01442', '陳陽明', '<EMAIL>', '', '', '', 'G', '20160101', '3', '電子', '1', '2024-07-30 10:24:00', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02400000', '37', 'E10', '', '02400000', '<EMAIL>', 0, 0, 0, '08627', '樊丽香', '<EMAIL>', '', '', '', 'A', '20100319', '20251220', '機械', '7', '2024-07-30 10:24:00', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02400000', '165', 'E10半导体', '', '02400000', '<EMAIL>', 0, 0, 0, '08627', '樊丽香', '<EMAIL>', '', '', '', 'A', '20100319', '20241231', '機械', '7', '2024-07-30 10:24:00', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
INSERT INTO `escloud-db`.mars_customerservice (CustomerServiceCode, ProductCode, ProductCategory, ProductVersion, Sales, SalesContact, HasOwnerService, HasTextService, HasOwnerIssueService, ServiceStaffCode, ServiceStaff, ServiceStaffContact, ServiceStaffQQ, Consultant, ConsultantContact, ContractState, ContractStartDate, ContractExprityDate, IndustryCode, AreaCode, __version__, HostAuthNum, ClientAuthNum, SnmpAuthNum, pmWorkno, cust_level, trial_expired, service_department, cust_borrowing_due_date, status, service_cc_staff_emails, formal, IsTrial, window_department, businessDepartmentCode, businessDepartmentCodeACP, serviceUnitType, deliveryMode, contractSource) VALUES ('02100000', '37', 'E10', '2.0.1', '02100000', '<EMAIL>', 0, 0, 0, '00815', '曹碧琪', '<EMAIL>', '', '', '', 'B', '20100101', '20251231', '電子', '1', '2024-07-30 10:23:53', null, null, null, null, null, null, null, null, null, null, true, null, null, null, null, null, '2', 1);
