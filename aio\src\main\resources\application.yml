# DataSource Config
#spring:
#  datasource:
#    driver-class-name: com.mysql.jdbc.Driver
#    url: **********************************************************************
#    username: readniss
#    password: Escread001
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *************************************
    username: root
    password: 123456
  data:
    redis:
      host: ************
      password:
      port: 31330

#  sql:
#    init:
#      schema-locations: classpath:db/schema-h2.sql
#      data-locations: classpath:db/data-h2.sql
#      mode: always

# Logger Config
logging:
  level:
    com.baomidou.mybatisplus.samples.quickstart: debug
mybatis:
  mapper-locations: classpath:mapper/*.xml
server:
  port: 8083

esc:
  integration:
    iamAddress: https://iam-test.digiwincloud.com.cn
#    iamAddress: https://iam.digiwincloud.com
    appToken: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE1MTUxMzM3ODgwNjEsInNpZCI6OTQ5MTY2MDE1NTYzMDQyODE2LCJpZCI6IlNlcnZpY2VDbG91ZCJ9.9xL7lSrjTvv_hRYaU5RXVMIZ9Zkz_PbA5oXxyUYekmI
    datauri: https://hw-ddp-dataapi.digiwincloud.com.cn
