package com.example.aio.utils;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * 长整数实用类
 */
public final class LongUtil {
    /**
     * 检查长整数是否为空或小于等于0
     * @param value 数值
     * @return 是否为空或小于等于0
     */
    public static boolean isEmpty(Long value) {
        return value == null || value <= 0;
    }

    /**
     * 检查长整数非空且大于0
     * @param value 数值
     * @return 是否非空且大于0
     */
    public static boolean isNotEmpty(Long value) {
        return !isEmpty(value);
    }

    /**
     * 尝试将字串转换为长整数
     * @param value 字串
     * @return 长整数
     */
    public static Optional<Long> tryParseLongByString(String value) {
        if (StringUtils.isEmpty(value)) {
            return Optional.empty();
        }
        try {
            return Optional.of(Long.parseLong(value));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 将对象转换为长整数
     * @param obj 对象
     * @return 长整数
     */
    public static Long objectToLong(Object obj) {
        return objectToLong(obj, 0L);
    }

    /**
     * 将对象转换为长整数
     * @param obj 对象
     * @param defaultValue 转换失败后的默认值
     * @return 长整数
     */
    public static Long objectToLong(Object obj, Long defaultValue) {
        if (obj == null) {
            return defaultValue;
        }

        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof Double) {
            return ((Double) obj).longValue();
        }
        if (obj instanceof Float) {
            return ((Float) obj).longValue();
        }

        return tryParseLongByString(ObjectUtils.toString(obj)).orElse(defaultValue);
    }

    /**
     * 检查实际值，若为空时返回默认值
     * @param value 实际值
     * @param defaultValue 默认值
     * @return 长整数
     */
    public static Long checkCurrentOrDefault(Long value, Long defaultValue) {
        return LongUtil.isEmpty(value) ? defaultValue : value;
    }

    /**
     * 安全将长整数输出为字串
     * @param value 值
     * @return 字串值(当为null时输出空字串)
     */
    public static String safeToString(Long value) {
        return safeToString(value, "");
    }

    /**
     * 安全将长整数输出为字串
     * @param value 值
     * @param nullDefaultValue null时的默认值
     * @return 字串值
     */
    public static String safeToString(Long value, String nullDefaultValue) {
        if (value == null) {
            return nullDefaultValue;
        }
        return Long.toString(value);
    }
}
