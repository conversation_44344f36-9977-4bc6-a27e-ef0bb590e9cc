2024-12-02 14:52:09.670  INFO 1 --- [nio-9104-exec-9] c.d.escloud.aioitms.util.RestItemsUtil   : [deviceAccGet] req:{"deviceIdList":["542869868194575155"],"adcdIdSet":[797833518060109,797884977869379,797833518060108,797884977869378,797833518060111,797884977869377,797833518060110,797884977869376,797833518060105,797884977869383,797833518060104,797884977869382,797833518060107,797884977869381,797833518060106,797884977869380,797833518060101,797833518060100,797833518060103,797833518060102,797833518060097,797833518056000,797833518060096,797833518060099,797833518060098,797833518060116,797833518060119,797833518060118,797833518060113,797833518060112,797833518060115,797833518060114],"pageEnable":false}
2024-12-02 14:52:09 [INFO ](com.navercorp.pinpoint.bootstrap.config.DefaultProfilerConfig) profiler.jdk.http.param=true
2024-12-02 14:52:10.618  INFO 1 --- [nio-9104-exec-9] c.d.e.a.u.service.UpgradeServiceImpl     : [getAccList] queryMap:{"deviceIdList":["542869868194575155"],"adcdIdSet":[797833518060109,797884977869379,797833518060108,797884977869378,797833518060111,797884977869377,797833518060110,797884977869376,797833518060105,797884977869383,797833518060104,797884977869382,797833518060107,797884977869381,797833518060106,797884977869380,797833518060101,797833518060100,797833518060103,797833518060102,797833518060097,797833518056000,797833518060096,797833518060099,797833518060098,797833518060116,797833518060119,797833518060118,797833518060113,797833518060112,797833518060115,797833518060114],"pageEnable":false} dcpDcdList: [{"accId":796845575389760,"adcId":797833355813440,"collectCode":"E10CollectProcessCPU","collectName":"E10-進程CPU採集","cron":"0 */1 * * * * ","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060115,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:23","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845577724480,"adcId":797833355813440,"collectCode":"E10CollectProcessMEM","collectName":"E10-進程MEM採集","cron":"0 */1 * * * * ","deviceId":"542869868194575155","errorMessage":"errNum:1: Runner[E10CollectProcessMEM.797833518060116] \"node_JYcNTcnacK5qrU7\" Execute error: Runner[E10CollectProcessMEM.797833518060116] \"node_JYcNTcnacK5qrU7\" \"CollectProcessInfo\" task execution failed and gave up after 3 tries, last error: [wmi]win32api not Found Designation Program name  ","hasExecParams":false,"id":797833518060116,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:51:21","lastExecStatus":false,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":
"DefaultConfig","settingCron":false},{"accId":796852410720832,"adcId":797833355813440,"collectCode":"E10AppAutoUpdate","collectName":"E10应用自动更新","cron":"0 0 */3 * * * ","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869376,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:05:27","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845566145088,"adcId":797833355813440,"collectCode":"WindowsUpdateCollected","collectName":"WindowsUpdate资讯","cron":"5 44 16 * * *","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060112,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:41:54","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845537460800,"adcId":797833355813440,"collectCode":"WmiCpuUsageCollected","collectName":"Windows_Wmi的Cpu使用率","cron":"0 */5 * * * *","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060100,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:09","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845564310080,"adcId":797833355813440,"collectCode":"WmiBaseBoardCollected","collectName":"Windows_主机板信息","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060111,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-
12-02 13:41:54","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845557285440,"adcId":797833355813440,"collectCode":"WmiProcessorStatusCollected","collectName":"Windows_处理器状态检查","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060108,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:41:56","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845546758720,"adcId":797833355813440,"collectCode":"WindowsSecurityCEV20200796Collected","collectName":"Windows_安全漏洞_CVE-2020-0796","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060118,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:42:09","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845546758720,"adcId":797833355813440,"collectCode":"WindowsSecurityCEV20200796Collected","collectName":"Windows_安全漏洞_CVE-2020-0796","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060103,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:42:09","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845548671552,"adcId":797833355813440,"collectCode":"WindowsSecurityCEV20201472Collected","collectName":"Windows_安全漏洞_CVE-2020-1472","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id"
:797833518060104,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:42:08","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845550551616,"adcId":797833355813440,"collectCode":"WindowsSecurityMS17010Collected","collectName":"Windows_安全漏洞_MS-17-010","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060105,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:42:09","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845559181888,"adcId":797833355813440,"collectCode":"WmiPhysicalMemoryCollected","collectName":"Windows_物理内存状态检查","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060109,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:41:55","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845568336448,"adcId":797833355813440,"collectCode":"WindowsLoginErrorCollectedV2","collectName":"Windows_登入异常检测V2","deviceId":"542869868194575155","errorMessage":"Runner[WindowsLoginErrorCollectedV2.797833518060113] metric and reader in config is nil ","hasExecParams":false,"id":797833518060113,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:36:10","lastExecStatus":false,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845542593088,
"adcId":797833355813440,"collectCode":"WindowsLoginErrorCollected","collectName":"Windows_登录异常检测","deviceId":"542869868194575155","errorMessage":"Runner[WindowsLoginErrorCollected.797833518060102] metric and reader in config is nil ","hasExecParams":false,"id":797833518060102,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:36:10","lastExecStatus":false,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845554328128,"adcId":797833355813440,"collectCode":"WmiSmartStatusCollected","collectName":"Windows_磁盘SMART状态检查","cron":"0 */5 * * * *","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060107,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:51:31","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845552423488,"adcId":797833355813440,"collectCode":"WmiDiskDriverStatusCollected","collectName":"Windows_磁盘状态检查","cron":"0 */5 * * * *","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060106,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:15","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845562040896,"adcId":797833355813440,"collectCode":"WmiNetworkAdapterCollected","collectName":"Windows_网络适配器状态检查","cron":"0 0 0 */1 * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060110,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":f
alse,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 13:41:55","lastExecStatus":true,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845539414592,"adcId":797833355813440,"collectCode":"WindowsAntivirusCollected","collectName":"Windows_防毒状态检查","cron":"0 0 */4 * * *","deviceId":"542869868194575155","errorMessage":"Runner WindowsAntivirusCollected.797833518060101 NewExecutorTransform transform type binaryparsing unmarshal config error: binaryparsing.BinaryParsing.ReverseResult: CurNodeName: OutOfRangeValue: ReadString: expects \" or n, but found 0, error found in #10 byte of ...|e_value\":0,\"node_nam|..., bigger context ...|\",\"source_data_template\":\"\",\"out_of_range_value\":0,\"node_name\":\"node_JQDnKtbux94S9N1\",\"reverse_resul|... ","hasExecParams":false,"id":797833518060101,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:36:10","lastExecStatus":false,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845518086720,"adcId":797833355813440,"collectCode":"WindowsSoftwareInfoCollected","collectName":"Windows软件信息","cron":"0 0 0 */1 * ? ","deviceId":"542869868194575155","hasExecParams":false,"id":797833518056000,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"","lastExecStatus":false,"runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":797840332264000,"adcId":797833355813440,"collectCode":"offline_aiops_112903","collectName":"离线aiops_112903","cron":"0 0/5 * * * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869377,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50
:28","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796956455690816,"adcId":797833355813440,"collectCode":"offline_aiops_112904","collectName":"离线aiops_112904","cron":"0 0/5 * * * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869378,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:51:42","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":797871928816192,"adcId":797833355813440,"collectCode":"offline_aiops_120201","collectName":"离线aiops_120201V1","cron":"0 0/5 * * * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869379,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:14","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":797871931200064,"adcId":797833355813440,"collectCode":"offline_aiops_120202","collectName":"离线aiops_120202","cron":"0 0/5 * * * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869380,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:51:18","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796852413395520,"adcId":797833355813440,"collectCode":"offline_aio_pre_112801_edit","collectName":"离线aio同步流程112801_edit","cron":"* * 0/9 * * ?","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797884977869381,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isE
nable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:12:45","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845521433152,"adcId":797833355813440,"collectCode":"CpuCollected","collectName":"设备CPU性能指标","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060096,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:46:12","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845530747456,"adcId":797833355813440,"collectCode":"MemoryCollected","collectName":"设备内存性能指标","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060098,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:25","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845573501504,"adcId":797833355813440,"collectCode":"DiskioCollected","collectName":"设备磁盘I/O性能指标","deviceId":"542869868194575155","errorMessage":"Runner DiskioCollected.797833518060119 NewCollectorMetric metric type: diskio unmarshal config error: system.DiskIOStats.NameTemplates: []string: decode slice: expect [ or n, but found \", error found in #10 byte of ...|mplates\":\"\",\"file_pa|..., bigger context ...|PrV789OqaIFrQ\",\"total_cpu\":true,\"name_templates\":\"\",\"file_param_names\":\"\",\"mount_points\":[],\"devices|... ","hasExecParams":false,"id":797833518060119,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:36:10","lastExecStatus":false,"r
unningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845573501504,"adcId":797833355813440,"collectCode":"DiskioCollected","collectName":"设备磁盘I/O性能指标","deviceId":"542869868194575155","errorMessage":"Runner DiskioCollected.797833518060114 NewCollectorMetric metric type: diskio unmarshal config error: system.DiskIOStats.Devices: []string: decode slice: expect [ or n, but found \", error found in #10 byte of ...|devices\":\"\",\"device_|..., bigger context ...|m_names\":\"\",\"skip_serial_number\":false,\"devices\":\"\",\"device_tags\":\"\",\"per_cpu\":true,\"total_cpu\":true|... ","hasExecParams":false,"id":797833518060114,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:36:10","lastExecStatus":false,"runningStatus":"init","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845526753856,"adcId":797833355813440,"collectCode":"DiskCollected","collectName":"设备磁盘空间信息","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060097,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:51:11","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false},{"accId":796845535539776,"adcId":797833355813440,"collectCode":"NetworkCollected","collectName":"设备网络流量监控","deviceId":"542869868194575155","errorMessage":"","hasExecParams":false,"id":797833518060099,"interval":0,"isCustomizeCollectWarning":false,"isDataSync":false,"isEnable":true,"isExecParamsSetCompleted":false,"isOldDataSync":false,"lastExecDateTime":"2024-12-02 14:50:36","lastExecStatus":true,"runningStatus":"runtime","runtimeVersion":"v2","scopeId":"DefaultConfig","settingCron":false}]