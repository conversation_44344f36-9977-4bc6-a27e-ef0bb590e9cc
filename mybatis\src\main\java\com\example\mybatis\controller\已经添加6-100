2024-10-12T16:27:29.973+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : selectE10 count:421
2024-10-12T16:27:30.404+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:518439693906496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=741c199971c2634b6196e2f5ee332eac&deviceId=496515776438613059
2024-10-12T16:27:30.497+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7a403af0b756fccfbd46563a9ef2bdc8\\\",\\\"dbIdValue\\\":\\\"7a403af0b756fccfbd46563a9ef2bdc8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MLQC\\\",\\\"targetValue\\\":\\\"MLQC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   496515776438613059
2024-10-12T16:27:32.316+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:348728872600128 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a40aca9629a102456351b5496bc79bf9&deviceId=496195688833495347
2024-10-12T16:27:32.317+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"40983b97e74c306b59e15f494ac6f67c\\\",\\\"dbIdValue\\\":\\\"40983b97e74c306b59e15f494ac6f67c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"T-motor\\\",\\\"targetValue\\\":\\\"T-motor\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   496195688833495347
2024-10-12T16:27:33.715+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:230554779136576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9b470f561e0e00e3a02eea0ce979f9ab&deviceId=493768354209743416
2024-10-12T16:27:33.716+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"90905ecd907e870ae87255f00c6340f4\\\",\\\"dbIdValue\\\":\\\"90905ecd907e870ae87255f00c6340f4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"VSITS\\\",\\\"targetValue\\\":\\\"VSITS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   493768354209743416
2024-10-12T16:27:35.053+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324750455360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=06eacf53691d6b93a29f36cf3f415588&deviceId=493303111239086644
2024-10-12T16:27:35.053+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d4b985c334fedf3d780f8876bb991b8b\\\",\\\"dbIdValue\\\":\\\"d4b985c334fedf3d780f8876bb991b8b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GY\\\",\\\"targetValue\\\":\\\"GY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   493303111239086644
2024-10-12T16:27:36.366+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320832524864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f2565dcfba2244baffc7c31c9558f730&deviceId=493191291933309234
2024-10-12T16:27:36.367+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6c22217cb98b7af5c7958fcacdd14345\\\",\\\"dbIdValue\\\":\\\"6c22217cb98b7af5c7958fcacdd14345\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_KYL\\\",\\\"targetValue\\\":\\\"E10_KYL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   493191291933309234
2024-10-12T16:27:37.709+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:53702384140864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=494d7511b3ec4f84e9be784264d0af3b&deviceId=493171275976423238
2024-10-12T16:27:37.710+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e2d631cf214522143e53d0f6ac3e31bc\\\",\\\"dbIdValue\\\":\\\"e2d631cf214522143e53d0f6ac3e31bc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FRECOM\\\",\\\"targetValue\\\":\\\"FRECOM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   493171275976423238
2024-10-12T16:27:39.036+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318777201216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=31899d6f899cc50773dfc05b7b462c37&deviceId=434637941080340036
2024-10-12T16:27:39.036+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ee3cefb4ca021809b962037bef0fa5f7\\\",\\\"dbIdValue\\\":\\\"ee3cefb4ca021809b962037bef0fa5f7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SDZS\\\",\\\"targetValue\\\":\\\"SDZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   434637941080340036
2024-10-12T16:27:40.391+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:238592525902400 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5e3501a6f4b6a46b5e5ee00f881b2473&deviceId=492597660730017091
2024-10-12T16:27:40.392+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"dd863582ace8ad42a5a9fa88cca2e0de\\\",\\\"dbIdValue\\\":\\\"dd863582ace8ad42a5a9fa88cca2e0de\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   492597660730017091
2024-10-12T16:27:41.717+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:463983668195904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=758c66724a66ec56a40a6c922adfea59&deviceId=492461296340449604
2024-10-12T16:27:41.718+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e899c90352b40059ab27cdbafd6ae147\\\",\\\"dbIdValue\\\":\\\"e899c90352b40059ab27cdbafd6ae147\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HL_E10\\\",\\\"targetValue\\\":\\\"HL_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   492461296340449604
2024-10-12T16:27:43.072+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:255912587641408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5924a1494361b7e9f4d839546a0cdf5f&deviceId=492428908411503671
2024-10-12T16:27:43.073+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9e3a4e45c254f6410b2ba765c26870d5\\\",\\\"dbIdValue\\\":\\\"9e3a4e45c254f6410b2ba765c26870d5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HK_GROUP\\\",\\\"targetValue\\\":\\\"HK_GROUP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   492428908411503671
2024-10-12T16:27:44.517+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:456740097024576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=495c1fabb7d0630856566cf01c964553&deviceId=491696240141025857
2024-10-12T16:27:44.517+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b55a3b978432c1683669cae89cbf2116\\\",\\\"dbIdValue\\\":\\\"b55a3b978432c1683669cae89cbf2116\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HFKXW\\\",\\\"targetValue\\\":\\\"HFKXW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   491696240141025857
2024-10-12T16:27:45.867+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:342800235729472 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=617929f4bc6499c17a64813e9889b724&deviceId=491417398415471683
2024-10-12T16:27:45.868+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0fb301e7a0ab3a2b47e8cc71a1e7c30d\\\",\\\"dbIdValue\\\":\\\"0fb301e7a0ab3a2b47e8cc71a1e7c30d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AH\\\",\\\"targetValue\\\":\\\"AH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   491417398415471683
2024-10-12T16:27:47.203+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:457477368783424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d9654599804e36b80022520c6f39e1d5&deviceId=491289684543025458
2024-10-12T16:27:47.204+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f128760870ae61efc0cd72cba85fd3f5\\\",\\\"dbIdValue\\\":\\\"f128760870ae61efc0cd72cba85fd3f5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XGDATA\\\",\\\"targetValue\\\":\\\"XGDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   491289684543025458
2024-10-12T16:27:48.666+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:367485137883712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f7d0098ee7b8832645a4e364bb7d92f5&deviceId=491265931394167875
2024-10-12T16:27:48.667+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5579787ba759e71442337f7ea694babf\\\",\\\"dbIdValue\\\":\\\"5579787ba759e71442337f7ea694babf\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_MN\\\",\\\"targetValue\\\":\\\"E10_MN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   491265931394167875
2024-10-12T16:27:50.128+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:350940886487616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3108a3a5df7e93b6a5ffbf07de7aa735&deviceId=490711914435851331
2024-10-12T16:27:50.129+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c60181e81c4caf9977e75bca1751f13f\\\",\\\"dbIdValue\\\":\\\"c60181e81c4caf9977e75bca1751f13f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   490711914435851331
2024-10-12T16:27:51.615+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:347313295761984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9c0ea294dcbf8a5fc9a123262404c0c9&deviceId=490394753364407093
2024-10-12T16:27:51.615+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1954843813f73e27fae8136281e4f840\\\",\\\"dbIdValue\\\":\\\"1954843813f73e27fae8136281e4f840\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZMODATA\\\",\\\"targetValue\\\":\\\"ZMODATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   490394753364407093
2024-10-12T16:27:52.957+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:450061819605568 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=707aa0d216daef460210be7ad9796985&deviceId=490139351070553155
2024-10-12T16:27:52.957+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5443557208c972212390de9512a86163\\\",\\\"dbIdValue\\\":\\\"5443557208c972212390de9512a86163\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SDX_DATA\\\",\\\"targetValue\\\":\\\"SDX_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   490139351070553155
2024-10-12T16:27:54.339+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:117046633181760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1bf597f39b63822b2995d0bdd4bac7db&deviceId=472917103662347056
2024-10-12T16:27:54.339+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a1654ee5597ea3e2c317d2a784b82303\\\",\\\"dbIdValue\\\":\\\"a1654ee5597ea3e2c317d2a784b82303\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LSMDATA\\\",\\\"targetValue\\\":\\\"LSMDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472917103662347056
2024-10-12T16:27:55.726+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:516227840713280 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=de8b4a6225bb798d40526369a4abcb15&deviceId=489669949766252345
2024-10-12T16:27:55.727+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"61ebfd38bf141d45151833df1c7308e3\\\",\\\"dbIdValue\\\":\\\"61ebfd38bf141d45151833df1c7308e3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BRK\\\",\\\"targetValue\\\":\\\"BRK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   489669949766252345
2024-10-12T16:27:57.087+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:99308179759680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=27871164829e6c9633cbfc812440ab0d&deviceId=489665660704534833
2024-10-12T16:27:57.087+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"42a291f8313507cdf41254bf2b37a27c\\\",\\\"dbIdValue\\\":\\\"42a291f8313507cdf41254bf2b37a27c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HY\\\",\\\"targetValue\\\":\\\"HY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   489665660704534833
2024-10-12T16:27:58.459+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:219345114653248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6a035d27dbc1f0ad06aa77d744c3d44e&deviceId=489545836116586550
2024-10-12T16:27:58.460+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bd4509cb1ba35e416cac1dc4078d6531\\\",\\\"dbIdValue\\\":\\\"bd4509cb1ba35e416cac1dc4078d6531\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LDGD\\\",\\\"targetValue\\\":\\\"LDGD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   489545836116586550
2024-10-12T16:27:59.812+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:333863177056832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4fa2fb19ff041658a1725c3fb13b4551&deviceId=489529470412140596
2024-10-12T16:27:59.813+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d0c1450123f869dbd0e0c0149754478b\\\",\\\"dbIdValue\\\":\\\"d0c1450123f869dbd0e0c0149754478b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BL_ZSK\\\",\\\"targetValue\\\":\\\"BL_ZSK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   489529470412140596
2024-10-12T16:28:01.161+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:319704657121856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dda9293ae54eaa60b29fd5498b4ce2c4&deviceId=489525563518628931
2024-10-12T16:28:01.162+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e6af8833ad657a86c82803ce0a5996f3\\\",\\\"dbIdValue\\\":\\\"e6af8833ad657a86c82803ce0a5996f3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TYHX2023\\\",\\\"targetValue\\\":\\\"TYHX2023\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   489525563518628931
2024-10-12T16:28:02.515+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:537815772021312 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=533b825f57c6b34c50c3b3a29e5605aa&deviceId=489122840121984048
2024-10-12T16:28:02.515+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0d93fb52ac069db1bbe6e44a5819af47\\\",\\\"dbIdValue\\\":\\\"0d93fb52ac069db1bbe6e44a5819af47\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10ZQdata\\\",\\\"targetValue\\\":\\\"E10ZQdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503dff15c6511e9ab0232adbc3ad9c3819fa44d5b92e3fae8d41d4c01ad7dcb9c5e00f42d8f6748519115044269c2475875df29dda8ba5c64ec4194d31e8e8e6f66869587d0e81d821479d96e6f6c36a79321e0a3e9cf2a0a47b7c320d107a384eebe82e5ea893dd258ee9bacb18458eaeb671e363ada01ca0a83ac4c2e8073e5412374c929440a78d5616c4a357b43d02f244b67dcece90635e50cf0e8f608ab6c3b4223b48ebabf1bba8744ffab2a974f63be6fd41db8051fa5eac167af59031b65d977422d822debddc48c53ed1e6a1c995e9593a2fceb8a27929488311f477cbedddfc90a640fa265d0f34c0caa7b1bddac9a6776e7789eccb10921d6accd96","collectName":"E10附件数据采集","accId":779544910123584,"adimId":666922647974464,"id":779872192971328,"adcId":666612965274176,"execParamsVersion":"42d8f4e7c79d37f9bce8d2a77397bb67","aiId":666922647953986,"isEnable":1},"paramsMap":{"deviceId":"489122840121984048","eid":537815772021312,"aiId":666922647953986,"execParamsDbAiId":666922524914240,"execParamsDbId":"0d93fb52ac069db1bbe6e44a5819af47"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779872192971328 acc incomplete","batchId":779872194187840,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   489122840121984048
2024-10-12T16:28:03.980+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:303782651093568 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b69b0e173f40cb50e2887769af86becf&deviceId=488662613589247553
2024-10-12T16:28:03.980+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ffe86753a7becfce3643b8a5585c5442\\\",\\\"dbIdValue\\\":\\\"ffe86753a7becfce3643b8a5585c5442\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   488662613589247553
2024-10-12T16:28:05.332+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:241069755806272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9b2a7e8ee72b59e422cc5f147ab69617&deviceId=435914263559681091
2024-10-12T16:28:05.333+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5e811ec84b7987f0173eb5efdc7dab16\\\",\\\"dbIdValue\\\":\\\"5e811ec84b7987f0173eb5efdc7dab16\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Demo\\\",\\\"targetValue\\\":\\\"E10_Demo\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   435914263559681091
2024-10-12T16:28:06.720+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:462166436569664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=33a2e6b66113b8a490f960cf7f34b28d&deviceId=464763671025824835
2024-10-12T16:28:06.721+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"42048e9b8485bb07c24392594b08b3fc\\\",\\\"dbIdValue\\\":\\\"42048e9b8485bb07c24392594b08b3fc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HB\\\",\\\"targetValue\\\":\\\"HB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   464763671025824835
2024-10-12T16:28:08.034+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321318765120 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8a43f7efde81a691193db32c9adf7ac5&deviceId=488109070050407491
2024-10-12T16:28:08.034+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"67c448bfef0affeaa9ababeed1614400\\\",\\\"dbIdValue\\\":\\\"67c448bfef0affeaa9ababeed1614400\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JUST\\\",\\\"targetValue\\\":\\\"JUST\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   488109070050407491
2024-10-12T16:28:09.404+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:486246451118656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=da8ff192a94077c29d4c4f5211158ab1&deviceId=435488249645905221
2024-10-12T16:28:09.405+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"99029523a216238b24a1554016f696ad\\\",\\\"dbIdValue\\\":\\\"99029523a216238b24a1554016f696ad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CZNY\\\",\\\"targetValue\\\":\\\"CZNY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   435488249645905221
2024-10-12T16:28:10.782+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324154901056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=32a07ffd7ec378001b756028443dcb12&deviceId=418082076756030531
2024-10-12T16:28:10.782+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f524783c4436b359d5392f18b98fbec3\\\",\\\"dbIdValue\\\":\\\"f524783c4436b359d5392f18b98fbec3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_SANDA\\\",\\\"targetValue\\\":\\\"E10_SANDA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   418082076756030531
2024-10-12T16:28:12.101+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:340868538053184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c8ad19f59581b77bf334791a9850c8e6&deviceId=488081100116079683
2024-10-12T16:28:12.102+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"95c4b39a8edc399c21e300333c25bf76\\\",\\\"dbIdValue\\\":\\\"95c4b39a8edc399c21e300333c25bf76\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   488081100116079683
2024-10-12T16:28:13.503+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:306613661614656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7806bf4019b0408da8bebcff6144f80c&deviceId=487672518115602480
2024-10-12T16:28:13.504+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1d46cb5661eb279e9d626a80c328ce3a\\\",\\\"dbIdValue\\\":\\\"1d46cb5661eb279e9d626a80c328ce3a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BLW_DATA\\\",\\\"targetValue\\\":\\\"BLW_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487672518115602480
2024-10-12T16:28:14.887+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:334924905103936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=14e07fd95292510b01fea29ff63c0902&deviceId=487493168569267267
2024-10-12T16:28:14.887+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1a177839caae8aa94f13aea75d43c997\\\",\\\"dbIdValue\\\":\\\"1a177839caae8aa94f13aea75d43c997\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JSWELZS\\\",\\\"targetValue\\\":\\\"JSWELZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487493168569267267
2024-10-12T16:28:16.225+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:424218141745728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2a74c84db2f63fa313fcd2148a2facd1&deviceId=487391150999614531
2024-10-12T16:28:16.225+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cd63263c0e24143e27a938c122f69433\\\",\\\"dbIdValue\\\":\\\"cd63263c0e24143e27a938c122f69433\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KYdata\\\",\\\"targetValue\\\":\\\"KYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487391150999614531
2024-10-12T16:28:17.556+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:519147656208960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=393118e2d5b4bfa4accc332357f4ee88&deviceId=487383441331992887
2024-10-12T16:28:17.557+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"dbf0de9964bd698755b3aced90033cbd\\\",\\\"dbIdValue\\\":\\\"dbf0de9964bd698755b3aced90033cbd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YPZS\\\",\\\"targetValue\\\":\\\"YPZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487383441331992887
2024-10-12T16:28:18.914+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321761735232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ffe92e8bc1aeb7196235206561d1d1db&deviceId=487387476537652291
2024-10-12T16:28:18.914+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6ba1c3b122ba2fa34d6d5a583410d443\\\",\\\"dbIdValue\\\":\\\"6ba1c3b122ba2fa34d6d5a583410d443\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CS\\\",\\\"targetValue\\\":\\\"CS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487387476537652291
2024-10-12T16:28:20.280+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:261585245823552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9e1316eb4172cfc9aa2402c4b4b95081&deviceId=487379772473619523
2024-10-12T16:28:20.281+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9825c12356f3752e4891eaf75c4ba584\\\",\\\"dbIdValue\\\":\\\"9825c12356f3752e4891eaf75c4ba584\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HTM202103\\\",\\\"targetValue\\\":\\\"HTM202103\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487379772473619523
2024-10-12T16:28:21.648+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:327507393045056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2b47079561cf58fa92376bb2050b2be3&deviceId=487376444612162627
2024-10-12T16:28:21.649+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7730c540ae218a5cacb81d63e1c545c2\\\",\\\"dbIdValue\\\":\\\"7730c540ae218a5cacb81d63e1c545c2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":0,"errMsg":"success"}   ---   487376444612162627
2024-10-12T16:28:22.771+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41319652053568 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8be8f580235d09cda4ab4463e55b8fd1&deviceId=487227426695295284
2024-10-12T16:28:22.772+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b6c76bf72892d0310fd6428bc6bfbffb\\\",\\\"dbIdValue\\\":\\\"b6c76bf72892d0310fd6428bc6bfbffb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SRdata\\\",\\\"targetValue\\\":\\\"SRdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487227426695295284
2024-10-12T16:28:24.079+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:301659362316864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=29384dbde580decc5fb7304b0bfbf8a9&deviceId=487211785967907632
2024-10-12T16:28:24.079+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"73c0ca464b3ba0139ab951d232566614\\\",\\\"dbIdValue\\\":\\\"73c0ca464b3ba0139ab951d232566614\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BTLDATA\\\",\\\"targetValue\\\":\\\"BTLDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487211785967907632
2024-10-12T16:28:25.528+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:342446291751488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d192e3758c8dc65d032c950a999c8565&deviceId=487072885282715460
2024-10-12T16:28:25.529+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"143e7e827bf657bfc3e243b32e749e05\\\",\\\"dbIdValue\\\":\\\"143e7e827bf657bfc3e243b32e749e05\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GBEST\\\",\\\"targetValue\\\":\\\"GBEST\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487072885282715460
2024-10-12T16:28:26.866+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:204437148615232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6642fab1edd99e04f57db8ebc871774f&deviceId=486668115569030211
2024-10-12T16:28:26.866+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bab24957e97b92fc482cceda18c6ffef\\\",\\\"dbIdValue\\\":\\\"bab24957e97b92fc482cceda18c6ffef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HZ-DATA\\\",\\\"targetValue\\\":\\\"HZ-DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   486668115569030211
2024-10-12T16:28:28.206+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:313427192324672 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=18709cc141af66405e8b0646e795e90f&deviceId=486479662772400176
2024-10-12T16:28:28.207+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7cd9b67c49c6c14304141fa7bf020d34\\\",\\\"dbIdValue\\\":\\\"7cd9b67c49c6c14304141fa7bf020d34\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Pinnacle\\\",\\\"targetValue\\\":\\\"Pinnacle\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   486479662772400176
2024-10-12T16:28:29.592+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:247988449624640 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d06f480f4485e7cf5bba762ebba99913&deviceId=485509922759390275
2024-10-12T16:28:29.592+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0287d1fe5089d07a40a42929894853e6\\\",\\\"dbIdValue\\\":\\\"0287d1fe5089d07a40a42929894853e6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"kggwh\\\",\\\"targetValue\\\":\\\"kggwh\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503c2d76693fdd195821cdcc8031e261944d164746a1e144dc65ad5fb0131a8862e8799e9eaabb9ec79139924ef5c6a339d8a6aa42de41f9ce3087049c883e5960fca6afc23c0227905237a758dbfe3ab0de9c38f1c3658fefc529f65986dd2f1ad415cc6bcd3c203660d58f43df45b92ac9a23dd1d7268a7425f0419c5e17be2667e6ba34fb79d0a1c9b994efa81b2e1c40a276fd57576f22180d740a01da63a00ed734b3125d3537c759e4ac507452d1ddc588f597fb0d481af281771e755228eb675b14af465f770243cb198aa51a0049b0ca6148afd872a3aaf06f3d6c0a8f3804adc5c63a5abf608c1f97ebb3cf26eb11f7429ee8d7c19","collectName":"E10附件数据采集","accId":779544910123584,"adimId":657789457392192,"id":779872303866432,"adcId":657789355844160,"execParamsVersion":"63b194f199c66eada430530d134dc35d","aiId":657789457371713,"isEnable":1},"paramsMap":{"deviceId":"485509922759390275","eid":247988449624640,"aiId":657789457371713,"execParamsDbAiId":657788872045120,"execParamsDbId":"0287d1fe5089d07a40a42929894853e6"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779872303866432 acc incomplete","batchId":779872304898624,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   485509922759390275
2024-10-12T16:28:30.977+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:498971851244096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d5c455dffcfb2083a67fd35c98ca7e10&deviceId=485494740821750851
2024-10-12T16:28:30.979+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1120788cc2abe1a476464eac2b4c0d5d\\\",\\\"dbIdValue\\\":\\\"1120788cc2abe1a476464eac2b4c0d5d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_BN\\\",\\\"targetValue\\\":\\\"E10_BN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485494740821750851
2024-10-12T16:28:32.296+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:130494520853056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8d72bed286e5f21ee5086ef567cd4ca7&deviceId=485328769981104440
2024-10-12T16:28:32.297+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d95c7b487294231fcf1580a436207160\\\",\\\"dbIdValue\\\":\\\"d95c7b487294231fcf1580a436207160\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1DemoData.CHS_Single\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1DemoData.CHS_Single\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485328769981104440
2024-10-12T16:28:33.709+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:498891071644224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=286b29229c6f4daa6b2ce2246a85b2fb&deviceId=485322213646415153
2024-10-12T16:28:33.709+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8622dc635e0947bf74d0a7cac40258ea\\\",\\\"dbIdValue\\\":\\\"8622dc635e0947bf74d0a7cac40258ea\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_GD\\\",\\\"targetValue\\\":\\\"E10_GD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485322213646415153
2024-10-12T16:28:35.060+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:462520306000448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ea0335598deabdad1e201249a4b4f062&deviceId=485207744345814081
2024-10-12T16:28:35.060+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f0c7c262be5e6b4078a1ca0d6a115f32\\\",\\\"dbIdValue\\\":\\\"f0c7c262be5e6b4078a1ca0d6a115f32\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HXYB\\\",\\\"targetValue\\\":\\\"HXYB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485207744345814081
2024-10-12T16:28:36.428+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:90590010155584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5b4a8564726d739acdbe6aff92d6f7ab&deviceId=485199091261781043
2024-10-12T16:28:36.429+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2f6b3e8c5f02ccf7c30d66aac335bf63\\\",\\\"dbIdValue\\\":\\\"2f6b3e8c5f02ccf7c30d66aac335bf63\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JSJL\\\",\\\"targetValue\\\":\\\"JSJL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485199091261781043
2024-10-12T16:28:37.845+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:231351674753600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e12dce33f7e466cdd206fd21d73b618c&deviceId=484605263177856067
2024-10-12T16:28:37.846+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fb1dbb4c6813c463258c6894c4b33b87\\\",\\\"dbIdValue\\\":\\\"fb1dbb4c6813c463258c6894c4b33b87\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BYJC\\\",\\\"targetValue\\\":\\\"BYJC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484605263177856067
2024-10-12T16:28:39.196+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:456150271787584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=46d3cceb61dc258d74e4ff98ef62a021&deviceId=484597082607337798
2024-10-12T16:28:39.196+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6abd78fe8f7117ee663feaa7d4a2c4b9\\\",\\\"dbIdValue\\\":\\\"6abd78fe8f7117ee663feaa7d4a2c4b9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HHYX-E10\\\",\\\"targetValue\\\":\\\"HHYX-E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484597082607337798
2024-10-12T16:28:40.554+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323058442816 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3cf941696d215452962bdc1601d5a085&deviceId=484462191643210305
2024-10-12T16:28:40.555+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0dab31e132841e038ef07b5012f16b65\\\",\\\"dbIdValue\\\":\\\"0dab31e132841e038ef07b5012f16b65\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJBH2019\\\",\\\"targetValue\\\":\\\"ZJBH2019\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484462191643210305
2024-10-12T16:28:41.895+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:396667150750272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fffb7d4c2da4eb768fd685c4c0911b21&deviceId=484462065646321976
2024-10-12T16:28:41.895+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"364b1951a090b20e9e87a8e23d682d3d\\\",\\\"dbIdValue\\\":\\\"364b1951a090b20e9e87a8e23d682d3d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10PROD\\\",\\\"targetValue\\\":\\\"E10PROD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85032d2d4ee2554ca1926efc5ac13777bc0f7778cd4da765a025421bd37907ef87e815b1908fb772fa52a1e808e5ba0811ec7e4ae255a99c87ee63ab692aae66bb1a1d7c1a9dd6fc3c562428831f416027fd7fb56ddf2b003f9e8c872dae2dbe9692cd5ed6fd9a7ff331ec607c9b65846aac26d08a11295541a385b2d5e5dfefbc9a42d7555b415659e59334b0dde6e977345faf67f0c4249b6b026436e3ef3a660bf17d43b66af2c129dcb4aba8704115caa2733a32896b43740acce6982681dc4356dc7e8db7c9373e0a0ab6d8c0b773ee980952e35cd2eb53a04ed8c100499243a677ebd93ef770770f6337ada654c79459ab10da253ebf20","collectName":"E10附件数据采集","accId":779544910123584,"adimId":655230866866752,"id":779872354288192,"adcId":655230761546304,"execParamsVersion":"e8d07c130d9005a897be50e82c90ed16","aiId":655230866760258,"isEnable":1},"paramsMap":{"deviceId":"484462065646321976","eid":396667150750272,"aiId":655230866760258,"execParamsDbAiId":655230696333888,"execParamsDbId":"364b1951a090b20e9e87a8e23d682d3d"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779872354288192 acc incomplete","batchId":779872355570240,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   484462065646321976
2024-10-12T16:28:43.329+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324285932096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a8cf86ff517d7e505c2ee5af08894a75&deviceId=473187655882912835
2024-10-12T16:28:43.329+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3d64b54dd3ca891e14ca1c2b8570d7f7\\\",\\\"dbIdValue\\\":\\\"3d64b54dd3ca891e14ca1c2b8570d7f7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GZ\\\",\\\"targetValue\\\":\\\"GZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   473187655882912835
2024-10-12T16:28:44.638+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:302455707386432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a48aca33831b67b797d2366af3b9afcd&deviceId=484187946723853364
2024-10-12T16:28:44.639+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"683a5f162473b6f5158e583a32934df7\\\",\\\"dbIdValue\\\":\\\"683a5f162473b6f5158e583a32934df7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Hengdong\\\",\\\"targetValue\\\":\\\"Hengdong\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484187946723853364
2024-10-12T16:28:46.011+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320972927552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=272747cee22f0a874b96499d0f5e1ac3&deviceId=484184545478849603
2024-10-12T16:28:46.012+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8154dde0ac71de8284b249e7d79b9001\\\",\\\"dbIdValue\\\":\\\"8154dde0ac71de8284b249e7d79b9001\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ATMS\\\",\\\"targetValue\\\":\\\"ATMS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484184545478849603
2024-10-12T16:28:47.350+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324557464128 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=40e58a04e2f7d9a5d64d55f96ecd8f52&deviceId=484169469002790969
2024-10-12T16:28:47.350+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"20e4c0a464a5219b8878b1a69ab6b60b\\\",\\\"dbIdValue\\\":\\\"20e4c0a464a5219b8878b1a69ab6b60b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DFSP\\\",\\\"targetValue\\\":\\\"DFSP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   484169469002790969
2024-10-12T16:28:48.784+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:343345727775296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0fb82d72098e98845fa4b08f366590aa&deviceId=483616688307188803
2024-10-12T16:28:48.784+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"80b476a8ffce833fbb19a94a2c8f0c13\\\",\\\"dbIdValue\\\":\\\"80b476a8ffce833fbb19a94a2c8f0c13\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FEG\\\",\\\"targetValue\\\":\\\"FEG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483616688307188803
2024-10-12T16:28:50.123+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:284041950188096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e4cd7e3c6359d296335e480a2adc57cf&deviceId=483612733011407412
2024-10-12T16:28:50.123+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"efa1afde8f2efeea1624fdeb034d420e\\\",\\\"dbIdValue\\\":\\\"efa1afde8f2efeea1624fdeb034d420e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LBJM-202302\\\",\\\"targetValue\\\":\\\"LBJM-202302\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483612733011407412
2024-10-12T16:28:51.510+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324326138432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=18adba88ace433576a492bb62a7fc5df&deviceId=483608397443249219
2024-10-12T16:28:51.511+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f002c911396cbb17508590428f74e89c\\\",\\\"dbIdValue\\\":\\\"f002c911396cbb17508590428f74e89c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FL\\\",\\\"targetValue\\\":\\\"FL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483608397443249219
2024-10-12T16:28:52.844+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321857815104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=65b4ba5be1d28f2b8738c5ba548a43e7&deviceId=483468864642888771
2024-10-12T16:28:52.844+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c2c64641220d63e190730dcabff276d6\\\",\\\"dbIdValue\\\":\\\"c2c64641220d63e190730dcabff276d6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TEST-0924\\\",\\\"targetValue\\\":\\\"TEST-0924\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483468864642888771
2024-10-12T16:28:54.172+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:187260129948224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=68f84686c4731e36fda05e664b389987&deviceId=471614916772508739
2024-10-12T16:28:54.173+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"be8e7d45f7227a9bfe9b6d7be90bae4f\\\",\\\"dbIdValue\\\":\\\"be8e7d45f7227a9bfe9b6d7be90bae4f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YHZM\\\",\\\"targetValue\\\":\\\"YHZM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   471614916772508739
2024-10-12T16:28:55.581+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41317244518976 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ada1936c4a3b8f4a6cda32af4f16697b&deviceId=479263625790375217
2024-10-12T16:28:55.581+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5e2c8a3fba2ad5d0d4857af6670c74ca\\\",\\\"dbIdValue\\\":\\\"5e2c8a3fba2ad5d0d4857af6670c74ca\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YJJQ\\\",\\\"targetValue\\\":\\\"YJJQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479263625790375217
2024-10-12T16:28:56.920+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:233429594370624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=18f2c9a7aaf2d608829744c747aaa52b&deviceId=483323322562528322
2024-10-12T16:28:56.920+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9d7d172231f8436d9d2b35d678fcf512\\\",\\\"dbIdValue\\\":\\\"9d7d172231f8436d9d2b35d678fcf512\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SFTHMN\\\",\\\"targetValue\\\":\\\"SFTHMN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483323322562528322
2024-10-12T16:28:58.275+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:45583125127744 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4dc39e1614930b1e23aacc74143e4b4d&deviceId=483317215085474096
2024-10-12T16:28:58.276+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e44d2208d0407112ccbf06413846f89c\\\",\\\"dbIdValue\\\":\\\"e44d2208d0407112ccbf06413846f89c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DJFT\\\",\\\"targetValue\\\":\\\"DJFT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483317215085474096
2024-10-12T16:28:59.628+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322516001344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1d9104132188b17c4c3e5adf412c7849&deviceId=483038001996248370
2024-10-12T16:28:59.629+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0bd35283112d1d893e3b8cd67753b98e\\\",\\\"dbIdValue\\\":\\\"0bd35283112d1d893e3b8cd67753b98e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CSF\\\",\\\"targetValue\\\":\\\"CSF\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   483038001996248370
2024-10-12T16:29:00.971+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:198480526672448 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8f5f9d476b53497948eb03d7f4219b05&deviceId=482590251622085953
2024-10-12T16:29:00.971+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1d42bc64b5e052cad1fe3bc17b96b7c3\\\",\\\"dbIdValue\\\":\\\"1d42bc64b5e052cad1fe3bc17b96b7c3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10PROD\\\",\\\"targetValue\\\":\\\"E10PROD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850381eaee5b752afb483d62313c4223d4c5728625ace7ad103b4f4cc6286d741e30448dd10dd3e53f6c4bfdc01813efd448527874c6a594b1c93fe6d2e7b4c6d529826f7e3f4762dbb384f485d6f9e3b6216dd95ca56a4fecf79da0b521d3f0ebf9eff2a4b30271dc2796b9b59ea55e6f8cf595115cdf4cb4687b2b3e08fa3261d13129cb5c3c5e33f12bd7f7ed2e189845e226a9b91d5993914a19c2b1b696a40d938f544f16592cf97edb474c33b163d8409c484aee5bd8b450a60f493176fc1f4a6e98a42047d2e49ce20c24c449f7c354bb0c1d0b33822c800d50129242b587e81a536c4208f47342ff64dcd0e930cd4bae5916de8991ed","collectName":"E10附件数据采集","accId":779544910123584,"adimId":650661956772416,"id":779872432403008,"adcId":650661834678849,"execParamsVersion":"d1def05fe844571c1bc311ec3857f577","aiId":650661956702786,"isEnable":1},"paramsMap":{"deviceId":"482590251622085953","eid":198480526672448,"aiId":650661956702786,"execParamsDbAiId":650661385093696,"execParamsDbId":"1d42bc64b5e052cad1fe3bc17b96b7c3"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779872432403008 acc incomplete","batchId":779872433508928,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   482590251622085953
2024-10-12T16:29:02.356+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324453806656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b7c0c576a338f90cba1effb7b15c2f16&deviceId=482566160647206966
2024-10-12T16:29:02.356+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4943e11890a6c31f52d9825b28ef2ad9\\\",\\\"dbIdValue\\\":\\\"4943e11890a6c31f52d9825b28ef2ad9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GMYSJX\\\",\\\"targetValue\\\":\\\"GMYSJX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482566160647206966
2024-10-12T16:29:03.876+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:360167675642432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=da5e3d9311fd9a7fb17020eba58ebbe3&deviceId=482458152638366772
2024-10-12T16:29:03.877+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7b3636ad3c023df1410b2ad1f217a25f\\\",\\\"dbIdValue\\\":\\\"7b3636ad3c023df1410b2ad1f217a25f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482458152638366772
2024-10-12T16:29:05.228+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:501262391939648 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a44ac92cfbcfed3a3b1f83d6548053ba&deviceId=482450389082325296
2024-10-12T16:29:05.229+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7c4b473704eabeac74d0f7f762bdab72\\\",\\\"dbIdValue\\\":\\\"7c4b473704eabeac74d0f7f762bdab72\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_YA\\\",\\\"targetValue\\\":\\\"E10_YA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482450389082325296
2024-10-12T16:29:06.718+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324486632000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=df48e23f29b3a50135bc3b43edee37af&deviceId=482428010960533043
2024-10-12T16:29:06.719+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"17b0aaf0cbfbd17067a00c292f89a45d\\\",\\\"dbIdValue\\\":\\\"17b0aaf0cbfbd17067a00c292f89a45d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TX_E10\\\",\\\"targetValue\\\":\\\"TX_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482428010960533043
2024-10-12T16:29:08.089+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:184641471373888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ae51f9e8201d94ae30c6038220f8b23d&deviceId=482279551641076787
2024-10-12T16:29:08.090+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7afbd0bc20889d2d5629f3687d6db3c9\\\",\\\"dbIdValue\\\":\\\"7afbd0bc20889d2d5629f3687d6db3c9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LJ2021\\\",\\\"targetValue\\\":\\\"LJ2021\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482279551641076787
2024-10-12T16:29:09.426+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:541797042270784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5b5fe456e594bd4ad4518fa1ed5cd3f1&deviceId=482297779582940227
2024-10-12T16:29:09.426+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0cc3ae2f0a5541cee932fdecbdd541b2\\\",\\\"dbIdValue\\\":\\\"0cc3ae2f0a5541cee932fdecbdd541b2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XL\\\",\\\"targetValue\\\":\\\"XL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482297779582940227
2024-10-12T16:29:10.748+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:411448585781824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=22b1d1dae7255d357fb922a0ad68faad&deviceId=482031766639493939
2024-10-12T16:29:10.749+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2ea9dd88fa74b2b0cacd1ac5972ad1e2\\\",\\\"dbIdValue\\\":\\\"2ea9dd88fa74b2b0cacd1ac5972ad1e2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_HS1\\\",\\\"targetValue\\\":\\\"E10_HS1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482031766639493939
2024-10-12T16:29:12.087+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324249039424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f20f7e3431f77c0cc3cb059e1a0a424d&deviceId=482177022014010167
2024-10-12T16:29:12.087+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"249360c8b67fccd3dffafc06a1a7082e\\\",\\\"dbIdValue\\\":\\\"249360c8b67fccd3dffafc06a1a7082e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HMNEW01\\\",\\\"targetValue\\\":\\\"HMNEW01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482177022014010167
2024-10-12T16:29:13.426+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:233423418528320 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4b5d8aab806c2211fff9d7a833320ce6&deviceId=482173556864595011
2024-10-12T16:29:13.426+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b0ec78e555f79a7d1c0088af6035a16e\\\",\\\"dbIdValue\\\":\\\"b0ec78e555f79a7d1c0088af6035a16e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_CSHX\\\",\\\"targetValue\\\":\\\"E10_CSHX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482173556864595011
2024-10-12T16:29:14.765+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:380638048039488 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d4dfdf3b6be87401eeebb5bbdc56df78&deviceId=482172782780625972
2024-10-12T16:29:14.765+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"89501d0c01723fe6f84291a6e4241aa7\\\",\\\"dbIdValue\\\":\\\"89501d0c01723fe6f84291a6e4241aa7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Tartan\\\",\\\"targetValue\\\":\\\"Tartan\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482172782780625972
2024-10-12T16:29:16.098+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318614385216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3e711ab02ee9206285f7a66b8b27126c&deviceId=482164500624061507
2024-10-12T16:29:16.098+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1d4c3c490433c4d6a4d178272f8fffef\\\",\\\"dbIdValue\\\":\\\"1d4c3c490433c4d6a4d178272f8fffef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"keekoe\\\",\\\"targetValue\\\":\\\"keekoe\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482164500624061507
2024-10-12T16:29:17.467+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318481982016 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=82a039409639cea5e969fcb02b61b3f2&deviceId=482156590535817281
2024-10-12T16:29:17.467+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"de23519c91d431edbeb1d8c0b8f36458\\\",\\\"dbIdValue\\\":\\\"de23519c91d431edbeb1d8c0b8f36458\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HYdata\\\",\\\"targetValue\\\":\\\"HYdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482156590535817281
2024-10-12T16:29:18.789+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:233427091132992 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2f374dedd19c35ca23c57631e08c0ce1&deviceId=482142818236642371
2024-10-12T16:29:18.789+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0274dd246fc761d2946decaacb6ce85a\\\",\\\"dbIdValue\\\":\\\"0274dd246fc761d2946decaacb6ce85a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Hdata\\\",\\\"targetValue\\\":\\\"Hdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482142818236642371
2024-10-12T16:29:20.116+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:389160906404416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8f8de5e446a9b7a05c0fe365c571d09e&deviceId=482140084087305283
2024-10-12T16:29:20.116+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4cfd5040db194e392b49a327373f9ca5\\\",\\\"dbIdValue\\\":\\\"4cfd5040db194e392b49a327373f9ca5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CCIdata\\\",\\\"targetValue\\\":\\\"CCIdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482140084087305283
2024-10-12T16:29:21.499+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:236291753914944 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b68dec49530f67605dd293556e9239b5&deviceId=435811649660662851
2024-10-12T16:29:21.499+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"01ad05ec4adfc53cf17441a65547ba09\\\",\\\"dbIdValue\\\":\\\"01ad05ec4adfc53cf17441a65547ba09\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ALS\\\",\\\"targetValue\\\":\\\"ALS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   435811649660662851
2024-10-12T16:29:22.851+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320112595520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a283f06cbd9da234e5b327291e12b840&deviceId=482025573598638133
2024-10-12T16:29:22.851+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b617ef46cbf5b0cf994d1c73d2689854\\\",\\\"dbIdValue\\\":\\\"b617ef46cbf5b0cf994d1c73d2689854\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Demo20180920\\\",\\\"targetValue\\\":\\\"Demo20180920\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482025573598638133
2024-10-12T16:29:24.177+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:389912943968832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d4d194af4e336a45359c36222b184266&deviceId=482020021497775410
2024-10-12T16:29:24.178+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2f0917d0582b82bbb72833d035e33676\\\",\\\"dbIdValue\\\":\\\"2f0917d0582b82bbb72833d035e33676\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Data\\\",\\\"targetValue\\\":\\\"E10_Data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482020021497775410
2024-10-12T16:29:25.517+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:390325792657984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3ce18ffdb036cd6f7d0449961fb9ad8e&deviceId=482009894535313217
2024-10-12T16:29:25.517+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9dd09390bdedc497f609ae1c689361d6\\\",\\\"dbIdValue\\\":\\\"9dd09390bdedc497f609ae1c689361d6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   482009894535313217
2024-10-12T16:29:26.885+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:378101846323776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f499c115688f5fa9c5e29cb779f23d7b&deviceId=481996653503465011
2024-10-12T16:29:26.887+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"40f07deb8e53e73d2c1dcd09d1b25542\\\",\\\"dbIdValue\\\":\\\"40f07deb8e53e73d2c1dcd09d1b25542\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Gttech\\\",\\\"targetValue\\\":\\\"Gttech\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   481996653503465011
2024-10-12T16:29:28.207+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41319342043712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f343dc2fe3defe9e0f53843a5f073ff9&deviceId=481992210913444145
2024-10-12T16:29:28.207+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d42252efca6f6368414ab6ab3eb00331\\\",\\\"dbIdValue\\\":\\\"d42252efca6f6368414ab6ab3eb00331\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RHDZ_7YUE_NEW\\\",\\\"targetValue\\\":\\\"RHDZ_7YUE_NEW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   481992210913444145
2024-10-12T16:29:29.552+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322312503872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bba8aef33500371df574d23c468baa50&deviceId=481986395980706883
2024-10-12T16:29:29.553+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d5242a22d641e209adc6d0c9d7c64b5e\\\",\\\"dbIdValue\\\":\\\"d5242a22d641e209adc6d0c9d7c64b5e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_1\\\",\\\"targetValue\\\":\\\"E10_1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   481986395980706883
2024-10-12T16:29:30.910+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321348358720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=54dab80dbd419fd242e2d61adad0d0c4&deviceId=481876265553375812
2024-10-12T16:29:30.910+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fe1b443e12ac8d994ca173bae7cb8d9f\\\",\\\"dbIdValue\\\":\\\"fe1b443e12ac8d994ca173bae7cb8d9f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LTX\\\",\\\"targetValue\\\":\\\"LTX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   481876265553375812
2024-10-12T16:29:32.239+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:407555796488768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4a65c6867d9a69dbc71981beb4299a3d&deviceId=481726712678200897
2024-10-12T16:29:32.239+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"98965e98f8335987c3d05da4fb3a04e8\\\",\\\"dbIdValue\\\":\\\"98965e98f8335987c3d05da4fb3a04e8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JYBdata\\\",\\\"targetValue\\\":\\\"JYBdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   481726712678200897
2024-10-12T16:29:33.745+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323158536768 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d02eaae8268ddf164f4b308551244acf&deviceId=480290018233955889
2024-10-12T16:29:33.746+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"159288b4a9d83db565870ec2569fd4f3\\\",\\\"dbIdValue\\\":\\\"159288b4a9d83db565870ec2569fd4f3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GDTC2017\\\",\\\"targetValue\\\":\\\"GDTC2017\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85036c189826d2d75709c325dc2fef0a7e7bdf480991a8816040257cb3093a82aa7218c14e75b43c1c97f6952a599f95e770be7d7ec7d79a014caa3c72b95dd27a8c01b37a159f2d96abc49ec616a9d0e0221a11257e376087d11dd75a852d658a7c21fe6b0be538af045202fb51544c7ec38c3fcd5c077659ed0b16c8a66e7ec4d23485d60926d680263bb64e19048ce0d2e959e28f59bf4b008e011c433f21aad977c5b0c7297868002822987e944e0cc7f4194a0bb96af457bcd37f320671ee3ab8e85f12b47a12de09ea309ec971ceaf4910429019776f1a5bdfb5ce15ac8df2afde6f5f4b7e6fcac21f6e70b3f4937eea60e9f5c82bae64","collectName":"E10附件数据采集","accId":779544910123584,"adimId":645049421558336,"id":779872566624832,"adcId":645047841243712,"execParamsVersion":"014e60fe252c4fab023db9168442fe50","aiId":645049421496897,"isEnable":1},"paramsMap":{"deviceId":"480290018233955889","eid":41323158536768,"aiId":645049421496897,"execParamsDbAiId":645049287021120,"execParamsDbId":"159288b4a9d83db565870ec2569fd4f3"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779872566624832 acc incomplete","batchId":779872567636544,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   480290018233955889
2024-10-12T16:29:35.121+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:251518906839616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0191fe0fccc9d8c010f83aac59325ea3&deviceId=480142391550949443
2024-10-12T16:29:35.121+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8af48ee367255199f0dd36ec34713281\\\",\\\"dbIdValue\\\":\\\"8af48ee367255199f0dd36ec34713281\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10PROD\\\",\\\"targetValue\\\":\\\"E10PROD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   480142391550949443
2024-10-12T16:29:36.462+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:284042054746688 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5e822a9a4b12cdd5b4275c404abe2fa3&deviceId=480129230227649860
2024-10-12T16:29:36.462+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5e511ae1f7b12707fbd01b49822f57ab\\\",\\\"dbIdValue\\\":\\\"5e511ae1f7b12707fbd01b49822f57ab\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_SY\\\",\\\"targetValue\\\":\\\"E10_SY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   480129230227649860
2024-10-12T16:29:37.861+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322488783424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9a9387f353da4be2dfcd3bf0e52d67d5&deviceId=480104300224198963
2024-10-12T16:29:37.862+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28b7b52f92ca144718c689ad5de72bd1\\\",\\\"dbIdValue\\\":\\\"28b7b52f92ca144718c689ad5de72bd1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10SYR\\\",\\\"targetValue\\\":\\\"E10SYR\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   480104300224198963
2024-10-12T16:29:39.215+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:169423411847744 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f74db44f7c2ba4ee26cc2ae0de93b9ff&deviceId=480000722037913909
2024-10-12T16:29:39.215+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"616b60cbab1935cf9ac0df9158753ed3\\\",\\\"dbIdValue\\\":\\\"616b60cbab1935cf9ac0df9158753ed3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RNZS\\\",\\\"targetValue\\\":\\\"RNZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   480000722037913909
2024-10-12T16:29:40.541+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:62542094783040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a6501b5f42eb640cb856e09bd87cfb91&deviceId=479983787468408130
2024-10-12T16:29:40.542+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"df7cc91b87c3de1c7dd1ba421fc7e100\\\",\\\"dbIdValue\\\":\\\"df7cc91b87c3de1c7dd1ba421fc7e100\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FY2022\\\",\\\"targetValue\\\":\\\"FY2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479983787468408130
2024-10-12T16:29:41.935+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41319562818112 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bbf1640305dddd3acd1e70290f21bd69&deviceId=479982478325463362
2024-10-12T16:29:41.936+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"37694993aa477dea92b8794da5535e15\\\",\\\"dbIdValue\\\":\\\"37694993aa477dea92b8794da5535e15\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TYST\\\",\\\"targetValue\\\":\\\"TYST\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479982478325463362
2024-10-12T16:29:43.334+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:510683554583104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ad1d4fefc0948948fd2890419a060033&deviceId=479964613627098179
2024-10-12T16:29:43.335+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e0637f06e10ff09a89289a6ff8eec2cd\\\",\\\"dbIdValue\\\":\\\"e0637f06e10ff09a89289a6ff8eec2cd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SM\\\",\\\"targetValue\\\":\\\"SM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479964613627098179
2024-10-12T16:29:44.855+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:516833707049536 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8be7480ede5e3512c96972ca99aaa6d2&deviceId=479958556582360131
2024-10-12T16:29:44.856+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bbc987e33a01d18daa096434f7bf98aa\\\",\\\"dbIdValue\\\":\\\"bbc987e33a01d18daa096434f7bf98aa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XMDQ\\\",\\\"targetValue\\\":\\\"XMDQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479958556582360131
2024-10-12T16:29:46.314+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318636196416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0975d491ab585530a430001f48d1493b&deviceId=479558361814152259
2024-10-12T16:29:46.315+08:00  INFO 31536 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c23480753240d7cc960c6630f3c19afa\\\",\\\"dbIdValue\\\":\\\"c23480753240d7cc960c6630f3c19afa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSKJ\\\",\\\"targetValue\\\":\\\"XSKJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   479558361814152259
deviceIds:,'496515776438613059','496195688833495347','493768354209743416','493303111239086644','493191291933309234','493171275976423238','434637941080340036','492597660730017091','492461296340449604','492428908411503671','491696240141025857','491417398415471683','491289684543025458','491265931394167875','490711914435851331','490394753364407093','490139351070553155','472917103662347056','489669949766252345','489665660704534833','489545836116586550','489529470412140596','489525563518628931','489122840121984048','488662613589247553','435914263559681091','464763671025824835','488109070050407491','435488249645905221','418082076756030531','488081100116079683','487672518115602480','487493168569267267','487391150999614531','487383441331992887','487387476537652291','487379772473619523','487376444612162627','487227426695295284','487211785967907632','487072885282715460','486668115569030211','486479662772400176','485509922759390275','485494740821750851','485328769981104440','485322213646415153','485207744345814081','485199091261781043','484605263177856067','484597082607337798','484462191643210305','484462065646321976','473187655882912835','484187946723853364','484184545478849603','484169469002790969','483616688307188803','483612733011407412','483608397443249219','483468864642888771','471614916772508739','479263625790375217','483323322562528322','483317215085474096','483038001996248370','482590251622085953','482566160647206966','482458152638366772','482450389082325296','482428010960533043','482279551641076787','482297779582940227','482031766639493939','482177022014010167','482173556864595011','482172782780625972','482164500624061507','482156590535817281','482142818236642371','482140084087305283','435811649660662851','482025573598638133','482020021497775410','482009894535313217','481996653503465011','481992210913444145','481986395980706883','481876265553375812','481726712678200897','480290018233955889','480142391550949443','480129230227649860','480104300224198963','480000722037913909','479983787468408130','479982478325463362','479964613627098179','479958556582360131','479558361814152259'