package com.example.demo.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DataExaminationComponentDynamicsParams {
    @ExcelProperty("ID")
    private String id;

    @ExcelProperty("组件参数ID")
    private String componentParamId;

    @ExcelProperty("字段Code")
    private String fieldCode;

    @ExcelProperty("字段名称")
    private String fieldName;

    @ExcelProperty("字段类型")
    private String fieldType;

    @ExcelProperty("字段默认值(不填)")
    private String fieldDefault;

    @ExcelProperty("绑定字段来源(不填)")
    private String bindFieldSource;

    @ExcelProperty("绑定字段Code(不填)")
    private Byte bindFieldCode;

    @ExcelProperty("绑定字段名称(不填)")
    private String bindFieldName;

    @ExcelProperty("依赖动态参数Code(不填)")
    private String parentEscServiceColumnCode;

    @ExcelProperty("是否多选")
    private Boolean isMultiple;

    @ExcelProperty("是否展示(不填)")
    private Boolean isShow;
}
