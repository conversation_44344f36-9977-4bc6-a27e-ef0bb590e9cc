package com.digiwin.escloud.aiouser.group.service.impl;

import com.digiwin.escloud.aiouser.group.entity.ModuleStaffSyncMapping;
import com.digiwin.escloud.aiouser.group.mapper.ModuleStaffSyncMappingMapper;
import com.digiwin.escloud.aiouser.group.service.IModuleStaffSyncMappingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 模组服务人员mapping 表 在同步模组合约时用到 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
public class ModuleStaffSyncMappingServiceImpl extends ServiceImpl<ModuleStaffSyncMappingMapper, ModuleStaffSyncMapping> implements IModuleStaffSyncMappingService {

}
