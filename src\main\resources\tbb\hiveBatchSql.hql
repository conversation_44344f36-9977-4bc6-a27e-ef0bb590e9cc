select concat('{"SafeStockSetRate":',model,'}') from SafeStockSetRate limit 1 ;
select concat('{"ProcureChangeRate":',model,'}') from ProcureChangeRate limit 1 ;
select concat('{"ProcureChangeCycle":',model,'}') from ProcureChangeCycle limit 1 ;
select concat('{"PurchaseBatchRate":',model,'}') from PurchaseBatchRate limit 1 ;
select concat('{"ProcurementQualityRate":',model,'}') from ProcurementQualityRate limit 1 ;
select concat('{"ProductAverageLevel":',model,'}') from ProductAverageLevel limit 1 ;
select concat('{"CostFluctuationsProportion":',model,'}') from CostFluctuationsProportion limit 1 ;
select concat('{"MissingTransactionNum":',model,'}') from MissingTransactionNum limit 1 ;
select concat('{"InventoryStrategySet":',model,'}') from InventoryStrategySet limit 1 ;
select concat('{"OrderChangeCycle":',model,'}') from OrderChangeCycle limit 1 ;
select concat('{"OrderReviewRate":',model,'}') from OrderReviewRate limit 1 ;
select concat('{"OrderReviewCycle":',model,'}') from OrderReviewCycle limit 1 ;
select concat('{"OrderToPlanCycle":',model,'}') from OrderToPlanCycle limit 1 ;
select concat('{"BlankInvoiceProportion":',model,'}') from BlankInvoiceProportion limit 1 ;
select concat('{"PaymentTermsProportion":',model,'}') from PaymentTermsProportion limit 1 ;
select concat('{"WorkOrderChangeRate":',model,'}') from WorkOrderChangeRate limit 1 ;
select concat('{"WorkChangeReviewCycle":',model,'}') from WorkChangeReviewCycle limit 1 ;
select concat('{"WorkOrderDelayNum":',model,'}') from WorkOrderDelayNum limit 1 ;
select concat('{"IncompleteSupplyCondition":',model,'}') from IncompleteSupplyCondition limit 1 ;
select concat('{"IncompleteCustomerCondition":',model,'}') from IncompleteCustomerCondition limit 1 ;
select concat('{"TOP10DiffInCost":',model,'}') from TOP10DiffInCost limit 1 ;
select concat('{"MissingRateOfMaterialMachine":',model,'}') from MissingRateOfMaterialMachine limit 1 ;
select concat('{"ProfitAndLossRate":',model,'}') from ProfitAndLossRate limit 1 ;
select concat('{"OtherPayableRate":',model,'}') from OtherPayableRate limit 1 ;
select concat('{"OtherAccountReceiveRate":',model,'}') from OtherAccountReceiveRate limit 1 ;
select concat('{"ProductBatchSetRate":',model,'}') from ProductBatchSetRate limit 1 ;
select concat('{"OutsourceProcureRate":',model,'}') from OutsourceProcureRate limit 1 ;
select concat('{"OutsourcingQualityRate":',model,'}') from OutsourcingQualityRate limit 1 ;
select concat('{"AccountsPayableModifyNum":',model,'}') from AccountsPayableModifyNum limit 1 ;
select concat('{"AccountsPayModifyRate":',model,'}') from AccountsPayModifyRate limit 1 ;
select concat('{"ReceiveAccountsRate":',model,'}') from ReceiveAccountsRate limit 1 ;
select concat('{"PlanAssociatedOrderRate":',model,'}') from PlanAssociatedOrderRate limit 1 ;
select concat('{"QualifiedRateOfWorkcenterMo":',model,'}') from QualifiedRateOfWorkcenterMo limit 1 ;
select concat('{"InactiveStockAmtRate":',model,'}') from InactiveStockAmtRate limit 1 ;
select concat('{"MoMaterialCompletionRate":',model,'}') from MoMaterialCompletionRate limit 1 ;
select concat('{"PurchaseOverentryRate":',model,'}') from PurchaseOverentryRate limit 1 ;
select concat('{"ProductionOverConsumptionRate":',model,'}') from ProductionOverConsumptionRate limit 1 ;
select concat('{"UniversalFactorDeletionRate":',model,'}') from UniversalFactorDeletionRate limit 1 ;
select concat('{"QualifiedRateOfSupplierMO":',model,'}') from QualifiedRateOfSupplierMO limit 1 ;
select concat('{"AverageSalesApprovalPeriod":',model,'}') from AverageSalesApprovalPeriod limit 1 ;
select concat('{"SalesOverDeliveryRate":',model,'}') from SalesOverDeliveryRate limit 1 ;
select concat('{"SummaryOfSalesReturnItems":',model,'}') from SummaryOfSalesReturnItems limit 1 ;
select concat('{"AveragePlanningPeriod":',model,'}') from AveragePlanningPeriod limit 1 ;
select concat('{"NegativeInvSummaryRate":',model,'}') from NegativeInvSummaryRate limit 1 ;
select concat('{"OverdeliveryRateOfMo":',model,'}') from OverdeliveryRateOfMo limit 1 ;
select concat('{"NotFinishedMO":',model,'}') from NotFinishedMO limit 1 ;
select concat('{"SummaryOfSalesIssueItems":',model,'}') from SummaryOfSalesIssueItems limit 1 ;
select concat('{"ActivedEndproductNum":',model,'}') from ActivedEndproductNum limit 1 ;
select concat('{"ExcessivePurchaseRate":',model,'}') from ExcessivePurchaseRate limit 1 ;
select concat('{"PurchaseQualifiedRate":',model,'}') from PurchaseQualifiedRate limit 1 ;

