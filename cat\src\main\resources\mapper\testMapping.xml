<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mybatis.plus.mapper.V4Mapper">
    <select id="selectAll" resultType="com.example.mybatis.plus.model.V4">
        select * from es_customerservice_v4_1_bak_20240729
    </select>
<!--    <select id="selectEs" resultType="com.example.mybatis.model.EsCustomerServiceV41">-->
<!--        select DISTINCT  g.* ,c.Sales,c.SalesContact, cc.*  &#45;&#45;  count(DISTINCT cc.eid)  &#45;&#45;-->
<!--        from   grp_bus_dpt_mapping g-->
<!--                   INNER JOIN mars_customerservicesatff s  on s.departmentcode = g.dpt_id-->
<!--                   INNER JOIN mars_customerservice c on c.SalesContact = s.email-->
<!--                   INNER JOIN mars_customer cc on cc.CustomerServiceCode = c.CustomerServiceCode-->
<!--        where s.workStatus = 'work'  and s.departmentcode in ('CD0100','CD0100','CD0110','CD0120','CD0130','CD0140')-->
<!--          and (c.ContractState like 'B%' or  c.ContractState like 'H%'  or  (c.ContractState like 'G%' and c.ContractState != 'G6'))-->
<!--          and c.ProductCode in ('37','152','08','165','100','06','164','176','178','MES','PLM','137','150'  )-->
<!--    </select>-->


</mapper>