package com.example.aio.asia;

import java.util.UUID;

public class UUIDGenerator {
    public static String generateDeviceId() {
        UUID uuid = UUID.randomUUID(); // 使用UUID类生成随机的UUID
        // 将UUID的各部分转换为十六进制，并格式化成20个字符的设备ID
        return String.format("%08X%04X%04X%02X%02X",
                uuid.getMostSignificantBits() & 0xFFFFFFFFL, // 获取UUID的前8个字节
                (uuid.getMostSignificantBits() >> 32) & 0xFFFFL, // 获取UUID的9-12字节
                (uuid.getMostSignificantBits() >> 16) & 0xFFFFL, // 获取UUID的13-16字节
                (uuid.getLeastSignificantBits() >> 8) & 0xFFL, // 获取UUID的17字节
                uuid.getLeastSignificantBits() & 0xFFL); // 获取UUID的18字节
    }

    public static void main(String[] args) {
        System.out.println(generateDeviceId());
        System.out.println(generateDeviceId());
        System.out.println(generateDeviceId());
    }
}
