$AiopsKitLocalUrl = '{{aiopskit_local_url}}'

function SxfUrlGet{
    return {{SCP_IP}}
#    return "*************"
}

#       1.透過深信服地址({{sxfAddress}})取的公鑰
function PublicKey {
    $sxfUrl = SxfUrlGet

    $response = Invoke-WebRequest -Uri https://$sxfUrl/janus/public-key -Method GET -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.public_key
    } else {
        Write-Output "请求失败: $($response.StatusCode)"
    }

}
#       2.透過地端ras實用接口，取得密碼 <-需開發地端
function PrivateKeyGet{

    $content = {{content}}
    $publicKey = PublicKey
    $Url = $AiopsKitLocalUrl + "/aiopskit/ras?content=$content&publicKey=$PublicKey"

    $Response = (Invoke-RestMethod -Method $Method -Uri "$Url")
    return $Response.data

}

#       3.透過深信服地址({{sxfAddress}})及用戶名({{sxfUserName}})及第2步取得的密碼，獲取Token({{sxfToken}})
function TokenGet {
    #        WF_ATN_AutoUpdate

    $privateKey = PrivateKeyGet
#    $privateKey = "7962ca4ff7f46298cf0695426ed9dfb3664345b1bf17dbb37c4bed632ed7eae59abaa13ba2f3740a78fb7b9b1460cdb00f75bb1659be989af56971a75fdbd042e8289767f21df1a091cda01161551ce6910bfa6a3120af38b3dff06e53361aad8a6cdc7c65b6ff195f63df30bb60a2446c83b93e77f5db8c2f8980a4dce6852137dcbf8c6e6e074767beee18618a7ebb6714e558f03d6806e1826e7ba2f4090a8b7bf08bfa4b802c3b7c53859535f4310673dfb1e9918c93bd8318af0f09bef0a6bfc4b26cdcfa5465d34e124297fc19ac8a25646aa5b7c81395f9f3acf51a91cc691840d8d9197feb360217abf346c9c7e043b9d5c89991fbe00b45f729d8dd"
    $sxfUserName = "{{sxfUserName}}"
#    $sxfUserName = "openlab"
    $sxfUrl = SxfUrlGet
    $payload = @{
        auth = @{
            passwordCredentials = @{
                username = "$sxfUserName"
                password = "$privateKey"
            }
        }
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri https://$sxfUrl/janus/authenticate -Method POST -ContentType "application/json" -Body $jsonBody -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.access.token.id
    } else {
        Write-Output "请求失败"
    }
    #       發送器邏輯
    #       发送器
    #       4.透過發送器儲存Token({{sxfToken}})到文件參數
    #       5.刷新地端執行參數緩存
}

$aa = TokenGet
Write-Output $aa
