package com.digiwin.escloud.aiocdp.group.service.impl;

import com.digiwin.escloud.aiocdp.group.entity.AioCdpGroupAnalysisRule;
import com.digiwin.escloud.aiocdp.group.mapper.AioCdpGroupAnalysisRuleMapper;
import com.digiwin.escloud.aiocdp.group.service.IAioCdpGroupAnalysisRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 群画像分析规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
public class AioCdpGroupAnalysisRuleServiceImpl extends ServiceImpl<AioCdpGroupAnalysisRuleMapper, AioCdpGroupAnalysisRule> implements IAioCdpGroupAnalysisRuleService {

}
