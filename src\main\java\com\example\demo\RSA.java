package com.example.demo;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class RSA {

//    public static void main(String[] args) throws Exception {
//        // 初始化KeyPairGenerator对象,并指定RSA算法
//        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
//        keyPairGenerator.initialize(2048); // 可以选择1024或2048位长度的密钥
//
//        // 生成密钥对
//        KeyPair keyPair = keyPairGenerator.generateKeyPair();
//        PublicKey publicKey = keyPair.getPublic();
//        PrivateKey privateKey = keyPair.getPrivate();
//
//        // 获取公钥，并转换为Base64编码的字符串，以便在网络上传输
//        String publicKeyString = Hex.encodeHexString(publicKey.getEncoded());
//
//        // 这里可以将公钥字符串发送给前端
//        System.out.println("公钥Base64编码: " + publicKeyString);
//
//        // 假设这是前端加密的数据
//        String secretMessage = "这是一个秘密消息";
//        byte[] encryptedData = encrypt(publicKey, secretMessage);
//
//        // 假设这是后端接收到的加密数据，并进行解密
//        String decryptedMessage = decrypt(privateKey, encryptedData);
//
//        System.out.println("解密后的消息: " + decryptedMessage);
//    }
//
//    // 使用公钥加密方法
//    public static byte[] encrypt(PublicKey publicKey, String message) throws Exception {
//        Cipher cipher = Cipher.getInstance("RSA");
//        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
//        return cipher.doFinal(message.getBytes());
//    }
//
//    // 使用私钥解密方法
//    public static String decrypt(PrivateKey privateKey, byte[] encryptedData) throws Exception {
//        Cipher cipher = Cipher.getInstance("RSA");
//        cipher.init(Cipher.DECRYPT_MODE, privateKey);
//        byte[] decryptedData = cipher.doFinal(encryptedData);
//        return new String(decryptedData);
//    }
//    public static void main(String[] args) throws Exception {
//        System.out.println(DesCryptUtils.getInstance("这是需要加密的数据").encrypt("我需要采集E10的采购单存储到数据中台并且将采购单金额做脱敏处理"));
//
//        String publicKeyString = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDq0QmrQFjM60AfLbUXTnqbFStoWqOrrAe9eLt7u1OpibsTLH1fdkU7gNp8afdVkeh7IxE2lOzJCSeYRtZakFFuWnPJPxxvBUKLsxQc9E0fp0gXKvNWABS4ZI6ksFCkUtQvIxxBeuNPFBmuWeIXyRB9iIEmwxDIcOCUcQVIyHji5W++OSFjbGfuIADH4LaaDj1SH6r6qPp858nz7Mfd5s08UaDmRQzZjrHGrUHdwCen82NczGdADzzr2hHQmfZjuYjVvcmNqWqXKoWAXpVy9RZhKHwACwVo+1L5GOAcpKlP8ozjbRFn/60hf8QUrdZ54vRorX6L/EDu5pzmACOFKQzRAgMBAAECggEBAMPSvBuCuAt8jKbzuTfMjNbUGrUWEbRS7MmH6+JN4IFciRR+X7fCrA3pcUucG1XTLmuyXOKF48nrxodpW81+2UoMSM8lwJKdcleKalWTLtYc7TPU78Vin+T+hy60kyvWn3kH8MJytyn1e005TCHF3eiyc/JdbpuZ9tAUSPHfrWCZmRPSOXnb3A2BFGtpvRLIs05yvaX6aOcBNSMk7tDsRfsbRhAe9pKCmJ7LaBIwbLLRp/aL88r1oupcRqDdrg7KMBHJKrqF0P6b/8MCH6DrWjHa37+IRxSsTdFEky0oImCibUl6OCka0tNacjjC/kUVzXbh1zr37+fe5lMoiflA6TkCgYEA/oH791RZBFA98Be/vv6Wi8lo6hBvko3UVawVacbxRjz7dh8Yj9ExDFh/vj7Hq8OC2hIKIJuEZGyDPocrU0B/r5OGuJBHKzoDdoNbgHimtQqsXSD1blmQxqWou+i98a5uaMdT+pYv/GEBzzvfS+uvN1Y65kuQ9/TsMISIjuQNrScCgYEA7DF/P+ecgmJhpg8QR83z8PDZPwYfN96KeWwJ+7BKMPlHapYjATVm2+rsGkBXhxwCUV0tRD2BkmpNZ+VVWT/yeqHii68q50sHuIRJbs2II2QLdAZdRXXUkFP9KmMLW5rvoVBYhY/peLiPis34v6v7kx5sWIH/yGuD0FsjwA0UIUcCgYATYdTNhiftUysUYMqiggL3DFSoFq5Yi+KSz7MlxhfT0IsZ+M5HDZhdp1O7Kk7Mfuw1LVCGITHY8AaiuWqMQYo31dVZsusomJdw7BoI06dmfDSaNwtZ5wpGhpEuFlB42l7gLeXwXIW1A7I+lQGxZ3aFkVSU+0ZEC+v82esIbse32wKBgQDCmoGauI0WTFLiTDUAxGsCEJ8eM0ATezMimeabzgNC2JCD/FG5FDO8bJG9i1fShTxPp3K9b+PLK/S87pJkp7B0Y99RcbDqwsIIwE30kjlh13MsVHYS3eOWSu0jOFnKTxyzD4zv9sPJaF80YUJbQitUatOgFPQXQKZpTD5nZR85zwKBgB8oeWVAYFct3u56g9NCA351NBfVzm12iFsHFmr6CtB5jsAtdFaywo8KcObuBzuRMITzN3nkrOpLMM5mnMAOMY0latFQfDBRcgrboZIRe1Hd+qvWcbBIEcJFkHQ/AOiGc7MrwE/t2TDVya6eb327PVkBCfoMMA84ZnDTQYqx/g1C";
//
//        // 将字符串格式的公钥转换为PublicKey对象
//        byte[] publicKeyBytes = Base64.getDecoder().decode  (publicKeyString);
//        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
//        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
//        PublicKey publicKey = keyFactory.generatePublic(keySpec);
//
//        // 待加密的数据
//        String dataToEncrypt = "phWubXg0G2tX6o0p3Fx4XW+9P/Uys+4uiba8WDkStc47wBxIV0RNQ+nuHaLWytpgNfXCnnJmptHXj/LnzMMZuytaZSfXtfs2+mIOA37kK/HnQVtZSGx59G27o9pwN5s9uZjsDkk+1u9Aud0xblPlV4WV/1J8AUilbQqGrMFQIsG5nEOu5KoR0mli5u0wC09WG2BrTbrKCrxyzliq/fT/Y4RUkV6luVaHGqMTMc13x0+xzfw1KVnhb9hwCi1R+AmGuAQN2L5efpEaaKvQnvqiw65mkk4sDGfBrgGBxr4h8fkjuicfFpO2Oj2Kpov3foTjA7EtzbG5N7SwSoL6qh+9IA==";
//        byte[] data = dataToEncrypt.getBytes();
//
//        // 使用公钥进行加密
//        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 使用标准的RSA加密算法
//        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
//
//        // 执行加密操作
//        byte[] encryptedData = cipher.doFinal(data);
//
//        // 将加密后的数据转换为Base64编码的字符串
//        String encryptedDataString = Hex.encodeHexString(encryptedData);
//        System.out.println("加密后的数据（Base64编码）: " + encryptedDataString);
//    }

    public static void main(String[] args) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        // 将Base64编码的字符串解码成字节数组
        String base64EncodedPrivateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDq0QmrQFjM60AfLbUXTnqbFStoWqOrrAe9eLt7u1OpibsTLH1fdkU7gNp8afdVkeh7IxE2lOzJCSeYRtZakFFuWnPJPxxvBUKLsxQc9E0fp0gXKvNWABS4ZI6ksFCkUtQvIxxBeuNPFBmuWeIXyRB9iIEmwxDIcOCUcQVIyHji5W++OSFjbGfuIADH4LaaDj1SH6r6qPp858nz7Mfd5s08UaDmRQzZjrHGrUHdwCen82NczGdADzzr2hHQmfZjuYjVvcmNqWqXKoWAXpVy9RZhKHwACwVo+1L5GOAcpKlP8ozjbRFn/60hf8QUrdZ54vRorX6L/EDu5pzmACOFKQzRAgMBAAECggEBAMPSvBuCuAt8jKbzuTfMjNbUGrUWEbRS7MmH6+JN4IFciRR+X7fCrA3pcUucG1XTLmuyXOKF48nrxodpW81+2UoMSM8lwJKdcleKalWTLtYc7TPU78Vin+T+hy60kyvWn3kH8MJytyn1e005TCHF3eiyc/JdbpuZ9tAUSPHfrWCZmRPSOXnb3A2BFGtpvRLIs05yvaX6aOcBNSMk7tDsRfsbRhAe9pKCmJ7LaBIwbLLRp/aL88r1oupcRqDdrg7KMBHJKrqF0P6b/8MCH6DrWjHa37+IRxSsTdFEky0oImCibUl6OCka0tNacjjC/kUVzXbh1zr37+fe5lMoiflA6TkCgYEA/oH791RZBFA98Be/vv6Wi8lo6hBvko3UVawVacbxRjz7dh8Yj9ExDFh/vj7Hq8OC2hIKIJuEZGyDPocrU0B/r5OGuJBHKzoDdoNbgHimtQqsXSD1blmQxqWou+i98a5uaMdT+pYv/GEBzzvfS+uvN1Y65kuQ9/TsMISIjuQNrScCgYEA7DF/P+ecgmJhpg8QR83z8PDZPwYfN96KeWwJ+7BKMPlHapYjATVm2+rsGkBXhxwCUV0tRD2BkmpNZ+VVWT/yeqHii68q50sHuIRJbs2II2QLdAZdRXXUkFP9KmMLW5rvoVBYhY/peLiPis34v6v7kx5sWIH/yGuD0FsjwA0UIUcCgYATYdTNhiftUysUYMqiggL3DFSoFq5Yi+KSz7MlxhfT0IsZ+M5HDZhdp1O7Kk7Mfuw1LVCGITHY8AaiuWqMQYo31dVZsusomJdw7BoI06dmfDSaNwtZ5wpGhpEuFlB42l7gLeXwXIW1A7I+lQGxZ3aFkVSU+0ZEC+v82esIbse32wKBgQDCmoGauI0WTFLiTDUAxGsCEJ8eM0ATezMimeabzgNC2JCD/FG5FDO8bJG9i1fShTxPp3K9b+PLK/S87pJkp7B0Y99RcbDqwsIIwE30kjlh13MsVHYS3eOWSu0jOFnKTxyzD4zv9sPJaF80YUJbQitUatOgFPQXQKZpTD5nZR85zwKBgB8oeWVAYFct3u56g9NCA351NBfVzm12iFsHFmr6CtB5jsAtdFaywo8KcObuBzuRMITzN3nkrOpLMM5mnMAOMY0latFQfDBRcgrboZIRe1Hd+qvWcbBIEcJFkHQ/AOiGc7MrwE/t2TDVya6eb327PVkBCfoMMA84ZnDTQYqx/g1C";
        byte[] privateKeyBytes = Base64.getDecoder().decode(base64EncodedPrivateKey);
        // 生成私钥对象
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(keySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        byte[] decryptedData = Base64.getDecoder().decode("phWubXg0G2tX6o0p3Fx4XW+9P/Uys+4uiba8WDkStc47wBxIV0RNQ+nuHaLWytpgNfXCnnJmptHXj/LnzMMZuytaZSfXtfs2+mIOA37kK/HnQVtZSGx59G27o9pwN5s9uZjsDkk+1u9Aud0xblPlV4WV/1J8AUilbQqGrMFQIsG5nEOu5KoR0mli5u0wC09WG2BrTbrKCrxyzliq/fT/Y4RUkV6luVaHGqMTMc13x0+xzfw1KVnhb9hwCi1R+AmGuAQN2L5efpEaaKvQnvqiw65mkk4sDGfBrgGBxr4h8fkjuicfFpO2Oj2Kpov3foTjA7EtzbG5N7SwSoL6qh+9IA==");

        byte[] decryptedBytes = cipher.doFinal(decryptedData);
        String aesKey = new String(decryptedBytes);
        System.out.println(aesKey);
    }

    private static void aa()throws Exception{
        // 1. 生成 RSA 密钥对
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        keyPairGen.initialize(2048, new SecureRandom());
        KeyPair keyPair = keyPairGen.generateKeyPair();
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        // 2. 使用公钥加密数据
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        String input = "这是一段用于加密的数据";
        byte[] encryptedData = cipher.doFinal(input.getBytes());

        // 打印加密后的数据
        System.out.println("加密后的数据: " + bytesToHex(encryptedData));

        // 3. 使用私钥解密数据
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedData = cipher.doFinal(encryptedData);

        // 打印解密后的数据
        System.out.println("解密后的数据: " + new String(decryptedData));
    }

    // 将字节数组转换为十六进制字符串
    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
