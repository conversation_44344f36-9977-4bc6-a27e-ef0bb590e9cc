function IpAndPort{
    return "{{http_service_address}}"
    #    return "https://************:4435"
}

function UserNameGet{
    #        return "admin"
    return "{{http_basic_login_id}}"
}

function PassGet{
    #        return "#1TXUo6aK6"
    return "{{http_basic_login_pwd}}"
}

function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}




function QueryResource {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/azs" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.data
    }
}

function QueryDetail {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $array=QueryResource
    $myArray = New-Object System.Collections.ArrayList
    foreach ($item in $array) {
        $rid =  $item.id
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/overview?az_id=$rid" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck
        if ($response.StatusCode -eq 200) {
            $obj = New-Object -TypeName PSObject
            $obj | Add-Member -MemberType NoteProperty -Name "az_id" -Value $item.id
            $jsonObject = $response.Content | ConvertFrom-Json

            foreach($d in $jsonObject.data.physical_resources){
                if ($d.name -eq "storage")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "storage_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "storage_used" -Value $d.used
                }
                if ($d.name -eq "cpu")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "cpu_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "cpu_used" -Value $d.used
                }
                if ($d.name -eq "memory")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "memory_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "memory_used" -Value $d.used
                }
            }

            foreach($d in $jsonObject.data.virtual_resources){
                if ($d.name -eq "CPU")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_allocated" -Value $d.allocated
                    $obj | Add-Member -MemberType NoteProperty -Name "server_cpu_occupied" -Value $d.occupied
                }
                if ($d.name -eq "内存")
                {
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_total" -Value $d.total
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_allocated" -Value $d.allocated
                    $obj | Add-Member -MemberType NoteProperty -Name "server_mem_occupied" -Value $d.occupied
                }

            }

            $obj | Add-Member -MemberType NoteProperty -Name "server_running_count" -Value $jsonObject.data.server.running_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_offline_count" -Value $jsonObject.data.server.offline_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_total" -Value $jsonObject.data.server.total
            $obj | Add-Member -MemberType NoteProperty -Name "server_alarm_count" -Value $jsonObject.data.server.alarm_count
            $obj | Add-Member -MemberType NoteProperty -Name "server_error_count" -Value $jsonObject.data.server.error_count

            $obj | Add-Member -MemberType NoteProperty -Name "host_error_count" -Value $jsonObject.data.host.offline_count
            $obj | Add-Member -MemberType NoteProperty -Name "host_total" -Value $jsonObject.data.host.total
            $obj | Add-Member -MemberType NoteProperty -Name "host_alarm_count" -Value $jsonObject.data.host.alarm_count
            $obj | Add-Member -MemberType NoteProperty -Name "host_online_count" -Value $jsonObject.data.host.online_count
            $myArray.Add($obj)
        }
    }
    return $myArray

}
$aa=QueryDetail
$result = $aa | Select-Object -ExcludeProperty "PS*"
$result = $result | ConvertTo-Json
Write-Output $result
