beeline -d "com.cloudera.impala.jdbc41.Driver" -u "************************" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_33_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Order_lead_time') AS DECIMAL(38, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.Delivery_time') AS DECIMAL(38, 2)),get_json_object(model,'$.BasicInfo.sort_minus_1_field'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderLeadTime"


beeline -d "com.cloudera.impala.jdbc41.Driver" -u "*****************************" -n service_cloud -e " INVALIDATE METADATA eai.hive_eai_cloud_backup_top5_hosts_biz_info;"