package com.example.demo.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DataExaminationComponent {
    @ExcelProperty("ID")
    private String id;

    @ExcelProperty("组件图标(可不填)")
    private String imgUrl;

    @ExcelProperty("组件名称")
    private String componentName;

    @ExcelProperty("组件Code")
    private String componentCode;

    @ExcelProperty("组件类型(TBB、GRAFANA、CUSTOM、GROUP)")
    private String componentType;

    @ExcelProperty("运维项目")
    private String aiopsItem;

    @ExcelProperty("层级")
    private Integer layerIndex;

    @ExcelProperty("行数")
    private Integer rows;

    @ExcelProperty("列数")
    private Integer cols;

    @ExcelProperty("组件来源")
    private String componentSource;

    @ExcelProperty("appCode")
    private String appCode;

    @ExcelProperty("eid")
    private Long eid;

    @ExcelProperty("sid")
    private Long sid;

    @ExcelProperty("创建人")
    private String createUser;

    @ExcelProperty("创建邮箱")
    private String createMail;

}
