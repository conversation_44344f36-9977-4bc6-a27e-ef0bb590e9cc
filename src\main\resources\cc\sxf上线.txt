0.大陆正式环境的 (低代码平台)10个模型 低代码平台可以发布相关模型过去

1.创建http执行参数模型(企业运维服务云)

2.工单执行

3.(企业运维服务云) INSERT INTO aiops_product_app (id, spId, modelCode, modelRelateCode, sourceModelCode, rootModelCode, __version__) VALUES (672261417853504, 102, 'CRHModel', 'HOST_CRHModel', 'HOST', 'HOST', '2023-12-13 14:38:42.728');
3.1(企业运维服务云)执行 同步收集项到大数据平台 api
4.修改日志收集项(企业运维服务云)
SxfOperationLog
function IpAndPort{
    return "{{http_service_address}}"

}

function UserNameGet{

    return "{{http_basic_login_id}}"
}

function PassGet{

    return "{{http_basic_login_pwd}}"
}

function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function QueryAzs {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    #    $token = 'ff336f7941df4d4385ed509eb06bef8a'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $startDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd 00:00:00')
    $endDate=(Get-Date).AddDays(0).ToString('yyyy-MM-dd 00:00:00')
    $myArray = New-Object System.Collections.ArrayList
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20180725/tasks?page_num=$next_page_num&page_size=1000&sort=begin_time&order_by=DESC&begin_time=$startDate&end_time=$endDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            foreach ($item in $jsonObject.data.data) {
                $item | Add-Member -MemberType NoteProperty -Name "sxf_log_status" -Value $item.status -Force
            }
            $myArray.AddRange($jsonObject.data.data)
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}

$aa=QueryAzs
$result = Write-Output $aa | ConvertTo-Json
$jsonBytes = [System.Text.Encoding]::UTF8.GetBytes($result)
$jsonUTF8 = [System.Text.Encoding]::UTF8.GetString($jsonBytes)
Write-Output $jsonUTF8

5.修改告警收集项(企业运维服务云)
SxfWarningInfo
function IpAndPort{
    return "{{http_service_address}}"

}


function KitUrlGet{
    return "127.0.0.1:7513"
    #return "{{aiopskit_local_url}}"
}


function QueryWarn {
    # 调用函数并获取返回值
    $token = '{{sxfToken}}'
    $ipAndPort = IpAndPort
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $myArray = New-Object System.Collections.ArrayList
    $currDate=(Get-Date).AddDays(-1).ToString('yyyy-MM-dd')
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "$ipAndPort/janus/20190725/alarms?page_num=$next_page_num&page_size=1000&start_time=$currDate" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            if ($next_page_num -eq "20")
            {
                $next_page_num = ""
            }
            $myArray.AddRange($jsonObject.data.data)
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}


$aa=QueryWarn
$result = Write-Output $aa | ConvertTo-Json
$jsonBytes = [System.Text.Encoding]::UTF8.GetBytes($result)
$jsonUTF8 = [System.Text.Encoding]::UTF8.GetString($jsonBytes)
Write-Output $jsonUTF8

6.修改日志模型 增加日志状态 日志变为枚举(企业运维服务云、低代码平台)

7.修改告警模型 增加告警新列 告警级别变为枚举(企业运维服务云、低代码平台)

8.(低代码平台)添加存储定义 etl_engine 修改对应环境的etl_engine表的field_json 通过 hello20 可以调整

9.(低代码平台)删除模型SxfPhysicalMachineMonitor缓存



11.(企业运维服务云)收集项加到相关设备上 开始测试



 Invoke-WebRequest -Uri https://*************/janus/public-key -Method GET -ContentType "application/json" -SkipCertificateCheck