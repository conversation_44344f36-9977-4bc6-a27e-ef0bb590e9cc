INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (0, 783341198410304, 'TagValSetting', 99.00, '服务器', NULL, NULL, NULL, NULL, 90.56, 0, '2024-10-22 13:14:45', '2024-10-22 13:27:26');
INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (1, 783341071860288, 'TagVal', 139.00, '', NULL, NULL, NULL, NULL, 80.25, 0, '2024-10-22 13:15:05', '2024-10-22 13:26:37');
INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (3, 783339669545536, 'TagValRange', 100.00, NULL, '0', '20', '>=', '<', 78.50, 0, '2024-10-22 13:15:32', '2024-10-22 13:19:41');
INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (4, 783339669545536, 'TagValRange', 90.50, '', '21', '50', '>=', '<', 80.23, 0, '2024-10-22 13:15:32', '2024-10-22 13:19:33');
INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (5, 783339669545536, 'TagValRange', 82.56, '', '51', '', '>=', '<', 100.00, 0, '2024-10-22 13:15:32', '2024-10-22 13:19:26');
INSERT INTO `dmp_tag_tag_value_setting` (`id`, `tagId`, `scoreSource`, `score`, `operatorValue`, `leftOperatorValue`, `rightOperatorValue`, `leftOperator`, `rightOperator`, `weight`, `orderNum`, `createTime`, `updateTime`) VALUES (6, 783341198410304, 'TagValSetting', 80.55, '非服务器', '', '', '=', '', 50.50, 0, '2024-10-22 13:21:37', '2024-10-22 13:24:18');
