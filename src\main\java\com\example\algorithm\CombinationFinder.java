package com.example.algorithm;

import java.util.ArrayList;
import java.util.List;

public class CombinationFinder {

    public static void main(String[] args) {
        double targetSum = 1011159.43;
        double[] numbers = {
                229153.07, 231514.84, 239213.85, 240222.73,
                192566.66, 192566.66, 183733.33, 33616.42,
                16426.34, 17961.70, 17977.60, 17937.98,
                7780.58, 7780.58, 7780.58, 118710.99,
                4828.7, 953.75, 2534.25, 1193.55,
                3853.15, 4349.10, 4308.16, 37032.23,
                36992.40, 2346.55, 5177.50, 2382.74,
                1499.84, 717.76, 1308.00
        };

        List<Double> sortedNumbers = new ArrayList<>();
        for (double num : numbers) {
            sortedNumbers.add(num);
        }
        sortedNumbers.sort(Double::compareTo);

        List<List<Double>> combinations = findCombinations(sortedNumbers, targetSum);
        System.out.println("Found " + combinations.size() + " combinations:");
        for (List<Double> combination : combinations) {
            System.out.println(combination);
        }
    }

    private static List<List<Double>> findCombinations(List<Double> numbers, double targetSum) {
        List<List<Double>> allCombinations = new ArrayList<>();
        findCombinationsHelper(numbers, targetSum, 0, 0, new ArrayList<>(), allCombinations);
        return allCombinations;
    }

    private static void findCombinationsHelper(List<Double> numbers, double targetSum, int startIndex, double currentSum, List<Double> currentCombination, List<List<Double>> allCombinations) {
        if (currentSum == targetSum) {
            allCombinations.add(new ArrayList<>(currentCombination));
            return;
        }

        if (currentSum > targetSum) {
            return;  // 终止条件
        }

        for (int i = startIndex; i < numbers.size(); i++) {
            double nextNum = numbers.get(i);
            currentCombination.add(nextNum);
            findCombinationsHelper(numbers, targetSum, i + 1, currentSum + nextNum, currentCombination, allCombinations);
            currentCombination.remove(currentCombination.size() - 1);
        }
    }
}