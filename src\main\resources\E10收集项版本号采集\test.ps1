$testPaths = @(
    "D:\Program Files\Digiwin\E10\Server\Control",
    "E:\E10\Server\Control",
    "C:\Another\Path\E10\Control",
    "F://E10"
)

# foreach ($path in $testPaths) {
#     # 将路径拆分为各部分
#     $pathParts = $path -split '\\'
#
#     # 查找 'E10' 在路径中的位置
#     $e10Index = [array]::IndexOf($pathParts, "E10")
#
#     if ($e10Index -ne -1) {
#         # 构建根目录路径
#         $rootDirParts = $pathParts[0..$e10Index]
#         $rootDir = $rootDirParts -join '\'
#         Write-Host "Matched root directory: $rootDir"
#     } else {
#         Write-Host "No match found for path: $path"
#     }
# }

foreach ($path in $testPaths){
      if ($path -match '^([a-zA-Z]:\\[^\\]+)') {
            # 提取根目录
            $rootDir = $matches[1]
 Write-Host $rootDir
            # 构建新的路径以查找版本文件
#             $newPath = Join-Path -Path $rootDir -ChildPath "Server"
#             $newPath = Join-Path -Path $newPath -ChildPath "Application"
#             $versionFilePath = Join-Path -Path $newPath -ChildPath "Version.xml"
            Write-Host "Matched root directory: $versionFilePath"
    } else {
            Write-Host "No match found for path: $path"
    }
}
