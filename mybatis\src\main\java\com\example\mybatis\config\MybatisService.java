package com.example.mybatis.config;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.example.mybatis.model.*;
import com.example.mybatis.dao.TestMapping;
import com.example.mybatis.utils.DesCryptUtils;
import com.example.mybatis.utils.SnowFlake;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MybatisService {
    @Autowired
    private TestMapping testMapping;
    @Resource
    private RestTemplate restTemplate;

    private final static String PREFIX = "http://172.16.1.152:30010/aiogateway/aioitms";
    private final static String PER_PREFIX = "https://aiops-pre.digiwincloud.com.cn/aiogateway/aioitms";
    private final static String PER_TAI_WAN_PREFIX = "https://aiops-pre.digiwincloud.com/aiogateway/aioitms";
    private final static String DA_LU_PREFIX = "https://aiops.digiwincloud.com.cn/aiogateway/aioitms";

    /**
     * 正式环境
     *
     * @return
     */
    @DataSource(value = "slave")
    public List<EsCustomerServiceV41> selectEs() {
        return testMapping.selectEs();
    }

    @DataSource(value = "master")
    public List<EsCustomerServiceV41> selectEsMaster() {
        return testMapping.selectEsMaster();
    }

    @DataSource(value = "master")
    public Object insertBatch(Set<EsCustomerServiceV41> list) {
        return testMapping.insertBatch(list);
    }

    @DataSource(value = "slave")
    public List<GrpBusDptMapping> selectV3Es() {
        return testMapping.selectV3Es();
    }

    @DataSource(value = "master")
    public Object insertV3Batch(Set<GrpBusDptMapping> list) {
        return testMapping.insertV3Batch(list);
    }

    @DataSource(value = "master")
    public List<AiopsKitDevice> selectDevice() {
        return testMapping.selectDevice();
    }


    public void addSwapCollect2LinuxDevice(Long accId, Boolean isEnable, String collectName, String token) {
        List<AiopsKitDevice> aiopsKitDevices = selectDevice();
        aiopsKitDevices.forEach(ad -> sendDeviceData(ad, accId, isEnable, collectName, token));
    }

    private void sendDeviceData(AiopsKitDevice ad, Long accId, Boolean isEnable, String collectName, String token) {
        String deviceId = ad.getDeviceId();
        String url = DA_LU_PREFIX + "/v2/device/collect/detail/by/mcl/list?aiopsItemId=" + deviceId + "&deviceId=" + deviceId;

        // 假设这些变量在类中已经被定义，否则应该从合适的地方获取它们
        ModuleCollectLayered.ModuleCollectMapping mapping = new ModuleCollectLayered.ModuleCollectMapping(accId, isEnable, collectName);

        List<ModuleCollectLayered.ModuleCollectMapping> dataL = new ArrayList<>();
        dataL.add(mapping);

        ModuleCollectLayered moduleCollectLayered = new ModuleCollectLayered();
        moduleCollectLayered.setModuleCollectMappingList(dataL);

        List<ModuleCollectLayered> data = new ArrayList<>();
        data.add(moduleCollectLayered);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        HttpEntity<List<ModuleCollectLayered>> listHttpEntity = new HttpEntity<>(data, headers);
        try {
//            BaseResponse baseResponse = restTemplate.postForObject(url, listHttpEntity, BaseResponse.class);
//            System.out.println(JSON.toJSON(baseResponse) + "   ---   " + deviceId);
            // 可以根据 baseResponse 做进一步处理
        } catch (Exception e) {
            // 处理异常，例如打印日志或者其他错误处理
            e.printStackTrace();
        }
    }

    @DataSource(value = "master")
    public List<E10Model> selectE10(int num, int size) {
        int i = testMapping.selectE10Count();
        log.info("selectE10 count:{}", i);
        return testMapping.selectE10(num, size);
    }

    public void addSwapCollect2LinuxDeviceV2(Long accId, Boolean isEnable, String collectName, String token, int num, int size, String execModel) throws Exception {
        int pageSize = size;
        int pageNum = (num - 1) * pageSize;
//        sendDeviceDataV2(, accId, isEnable, collectName, token, );
        List<E10Model> e10Models = selectE10(pageNum, pageSize);
        StringBuilder deviceIds = new StringBuilder();
        for (E10Model e10Model : e10Models) {
            String decrypt = DesCryptUtils.getInstance().decrypt(e10Model.getExecParamsContent());
            decrypt = "{\"ExecParams\":" + decrypt + "}";
            String replace = decrypt.replace("\"", "\\\"");

            sendDeviceDataV2(e10Model.getDeviceId(), accId, isEnable, collectName, token, replace, e10Model.getAiopsItemId(), e10Model.getEid(), execModel);
            deviceIds.append(",'").append(e10Model.getDeviceId()).append("'");
        }
        System.out.println("deviceIds:" + deviceIds.toString());

    }

    private void sendDeviceDataV2(String deviceId, Long accId, Boolean isEnable, String collectName, String token, String execParams, String aiopsItemId,
                                  Long eid, String execModel) {
        String url = DA_LU_PREFIX + "/v2/device/collect/detail/by/mcl/list?aiopsItemId=" + aiopsItemId + "&deviceId=" + deviceId;

        // 假设这些变量在类中已经被定义，否则应该从合适的地方获取它们
        ModuleCollectLayered.ModuleCollectMapping mapping =
                new ModuleCollectLayered.ModuleCollectMapping(accId, isEnable, collectName, StringUtils.isEmpty(execModel) ? "DbExtExecParams" : execModel, execParams);

        List<ModuleCollectLayered.ModuleCollectMapping> dataL = new ArrayList<>();
        dataL.add(mapping);

        ModuleCollectLayered moduleCollectLayered = new ModuleCollectLayered();
        moduleCollectLayered.setModuleCollectMappingList(dataL);

        List<ModuleCollectLayered> data = new ArrayList<>();
        data.add(moduleCollectLayered);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        HttpEntity<List<ModuleCollectLayered>> listHttpEntity = new HttpEntity<>(data, headers);
        log.info("eid:{} ,url:{}", eid, url);
        log.info("data:{}", JSONObject.toJSONString(data));
        try {
            BaseResponse baseResponse = restTemplate.postForObject(url, listHttpEntity, BaseResponse.class);
            System.out.println(JSON.toJSON(baseResponse) + "   ---   " + deviceId);
            // 可以根据 baseResponse 做进一步处理
            TimeUnit.MILLISECONDS.sleep(500);
        } catch (Exception e) {
            // 处理异常，例如打印日志或者其他错误处理
            e.printStackTrace();
        }
    }

    @DataSource(value = "master")
    public List<AdcdModel> selectAdcd() {
        return testMapping.selectAdcd();
    }

    public void deviceAccEnable(Boolean isEnable, String token) {
        List<AdcdModel> adcdModels = selectAdcd();
        adcdModels.forEach(adcd -> deviceAccEnable(adcd.getDeviceId(), adcd.getAdcdId(), isEnable, token));
    }

    private void deviceAccEnable(String deviceId, Long adcdId, Boolean isEnable, String token) {
        String url = DA_LU_PREFIX + "/v2/device/modifyCollectDetailEnableForDevice?adcdId=" + adcdId + "&isEnable=" + isEnable + "&deviceId=" + deviceId;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        HttpEntity<List<ModuleCollectLayered>> listHttpEntity = new HttpEntity<>(headers);

        try {
            restTemplate.put(url, listHttpEntity);
//            System.out.println(JSON.toJSON(baseResponse) + "   ---   " + deviceId);
            // 可以根据 baseResponse 做进一步处理
            TimeUnit.SECONDS.sleep(1);
            log.info("url:{}", url);
        } catch (Exception e) {
            // 处理异常，例如打印日志或者其他错误处理
            e.printStackTrace();
        }
        System.out.println("done!");
    }

    public void deleteConfigDevice(String token, List<String> deviceList) {
        for (String deviceId : deviceList) {
            collectConfigDeviceClear(deviceId, token);
        }
    }

    /**
     * 删除特定设备Id收集项缓存
     */
    private void collectConfigDeviceClear(String deviceId, String token) {
        String url = DA_LU_PREFIX + "/collect/config/device/" + deviceId + "/clear";

        // 假设这些变量在类中已经被定义，否则应该从合适的地方获取它们

        List<ModuleCollectLayered.ModuleCollectMapping> dataL = new ArrayList<>();

        ModuleCollectLayered moduleCollectLayered = new ModuleCollectLayered();
        moduleCollectLayered.setModuleCollectMappingList(dataL);

        List<ModuleCollectLayered> data = new ArrayList<>();
        data.add(moduleCollectLayered);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        HttpEntity<List<ModuleCollectLayered>> listHttpEntity = new HttpEntity<>(data, headers);
        log.info("data:{}", JSONObject.toJSONString(data));
        try {
            ResponseEntity<BaseResponse> exchange = restTemplate.exchange(url, HttpMethod.DELETE, listHttpEntity, BaseResponse.class);
            System.out.println(JSONObject.toJSONString(exchange.getBody()) + "   ---   " + deviceId);
            // 可以根据 baseResponse 做进一步处理
            TimeUnit.MILLISECONDS.sleep(500);
        } catch (Exception e) {
            // 处理异常，例如打印日志或者其他错误处理
            e.printStackTrace();
        }
    }


    @DataSource(value = "esclientserver")
    public List<EsclientContractSetting> selectEsclientTransferContractSetting(int pageNum, int pageSize, List<String> ecServiceCodeList,
                                                                               List<String> filterProdcutList) {
        return testMapping.selectEsclientTransferContractSetting(ecServiceCodeList, filterProdcutList, pageNum, pageSize);
    }

    @DataSource(value = "esclientserver")
    public int selectEsclientTransferContractSettingCount(List<String> ecServiceCodeList,
                                                          List<String> filterProdcutList) {
        return testMapping.selectEsclientTransferContractSettingCount(ecServiceCodeList, filterProdcutList);
    }


    @DataSource(value = "cnprodee")
    public List<TenantModuleContract.Detail> selectEid(Set<String> serviceCodeList) {
        return testMapping.selectEid(serviceCodeList);
    }

    @DataSource(value = "cnprodee")
    public List<TenantModuleContract> selectTmc(List<Long> eidList) {
        return testMapping.selectTmc(eidList);
    }

    @DataSource(value = "cnprodee")
    public Long selectEmp(String name) {
        return testMapping.selectEmp(name);
    }
    @DataSource(value = "master")
    public List<String> selectMaintainDevice(List<String> eidList){
        return testMapping.selectMaintainDevice(eidList);
    }

    @DataSource(value = "master")
    public List<Tenant> selectTenant(List<String> deviceIdList){
        return testMapping.selectTenant(deviceIdList);
    }


    public void read147ProductLineTmc() {
        String fileName = "D:\\des\\1111.xlsx";

        // 使用 EasyExcel 读取 Excel 文件
        EasyExcel.read(fileName, Excel147Data.class, new PageReadListener<Excel147Data>(dataList -> {
            Set<String> serviceCode = dataList.stream().map(Excel147Data::getServiceCode).collect(Collectors.toSet());
            List<TenantModuleContract.Detail> detailList = selectEid(serviceCode);
            Map<String, Long> serviceCodeMap = detailList.stream()
                    .collect(Collectors.toMap(TenantModuleContract.Detail::getServiceCode, TenantModuleContract.Detail::getEid));

            List<Long> eidList = detailList.stream().map(TenantModuleContract.Detail::getEid).collect(Collectors.toList());
            List<TenantModuleContract> tenantModuleContracts = selectTmc(eidList);
            Map<Long, List<TenantModuleContract>> eidMap = tenantModuleContracts.stream().collect(Collectors.groupingBy(TenantModuleContract::getEid));
            Map<String, List<Excel147Data>> serviceCodeExcelMap = dataList.stream().collect(Collectors.groupingBy(Excel147Data::getServiceCode));
            for (Map.Entry<String, Long> entry : serviceCodeMap.entrySet()) {
                List<TenantModuleContract> tmcList = eidMap.getOrDefault(entry.getValue(), new ArrayList<>());
                List<Excel147Data> excel147DataList = serviceCodeExcelMap.getOrDefault(entry.getKey(), new ArrayList<>());
                for (Excel147Data excel147Data : excel147DataList) {
                    Long samcid = null;
                    if (excel147Data.getStatus().equals("订阅到期")) {
                        excel147Data.setStatus("4");
                    }
                    if (excel147Data.getStatus().equals("已订阅")) {
                        excel147Data.setStatus("3");
                    }
                    if (excel147Data.getModuleCode().equals("IT运维")) {
                        excel147Data.setModuleCode("1");
                        samcid = 105000000000101L;
                    }
                    if (excel147Data.getModuleCode().equals("SQL Server数据库运维")) {
                        excel147Data.setModuleCode("2001");
                        samcid = 105000000002001L;
                    }
                    if (excel147Data.getModuleCode().equals("ORACLE数据库运维")) {
                        excel147Data.setModuleCode("3001");
                        samcid = 105000000003001L;
                    }
                    TenantModuleContract tmc = tmcList.stream().filter(e -> e.getModuleId().equals(Long.parseLong(excel147Data.getModuleCode())))
                            .findFirst().orElse(null);
                    DateTime start = DateUtil.parse(excel147Data.getStartDate(), "yyyyMMdd");
                    DateTime end = DateUtil.parse(excel147Data.getEndDate(), "yyyyMMdd");
                    if (tmc == null) {
                        // 新增
                        Long userSid = selectEmp(excel147Data.getResponsiblePerson());
                        long tmcId = SnowFlake.getInstance().newId();
                        if (!excel147Data.getStatus().equals("3")) {
                            continue;
                        }
                        String sql = "insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)\n" +
                                "values (" + tmcId + "," + 241199971893824L + "," + entry.getValue() + "," + excel147Data.getModuleCode() + "," + 241199971893824L + "," + excel147Data.getStatus() + "," + 1 + ",'" +
                                DateUtil.format(start, "yyyy-MM-dd") + "','" + DateUtil.format(end, "yyyy-MM-dd") + "',0,0,0," + userSid + ",0,'" + excel147Data.getResponsiblePerson() + "');";
                        String detailSql = "insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)\n" +
                                "values (" + SnowFlake.getInstance().newId() + "," + tmcId + "," +samcid+ ",0," + (excel147Data.getModuleCode().equals("1") ? 3 : 1) + ");";
//                        System.out.println(sql);
//                        System.out.println(detailSql);
                    } else {
                        if (
                                !tmc.getStartDate().equals(start) ||
                                        !tmc.getEndDate().equals(end)) {
                            String tmString = entry.getKey() + "_" + tmc.getEid() + "_" + tmc.getModuleId() + "_" + DateUtil.format(tmc.getStartDate(), "yyyy-MM-dd") + "_" +
                                    DateUtil.format(tmc.getEndDate(), "yyyy-MM-dd") + "_" + tmc.getStatus();

                            String aa = "update tenant_module_contract set startDate = '" + DateUtil.format(start, "yyyy-MM-dd")
                                    + "' and endDate = '" + DateUtil.format(end, "yyyy-MM-dd")
                                    + "' and status = " + excel147Data.getStatus() + " where moduleId = " + excel147Data.getModuleCode() + " and "
                                    + " eid = " + entry.getValue()+";";
//                            log.error("[update]tmc:{} , sql:{}", tmString, aa);
                            System.out.println(aa);

                        }else {
//                            if (tmc.getStatus() != Integer.parseInt(excel147Data.getStatus())){
//                                log.error("[noUpdate]tmcS:{} , excel147DataS:{}", tmc.getStatus(), excel147Data.getStatus());
//                            }
                        }
                    }
                }
            }
        })).sheet().doRead();


    }





}
