{"name": "sxf_test.657803324531264", "collect_interval": 60, "batch_size": 1, "batch_interval": 60, "errors_list_cap": 0, "createtime": "2024-03-12 15:06:15", "extra_info": false, "log_audit": false, "send_raw": false, "read_time": false, "internal_key_prefix": "", "max_line_len": 0, "is_block": false, "file_param_names": "", "upload_run_track_mode": 0, "reader": {"runner_name": "sxf_test.657803324531264", "name": "sxf_test.657803324531264.reader", "mode": "httpfetch_v2", "http_method": "GET", "http_service_address": "https://*************/janus/20180725/tasks", "http_service_path": "", "http_cron": "0 */1 * * * * ", "http_exec_onstart": "false", "http_dial_timeout": "30", "http_response_timeout": "30", "http_headers": "{\"Authorization\":\"Token 7d26b2e4adee89cb9f963beb8cfb4c92\"}", "http_body": "", "execute_error_return": "true", "execute_error_return_key": "execError", "execute_error_return_content": "", "package_return_content": "", "quote_content": "false"}, "parser": {"runner_name": "sxf_test.657803324531264", "name": "sxf_test.657803324531264.parser", "type": "json_v2", "not_exist_error_tag": "cmdNotExist", "error_tag": "execError", "special_error_tags": "", "error_continue_parse": "false", "disable_record_errdata": "false", "keep_raw_data": "false", "contain_source_id": "false", "source_id_key": "source_db_id", "source_id_value": "{{db_id}}"}, "transforms": [{"type": "filter", "key": "code", "mode": "keep", "pattern": "0"}, {"tile_key": "data.data", "has_error_break": true, "type": "tilekeys"}, {"key_case_type": "NO_CHANGE", "special_key_map": "{\"id\":\"task_id\"}", "add_key_prefix": "", "add_key_suffix": "", "type": "adjustkeyname", "add_key_contain_special_key": true}], "senders": [{"runner_name": "sxf_test.657803324531264", "sender_type": "http_v2", "http_sender_url": "{{digiwin_upload_url}}", "http_sender_protocol": "body_json", "http_sender_escape_html": "true", "http_sender_csv_split": ",", "http_sender_gzip": "false", "http_sender_other_headers": "{\"eid\":\"{{eid}}\",\"token\":\"{{token}}\"}", "http_sender_collect_config_id": "657803324531264", "http_sender_upload_data_model_code": "sxfTest", "http_sender_package_template": "{\"deviceId\":\"{{device_id}}\",\"eid\":\"{{eid}}\",\"collectedTime\":\"{{collected_time}}\",\"collectConfigId\":\"{{__collectConfigId__}}\",\"deviceCollectDetailId\":\"{{__deviceCollectDetailId__}}\",\"uploadDataModelCode\":\"{{__uploadDataModelCode__}}\",\"aiId\":\"{{__aiId__}}\",\"aiopsItem\":\"{{__aiopsItem__}}\",\"dataContent\":{{__selfData__}}}", "http_sender_send_data_to_string": "true", "http_sender_timeout": "30s", "ft_strategy": "backup_only", "ft_discard_failed_data": "false", "ft_retry_count_before_discard": "6", "ft_memory_channel": "false", "ft_long_data_discard": "false", "max_disk_used_bytes": "524288000", "max_size_per_file": "104857600", "http_sender_quote_package_data": "true", "http_sender_parent_package_template": "[{\"headers\": {\"namenode\":\"namenode.example.com\",\"datanode\":\"random_datanode.example.com\",\"SourceType\":\"JSON\"},\"body\":{{__packageData__}}}]", "http_sender_encrypt_self_data": "false", "http_sender_use_proxy": "true", "http_sender_data_batch_send_size": "300", "http_sender_fixed_sending_data": "{\"collected_time\":\"{{collected_time}}\"}"}], "router": {"router_key_name": "", "router_match_type": "", "router_default_sender": 0, "router_routes": {}}, "web_folder": true, "is_stopped": false, "is_hidden": false}