{"job": {"content": [{"writer": {"parameter": {"partitionEnabled": false, "writeMode": "truncate", "fieldDelimiter": "\t", "column": [{"type": "STRING", "name": "eid"}, {"type": "TIMESTAMP", "name": "caldatetime"}, {"type": "STRING", "name": "cxotype"}, {"type": "STRING", "name": "cxotag"}, {"type": "STRING", "name": "cxochildtag"}, {"type": "DOUBLE", "name": "cxoscore"}], "path": "/user/hive/warehouse/tbb.db/aiops_cxo_tag", "fileType": "text", "defaultFS": "hdfs://ddp1:8020", "fileName": "aiops_cxo_tag"}, "name": "hdfswriter"}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , '' as cxochildtag, chotag01score * 0.5 + managertag01score * 0.3 + hrtag01score * 0.2 as cxoscore from (select eid , ifnull(f_highEmploymentRisk,0) * 100 as chotag01score, ifnull(f_contractRiskControlDifficult,0) * 20 + ifnull(f_multipleInspectionInstitutionsLowPassRate,0) * 60 +ifnull(f_overseasCompRisk,0) * 20 as managertag01score, ifnull(f_paperDocCtrlDiff,0) * 100 + ifnull(f_factoryAuditLow,0) * 60 + ifnull(f_paySlipConfidLow,0) * 5 as hrtag01score from servicecloud.CHORoleSurvey ) as tag001 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype,'人工成本高' as cxotag , '' as cxochildtag, chotag02score * 0.5 + managertag02score * 0.3 + hrtag02score * 0.2 as cxoscore from (select eid , ifnull(f_highLaborCost,0) * 100 as chotag02score, ifnull(f_staffControlDiff,0) * 30 + ifnull(f_budgetControlDiff,0) * 30 + ifnull(f_OTHoursVerifyDiff,0) * 40 as managertag02score, ifnull(f_leaveCtrlDiff ,0)* 50 + ifnull(f_OTHoursVerifyDiff ,0)* 50 as hrtag02score from servicecloud.CHORoleSurvey ) as tag002 union all select eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , '' as cxochildtag, chotag03score * 0.5 + managertag03score * 0.3 + hrtag03score * 0.2 as cxoscore from (select eid , ifnull(f_lowEfficiencyHR,0) * 100 as chotag03score, ifnull(f_payrollPeriodLong ,0)* 50 + ifnull(f_HRSalaryCycleLong,0) * 50 as managertag03score, ifnull(f_HRInfoCollectDiff ,0)* 10 + ifnull(f_HRChangeProcComplex,0) * 5 + ifnull(f_reportDiff,0) * 5 + + ifnull(f_attDataCollectDiff ,0) * 10 + ifnull(f_shiftChangeFreq,0) * 15 + ifnull(f_attDataCollectDiff,0) * 40 +ifnull( f_excelPRLinkDiff,0) * 10 + ifnull(f_pieceworkOutputDiff,0) * 10 + ifnull(f_taxCalcDiff,0) * 10 + ifnull(f_salaryCycleLong,0) * 15 + ifnull(f_partTimeCostAllocDiff,0) * 5 as hrtag03score from servicecloud.CHORoleSurvey ) as tag003 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '用工风险高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741929574449728 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人工成本高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741932005069376 union all select id as eid, CURRENT_DATE() as caldatetime , 'CHO' as cxotype, '人资效率低' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=741933931708992 limit 1000", "select a.eid ,CURDATE() as caldatetime , 'CSO' as cxotype, '报价周期长，容易错失商机' as cxotag , '' as cxochildtag, ifnull(a.f_quotingDifficulty, 0) * 30 + case when b.sqQuoteConfirmation = '1' and b.sqQuoteEfficiency >= '3' then 25 else 0 end + ifnull(a.f_quotingTraceability, 0) * 25 + ifnull(a.f_productDeliveryControl, 0) * 20 as cxoscore from servicecloud.CSORoleSurvey a left join servicecloud.IndustryPlanQuickScreening b on a.eid= b.eid union all select a.eid ,CURDATE() as caldatetime , 'CSO' as cxotype,'接单周期长，影响客户满意度' as cxotag , '' as cxochildtag, case when b.sqOrderConfirmation = '1' and b.sqOrderEfficiency >= '3' then 40 else 0 end + ifnull(a.f_freeClericalWorkload, 0) * 25 + ifnull(a.f_salesProdFinDependency, 0) * 35 as cxoscore from servicecloud.CSORoleSurvey a left join servicecloud.IndustryPlanQuickScreening b on a.eid= b.eid union all select a.eid , CURDATE() as caldatetime , 'CSO' as cxotype, '商品型号复杂，出错概率高' as cxotag , '' as cxochildtag, case when a.f_CSOcatLevels0103 > '1' then 15 else 0 end + case when b.sqProdTypeRatio >= 30 then 50 else 0 end + case when b.sqProdModelCount >= 200 then 35 else 0 end as cxoscore from servicecloud.CSORoleSurvey a left join servicecloud.IndustryPlanQuickScreening b on a.eid= b.eid union all select a.eid , CURDATE() as caldatetime , 'CSO' as cxotype, '组织结构复杂，业务管理难' as cxotag , '' as cxochildtag, ifnull(a.f_orgStructureComplexity, 0) * 40 + ifnull(a.f_dataAnalysisEfficiency, 0) * 40 as cxoscore from servicecloud.CSORoleSurvey a left join servicecloud.IndustryPlanQuickScreening b on a.eid= b.eid union all select id as eid, CURDATE() as caldatetime , 'CSO' as cxotype, '报价周期长，容易错失商机' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745801124532800 union all select id as eid, CURDATE() as caldatetime , 'CSO' as cxotype, '接单周期长，影响客户满意度' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745860009935424 union all select id as eid, CURDATE() as caldatetime , 'CSO' as cxotype, '商品型号复杂，出错概率高' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=744850007028288 union all select id as eid, CURDATE() as caldatetime , 'CSO' as cxotype, '组织结构复杂，业务管理难' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=744837950399040 \n", "select eid, CURDATE() as caldatetime , 'CFO' as cxotype, '出表精度待提升' as cxotag , '' as cxochildtag, cfotag01score * 0.75 + managertag01score * 0.25 as cxoscore from (select eid , ifnull(f_CFOUnrealizedPnLCalc0102,0) * 100 as cfotag01score, ifnull(f_CFOIntManConsReptReq0205,0) * 100 as managertag01score from servicecloud.CFORoleSurvey ) as tag001 union all select eid, CURDATE() as caldatetime , 'CFO' as cxotype,'出表效率待提升' as cxotag , '' as cxochildtag, cfotag02score * 0.75 + managertag02score * 0.25 as cxoscore from (select eid , ifnull(f_CFOMonthlyMgmtMtgCapRef0101,0) * 100 as cfotag02score, ifnull(f_CFOMonthlyCloseSch0201,0) * 25 + case f_CFOGrpConsStmtDay0202 when '1' then 25 when '2' then 25 when '3' then 15 else 0 end + ifnull(f_CFOMonthlyIntTransDiff0203,0) * 25 + case when f_CFOConsolidElimChallenges0208 is null or f_CFOConsolidElimChallenges0208 = '' then 25 else 0 end as managertag02score from servicecloud.CFORoleSurvey ) as tag002 union all select eid, CURDATE() as caldatetime , 'CFO' as cxotype, '合规性满足度' as cxotag , '' as cxochildtag, cfotag03score * 0.75 + managertag03score * 0.25 as cxoscore from (select eid , ifnull(f_CFOListingPrepGrpDisc0103,0) * 50 + ifnull(f_CFOMthlyConsWrkTransp0104,0) * 50 as cfotag03score, ifnull(f_CFOMthlyFullConsFs0204 ,0)* 100 as managertag03score from servicecloud.CFORoleSurvey ) as tag003 union all select id as eid, CURDATE() as caldatetime , 'CFO' as cxotype, '出表精度待提升' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745905290920512 union all select id as eid, CURDATE() as caldatetime , 'CFO' as cxotype, '出表效率待提升' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745906542699072 union all select id as eid, CURDATE() as caldatetime , 'CFO' as cxotype, '合规性满足度' as cxotag , tagValue as cxochildtag, 0 as cxoscore from AIEOM.tenant_tag_string where tagId=745908308824640"], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}