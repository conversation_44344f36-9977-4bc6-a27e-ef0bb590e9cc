package com.example.aio.utils;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * Des加解密实用类
 */
public class DesCryptUtils {
    private final static String DIGIWIN_KEY = "digiwin_aiopskit";
    private final static Map<String, DesCryptUtils> _instanceMap = new HashMap<>();

    private Map<Integer, Cipher> cipherMap;

    /**
     * 获取默认键单例实例
     * @return Des加解密实用类
     * @throws Exception 异常对象
     */
    public static DesCryptUtils getInstance() throws Exception {
        return getInstance(DIGIWIN_KEY);
    }

    /**
     * 获取特定键单例实例
     * @param key 特定键
     * @return Des加解密实用类
     * @throws Exception 异常对象
     */
    public static DesCryptUtils getInstance(String key) throws Exception {
        DesCryptUtils instance = _instanceMap.get(key);
        if (instance == null) {
            instance = new DesCryptUtils(key);
            _instanceMap.put(key, instance);
        }
        return instance;
    }

    public static DesCryptUtils getAsyncInstance() throws Exception {
        return new DesCryptUtils(DIGIWIN_KEY);
    }

    /**
     * 特定密钥构造器(不公开)
     * @param key 密钥
     * @throws Exception 异常对象
     */
    private DesCryptUtils(String key) throws Exception {
        byte[] md5ByteValue = Md5Utils.getMd5ByBytes(key);
        if (md5ByteValue == null) {
            //输出的异常讯息可能会显示到前端，因此内容不写得很明确
            throw new Exception("not get Md5 value");
        }
        cipherMap = new HashMap<>(4);
        // 获取密钥并初始化
        byte[] arrTempByteArray = get8Bytes(md5ByteValue);
        java.security.Key objKey = new SecretKeySpec(arrTempByteArray, "DES");
        IvParameterSpec objIv = new IvParameterSpec(arrTempByteArray);
        Cipher objCipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        initAndPutCipherMap(objCipher, Cipher.ENCRYPT_MODE, objKey, objIv);
        objCipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        initAndPutCipherMap(objCipher, Cipher.DECRYPT_MODE, objKey, objIv);
    }

    private byte[] get8Bytes(byte[] strPrivateKey){
        //从指定的字串制成密钥，密钥所需的字元阵列长度为8位，不足及超过都要处理
        byte[] arrTempByteArray = new byte[8];
        // 将原始字元阵列转换为8位
        for (int i = 0; i < strPrivateKey.length && i < arrTempByteArray.length; i++) {
            arrTempByteArray[i] = strPrivateKey[i];
        }
        return arrTempByteArray;
    }

    private void initAndPutCipherMap(Cipher objCipher, int mode, java.security.Key objKey, IvParameterSpec objIv)
            throws Exception {
        objCipher.init(mode, objKey, objIv);
        cipherMap.put(mode, objCipher);
    }

    private String byte2Hex(byte[] bytes) {
        return Hex.encodeHexString(bytes);
    }

    private byte[] hex2Byte(String hexString) throws DecoderException {
        return Hex.decodeHex(hexString);
    }

    /**
     * 加密字串
     * @param byteArray byte数组
     * @return 加密后的byte数组
     * @throws Exception 异常对象
     */
    private byte[] doEncrypt(byte[] byteArray) throws Exception {
        Cipher cipher = cipherMap.get(Cipher.ENCRYPT_MODE);
        if (cipher == null) {
            throw new RuntimeException("doEncrypt error by cipher is null");
        }
        return cipher.doFinal(byteArray);
    }

    /**
     * 加密字串
     * @param encryptString 欲加密的字串
     * @return 加密后的结果字串
     * @throws Exception 异常对象
     */
    public synchronized String encrypt(String encryptString) throws Exception {
        return byte2Hex(doEncrypt(encryptString.getBytes()));
    }

    /**
     * 解密字串
     * @param byteArray byte数组
     * @return 解密后的byte数组
     * @throws Exception 异常对象
     */
    private byte[] doDecrypt(byte[] byteArray) throws Exception {
        Cipher cipher = cipherMap.get(Cipher.DECRYPT_MODE);
        if (cipher == null) {
            throw new RuntimeException("doDecrypt error by cipher is null");
        }
        return cipher.doFinal(byteArray);
    }

    /**
     * 解密字串
     * @param decryptString 欲解密的字串
     * @return 解密后的结果字串
     * @throws Exception 异常对象
     */
    public synchronized String decrypt(String decryptString) throws Exception {
        return new String(doDecrypt(hex2Byte(decryptString)));
    }

    public static void main(String[] args) throws Exception {
        String desCryptUtils = DesCryptUtils.getInstance().decrypt("6480129084f819a63c090312f4561978aeb0fbd8f916b32da4636fc52b0f9c104041a4493a599e0ff5c4dd68928fcc672d590bf149cf636444d36f354e424d1ad1b349cbbffbdabcb11b969e4b1fdd98505a890d79edc53298fff0c05f0f30d4");
        System.out.println(desCryptUtils);
    }
}
