主机1

规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',1,90.50,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',4,80.5,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',5,100,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',6,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',7,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',8,100,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',9,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',10,100,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('472898532861294404',11,100,'2024-11-11 11:11:11','2024-11-11');

主机2

规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',1,82.5,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',4,75.3,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',5,80.05,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',6,90.6,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',7,65,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',8,72,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',9,82.6,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',10,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('477551599447715894',11,100,'2024-11-11 11:11:11','2024-11-11');


主机3
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',1,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',5,80,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',6,90.6,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',7,100,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',8,60,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',9,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',10,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('528853789327766597',11,80,'2024-11-11 11:11:11','2024-11-11');


终端1
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',1,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',5,90,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',6,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',7,75,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',8,90,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',9,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',10,95,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('516799307588842564',11,80,'2024-11-11 11:11:11','2024-11-11');

终端2
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',1,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',5,90,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',6,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',7,75,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',8,90,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',9,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',10,95,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('535360676290377284',11,80,'2024-11-11 11:11:11','2024-11-11');


数据库1
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',1,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',4,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',5,85,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',6,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',7,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',8,70,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',9,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',10,50,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('273131ace29ac0778f793901f61e2121',11,50,'2024-11-11 11:11:11','2024-11-11');

数据库2
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',1,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',4,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',5,100,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',6,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',7,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',8,90,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',9,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',10,50,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('5ff5ac3b12eaef58043183df210c006a',11,50,'2024-11-11 11:11:11','2024-11-11');

数据库3
规范
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',1,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',4,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',5,100,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',6,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',7,100,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',8,100,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',9,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',10,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id, tagId, tagValue, updateTime, tagDate) values ('ee57629c71b4ce20c240af843929cc41',11,80,'2024-11-11 11:11:11','2024-11-11');
