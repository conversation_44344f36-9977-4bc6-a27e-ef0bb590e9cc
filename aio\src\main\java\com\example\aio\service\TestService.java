package com.example.aio.service;

import com.alibaba.fastjson.JSONPath;
import com.example.aio.dao.TestMapper;
import com.example.aio.model.DataExaminationMenu;
import com.github.houbb.opencc4j.support.data.impl.OpenccDatas;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.jayway.jsonpath.JsonPath;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 網安智檢
 */
@Service
public class TestService {
    @Resource
    private TestMapper testMapper;
    public List<DataExaminationMenu> test(String parentCode,String menuCode,String menuName,String menuSource) {
        List<DataExaminationMenu> retList = new ArrayList<>();
        var menuList = testMapper.selectByMenu(parentCode);
        Map<String, List<DataExaminationMenu>> sourceMap = menuList
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(DataExaminationMenu::getMenuSource));

        sourceMap.forEach((type, dataExaminationMenuList) -> {

            if (StringUtils.hasText(menuSource)) {
                if (type.equals(menuSource)) {
                    dataExaminationMenuList.forEach(menu->{
                        menu.setMenuCode(menuCode);
                        menu.setParentId(menu.getId());
                        if (ZhConverterUtil.isTraditional(menuName)) {
                            menu.setMenuName_CN(ZhConverterUtil.toSimple(menuName));
                        }else {
                            menu.setMenuName_CN(menuName);
                        }
                        if (ZhConverterUtil.isSimple(menuName)) {
                            menu.setMenuName_TW(ZhConverterUtil.toTraditional(menuName));
                        }else {
                            menu.setMenuName_TW(menuName);
                        }
                        menu.setMenuName(menuName);
                        List<DataExaminationMenu> childList = testMapper.selectByMenuByParentId(menu.getParentId());
                        //获取list中 menuOrder最大的数字
                        // 获取具有最大 menuOrder 的 DataExaminationMenu 对象
                        Optional<DataExaminationMenu> maxMenu = childList.stream()
                                .max(Comparator.comparingInt(DataExaminationMenu::getMenuOrder));

                        // 获取最大 menuOrder 的值，如果 list 为空则使用orElseGet()提供默认值
                        int maxMenuOrder = maxMenu.map(DataExaminationMenu::getMenuOrder).orElseGet(() -> 0);
                        menu.setMenuOrder(maxMenuOrder);
                        menu.setId(menu.getBusKey());
                        testMapper.insertMenu(menu);
                        retList.add(menu);
                    });
                }
            }else {
                dataExaminationMenuList.forEach(menu->{
                    menu.setMenuCode(menuCode);
                    menu.setParentId(menu.getId());
                    if (ZhConverterUtil.isTraditional(menuName)) {
                        menu.setMenuName_CN(ZhConverterUtil.toSimple(menuName));
                    }else {
                        menu.setMenuName_CN(menuName);
                    }
                    if (ZhConverterUtil.isSimple(menuName)) {
                        menu.setMenuName_TW(ZhConverterUtil.toTraditional(menuName));
                    }else {
                        menu.setMenuName_TW(menuName);
                    }
                    menu.setMenuName(menuName);
                    List<DataExaminationMenu> childList = testMapper.selectByMenuByParentId(menu.getParentId());
                    //获取list中 menuOrder最大的数字
                    // 获取具有最大 menuOrder 的 DataExaminationMenu 对象
                    Optional<DataExaminationMenu> maxMenu = childList.stream()
                            .max(Comparator.comparingInt(DataExaminationMenu::getMenuOrder));

                    // 获取最大 menuOrder 的值，如果 list 为空则使用orElseGet()提供默认值
                    int maxMenuOrder = maxMenu.map(DataExaminationMenu::getMenuOrder).orElseGet(() -> 0);
                    menu.setMenuOrder(maxMenuOrder);
                    menu.setId(menu.getBusKey());
                    testMapper.insertMenu(menu);
                    retList.add(menu);
                });
            }


        });
        return retList;
    }


    public static void main(String[] args) {
        Map<String, Object> modelData = new HashMap<>();
        modelData.put("GZDNUC039", "123");
        Object val = JsonPath.read(modelData, "$.gzdnuc039"  );
        System.out.println(val);
    }


}
