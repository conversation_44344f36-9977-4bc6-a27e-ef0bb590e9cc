SELECT *
FROM SYSTEM.CATALOG
WHERE  TABLE_NAME = 'WINDOWSSECURITYCEV20201472COLLECTED';

CREATE TABLE `CVE20201472EventModel_test_sync` (
                                         `deviceId` varchar(65533) NOT NULL COMMENT "",
                                         `collectedTime` datetime NULL COMMENT "",
                                         `eid` varchar(65533) NULL COMMENT "",
                                         `collectConfigId` varchar(65533) NULL COMMENT "",
                                         `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                         `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
                                         `aiId` varchar(65533) NULL COMMENT "",
                                         `aiopsItem` varchar(65533) NULL COMMENT "",
                                         `code` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`deviceId`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`deviceId`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);