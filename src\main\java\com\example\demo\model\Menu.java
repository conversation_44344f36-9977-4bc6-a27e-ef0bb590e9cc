package com.example.demo.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class Menu {
    @ExcelProperty("id(可以不填)")
    private String id = "0";

    @ExcelProperty("菜单名称")
    private String menuName;

    @ExcelProperty("菜单名称(CN)")
    private String menuName_CN;

    @ExcelProperty("菜单名称(TW)")
    private String menuName_TW;


    @ExcelProperty("菜单Code")
    private String menuCode;

    @ExcelProperty("菜单父id")
    private String parentId;

    @ExcelProperty("菜单状态(1 未发布 2已发布 3已删除)")
    private Integer menuStatus;

    @ExcelProperty("菜单类型(Report、Component、Fix)")
    private String menuType;

    @ExcelProperty("菜单顺序")
    private Integer menuOrder;

    @ExcelProperty("菜单来源(AIEOM、MIS、AIEOM_MIS)")
    private String menuSource;

    @ExcelProperty("imgUrl")
    private String imgUrl;
    @ExcelProperty("是否页面")
    private String isPage;
}
