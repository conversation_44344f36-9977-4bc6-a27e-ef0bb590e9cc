#!/bin/bash

# 定义函数SxfUrlGet
SxfUrlGet() {
    echo "{{SCP_IP}}"
    # 或者使用固定的IP地址
#     echo "*************"
}

# 定义函数PublicKey
PublicKey() {
    sxfUrl=$(SxfUrlGet)
    response=$(curl -k -s -X GET https://$sxfUrl/janus/public-key)
    if [ $? -eq 0 ]; then
        publicKey=$(echo "$response" |  awk -F '[:,}"]' '{for(i=1;i<=NF;i++){if($i == "public_key") print $(i+3)}}')
        echo "$publicKey"
    else
        echo "请求失败"
    fi
}

# 定义函数PrivateKeyGet
PrivateKeyGet() {
    content="{{content}}"
    publicKey=$(PublicKey)
    aiopsKitLocalUrl="{{aiopskit_local_url}}"
    url="$aiopsKitLocalUrl/aiopskit/ras?content=$content&publicKey=$publicKey"
    response=$(curl -s "$url")
    #todo
    echo "$response" | jq -r '.data'
}

# 定义函数TokenGet
TokenGet() {
#     privateKey="7962ca4ff7f46298cf0695426ed9dfb3664345b1bf17dbb37c4bed632ed7eae59abaa13ba2f3740a78fb7b9b1460cdb00f75bb1659be989af56971a75fdbd042e8289767f21df1a091cda01161551ce6910bfa6a3120af38b3dff06e53361aad8a6cdc7c65b6ff195f63df30bb60a2446c83b93e77f5db8c2f8980a4dce6852137dcbf8c6e6e074767beee18618a7ebb6714e558f03d6806e1826e7ba2f4090a8b7bf08bfa4b802c3b7c53859535f4310673dfb1e9918c93bd8318af0f09bef0a6bfc4b26cdcfa5465d34e124297fc19ac8a25646aa5b7c81395f9f3acf51a91cc691840d8d9197feb360217abf346c9c7e043b9d5c89991fbe00b45f729d8dd"
    privateKey=$(PrivateKeyGet)
#     sxfUserName="openlab"
    sxfUserName="{{sxfUserName}}"
    sxfUrl=$(SxfUrlGet)
    payload='{"auth": {"passwordCredentials": {"username": "'"${sxfUserName}"'", "password": "'"${privateKey}"'"}}}'
    response=$(curl -k -s -X POST -H "Content-Type: application/json" -d "$payload" https://$sxfUrl/janus/authenticate)
    code=$(echo "$response" |  awk -F '[:,}"]' '{for(i=1;i<=NF;i++){if($i == "code") print $(i+2)}}' |head -1)
           if [[ "$code" == "0" ]]; then
                access_token_id=$(echo "$response" |  awk -F '[:,}"]' '{for(i=1;i<=NF;i++){if($i == "id") print $(i+3)}}' |head -1)
               echo "$access_token_id"
           else
               echo "请求失败"
           fi
    # 还可添加发送器逻辑
    # 发送器
    # 4. 将Token({{sxfToken}})保存到文件参数
    # 5. 刷新地端执行参数缓存
}

aa=$(TokenGet)
echo "$aa"