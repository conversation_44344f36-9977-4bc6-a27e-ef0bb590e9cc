[{"fieldCode": "deviceId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.deviceId"}, {"fieldCode": "eid", "fieldType": "VARCHAR", "valuePath": "BasicInfo.eid"}, {"fieldCode": "collectedTime", "fieldType": "VARCHAR", "valuePath": "BasicInfo.collectedTime"}, {"fieldCode": "collectConfigId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.collectConfigId"}, {"fieldCode": "uploadDataModelCode", "fieldType": "VARCHAR", "valuePath": "BasicInfo.uploadDataModelCode"}, {"fieldCode": "deviceCollectDetailId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.deviceCollectDetailId"}, {"fieldCode": "aiId", "fieldType": "VARCHAR", "valuePath": "BasicInfo.aiId"}, {"fieldCode": "aiopsItem", "fieldType": "VARCHAR", "valuePath": "BasicInfo.aiopsItem"}, {"fieldCode": "flumeTimestamp", "fieldType": "VARCHAR", "valuePath": "BasicInfo.flumeTimestamp"}, {"fieldCode": "status", "fieldType": "VARCHAR", "valuePath": "DataContent.status"}, {"fieldCode": "action", "fieldType": "VARCHAR", "valuePath": "DataContent.action"}, {"fieldCode": "object_type", "fieldType": "VARCHAR", "valuePath": "DataContent.object_type"}, {"fieldCode": "object_id", "fieldType": "VARCHAR", "valuePath": "DataContent.object_id"}, {"fieldCode": "az_id", "fieldType": "VARCHAR", "valuePath": "DataContent.az_id"}, {"fieldCode": "begin_time", "fieldType": "VARCHAR", "valuePath": "DataContent.begin_time"}, {"fieldCode": "end_time", "fieldType": "VARCHAR", "valuePath": "DataContent.end_time"}, {"fieldCode": "user_id", "fieldType": "VARCHAR", "valuePath": "DataContent.user_id"}, {"fieldCode": "description", "fieldType": "VARCHAR", "valuePath": "DataContent.description"}, {"fieldCode": "user_ip", "fieldType": "VARCHAR", "valuePath": "DataContent.user_ip"}, {"fieldCode": "creator_id", "fieldType": "VARCHAR", "valuePath": "DataContent.creator_id"}, {"fieldCode": "progress", "fieldType": "INT", "valuePath": "DataContent.progress"}, {"fieldCode": "project_id", "fieldType": "VARCHAR", "valuePath": "DataContent.project_id"}, {"fieldCode": "task_id", "fieldType": "VARCHAR", "valuePath": "DataContent.task_id"}]