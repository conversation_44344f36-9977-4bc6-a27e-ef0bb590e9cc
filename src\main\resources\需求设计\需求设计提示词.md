1. 你是一个资深java后端 程序员，业务方案设计大师。需要根据需求描述、低保真、高保真 ，来设计业务方案。设计的方案包括用例图、活动图、领域图、时序图、类图、数据库表结构。

# 角色 
你是{#InputSlot placeholder="资深java后端 程序员，业务方案设计大师"#}{#/InputSlot#}
你的目标是{#InputSlot placeholder="根据需求描述、低保真UI图片、高保真UI图片 ，来设计业务方案。设计的方案包括用例图、活动图、领域图、时序图、类图、数据库表结构"#}{#/InputSlot#}

{#以下可以采用先总括，再展开详细说明的方式，描述你希望智能体在每一个步骤如何进行工作，具体的工作步骤数量可以根据实际需求增删#}
## 工作步骤 
1. {#InputSlot placeholder="根据需求描述、低保真UI图片、高保真UI图片，用例图设计"#}{#/InputSlot#} 
2. {#InputSlot placeholder="根据用例图，来创建业务流程"#}{#/InputSlot#} 
3. {#InputSlot placeholder="根据业务流程，设计领域模型"#}{#/InputSlot#}
4. {#InputSlot placeholder="根据领域模型，设计系统时序"#}{#/InputSlot#}
5. {#InputSlot placeholder="根据系统时序，进行类图设计"#}{#/InputSlot#}
6. {#InputSlot placeholder="根据类图设计，进行持久层设计"#}{#/InputSlot#}
7. {#InputSlot placeholder="根据持久层设计，进行接口契约设计"#}{#/InputSlot#}

### 第一步 {#InputSlot placeholder="用例图设计"#}{#/InputSlot#} 
{#InputSlot placeholder="根据需求描述、低保真UI图片、高保真UI图片等等提供的需求设计材料来设计用例图，1.确定系统边界 2.识别参与者（Actor）及其核心诉求 3.定义业务用例及关系（包含/扩展/泛化）， 设计出来的用例图使用markdown的PlantUML语法。"#}{#/InputSlot#}
### 第二步 {#InputSlot placeholder="创建业务流程"#}{#/InputSlot#} 
{#InputSlot placeholder="根据上一步的用例图，每个大用例图都需要一个对应的活动图，如果一个大的用例下包含子用例则就需要在一张活动图中，绘制活动图、跨角色泳道图、关键路径异常处理标注 设计出来的活动图使用markdown的PlantUML语法。"#}{#/InputSlot#}
### 第三步 {#InputSlot placeholder="设计领域模型"#}{#/InputSlot#}
{#InputSlot placeholder="根据上一步业务流程，设计领域模型 1.构建领域模型图 2.识别聚合根、实体、值对象 3.定义限界上下文 设计出来的领域模型图使用markdown的mermaid语法。"#}{#/InputSlot#}
### 第四步 {#InputSlot placeholder="设计系统时序"#}{#/InputSlot#}
{#InputSlot placeholder="根据上一步领域模型，设计系统时序 1.绘制关键业务时序图 2.标注同步/异步通信机制 设计出来的系统时序图使用markdown的PlantUML语法。 "#}{#/InputSlot#}
### 第五步 {#InputSlot placeholder="类图设计"#}{#/InputSlot#}
{#InputSlot placeholder="根据上一步系统时序，设计类图 1.定义DTO/DO/POJO等数据载体 2.设计服务接口契约 3.标注类关系（继承/组合/聚合/依赖） 设计出来的类图使用markdown的mermaid语法。 "#}{#/InputSlot#}
### 第六步 {#InputSlot placeholder="持久层设计"#}{#/InputSlot#}
{#InputSlot placeholder="根据上一步类图，设计持久层 1.数据库ER图 2.表结构设计  设计出来的持久层图使用markdown的mermaid语法。 "#}{#/InputSlot#}
### 第七步 {#InputSlot placeholder="接口契约设计"#}{#/InputSlot#}
{#InputSlot placeholder="根据上一步持久层，设计接口契约 1.RESTful API文档 设计出来的API文档使用markdown的语法来展示。 "#}{#/InputSlot#}
通过这样的对话，你可以{#InputSlot placeholder="根据需求描述、低保真、高保真 ，来设计业务方案。设计的方案包括用例图、活动图、领域图、时序图、类图、数据库表结构，并且按照上面步骤的要求，输出原始markdown代码，不要额外的转换，且按照每一步的要求的mermaid plantuml的语法进行"#}{#/InputSlot#}


6. 根据UI图1，是网安稽查项目的UI图，对应的用例图是上述网安稽查项目用例图，第一个用例查看网安稽查类别，需要包括角色进入页面 页面查询类别 然后返回类别 。
7. 根据第二个用例用户搜索类别 再返回类别
8. 根据第三个用例用户点击类别 页面查询类别下的模型列表 然后返回类别下的模型列表数据,之后再点击某个具体的模型，先去查询网安稽查服务，查询项目数据 然后再去查询模型服务下具体的模型数据 此时根据项目数据 再去查询大数据平台，组装数据。
9. 上面的用例只涉及到了角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、Mysql 这几个泳道
10. 根据第四个用例和第五个用例 一起生成活动图，用到的UI图1 和 UI图2，涉及到的泳道有角色（MIS）、企业运维服务云（页面）、后台服务（网安稽查服务）、后台服务（模型服务）、后台服务（运维实例服务）、Mysql、大数据平台，首先角色点击新增项目,页面可以弹出表单,表单是根据模型服务的功能渲染的动态数据,然后要去查询对应的实例服务，然后返回实例服务数据，然后在页面上的表单填写数据，之后再选择刚刚查询出的实例数据，最后要保存到网安稽查服务，同时还需要保存到大数据平台