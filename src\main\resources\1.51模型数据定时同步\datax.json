{"job": {"setting": {"speed": {"channel": 1}, "errorLimit": {"record": 0, "percentage": 0}}, "content": [{"reader": {"name": "mysqlreader", "parameter": {"username": "aiops", "password": "Bysdba@453", "column": ["deviceName", "aiopsItem", "eid"], "connection": [{"querySql": ["select * from ((SELECT aggregated_top_devices.aggregated_device_names AS deviceName, '100' AS aiopsItem, eid FROM (SELECT top_10_individual_devices.eid, GROUP_CONCAT(DISTINCT top_10_individual_devices.deviceName ORDER BY top_10_individual_devices.deviceName SEPARATOR ',') AS aggregated_device_names, MAX(top_10_individual_devices.latest_device_version) AS max_version_within_eid_from_top_10 FROM (SELECT ad.eid, ad.deviceName, MAX(adcd.__version__) AS latest_device_version FROM aiops_device_collect_detail adcd JOIN aiops_device_collect adc ON adcd.adcId = adc.id JOIN aiops_device ad ON ad.deviceId = adc.deviceId WHERE adcd.accId IN (102000000002003) AND adcd.isEnable = TRUE AND ad.deviceName IS NOT NULL GROUP BY ad.eid, ad.deviceName ORDER BY latest_device_version DESC) AS top_10_individual_devices GROUP BY top_10_individual_devices.eid) AS aggregated_top_devices ORDER BY aggregated_top_devices.max_version_within_eid_from_top_10 DESC) union all (SELECT aggregated_top_devices.aggregated_device_names AS deviceName, '08' AS aiopsItem, eid FROM ( SELECT top_10_individual_devices.eid, GROUP_CONCAT(DISTINCT top_10_individual_devices.deviceName ORDER BY top_10_individual_devices.deviceName SEPARATOR ',') AS aggregated_device_names, MAX(top_10_individual_devices.latest_device_version) AS max_version_within_eid_from_top_10 FROM ( SELECT ad.eid, ad.deviceName, MAX(adcd.__version__) AS latest_device_version FROM aiops_device_collect_detail adcd JOIN aiops_device_collect adc ON adcd.adcId = adc.id JOIN aiops_device ad ON ad.deviceId = adc.deviceId WHERE adcd.accId IN (550562617242176) AND adcd.isEnable = TRUE AND ad.deviceName IS NOT NULL GROUP BY ad.eid, ad.deviceName ORDER BY latest_device_version DESC ) AS top_10_individual_devices GROUP BY top_10_individual_devices.eid ) AS aggregated_top_devices ORDER BY aggregated_top_devices.max_version_within_eid_from_top_10 DESC)) as aa\n"], "jdbcUrl": ["****************************************"]}]}}, "writer": {"name": "mysqlwriter", "parameter": {"username": "root", "password": "digiwin@123", "column": ["deviceName", "aiopsItem", "eid"], "connection": [{"table": ["NetworkSecurityExamInfoSystemDeploymentLocation"], "jdbcUrl": "***************************************************************************************************"}]}}}]}}