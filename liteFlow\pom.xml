<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.example</groupId>
	<artifactId>liteFlow</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>liteFlow</name>
	<description>liteFlow</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>17</java.version>
		<liteflow.version>2.12.4</liteflow.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>3.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter-test</artifactId>
			<version>3.0.3</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-spring-boot-starter</artifactId>
			<version>${liteflow.version}</version>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-script-groovy</artifactId>
			<version>${liteflow.version}</version>
		</dependency>

		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-script-java</artifactId>
			<version>${liteflow.version}</version>
		</dependency>

		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-script-aviator</artifactId>
			<version>${liteflow.version}</version>
		</dependency>
		<dependency>
			<groupId>com.yomahub</groupId>
			<artifactId>liteflow-rule-sql</artifactId>
			<version>${liteflow.version}</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.yomahub/liteflow-script-common -->
<!--		<dependency>-->
<!--			<groupId>com.yomahub</groupId>-->
<!--			<artifactId>liteflow-script-common</artifactId>-->
<!--			<version>2.8.5</version>-->
<!--		</dependency>-->

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.graalvm.buildtools</groupId>
				<artifactId>native-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
