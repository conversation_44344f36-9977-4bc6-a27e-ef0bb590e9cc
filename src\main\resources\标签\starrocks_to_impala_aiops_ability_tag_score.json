{"job": {"content": [{"writer": {"parameter": {"partitionEnabled": false, "writeMode": "truncate", "fieldDelimiter": "\t", "column": [{"type": "STRING", "name": "eid"}, {"type": "DOUBLE", "name": "OrderDeliveryScore"}, {"type": "DOUBLE", "name": "BasicAbilityScore"}, {"type": "DOUBLE", "name": "ProductionAbilityScore"}, {"type": "DOUBLE", "name": "MaterialAbilityScore"}, {"type": "DOUBLE", "name": "MonthlyAbilityScore"}, {"type": "DOUBLE", "name": "InventoryAbilityScore"}], "path": "/user/hive/warehouse/tbb.db/aiops_ability_tag_score", "fileType": "text", "defaultFS": "hdfs://ddp1:8020", "fileName": "aiops_ability_tag_score"}, "name": "hdfswriter"}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["SELECT id AS eid, MAX(CASE WHEN tagId = '754698248249920' THEN score ELSE 0 END) AS BasicAbilityScore, MAX(CASE WHEN tagId = '754699437040192' THEN score ELSE 0 END) AS OrderDeliveryScore, MAX(CASE WHEN tagId = '758200593494592' THEN score ELSE 0 END) AS ProductionAbilityScore, MAX(CASE WHEN tagId = '758211379151424' THEN score ELSE 0 END) AS MaterialAbilityScore, MAX(CASE WHEN tagId = '758236385014336' THEN score ELSE 0 END) AS MonthlyAbilityScore, MAX(CASE WHEN tagId = '758245165445696' THEN score ELSE 0 END) AS InventoryAbilityScore FROM AIEOM.tenant_tag_score WHERE tagId IN ( '754698248249920', '754699437040192', '758200593494592', '758211379151424', '758236385014336', '758245165445696' ) GROUP BY id "], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}