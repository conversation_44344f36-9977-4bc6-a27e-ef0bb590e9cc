beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_39_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Safe_stock_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NumberOfPensSet') AS INT),CAST(get_json_object(model,'$.DataContent.NumOfNeedtoSet') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SafeStockSetRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_16_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.IndicatorNumber') = 'B2.16.0' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Procure_change_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Change_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProcureChangeRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_35_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
ELSE get_json_object(model,'$.DataContent.Product_Line')
END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(38,6)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(38,6)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProcureChangeCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_54_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.purchase_batch_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NumberOfPensSet') AS INT),CAST(get_json_object(model,'$.DataContent.NumOfNeedtoSet') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PurchaseBatchRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_15_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Manufacturer_number'),get_json_object(model,'$.DataContent.Manufacturer_name'),CAST(get_json_object(model,'$.DataContent.Non_performing_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NumberofDefects') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_delivery_quantity') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  ProcurementQualityRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_34_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),CAST(get_json_object(model,'$.DataContent.Average_level') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  ProductAverageLevel"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_5_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Cost_fluctuations_proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.cost_fluctuations_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  CostFluctuationsProportion"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_18_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.missing_transaction_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MissingTransactionNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_53_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Type'),CAST(get_json_object(model,'$.DataContent.Proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.TransactionNum') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  InventoryStrategySet"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_31_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Change_num') AS INT),CAST(get_json_object(model,'$.DataContent.total_change_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderChangeCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_2_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.RateOfAudit') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderReviewRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_30_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderReviewCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_45_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Order_to_PlanCycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),CAST(get_json_object(model,'$.DataContent.order_to_plan_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OrderToPlanCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_41_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.blank_invoice_proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.blank_invoice_num') AS INT),CAST(get_json_object(model,'$.DataContent.Number_of_statements') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  BlankInvoiceProportion"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_37_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.StartDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.StartDate')
  END AS TIMESTAMP),CAST(CASE
    WHEN get_json_object(model, '$.DataContent.Product_Line') = 'T100' OR get_json_object(model, '$.DataContent.Product_Line') = 'TOPGP' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-01')
    WHEN get_json_object(model, '$.DataContent.Product_Line') = '易飞' THEN CONCAT(
      SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 1, 4), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 5, 2), '-', SUBSTRING(get_json_object(model, '$.DataContent.ExprityDate'), 7, 2))
    ELSE get_json_object(model, '$.DataContent.ExprityDate')
  END AS TIMESTAMP),get_json_object(model,'$.DataContent.Collection_condition_num'),get_json_object(model,'$.DataContent.Collection_condition'),CAST(get_json_object(model,'$.DataContent.Payment_terms_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.payment_terms_bishu') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp') from  PaymentTermsProportion"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_26_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.work_order_change_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Timely_transaction_count') AS INT),CAST(get_json_object(model,'$.DataContent.Change_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  WorkOrderChangeRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_40_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  WorkChangeReviewCycle"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_14_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.work_order_delay_num') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  WorkOrderDelayNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_19_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.incomplete_condition_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.incomplete_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  IncompleteSupplyCondition"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_26_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.incomplete_condition_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.incomplete_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  IncompleteCustomerCondition"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_6_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Item'),get_json_object(model,'$.DataContent.Product_name'),CAST(get_json_object(model,'$.DataContent.Cost_proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.CostVarianceAmount') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.current_average_cost') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.pre_average_cost') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  TOP10DiffInCost"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_17_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Deletion_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Missing_transactions') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_items') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MissingRateOfMaterialMachine"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_4_4_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.profit_and_loss_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.profit_and_loss_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProfitAndLossRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_4_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OtherPayableRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_1_6_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Module'),get_json_object(model,'$.DataContent.Doc_code'),CAST(get_json_object(model,'$.DataContent.Total_number_of_doc') AS INT),CAST(get_json_object(model,'$.DataContent.Supplementary_orders') AS INT),CAST(get_json_object(model,'$.DataContent.make_up_order_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Replenishment_time') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Document_review_time') AS DECIMAL(15, 2)),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OtherAccountReceiveRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_55_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.product_batch_set_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NumberOfPensSet') AS INT),CAST(get_json_object(model,'$.DataContent.NumOfNeedtoSet') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductBatchSetRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_7_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Procurement_delivery_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Zhunjiao_Bishu') AS INT),CAST(get_json_object(model,'$.DataContent.outsource_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OutsourceProcureRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_14_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Purchase_quality_defect_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NumberofDefects') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Received_quantity') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OutsourcingQualityRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_24_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.account_modify_num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsPayableModifyNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_25_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.credit_and_debit') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.accounts_pay_amount') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AccountsPayModifyRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_5_30_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Proportion') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.debit_and_credit') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.accounts_receive_amount') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ReceiveAccountsRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_2_38_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.plan_order_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.plan_order_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PlanAssociatedOrderRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_1 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),get_json_object(model,'$.DataContent.productCode'),get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.qualified_rate_workcenter') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.qualified_qty') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_qty') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  QualifiedRateOfWorkcenterMo"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_2 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.OE_AMT_RATE') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  InactiveStockAmtRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_3 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.MK_RATE') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  MoMaterialCompletionRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_0 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Purchase_Overentry_Rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Procurement_Overentry_Num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_of_purchases') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PurchaseOverentryRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_10 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Production_OverConsumption_Rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.ISSUED_QTY') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.REQUIRED_QTY') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ProductionOverConsumptionRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_11 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Deletion_rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Missing_transactions') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_items') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  UniversalFactorDeletionRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_12 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.qualified_rate_supplier') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.qualified_qty') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_qty') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  QualifiedRateOfSupplierMO"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_14 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Audit_cycle') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),CAST(get_json_object(model,'$.DataContent.Total_audit_time') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AverageSalesApprovalPeriod"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_15 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Overdelivery_Rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Overdelivery_Num_Order') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_orders') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SalesOverDeliveryRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_16 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Items_Num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SummaryOfSalesReturnItems"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_9 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Average_Planning_Period') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.order_to_mo_date') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  AveragePlanningPeriod"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_17 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Negative_Inv_Summay_Rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Negative_num') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  NegativeInvSummaryRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_4 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Overdelivery_Rate_Mo') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Overdelivery_Num_Mo') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  OverdeliveryRateOfMo"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_5 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.RateOfNotFinished') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.NotFinished_number_mo') AS INT),CAST(get_json_object(model,'$.DataContent.Total_number_work') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  NotFinishedMO"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_13 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Items_Num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  SummaryOfSalesIssueItems"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_6 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),CAST(get_json_object(model,'$.DataContent.ActivedEndproduct_Num') AS INT),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ActivedEndproductNum"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_7 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.OverPurchaseRate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.OverPurchase') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.PurchaseQty') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  ExcessivePurchaseRate"
beeline -d "com.cloudera.impala.jdbc41.Driver" -u "jdbc:impala://${impala-addr_894}" -n service_cloud -e "INSERT overwrite  table tbb.dws_aiops_pec_erpindex_6_1_8 SELECT key,get_json_object(model,'$.DataContent.source_db_id'),CASE
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'TOPGP' THEN '06'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'T100' THEN '100'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = '易飞' THEN '08'
        WHEN get_json_object(model,'$.DataContent.Product_Line') = 'E10' THEN '37'
        ELSE get_json_object(model,'$.DataContent.Product_Line')
    END AS Product_Line,get_json_object(model,'$.DataContent.IndicatorNumber'),CAST(get_json_object(model,'$.DataContent.enterpriseCode') AS INT),get_json_object(model,'$.DataContent.Account_Set'),get_json_object(model,'$.DataContent.Account_set_name'),get_json_object(model,'$.DataContent.Year'),get_json_object(model,'$.DataContent.Month'),CAST(get_json_object(model,'$.DataContent.Purchase_Qualified_Rate') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Purchase_qualified_qty') AS DECIMAL(15, 2)),CAST(get_json_object(model,'$.DataContent.Purchase_Total_qty') AS DECIMAL(15, 2)),get_json_object(model,'$.BasicInfo.deviceId'),get_json_object(model,'$.BasicInfo.eid'),get_json_object(model,'$.BasicInfo.collectedTime'),get_json_object(model,'$.BasicInfo.collectConfigId'),get_json_object(model,'$.BasicInfo.uploadDataModelCode'),get_json_object(model,'$.BasicInfo.deviceCollectDetailId'),get_json_object(model,'$.BasicInfo.aiId'),get_json_object(model,'$.BasicInfo.aiopsItem'),get_json_object(model,'$.BasicInfo.flumeTimestamp'),cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp ) from  PurchaseQualifiedRate"
