package com.digiwin.escloud.aiocdp.group.service.impl;

import com.digiwin.escloud.aiocdp.group.entity.AioCdpGroupAdvanceAnalysisRule;
import com.digiwin.escloud.aiocdp.group.mapper.AioCdpGroupAdvanceAnalysisRuleMapper;
import com.digiwin.escloud.aiocdp.group.service.IAioCdpGroupAdvanceAnalysisRuleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 进阶规则分析规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Service
public class AioCdpGroupAdvanceAnalysisRuleServiceImpl extends ServiceImpl<AioCdpGroupAdvanceAnalysisRuleMapper, AioCdpGroupAdvanceAnalysisRule> implements IAioCdpGroupAdvanceAnalysisRuleService {

}
