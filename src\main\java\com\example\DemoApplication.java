package com.example;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;

import static org.springframework.http.HttpHeaders.ACCEPT_LANGUAGE;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})    // 禁用数据源自动配置
@MapperScan("com.example.dao")
@RestController
public class DemoApplication {

    // hive -f queries.sql --silent > output.txt
    // select concat('{"BankAccounts":',model,'}') from BankAccounts limit 1;
    static String aa = "RateOfOrderChange";

    static String bb = "2.3.0";
    private Map<String, String> parse() {
        Map<String, String> retMap = new HashMap<>();
        String[] split = aa.split("\n");
        String[] ali = bb.split("\n");
        for (int i = 0; i < split.length; i++) {
            retMap.put(split[i], ali[i].replace(".", "_"));
        }
        return retMap;
    }

    static final String fileName = "D:\\work\\demo\\src\\main\\resources\\tbb\\1.txt";

    public static void main(String[] args) {
        SpringApplication.run(DemoApplication.class, args);

    }



    static Map<String, String> aliMap = new HashMap<>();

//    @Resource
//    private JdbcTemplate jdbcTemplate;

//    @GetMapping("/test7")
//    public String test7(@RequestParam(value = "local") int local) {
//        Map<String, Map<String, Object>> ret = new LinkedHashMap<>();
//        Map<String, Map<String, Object>> ret2 = new LinkedHashMap<>();
//        aliMap = parse();
//
//        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
//            String line;
//
//            while ((line = reader.readLine()) != null) {
//                parseJson(line, ret, ret2);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        Map<String, String> filed;
//        if (local == 0) {
//            List<Map<String, Object>> maps = jdbcTemplate.queryForList("select fieldCode,fieldType from cmdb_field", new Object[]{});
//            filed = maps.stream()
//                    .filter(map -> Objects.nonNull(map.get("fieldCode")))
//                    .collect(Collectors.toMap(map -> map.get("fieldCode").toString(), map -> map.get("fieldType").toString()));
//        } else {
//            filed = readCmdbField();
//        }
//
//        StringBuilder sb = new StringBuilder();
//
//        for (Map.Entry<String, Map<String, Object>> entry : ret.entrySet()) {
//            sb.append("CREATE TABLE IF NOT EXISTS ");
//
//            String tableName = "tbb.dws_aiops_pec_erpindex_" + aliMap.get(entry.getKey()).toLowerCase();
//            sb.append(tableName);
//            System.out.println( tableName);
//            sb.append("(\n  key STRING,");
//
//            boolean isDate = false;
//            for (Map.Entry<String, Object> e : entry.getValue().entrySet()) {
//                if (e.getKey().contains("Year")) {
//                    isDate = true;
//                }
//                sb.append(e.getKey()).append(" ");
//
//                String fieldType = filed.getOrDefault(e.getKey(), "string");
//                if (fieldType.contains("BIGINT")) {
//                    sb.append("BIGINT");
//                } else if (fieldType.contains("VARCHAR")) {
//                    sb.append("STRING");
//                } else if (fieldType.contains("DECIMAL")) {
//                    sb.append("DECIMAL(15, 2)");
//                } else if (fieldType.equals("DATE")) {
//                    sb.append("TIMESTAMP");
//                } else {
//                    sb.append(fieldType);
//                }
//
//                sb.append(",");
//            }
//
//            if (isDate) {
//                sb.append(" yearAndMonth TIMESTAMP");
//
//            }
//            if (sb.length() > 0 && sb.charAt(sb.length() - 1) == ',') {
//                sb.deleteCharAt(sb.length() - 1); // 移除最后一位逗号
//            }
//            sb.append(")");
//            sb.append("STORED AS PARQUET;\n");
//        }
//
//        return sb.toString();
//    }

//    @GetMapping("/test8")
//    public String test8(@RequestParam(value = "local") int local) {
//
//        Map<String, Map<String, Object>> ret = new LinkedHashMap<>();
//        Map<String, Map<String, Object>> ret2 = new LinkedHashMap();
//
//        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
//            String line;
//
//            while ((line = reader.readLine()) != null) {
//                parseJson(line, ret, ret2);
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        List<Map<String, Object>> maps = jdbcTemplate.queryForList("select fieldCode,fieldType from cmdb_field", new Object[]{});
//        Map<String, String> filed = (local == 0) ? maps.stream()
//                .filter(map -> Objects.nonNull(map.get("fieldCode")))
//                .collect(Collectors.toMap(map -> map.get("fieldCode").toString(), map -> map.get("fieldType").toString()))
//                : readCmdbField();
//
//        StringBuilder sb = new StringBuilder();
//        for (Map.Entry<String, Map<String, Object>> entry : ret2.entrySet()) {
//
//            sb.append("beeline -d \"com.cloudera.impala.jdbc41.Driver\" -u \"************************\" -n service_cloud -e \"");
//            sb.append("INSERT overwrite  table ");
//            String tableName = "tbb.dws_aiops_pec_erpindex_" + aliMap.get(entry.getKey());
//            tableName = tableName.toLowerCase();
//            sb.append(tableName);
//            sb.append(" SELECT key,");
//            int i = 0;
//            boolean isDate = false;
//            for (Map.Entry<String, Object> e : entry.getValue().entrySet()) {
//                String substring = e.getKey().substring(e.getKey().lastIndexOf(".") + 1);
//                if (Objects.nonNull(filed.get(substring))) {
//                    if (e.getKey().contains("Year")) {
//                        isDate = true;
//                    }
//                    if (filed.get(substring).contains("BIGINT")) {
//                        sb.append("CAST(get_json_object(model,'$.").append(e.getKey()).append("') AS BIGINT)");
//                    } else if (filed.get(substring).equals("INT")) {
//                        sb.append("CAST(get_json_object(model,'$.").append(e.getKey()).append("') AS INT)");
//                    } else if (substring.equals("ExprityDate")) {
//                        String productLine = "get_json_object(model, '$.DataContent.Product_Line')";
//                        String exprityDate = "get_json_object(model, '$.DataContent.ExprityDate')";
//
//                        sb.append("CAST(CASE\n");
//                        sb.append("    WHEN ").append(productLine).append(" = 'T100' OR ").append(productLine).append(" = 'TOPGP' THEN CONCAT(\n");
//                        sb.append("      SUBSTRING(").append(exprityDate).append(", 1, 4), '-', SUBSTRING(").append(exprityDate).append(", 5, 2), '-01')\n");
//                        sb.append("    WHEN ").append(productLine).append(" = '易飞' THEN CONCAT(\n");
//                        sb.append("      SUBSTRING(").append(exprityDate).append(", 1, 4), '-', SUBSTRING(").append(exprityDate).append(", 5, 2), '-', SUBSTRING(").append(exprityDate).append(", 7, 2))\n");
//                        sb.append("    ELSE ").append(exprityDate).append("\n");
//                        sb.append("  END AS TIMESTAMP)");
//                    } else if (substring.equals("StartDate")) {
//                        String productLine = "get_json_object(model, '$.DataContent.Product_Line')";
//                        String startDate = "get_json_object(model, '$.DataContent.StartDate')";
//
//                        sb.append("CAST(CASE\n");
//                        sb.append("    WHEN ").append(productLine).append(" = 'T100' OR ").append(productLine).append(" = 'TOPGP' THEN CONCAT(\n");
//                        sb.append("      SUBSTRING(").append(startDate).append(", 1, 4), '-', SUBSTRING(").append(startDate).append(", 5, 2), '-01')\n");
//                        sb.append("    WHEN ").append(productLine).append(" = '易飞' THEN CONCAT(\n");
//                        sb.append("      SUBSTRING(").append(startDate).append(", 1, 4), '-', SUBSTRING(").append(startDate).append(", 5, 2), '-', SUBSTRING(").append(startDate).append(", 7, 2))\n");
//                        sb.append("    ELSE ").append(startDate).append("\n");
//                        sb.append("  END AS TIMESTAMP)");
//                    } else if (filed.get(substring).contains("DECIMAL")) {
//                        sb.append("CAST(get_json_object(model,'$.").append(e.getKey()).append("') AS DECIMAL(15, 2))");
//                    } else if (filed.get(substring).equals("DATE")) {
//                        sb.append("CAST(get_json_object(model,'$.").append(e.getKey()).append("') AS timestamp)");
//                    } else {
//                        sb.append("get_json_object(model,'$.").append(e.getKey()).append("')");
//                    }
//                } else {
//                    sb.append("get_json_object(model,'$.").append(e.getKey()).append("')");
//                }
//
//                if (i != entry.getValue().entrySet().size() - 1) {
//                    sb.append(",");
//                }
//                i++;
//            }
//            if (isDate) {
//                sb.append(",cast(concat(get_json_object(model,'$.DataContent.Year'),'-',get_json_object(model,'$.DataContent.Month'),'-01') AS timestamp )");
//            }
//            sb.append(" from ");
//            sb.append(" ");
//            sb.append(entry.getKey());
//            sb.append("\"");
//            sb.append("\n");
//        }
//        return sb.toString();
//    }

    @GetMapping("test9")
    public void test9(){
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(ACCEPT_LANGUAGE,"en-US,en;q=0.9");
        Map<String, Object> paramMap = new HashMap<>();

        String jsonParam = JSONObject.toJSONString("");
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(paramMap, headers);

        Map forObject = restTemplate.postForObject("http://192.168.10.219:9999", request, HashMap.class);
    }


    private static void parseJson(String jsonData, Map<String, Map<String, Object>> ret, Map<String, Map<String, Object>> ret2) {


        // Parse JSON

        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(jsonData, JsonObject.class);
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            Map<String, Object> map = new LinkedHashMap<>();
            Map<String, Object> map2 = new LinkedHashMap<>();
            JsonObject asJsonObject = entry.getValue().getAsJsonObject();
            JsonObject dataContent = asJsonObject.getAsJsonObject("DataContent");
            JsonObject basicInfo = asJsonObject.getAsJsonObject("BasicInfo");
            convertToMap(dataContent, map, map2, "DataContent");
            convertToMap(basicInfo, map, map2, "BasicInfo");
            ret.put(entry.getKey(), map);
            ret2.put(entry.getKey(), map2);


        }


    }

    private static void convertToMap(JsonObject jsonObject, Map<String, Object> map, Map<String, Object> map2, String type) {

        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            map.put(key, value);
            map2.put(type + "." + key, value);
        }


    }


    private Map<String, String> readCmdbField() {
        Map<String, String> filed = new HashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader("D:\\work\\demo\\src\\main\\resources\\tbb\\taiwan-prd-cmdb-field.csv"))) {
            String line;

            while ((line = reader.readLine()) != null) {
                String[] split = line.split(",");
                filed.put(split[0], split[1]);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return filed;
    }


}
