package com.example.mybatis.config;

import com.example.mybatis.model.EsCustomerServiceV41;
import com.example.mybatis.model.GrpBusDptMapping;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class BaseService {
    @Resource
    private MybatisService mybatisService;

    public void saveV3(){
        List<GrpBusDptMapping> grpBusDptMappings = mybatisService.selectV3Es();
        Set<GrpBusDptMapping> set = new HashSet<>(grpBusDptMappings);
        mybatisService.insertV3Batch(set);
    }

    public void saveV4(){
        List<EsCustomerServiceV41> esCustomerServiceV41s = mybatisService.selectEs();
        Date date = new Date();
        esCustomerServiceV41s.forEach(es->{
            es.setUpdateDate(date);
        });
//        Set<EsCustomerServiceV41> esCustomerServiceV41s1 = new HashSet<>(esCustomerServiceV41s);
//        mybatisService.insertBatch(esCustomerServiceV41s1);
    }
}
