package com.example.mybatis.utils;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;

/**
 * Des加解密实用类
 */
public class DesCryptUtils {
    private final static String DIGIWIN_KEY = "digiwin_aiopskit";
    private final static Map<String, DesCryptUtils> _instanceMap = new HashMap<>();

    private Map<Integer, Cipher> cipherMap;

    /**
     * 获取默认键单例实例
     * @return Des加解密实用类
     * @throws Exception 异常对象
     */
    public static DesCryptUtils getInstance() throws Exception {
        return getInstance(DIGIWIN_KEY);
    }

    /**
     * 获取特定键单例实例
     * @param key 特定键
     * @return Des加解密实用类
     * @throws Exception 异常对象
     */
    public static DesCryptUtils getInstance(String key) throws Exception {
        DesCryptUtils instance = _instanceMap.get(key);
        if (instance == null) {
            instance = new DesCryptUtils(key);
            _instanceMap.put(key, instance);
        }
        return instance;
    }

    public static DesCryptUtils getAsyncInstance() throws Exception {
        return new DesCryptUtils(DIGIWIN_KEY);
    }

    /**
     * 特定密钥构造器(不公开)
     * @param key 密钥
     * @throws Exception 异常对象
     */
    private DesCryptUtils(String key) throws Exception {
        byte[] md5ByteValue = Md5Utils.getMd5ByBytes(key);
        if (md5ByteValue == null) {
            //输出的异常讯息可能会显示到前端，因此内容不写得很明确
            throw new Exception("not get Md5 value");
        }
        cipherMap = new HashMap<>(4);
        // 获取密钥并初始化
        byte[] arrTempByteArray = get8Bytes(md5ByteValue);
        java.security.Key objKey = new SecretKeySpec(arrTempByteArray, "DES");
        IvParameterSpec objIv = new IvParameterSpec(arrTempByteArray);
        Cipher objCipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        initAndPutCipherMap(objCipher, Cipher.ENCRYPT_MODE, objKey, objIv);
        objCipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        initAndPutCipherMap(objCipher, Cipher.DECRYPT_MODE, objKey, objIv);
    }

    private byte[] get8Bytes(byte[] strPrivateKey){
        //从指定的字串制成密钥，密钥所需的字元阵列长度为8位，不足及超过都要处理
        byte[] arrTempByteArray = new byte[8];
        // 将原始字元阵列转换为8位
        for (int i = 0; i < strPrivateKey.length && i < arrTempByteArray.length; i++) {
            arrTempByteArray[i] = strPrivateKey[i];
        }
        return arrTempByteArray;
    }

    private void initAndPutCipherMap(Cipher objCipher, int mode, java.security.Key objKey, IvParameterSpec objIv)
            throws Exception {
        objCipher.init(mode, objKey, objIv);
        cipherMap.put(mode, objCipher);
    }

    private String byte2Hex(byte[] bytes) {
        return Hex.encodeHexString(bytes);
    }

    private byte[] hex2Byte(String hexString) throws DecoderException {
        return Hex.decodeHex(hexString);
    }

    /**
     * 加密字串
     * @param byteArray byte数组
     * @return 加密后的byte数组
     * @throws Exception 异常对象
     */
    private byte[] doEncrypt(byte[] byteArray) throws Exception {
        Cipher cipher = cipherMap.get(Cipher.ENCRYPT_MODE);
        if (cipher == null) {
            throw new RuntimeException("doEncrypt error by cipher is null");
        }
        return cipher.doFinal(byteArray);
    }

    /**
     * 加密字串
     * @param encryptString 欲加密的字串
     * @return 加密后的结果字串
     * @throws Exception 异常对象
     */
    public synchronized String encrypt(String encryptString) throws Exception {
        return byte2Hex(doEncrypt(encryptString.getBytes()));
    }

    /**
     * 解密字串
     * @param byteArray byte数组
     * @return 解密后的byte数组
     * @throws Exception 异常对象
     */
    private byte[] doDecrypt(byte[] byteArray) throws Exception {
        Cipher cipher = cipherMap.get(Cipher.DECRYPT_MODE);
        if (cipher == null) {
            throw new RuntimeException("doDecrypt error by cipher is null");
        }
        return cipher.doFinal(byteArray);
    }

    /**
     * 解密字串
     * @param decryptString 欲解密的字串
     * @return 解密后的结果字串
     * @throws Exception 异常对象
     */
    public synchronized String decrypt(String decryptString) throws Exception {
        return new String(doDecrypt(hex2Byte(decryptString)));
    }

    public static void main(String[] args) throws Exception {
        String desCryptUtils = DesCryptUtils.getInstance().decrypt("686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503d7f99a35d236f61c59ca267614ff4db8cf413c44dd590c61273e1d2f673e66b4abb16e97f1ac176f572ad93ce5414968a853a315bd1ca1baabada0e3e3e3db18fd90686ae8ab9e37f01116c43f86e6d53d3594a809adef9d09a7cf9572758215c370d94ac56b574e434552b9093cafa094f7cb4cb391068f7a87f467dbcd277b0c709f2b5f827b89fe0c9a290bd8faa34b0369b4d1b953580c7b678532c8ed15cd5f0167d9dd22a49978fecf233db7249156f9efa72581de32b8169f8872b43d4a11ad7eb7f45ccc0b6b63890e6e383c47a891445c4816dffa3968572f028740355fdfe6e89e8a8ec33293512b5f6739998ea0d9aa830755504fa2cb614140549e5d4123c1766c965f1b12182848923cde1e0cd8ee94640d4b861fc1fdab314c2d6386be840abc9d");
        System.out.println(desCryptUtils);
    }
}
