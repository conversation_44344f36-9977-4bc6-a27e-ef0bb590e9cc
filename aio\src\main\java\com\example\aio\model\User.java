package com.example.aio.model;

public class User {
    private String id;
    private String name;
    private String email;
    private String telephone;
    private String wechat;
    private String password;
    private boolean enterprise;
    private String tenantId;
    private String tenantName;

    private String potentialCustomerId; //类型：String  必有字段  备注：潜客代号

    private String contacts;  //类型：String  可有字段  备注：联络人
    private String address;  //类型：String  可有字段  备注：地址
    private String tenantEmail; //类型：String  可有字段  备注：租户邮箱
    private String tenantTelephone; //类型：String  可有字段  备注：租户电话

    public String getPotentialCustomerId() {
        return potentialCustomerId;
    }

    public void setPotentialCustomerId(String potentialCustomerId) {
        this.potentialCustomerId = potentialCustomerId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isEnterprise() {
        return enterprise;
    }

    public void setEnterprise(boolean enterprise) {
        this.enterprise = enterprise;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTenantEmail() {
        return tenantEmail;
    }

    public void setTenantEmail(String tenantEmail) {
        this.tenantEmail = tenantEmail;
    }

    public String getTenantTelephone() {
        return tenantTelephone;
    }

    public void setTenantTelephone(String tenantTelephone) {
        this.tenantTelephone = tenantTelephone;
    }
}
