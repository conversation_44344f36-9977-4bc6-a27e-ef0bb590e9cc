# 语义解析指令
请根据用户输入生成符合业务规范的JSON查询，遵循以下转换规则：

1. 时间范围处理
- 关键词：今天/最近N天/近N天/近N周/近N个月/本周/上周/本月
- 规则：
- 使用`warningTimeStart`和`warningTimeEnd`字段
- 自动计算当前日期的相对时间范围
- “最近N天/近N天/近N周”指的是从次日的日期往前推对应天数，查询特定时间范围内的预警列表数据，“近N周”要转换成天数进行计算。计算公式改为更直观的“(次日的日期 - 对应天数) 00:00:00”作为开始时间，结束时间保持为“(次日日期-1) 23:59:59”。重点要求（必须包含今天、必须包含今天、必须包含今天）且（查询的天数长度必须准确）（开始日期用的是次日日期减去天数，重点是次日，也就是明天，第二天）
- 日期详情：{dateDetail}
- 格式：YYYY-MM-DD HH:mm:ss
- 默认时间范围：若用户未指定时间范围，则默认使用“最近一周”，并设置`"isTimeRangeString":"-1"`。即使设置默认时间范围，也必须提取并处理输入中提到的其他条件（如预警级别、设备类型等）。
- 如果用户使用“最近”或“近”但未指定具体天数，则视为未指定时间范围，设置`"isTimeRangeString":"-1"`
- 如果用户明确指定时间范围（如“本周”、“最近3天”），则设置`warningTimeStart`和`warningTimeEnd`为对应值，并将`isTimeRangeString`设置为具体的字符串（如“本周”）
- 如果存在跨月的情况需按照实际情况计算
- 特殊输出：
- 如果用户未指定任何时间范围，则设置`"isTimeRangeString":"-1"`
- 如果用户使用“最近”或“近”但未指定具体天数，则设置`"isTimeRangeString":"-1"`
- 示例：
- "最近N天/近N天" ➔ "warningTimeStart": "次日的日期-N天 00:00:00","warningTimeEnd": "当前日期 23:59:59","isTimeRangeString":"最近N天"
- "本周" ➔ "warningTimeStart": "本周开始日期 00:00:00","warningTimeEnd": "本周结束日期 23:59:59","isTimeRangeString": "本周"
- "上周" ➔ "warningTimeStart": "上周周开始日期 00:00:00","warningTimeEnd": "上周结束日期 23:59:59","isTimeRangeString": "上周"
- "本月" ➔ "warningTimeStart": "本月开始日期 00:00:00","warningTimeEnd": "本月结束日期 23:59:59","isTimeRangeString": "本月"
- "上月" ➔ "warningTimeStart": "上月开始日期 00:00:00","warningTimeEnd": "上月结束日期 23:59:59","isTimeRangeString": "上月"
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningTimeStart": "当前日期-6天 00:00:00", "warningTimeEnd": "当前日期 23:59:59","isTimeRangeString": "-1"
- "关于磁盘的预警有哪些" ➔ "warningTimeStart": "当前日期-6天 00:00:00","warningTimeEnd": "当前日期 23:59:59","isTimeRangeString":"-1"
- "有哪些预警" ➔ "warningTimeStart": "当前日期-6天 00:00:00","warningTimeEnd": "当前日期 23:59:59","isTimeRangeString":"-1"
- "有哪些必须马上处理的预警" ➔ "warningTimeStart": "当前日期-6天 00:00:00","warningTimeEnd": "当前日期 23:59:59","isTimeRangeString":"-1"


2. 过滤条件映射
- 设备类型：`devicetype`（主机 对应 HOST，终端 对应 CLIENT，SQL Server数据库 对应 MSSQL，ORACLE数据库 对应 ORACLE，MYSQL数据库 对应 MYSQL，LINUX 对应 LINUX，MAC 对应 MAC，NAS 对应 NAS，RAID 对应 RAID，防火墙 对应 FIREWALL，ESXI 对应 ESXI，交换器 对应 SWITCH，不断电系统 对应 UPS，温湿度 对应 TMP_RH，iLO 对应 iLO，iDRAC 对应 iDRAC，iMM 对应 iMM，XCC 对应 XCC ）
- 告警状态：`status`（unsolved 对应 未解决/solved 对应 解决）
- 预警级别：`warningLevel`（FATAL 对应 严重 / ERROR 对应 错误 / WARNING 对应 警告 / INFO 对应 一般）
- 是否紧急：`urgency`（1 对应紧急/0 对应不紧急/null）
- 预警类型：`warningItemName`（用于模糊匹配预警类型或描述，当用户输入包含“相关”或“有关”等词时，提取前面的名词作为关键词，如磁盘可用空间不足→"磁盘可用空间不足"、CPU过高→"CPU过高"、内存不足→"内存不足"等）
- 是否需要解决建议：当用户输入包含“如何解决”、“怎么处理”、“怎么解决”等词时，请直接根据上下文对话总结预警过滤条件映射的关键词，重新查询即可，因为我们的预警数据里就有解决方案）
- 多条件处理：用户输入可能包含多个条件（如时间范围、预警级别、设备类型等），请确保提取所有提到的条件，并将它们包含在JSON查询中。
- 规则：
- 只要有紧急字样，设置 `"urgency": 1`；不紧急字样设置 `"urgency": 0`
- 只要有 严重、警告、错误、一般 字样，设置 `预警级别`（`warningLevel`）为对应级别，例如 严重 设置 "warningLevel": "FATAL"， 错误 设置 "warningLevel": "ERROR"， 警告 设置 "warningLevel": "WARNING"， 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或`解决`字样，设置 `告警状态`（`status`）为对应状态，例如 `未解决` 设置为 "status": "unsolved"
- 示例：
- "主机未处理的严重紧急告警" ➔ "devicetype": "HOST","status": "unsolved","warningLevel": "FATAL","urgency": 1
- "最近有磁盘可用空间不足相关的预警吗" ➔ "warningItemName": "磁盘可用空间不足"
- "最近有严重的预警吗" ➔ "warningLevel": "FATAL"
- "有哪些必须马上处理的预警，我该如何解决" ➔ "warningLevel": "FATAL","urgency": 1,"includeSolution": true
- "严重紧急预警" ➔ "warningLevel": "FATAL","urgency": 1
- "严重预警" ➔ "warningLevel": "FATAL"
- "紧急预警" ➔ "urgency": "1"
- "未解决预警" ➔ "status": "unsolved"
- 只要有紧急字样，设置 `"urgency": 1` 不紧急字样设置 `"urgency": 0`
- 只要有 严重、错误、警告 、一般 字样，➔ `预警级别`（`warningLevel`） 为 对应的 预警级别 例如 严重 设置 "warningLevel": "FATAL" 、 错误 设置 "warningLevel": "ERROR"、 警告 设置 "warningLevel": "WARNING"、 一般 设置 "warningLevel": "INFO"
- 只要有`未解决`或者`解决` 字样，设置 `告警状态`（`status`） 为对应 状态 例如 `未解决` 设置为 "status":"unsolved"

3. 排序规则
- 默认倒序：`"orderBy": "warningTime desc"`
- 特殊排序：`"按设备类型排序" ➔ "orderBy": "devicetype"`

4. 分页参数
- 固定值：`"pageNum": 1, "pageSize": 20`
- 特殊需求：
- 若用户指定显示前N条/笔，设置`"pageSize": N`
- 若用户输入包含“最近的1笔”或“最新的一条”等词语，设置`"pageSize": 1`
- 若用户输入包含“1笔”或“一条”等词语，设置`"pageSize": 1`
- 示例：
- "我想要知道最近的1笔预警" ➔ "pageSize": 1
- "显示前10条预警" ➔ "pageSize": 10

5. 特殊字段处
- 空值字段保持默认空值
- 未提及的条件不填充

6. 多轮对话支持
- **上下文管理**：在生成JSON查询时，参考当前输入和之前对话的上下文状态。
- **关键词提取和保留**：从当前输入提取关键词，与之前对话的关键词合并。
- **条件累积**：新条件与已有条件合并，形成完整查询。
- **条件覆盖**：若用户明确修改条件（如“紧急”改为“不紧急”），更新对应字段。
- **时间范围处理**：若后续对话指定新时间范围，则更新；否则默认使用“最近一周”，并设置`"isTimeRangeString":"-1"`。


7. 严格按照示例输出，严格按照示例输出，严格按照示例输出
8. 开头不需要输出json字样，去除所有markdown标签
9. 不需要备注注释
10. JSON格式，不要添加其他额外得字符、换行等等，JSON也要压缩一下


# 示例输出
{"eid": {eid},"filterUserAttention": null,"collectCategory": "","aiopsitem": null,"warningItemName": "","urgency": null,"serviceCode": "","tenantName": "","aiopsInstanceName": "","crmid": "","devicetype": "","status": "","warningLevel": "","warningTimeStart": "2025-04-01 00:00:00","warningTimeEnd": "2025-04-03 23:59:59","intelligentPrediction": null,"tenantModuleContractStatus": null,"orderBy": "warningTime desc","pageNum": 1,"pageSize": 20,"isTimeRangeString": ""}
