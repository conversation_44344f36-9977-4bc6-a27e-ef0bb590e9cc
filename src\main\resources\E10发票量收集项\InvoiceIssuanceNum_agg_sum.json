{"job": {"content": [{"writer": {"name": "mysqlwriter", "parameter": {"writeMode": "insert", "username": "servicecloud", "password": "servicecloud@123", "column": ["eid", "YearMonth", "IndicatorNumber", "product_line", "REC_COUNT", "REC_D_COUNT", "PAY_COUNT", "PAY_D_COUNT", "recRatio", "payRatio"], "connection": [{"jdbcUrl": "*************************************************************************************************", "table": ["InvoiceIssuanceNum_agg_sum"]}]}}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["WITH AggregatedData AS ( SELECT eid, YearMonth, IndicatorNumber, product_line, SUM(CASE WHEN InvoiceCountType = 'REC' THEN PERIOD_COUNT ELSE 0 END) AS REC_COUNT, SUM(CASE WHEN InvoiceCountType = 'REC_D' THEN PERIOD_COUNT ELSE 0 END) AS REC_D_COUNT, SUM(CASE WHEN InvoiceCountType = 'PAY' THEN PERIOD_COUNT ELSE 0 END) AS PAY_COUNT, SUM(CASE WHEN InvoiceCountType = 'PAY_D' THEN PERIOD_COUNT ELSE 0 END) AS PAY_D_COUNT FROM InvoiceIssuanceNum GROUP BY eid, YearMonth, IndicatorNumber, product_line ) SELECT eid, YearMonth, IndicatorNumber, product_line, REC_COUNT, REC_D_COUNT, PAY_COUNT, PAY_D_COUNT, CASE WHEN REC_COUNT = 0 THEN 0 ELSE CAST(CAST(REC_D_COUNT AS DECIMAL(18,0)) / NULLIF(REC_COUNT, 0) AS INT) END AS recRatio, CASE WHEN PAY_COUNT = 0 THEN 0 ELSE CAST(CAST(PAY_D_COUNT AS DECIMAL(18,0)) / NULLIF(PAY_COUNT, 0) AS INT) END AS payRatio FROM AggregatedData "], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}