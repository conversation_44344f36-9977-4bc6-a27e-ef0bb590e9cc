2024-10-12T14:40:53.858+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : selectE10 count:621
2024-10-12T14:40:54.211+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:360167694901824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d2f09fc45f1f3ee8a530180d35e8eb18&deviceId=517977836867302467
2024-10-12T14:40:54.301+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3d6b94c5100ec703ba1d0caad202323f\\\",\\\"dbIdValue\\\":\\\"3d6b94c5100ec703ba1d0caad202323f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HHZS\\\",\\\"targetValue\\\":\\\"HHZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517977836867302467
2024-10-12T14:40:56.772+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318755545664 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=89740069bad0a5ab2964b42e399852ca&deviceId=517829560436273475
2024-10-12T14:40:56.772+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"358632e74e7209a360cff7ae6ced4a08\\\",\\\"dbIdValue\\\":\\\"358632e74e7209a360cff7ae6ced4a08\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517829560436273475
2024-10-12T14:40:58.402+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:122664650465856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3f1d894ec9e67710246b993b2e220592&deviceId=517824208856695601
2024-10-12T14:40:58.404+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b7c3633828eb807396d7fb4f70645f69\\\",\\\"dbIdValue\\\":\\\"b7c3633828eb807396d7fb4f70645f69\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YHXNYJT\\\",\\\"targetValue\\\":\\\"YHXNYJT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517824208856695601
2024-10-12T14:40:59.954+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319483327040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0d13a68c9e992c0cb707688a0b93d2bd&deviceId=425595448552600643
2024-10-12T14:40:59.954+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1f6cbcc8a5f8547dc47a22436a58a495\\\",\\\"dbIdValue\\\":\\\"1f6cbcc8a5f8547dc47a22436a58a495\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CSK1\\\",\\\"targetValue\\\":\\\"CSK1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   425595448552600643
2024-10-12T14:41:01.386+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320083944000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=514d5735ac29cd6eeb88e38547849ac8&deviceId=517684048504566851
2024-10-12T14:41:01.386+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0bb031e548b5fa3d2018c3b41975386f\\\",\\\"dbIdValue\\\":\\\"0bb031e548b5fa3d2018c3b41975386f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WZDATA\\\",\\\"targetValue\\\":\\\"WZDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517684048504566851
2024-10-12T14:41:02.921+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:284042037015104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=836d44e4ba7d931b5a763de2199e0ccf&deviceId=517645381450872114
2024-10-12T14:41:02.922+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"85248ba8a23b1acb42add003092c2f9d\\\",\\\"dbIdValue\\\":\\\"85248ba8a23b1acb42add003092c2f9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"[E10_XXDzs]\\\",\\\"targetValue\\\":\\\"[E10_XXDzs]\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517645381450872114
2024-10-12T14:41:04.337+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:131866030248512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=342993c4ffaf3957c4990cb6afa77a42&deviceId=517098949052478531
2024-10-12T14:41:04.337+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d76b35c3237837813d06fbb3bb9165c8\\\",\\\"dbIdValue\\\":\\\"d76b35c3237837813d06fbb3bb9165c8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517098949052478531
2024-10-12T14:41:05.665+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318596289088 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=08a87e0aa1192e0351d6e51a66b00d9f&deviceId=422156995479486768
2024-10-12T14:41:05.665+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1e441d971f643270730133c7d86e8399\\\",\\\"dbIdValue\\\":\\\"1e441d971f643270730133c7d86e8399\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   422156995479486768
2024-10-12T14:41:07.018+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:93023016227392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=08e88e3ef7394f3deb57863483bde453&deviceId=502890545073370932
2024-10-12T14:41:07.020+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a040045afa049dd4ebc462c31798ef95\\\",\\\"dbIdValue\\\":\\\"a040045afa049dd4ebc462c31798ef95\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   502890545073370932
2024-10-12T14:41:08.375+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324383269440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4e938992f48b0967543450e24d5bcf22&deviceId=516967696965846580
2024-10-12T14:41:08.376+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5485e891917023e8d2af7160e55aec09\\\",\\\"dbIdValue\\\":\\\"5485e891917023e8d2af7160e55aec09\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516967696965846580
2024-10-12T14:41:09.995+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323951587904 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cd6af893fd7cd7beb5c030a7422fc7a1&deviceId=516924682130175799
2024-10-12T14:41:09.996+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"afb3927e9f62a1222fe5be4db2e28924\\\",\\\"dbIdValue\\\":\\\"afb3927e9f62a1222fe5be4db2e28924\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZRDZ1\\\",\\\"targetValue\\\":\\\"ZRDZ1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516924682130175799
2024-10-12T14:41:11.371+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:166621762024000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5f4f4d7f3aace000683eaa1cd49bb7f9&deviceId=516778632136376633
2024-10-12T14:41:11.372+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9ee90fb32dfe678b5717e6ef580f1447\\\",\\\"dbIdValue\\\":\\\"9ee90fb32dfe678b5717e6ef580f1447\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516778632136376633
2024-10-12T14:41:12.814+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321855259200 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ccf1c48b341e628e8eb04a7916c3ad54&deviceId=516655651351441714
2024-10-12T14:41:12.814+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0e75148ff2a1d7bb82bc51c5bc35e903\\\",\\\"dbIdValue\\\":\\\"0e75148ff2a1d7bb82bc51c5bc35e903\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10TS2\\\",\\\"targetValue\\\":\\\"E10TS2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516655651351441714
2024-10-12T14:41:14.171+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41325135204928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1d68a2c1ae8397ec853f4644d6a3425d&deviceId=516529171191575603
2024-10-12T14:41:14.172+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6a9817332abc27b0b7d13675c21b214d\\\",\\\"dbIdValue\\\":\\\"6a9817332abc27b0b7d13675c21b214d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"mggf\\\",\\\"targetValue\\\":\\\"mggf\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516529171191575603
2024-10-12T14:41:15.508+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319319220800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a4bc28b22213f5b748104210262d278c&deviceId=516510421897720887
2024-10-12T14:41:15.509+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"919120ec7536803a7548ca79ed3ad3ee\\\",\\\"dbIdValue\\\":\\\"919120ec7536803a7548ca79ed3ad3ee\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516510421897720887
2024-10-12T14:41:16.880+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:288569818747456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=17357f4b6738f20dc3136d2d3a0f4807&deviceId=420403484316152899
2024-10-12T14:41:16.880+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a4929fc97387a1f0b0c606d4cbda16c4\\\",\\\"dbIdValue\\\":\\\"a4929fc97387a1f0b0c606d4cbda16c4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Ddata\\\",\\\"targetValue\\\":\\\"Ddata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   420403484316152899
2024-10-12T14:41:18.235+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:233426031419968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9f11c10740039cbc7bc88acf533636d7&deviceId=516486483192857397
2024-10-12T14:41:18.236+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f7f434cf0b7a620135726734bdec655d\\\",\\\"dbIdValue\\\":\\\"f7f434cf0b7a620135726734bdec655d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516486483192857397
2024-10-12T14:41:19.593+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:463817907868224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9bb6baa514f329ea870023420a061011&deviceId=516076303816864835
2024-10-12T14:41:19.593+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9af7626132c8f4e9778ec8a5b303f93e\\\",\\\"dbIdValue\\\":\\\"9af7626132c8f4e9778ec8a5b303f93e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10SZdata\\\",\\\"targetValue\\\":\\\"E10SZdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516076303816864835
2024-10-12T14:41:20.929+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320880288320 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=70c0f8f6b0ab94a9b745d2dd7e0ed830&deviceId=516062592368652852
2024-10-12T14:41:20.929+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fcb6a42abe6df72925f4c6897debc051\\\",\\\"dbIdValue\\\":\\\"fcb6a42abe6df72925f4c6897debc051\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"lp2022\\\",\\\"targetValue\\\":\\\"lp2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516062592368652852
2024-10-12T14:41:22.348+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:93023016403520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1b60a31e0895c5157acc672369524301&deviceId=437782144232731715
2024-10-12T14:41:22.349+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"55e9bd55f1074f5a300a36dc4d5b0132\\\",\\\"dbIdValue\\\":\\\"55e9bd55f1074f5a300a36dc4d5b0132\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DHS_DATA\\\",\\\"targetValue\\\":\\\"DHS_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   437782144232731715
2024-10-12T14:41:23.698+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:454292344828480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=18cba375638ad00be751dd53ccac574e&deviceId=515937228480852016
2024-10-12T14:41:23.698+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5b7cb3a200ffef90a92237d9b327d110\\\",\\\"dbIdValue\\\":\\\"5b7cb3a200ffef90a92237d9b327d110\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"PEK\\\",\\\"targetValue\\\":\\\"PEK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515937228480852016
2024-10-12T14:41:25.129+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:253800651649600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a9bbfb11cceb166da501e89ae3d14260&deviceId=515932743226373187
2024-10-12T14:41:25.130+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b942931f4a171cfda2005db2f9f6efba\\\",\\\"dbIdValue\\\":\\\"b942931f4a171cfda2005db2f9f6efba\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AXS_DATA\\\",\\\"targetValue\\\":\\\"AXS_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515932743226373187
2024-10-12T14:41:26.487+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:378101824160320 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d4541e59a7546df004401e6c1350648b&deviceId=515918104115557429
2024-10-12T14:41:26.487+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e80f926c092f999da96975dc4e838590\\\",\\\"dbIdValue\\\":\\\"e80f926c092f999da96975dc4e838590\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HYPWR\\\",\\\"targetValue\\\":\\\"HYPWR\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515918104115557429
2024-10-12T14:41:27.816+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:298215400993344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2d48eee56c7cdd0dca804550e28a6a66&deviceId=515904787988887619
2024-10-12T14:41:27.816+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"76f584dea4a0b5b423acb6b372b21cef\\\",\\\"dbIdValue\\\":\\\"76f584dea4a0b5b423acb6b372b21cef\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CHUANCHIP\\\",\\\"targetValue\\\":\\\"CHUANCHIP\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515904787988887619
2024-10-12T14:41:29.200+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:474764427522624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f386ca5e156e151d38a0ac780f46a31f&deviceId=515805773238907971
2024-10-12T14:41:29.200+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"933628693c3b13820b451a92f1d631aa\\\",\\\"dbIdValue\\\":\\\"933628693c3b13820b451a92f1d631aa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WHdata\\\",\\\"targetValue\\\":\\\"WHdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515805773238907971
2024-10-12T14:41:30.554+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:348728838312512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a222f8c104bc230dc1ef331eb874ed94&deviceId=515800104502834243
2024-10-12T14:41:30.554+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3c96164f010fda77019420a1ea00d527\\\",\\\"dbIdValue\\\":\\\"3c96164f010fda77019420a1ea00d527\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515800104502834243
2024-10-12T14:41:31.904+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321524421184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c55a2f2c6de6bba6e29eec548301c27b&deviceId=515800047309304899
2024-10-12T14:41:31.905+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b3308eaad07b209902709e720277790a\\\",\\\"dbIdValue\\\":\\\"b3308eaad07b209902709e720277790a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"EXC\\\",\\\"targetValue\\\":\\\"EXC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515800047309304899
2024-10-12T14:41:33.264+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:166621762085440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8a72a64c7d059e29c1b5aef3a6a19dde&deviceId=515758622014191666
2024-10-12T14:41:33.266+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2b238ab3a3425ef20ccf6abcf82c985d\\\",\\\"dbIdValue\\\":\\\"2b238ab3a3425ef20ccf6abcf82c985d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CTDQ\\\",\\\"targetValue\\\":\\\"CTDQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515758622014191666
2024-10-12T14:41:34.615+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:489816618103360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6528b6080792c33f400130c6c96ca547&deviceId=515654987389355585
2024-10-12T14:41:34.616+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7bf5b56c4e30f5223ac471bb76cbcfa2\\\",\\\"dbIdValue\\\":\\\"7bf5b56c4e30f5223ac471bb76cbcfa2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LHLX\\\",\\\"targetValue\\\":\\\"LHLX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515654987389355585
2024-10-12T14:41:36.029+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324671570496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ab14d76cfc8dbf389b927be678b085ff&deviceId=515637683637208134
2024-10-12T14:41:36.029+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fc942394dd99ba89934155b018cdce98\\\",\\\"dbIdValue\\\":\\\"fc942394dd99ba89934155b018cdce98\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TianAn\\\",\\\"targetValue\\\":\\\"TianAn\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515637683637208134
2024-10-12T14:41:37.353+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:368192898683456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c1fa41b38a2c67759df4b8a406cf38e2&deviceId=515622538928207922
2024-10-12T14:41:37.354+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d1df5ab25c17ee83c98b4732efa2d78b\\\",\\\"dbIdValue\\\":\\\"d1df5ab25c17ee83c98b4732efa2d78b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515622538928207922
2024-10-12T14:41:38.694+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:389234632823360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1bc36d97dc5b4849e35a4dd44478db6b&deviceId=515619438582576691
2024-10-12T14:41:38.695+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"703158957cdb35e96fcc349e618f45d2\\\",\\\"dbIdValue\\\":\\\"703158957cdb35e96fcc349e618f45d2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515619438582576691
2024-10-12T14:41:40.097+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41323807990336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2ae38fc6dc23846dd71d4718ff15fa9f&deviceId=515619540520939568
2024-10-12T14:41:40.097+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b3edcfe2897b9c11bb4db1715952d50a\\\",\\\"dbIdValue\\\":\\\"b3edcfe2897b9c11bb4db1715952d50a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515619540520939568
2024-10-12T14:41:41.475+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319362187840 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9f0a0341267f7adda6eaa16686cd5876&deviceId=515618179351851844
2024-10-12T14:41:41.475+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ef4d069df9861d85ac49e80a03168f8f\\\",\\\"dbIdValue\\\":\\\"ef4d069df9861d85ac49e80a03168f8f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GZD_50\\\",\\\"targetValue\\\":\\\"GZD_50\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515618179351851844
2024-10-12T14:41:42.813+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319689413184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=453270fd0d6ef93c9e1fc7a627bdcd4b&deviceId=515497245722817337
2024-10-12T14:41:42.814+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b3a9a65673d8978b27af9f66030ba89a\\\",\\\"dbIdValue\\\":\\\"b3a9a65673d8978b27af9f66030ba89a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Ddata\\\",\\\"targetValue\\\":\\\"Ddata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515497245722817337
2024-10-12T14:41:44.201+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322039116352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b95dd73f73ffdf22c31125e51c797553&deviceId=515493212597531715
2024-10-12T14:41:44.202+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8f7ce7bc8bc384a0065f9d28134da345\\\",\\\"dbIdValue\\\":\\\"8f7ce7bc8bc384a0065f9d28134da345\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515493212597531715
2024-10-12T14:41:45.557+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:303428765745728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eb0e443514ae6da04f91d042dfe485dc&deviceId=515475817594106934
2024-10-12T14:41:45.557+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"00cd9285961349adce242b8eeb001039\\\",\\\"dbIdValue\\\":\\\"00cd9285961349adce242b8eeb001039\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_ZS\\\",\\\"targetValue\\\":\\\"E10_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515475817594106934
2024-10-12T14:41:46.894+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319612088896 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f92407b9bcc2fd3b3c8a22e23bc64628&deviceId=515068654660890678
2024-10-12T14:41:46.895+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8634c2f319f8ec1688cca6d035ecf0f1\\\",\\\"dbIdValue\\\":\\\"8634c2f319f8ec1688cca6d035ecf0f1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515068654660890678
2024-10-12T14:41:48.310+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:336105932276288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=99cc1a19f7c297e8ccbf5f9fc83b66b0&deviceId=514937675707335222
2024-10-12T14:41:48.310+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28173ae20efb0258b423f41e4586aabe\\\",\\\"dbIdValue\\\":\\\"28173ae20efb0258b423f41e4586aabe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XWKDdata\\\",\\\"targetValue\\\":\\\"XWKDdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514937675707335222
2024-10-12T14:41:49.854+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321628459584 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=29504406d48f98e7f7ec2aba1736ac2d&deviceId=514936230132073268
2024-10-12T14:41:49.854+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7cbd2c7c64981629f0702b510eb60ff3\\\",\\\"dbIdValue\\\":\\\"7cbd2c7c64981629f0702b510eb60ff3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514936230132073268
2024-10-12T14:41:51.210+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:198790180704832 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=985d1d7ef870f362c34ccbb8b267601c&deviceId=514639507769799747
2024-10-12T14:41:51.210+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"92863a8470000a3918faa3e8911af045\\\",\\\"dbIdValue\\\":\\\"92863a8470000a3918faa3e8911af045\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AKM\\\",\\\"targetValue\\\":\\\"AKM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514639507769799747
2024-10-12T14:41:52.577+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:518439672132160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d44baebf0de54dfe1eab4c87b28cb8ee&deviceId=514745382840841283
2024-10-12T14:41:52.578+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0987cb58faa496362756b0cd89b58bf6\\\",\\\"dbIdValue\\\":\\\"0987cb58faa496362756b0cd89b58bf6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FSW\\\",\\\"targetValue\\\":\\\"FSW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514745382840841283
2024-10-12T14:41:54.021+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41324391354944 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3b47e2e9e34df15547131d6fc13346f3&deviceId=514490947535386166
2024-10-12T14:41:54.022+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b737d32acd9ff62a3f9bd4dd0f4a813c\\\",\\\"dbIdValue\\\":\\\"b737d32acd9ff62a3f9bd4dd0f4a813c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514490947535386166
2024-10-12T14:41:55.390+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320369058368 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=346630710eb41a30f2a22de2d65ab72c&deviceId=514052532188754744
2024-10-12T14:41:55.390+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cd2c2895277804341aaf7690ad19b5d0\\\",\\\"dbIdValue\\\":\\\"cd2c2895277804341aaf7690ad19b5d0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CS\\\",\\\"targetValue\\\":\\\"CS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   514052532188754744
2024-10-12T14:41:56.781+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317406003776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2e2ba2724c57d920032eb66031971d01&deviceId=514046157198996547
2024-10-12T14:41:56.782+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"648a68240bb5fed213da3d197e3aedc5\\\",\\\"dbIdValue\\\":\\\"648a68240bb5fed213da3d197e3aedc5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HXdata\\\",\\\"targetValue\\\":\\\"HXdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd8503520fa07ecceedf4351510e3fed54b23dc904ea813eeb4ebccfd563f9d242d7ce146002ef7ecc7f9e33cc1ee6cd061ea940fc6a8d32cdb374ceb47ba0bcd231cd4538b99bd82748c4adbbebc1b951806f045edeedb45c385918fecbba0790fa8c8db2aec5a1a70d2590acaee02d8c438f1c9d2f33333e259da3a1d7a91453c2dfb654450345b84c68eb9638cd3a09ed1cd62d48299080d18cd7ec2bdffac80339812e680c13227dda9456daefceeb26b498f531f31483857b73381e2004aaa99103017e14a8c49fb9ec70e627a33396603908516ff3c8098076634cdca885d95130a58638f1c45e29f3832bb64ccf624c3360697cb105db05","collectName":"E10附件数据采集","accId":779544910123584,"adimId":727466522702401,"id":779846117945920,"adcId":727461064987200,"execParamsVersion":"5f34f830283e9d5ca64aff364dfd0496","aiId":727466522686017,"isEnable":1},"paramsMap":{"deviceId":"514046157198996547","eid":41317406003776,"aiId":727466522686017,"execParamsDbAiId":468695104365120,"execParamsDbId":"648a68240bb5fed213da3d197e3aedc5"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779846117945920 acc incomplete","batchId":779846119141952,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   514046157198996547
2024-10-12T14:41:58.227+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:478357534323264 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7e5ba2d3b49eabb9d3d278bfae9cee4e&deviceId=513917646324904006
2024-10-12T14:41:58.228+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c41d3d164981b74b45e3705b0461d164\\\",\\\"dbIdValue\\\":\\\"c41d3d164981b74b45e3705b0461d164\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HTZY\\\",\\\"targetValue\\\":\\\"HTZY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513917646324904006
2024-10-12T14:41:59.613+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:213800798483008 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3734b5303a8a58d27008ba0585da4958&deviceId=513881787609395778
2024-10-12T14:41:59.614+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"86f9b36453b2cb528962e435268b87c8\\\",\\\"dbIdValue\\\":\\\"86f9b36453b2cb528962e435268b87c8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"sa\\\",\\\"targetValue\\\":\\\"sa\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513881787609395778
2024-10-12T14:42:00.952+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:73557019451968 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f34da9b8a256d099e900c4a6c17bcf37&deviceId=513876769577645380
2024-10-12T14:42:00.952+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a8ec3f3fc00662a983a2ba1ce7c22bd9\\\",\\\"dbIdValue\\\":\\\"a8ec3f3fc00662a983a2ba1ce7c22bd9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ERP_DATA\\\",\\\"targetValue\\\":\\\"ERP_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513876769577645380
2024-10-12T14:42:02.318+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41318699242048 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=25cb9f7cbd6df2eea7ffa95b92da10cb&deviceId=513620699080831032
2024-10-12T14:42:02.318+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cb103806196b24efe2b9c66a8d92bdbf\\\",\\\"dbIdValue\\\":\\\"cb103806196b24efe2b9c66a8d92bdbf\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GLB\\\",\\\"targetValue\\\":\\\"GLB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513620699080831032
2024-10-12T14:42:03.814+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:199203052184128 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9e93f290a9c97a49e5cb0410f90dc1be&deviceId=513622414433728068
2024-10-12T14:42:03.814+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c438e4f85cefb7dee424ecda307b0c2a\\\",\\\"dbIdValue\\\":\\\"c438e4f85cefb7dee424ecda307b0c2a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LGT\\\",\\\"targetValue\\\":\\\"LGT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513622414433728068
2024-10-12T14:42:05.174+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:478644751626816 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=67e2019b7d890a4c51c0573db653626d&deviceId=513592734313886787
2024-10-12T14:42:05.174+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c2ef6be013d649eee23d07b250ef17a6\\\",\\\"dbIdValue\\\":\\\"c2ef6be013d649eee23d07b250ef17a6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XZW_DATA\\\",\\\"targetValue\\\":\\\"XZW_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513592734313886787
2024-10-12T14:42:06.523+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:315201551118912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ae0142586bd21f7187a26ca8ade44813&deviceId=513587852479575602
2024-10-12T14:42:06.524+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f6473878c57ced617348abf63ba0af12\\\",\\\"dbIdValue\\\":\\\"f6473878c57ced617348abf63ba0af12\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513587852479575602
2024-10-12T14:42:07.870+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322092728896 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f567fc3b42b0dd83065175950638f558&deviceId=513478949121049399
2024-10-12T14:42:07.871+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"242d13d365b1f4c59350a06973397d9d\\\",\\\"dbIdValue\\\":\\\"242d13d365b1f4c59350a06973397d9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DEMO\\\",\\\"targetValue\\\":\\\"DEMO\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513478949121049399
2024-10-12T14:42:09.191+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:328908562862656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=58b771eb5ca87361f2e6c9fd7aa7bf1a&deviceId=513468910138308934
2024-10-12T14:42:09.192+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"66df138fdb37a8f453cd6a88390b44c9\\\",\\\"dbIdValue\\\":\\\"66df138fdb37a8f453cd6a88390b44c9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513468910138308934
2024-10-12T14:42:10.502+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:247988360983104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=01e3ce59f1f7def13745623d7d038caf&deviceId=513465312700543299
2024-10-12T14:42:10.502+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ab65a11eb1065d1fa283ac81aa0bda83\\\",\\\"dbIdValue\\\":\\\"ab65a11eb1065d1fa283ac81aa0bda83\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ARK\\\",\\\"targetValue\\\":\\\"ARK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513465312700543299
2024-10-12T14:42:11.855+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:380932977644096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=79dc3bd68b85fe184806378ca97d331e&deviceId=513183938269689156
2024-10-12T14:42:11.855+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a4c18e0b0ebe31e94126c985fe4573fb\\\",\\\"dbIdValue\\\":\\\"a4c18e0b0ebe31e94126c985fe4573fb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513183938269689156
2024-10-12T14:42:13.197+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322900587072 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=631532e13af054f639d5f42835387d1d&deviceId=507386931017954371
2024-10-12T14:42:13.197+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"410a14b2adaacfe04bcfc126ead4b71f\\\",\\\"dbIdValue\\\":\\\"410a14b2adaacfe04bcfc126ead4b71f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Demo\\\",\\\"targetValue\\\":\\\"E10_Demo\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   507386931017954371
2024-10-12T14:42:14.588+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:520960780313152 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e5e371edfd2d45416aefb8aafbe75473&deviceId=513178599776334915
2024-10-12T14:42:14.588+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"992b24240ea942d39689c2680e758d62\\\",\\\"dbIdValue\\\":\\\"992b24240ea942d39689c2680e758d62\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HMZN\\\",\\\"targetValue\\\":\\\"HMZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513178599776334915
2024-10-12T14:42:16.022+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:520960780313152 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=613bcd3a184f167f2044c1eb0b38cf34&deviceId=513175586420572484
2024-10-12T14:42:16.022+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"992b24240ea942d39689c2680e758d62\\\",\\\"dbIdValue\\\":\\\"992b24240ea942d39689c2680e758d62\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HMZN\\\",\\\"targetValue\\\":\\\"HMZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513175586420572484
2024-10-12T14:42:17.376+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317822399040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4ab8aba8569884d76982fab369b8fe01&deviceId=513154713131167792
2024-10-12T14:42:17.376+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"89e61cd7de55924deeeb313cbefe84d7\\\",\\\"dbIdValue\\\":\\\"89e61cd7de55924deeeb313cbefe84d7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"cytz\\\",\\\"targetValue\\\":\\\"cytz\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   513154713131167792
2024-10-12T14:42:18.778+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:391240008909376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=549847a61561f360449b2cc406c80815&deviceId=512902773134604340
2024-10-12T14:42:18.779+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"2d300b79142c5afd545362cb7d6f8ec7\\\",\\\"dbIdValue\\\":\\\"2d300b79142c5afd545362cb7d6f8ec7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512902773134604340
2024-10-12T14:42:20.166+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:215216377344577 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bccf79ce889295658ec866aeef145d8b&deviceId=512893257819109441
2024-10-12T14:42:20.166+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"03dcbe101dc3c4dd2f2d6d1bad8364bc\\\",\\\"dbIdValue\\\":\\\"03dcbe101dc3c4dd2f2d6d1bad8364bc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_JT01\\\",\\\"targetValue\\\":\\\"E10_JT01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512893257819109441
2024-10-12T14:42:21.575+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:183945496863296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1667bf07e959555edcf00c59941f9c47&deviceId=512863822965392441
2024-10-12T14:42:21.576+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8514f646aea83586bcd14650286b21f3\\\",\\\"dbIdValue\\\":\\\"8514f646aea83586bcd14650286b21f3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512863822965392441
2024-10-12T14:42:22.929+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320683455040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c5702f947b9ba4cdc2e5f56a87b3fcb2&deviceId=512756341996009538
2024-10-12T14:42:22.930+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f7cea1c55e616b8a3bf6ce047c6c4fd8\\\",\\\"dbIdValue\\\":\\\"f7cea1c55e616b8a3bf6ce047c6c4fd8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FGDATA\\\",\\\"targetValue\\\":\\\"FGDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512756341996009538
2024-10-12T14:42:24.281+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41320208687680 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=471502711165170a53287a511dd8278c&deviceId=465056219183199299
2024-10-12T14:42:24.281+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0930575466659d7273ac47fb3b7e0f13\\\",\\\"dbIdValue\\\":\\\"0930575466659d7273ac47fb3b7e0f13\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10JATA\\\",\\\"targetValue\\\":\\\"E10JATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   465056219183199299
2024-10-12T14:42:25.654+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321503699520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4154a9dbafde47743ad549d007b984cb&deviceId=512718210387162179
2024-10-12T14:42:25.655+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e503a18c6397c8ee0d296e664ce0e8e4\\\",\\\"dbIdValue\\\":\\\"e503a18c6397c8ee0d296e664ce0e8e4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HZ\\\",\\\"targetValue\\\":\\\"HZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512718210387162179
2024-10-12T14:42:27.015+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:433445355688512 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1a39644efd67a3add0f9be5284cbe6cf&deviceId=512718357456238647
2024-10-12T14:42:27.015+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c74677dddb4fa0d051b7037c78f3a7d6\\\",\\\"dbIdValue\\\":\\\"c74677dddb4fa0d051b7037c78f3a7d6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BY1026\\\",\\\"targetValue\\\":\\\"BY1026\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512718357456238647
2024-10-12T14:42:28.347+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:337786777825856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fdc633068a3b763719258f80fbc315ec&deviceId=512609936191009859
2024-10-12T14:42:28.347+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ee7ebc6cb2a776f9db070a9164582998\\\",\\\"dbIdValue\\\":\\\"ee7ebc6cb2a776f9db070a9164582998\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZSZZ\\\",\\\"targetValue\\\":\\\"ZSZZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512609936191009859
2024-10-12T14:42:29.669+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:171517263884864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5c2a0c0ca47df5c11569a8ec7ca6b622&deviceId=512607596725679942
2024-10-12T14:42:29.670+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4eb2811b4546fad913891ee98215e9b3\\\",\\\"dbIdValue\\\":\\\"4eb2811b4546fad913891ee98215e9b3\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YYG\\\",\\\"targetValue\\\":\\\"YYG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512607596725679942
2024-10-12T14:42:31.070+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:340868580823616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0147d9d4f83b5a881d2c9cdd8bcd27ac&deviceId=512595941274960197
2024-10-12T14:42:31.070+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c4842f42a486ea53492421bdd0bbdd0e\\\",\\\"dbIdValue\\\":\\\"c4842f42a486ea53492421bdd0bbdd0e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SZZS\\\",\\\"targetValue\\\":\\\"SZZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512595941274960197
2024-10-12T14:42:32.468+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:503974458245696 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bbb49cf3f0311bd78c54eff2a3737440&deviceId=512570257487968050
2024-10-12T14:42:32.469+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"05b8b3ae18046e9eaace951b7fe16235\\\",\\\"dbIdValue\\\":\\\"05b8b3ae18046e9eaace951b7fe16235\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RPDB\\\",\\\"targetValue\\\":\\\"RPDB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512570257487968050
2024-10-12T14:42:33.888+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:466766990156352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=02b11228a24b37edc12d1c7e5700831e&deviceId=512467938213639235
2024-10-12T14:42:33.888+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3dae01fe956358657e7709c6efe6fd1b\\\",\\\"dbIdValue\\\":\\\"3dae01fe956358657e7709c6efe6fd1b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Chuanyue\\\",\\\"targetValue\\\":\\\"Chuanyue\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   512467938213639235
2024-10-12T14:42:35.261+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:470955308393024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=006e233fa3c4a3cfa69b56fc1ea3ec54&deviceId=511451176043034929
2024-10-12T14:42:35.261+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"83aa79de9f11da2702a36f681b12e276\\\",\\\"dbIdValue\\\":\\\"83aa79de9f11da2702a36f681b12e276\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"xyE10\\\",\\\"targetValue\\\":\\\"xyE10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511451176043034929
2024-10-12T14:42:36.633+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41319237468736 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=81fadcd04bb12a743b16e5e53ce89482&deviceId=511425322755831861
2024-10-12T14:42:36.633+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9b5a6a5f1551aee872d86d41ecf487eb\\\",\\\"dbIdValue\\\":\\\"9b5a6a5f1551aee872d86d41ecf487eb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FQGZ\\\",\\\"targetValue\\\":\\\"FQGZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511425322755831861
2024-10-12T14:42:38.082+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:457256597324352 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=09433a24c83a4466eb1f44716fe7aef6&deviceId=511418022318061633
2024-10-12T14:42:38.083+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"257da83c059799fd1db698fc4a84ff9c\\\",\\\"dbIdValue\\\":\\\"257da83c059799fd1db698fc4a84ff9c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHdata\\\",\\\"targetValue\\\":\\\"SHdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511418022318061633
2024-10-12T14:42:39.504+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:478844790739520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9b4a15ff9fd3d067a43515083d7b5369&deviceId=511411393237365827
2024-10-12T14:42:39.504+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7a059f58dbe4e1f5ff5d2fd0c1b27dd0\\\",\\\"dbIdValue\\\":\\\"7a059f58dbe4e1f5ff5d2fd0c1b27dd0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TX\\\",\\\"targetValue\\\":\\\"TX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511411393237365827
2024-10-12T14:42:40.858+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:124429366305344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=137c32b5380c07b3e84246489452ebdb&deviceId=511410215292253251
2024-10-12T14:42:40.859+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"076728811f09c7c0818653e9140d2859\\\",\\\"dbIdValue\\\":\\\"076728811f09c7c0818653e9140d2859\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZJBD123\\\",\\\"targetValue\\\":\\\"ZJBD123\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511410215292253251
2024-10-12T14:42:42.244+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:177710698418752 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=154d3da838c1c77681eea6275132fdee&deviceId=511267788841567287
2024-10-12T14:42:42.244+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8f5b43ebcc0c4ef6b2af7629e7a0f7af\\\",\\\"dbIdValue\\\":\\\"8f5b43ebcc0c4ef6b2af7629e7a0f7af\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NJMLW\\\",\\\"targetValue\\\":\\\"NJMLW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":0,"errMsg":"success"}   ---   511267788841567287
2024-10-12T14:42:43.387+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:186375285858880 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e13804f956b66bacda17780b7090014c&deviceId=511271553934045766
2024-10-12T14:42:43.387+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"21f218b9a43d09aab4d9928b276a9ec1\\\",\\\"dbIdValue\\\":\\\"21f218b9a43d09aab4d9928b276a9ec1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"QHSdata\\\",\\\"targetValue\\\":\\\"QHSdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511271553934045766
2024-10-12T14:42:44.743+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:380298940912192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3dd1efc60b918920061655858a931d3c&deviceId=511267124027601220
2024-10-12T14:42:44.743+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d9362a5aea9cc3777ad63df5b63a4330\\\",\\\"dbIdValue\\\":\\\"d9362a5aea9cc3777ad63df5b63a4330\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LGXYD\\\",\\\"targetValue\\\":\\\"LGXYD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511267124027601220
2024-10-12T14:42:46.200+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:480834780418624 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=708c1b334a6b4b04dba92c30ab481ef7&deviceId=511012433070932803
2024-10-12T14:42:46.201+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a0a576d850a5123ff70d7638a6cb06a0\\\",\\\"dbIdValue\\\":\\\"a0a576d850a5123ff70d7638a6cb06a0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JFYDATA\\\",\\\"targetValue\\\":\\\"JFYDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511012433070932803
2024-10-12T14:42:47.608+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:315201536967232 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c03e918b4d8099d8dfd0dc62bacc07e1&deviceId=511010328989938755
2024-10-12T14:42:47.608+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4ec80066077d0b4bf020077397d1ca2e\\\",\\\"dbIdValue\\\":\\\"4ec80066077d0b4bf020077397d1ca2e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"AICERP027\\\",\\\"targetValue\\\":\\\"AICERP027\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   511010328989938755
2024-10-12T14:42:49.046+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:367485118313024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5b5b3a8a1d0c56d90da91a6ec6a374f1&deviceId=510987650035888961
2024-10-12T14:42:49.047+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6b73766673d87c6cf6984ef0f8ad1707\\\",\\\"dbIdValue\\\":\\\"6b73766673d87c6cf6984ef0f8ad1707\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XH_DATA\\\",\\\"targetValue\\\":\\\"XH_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510987650035888961
2024-10-12T14:42:50.367+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:85854756921920 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9d418617230f7e14affe0471ffcdbe57&deviceId=510984463321151280
2024-10-12T14:42:50.368+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7bd65371acbe2d733d751484d00760ed\\\",\\\"dbIdValue\\\":\\\"7bd65371acbe2d733d751484d00760ed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ARreally\\\",\\\"targetValue\\\":\\\"ARreally\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":0,"errMsg":"success"}   ---   510984463321151280
2024-10-12T14:42:51.481+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41322412257856 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9189be805b0fc2346df8f1430b929e18&deviceId=510982357830873911
2024-10-12T14:42:51.482+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"65045d86e71c317974a7d420ad5c71af\\\",\\\"dbIdValue\\\":\\\"65045d86e71c317974a7d420ad5c71af\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GDDZ\\\",\\\"targetValue\\\":\\\"GDDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510982357830873911
2024-10-12T14:42:52.833+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:131600592446016 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=04d5d7e58e512ec10e54d92862a7943d&deviceId=510976950215389253
2024-10-12T14:42:52.833+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"385c48e4f767587db6c536249f39b8f5\\\",\\\"dbIdValue\\\":\\\"385c48e4f767587db6c536249f39b8f5\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WBQC\\\",\\\"targetValue\\\":\\\"WBQC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510976950215389253
2024-10-12T14:42:54.233+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:493663498678848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8c06d03e9668a51ac052a96c0df6de4d&deviceId=510879660414874691
2024-10-12T14:42:54.234+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"18f52ec73457ce36298a975884ced0ce\\\",\\\"dbIdValue\\\":\\\"18f52ec73457ce36298a975884ced0ce\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NJBT\\\",\\\"targetValue\\\":\\\"NJBT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510879660414874691
2024-10-12T14:42:55.573+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:468846098281024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7917011df82d00d3c3f2af02ffc9e5e4&deviceId=510874271136887875
2024-10-12T14:42:55.574+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4eaf33a15b9a4ba436e679f448e29993\\\",\\\"dbIdValue\\\":\\\"4eaf33a15b9a4ba436e679f448e29993\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_ZSW\\\",\\\"targetValue\\\":\\\"E10_ZSW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510874271136887875
2024-10-12T14:42:56.912+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:390679682499136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=25c8b20dbfca3715a4bb865ecc437d69&deviceId=510871730596622640
2024-10-12T14:42:56.912+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b8feb5cd15d6c82c3276ba6401b049ce\\\",\\\"dbIdValue\\\":\\\"b8feb5cd15d6c82c3276ba6401b049ce\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CYPAG\\\",\\\"targetValue\\\":\\\"CYPAG\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510871730596622640
2024-10-12T14:42:58.262+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:369608465556032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f7b517d8d74f18671d5653ac1bd9ce03&deviceId=510869845592524353
2024-10-12T14:42:58.262+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"510252dc02fd3f62e474ccf199002e0c\\\",\\\"dbIdValue\\\":\\\"510252dc02fd3f62e474ccf199002e0c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HZLXT\\\",\\\"targetValue\\\":\\\"HZLXT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510869845592524353
2024-10-12T14:42:59.627+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:415341371286080 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=176724346e487cc29fb22b6504365827&deviceId=510857236944271161
2024-10-12T14:42:59.628+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"973c2d761162f01857c85c8783b459b4\\\",\\\"dbIdValue\\\":\\\"973c2d761162f01857c85c8783b459b4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HJRDATA\\\",\\\"targetValue\\\":\\\"HJRDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510857236944271161
2024-10-12T14:43:00.960+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:489800169996864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=43b17cc8d9e0afb7221b7f3d3df80805&deviceId=496652078333047875
2024-10-12T14:43:00.961+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"33da5ae10c40f7e3dc3534ba478a312a\\\",\\\"dbIdValue\\\":\\\"33da5ae10c40f7e3dc3534ba478a312a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ASH\\\",\\\"targetValue\\\":\\\"ASH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   496652078333047875
2024-10-12T14:43:02.297+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:230909444371008 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=871f4283eab934e41079ccd0ca23fd29&deviceId=419234115380851510
2024-10-12T14:43:02.297+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9c8fd20ff952c361b6733a1edca8e60d\\\",\\\"dbIdValue\\\":\\\"9c8fd20ff952c361b6733a1edca8e60d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":0,"errMsg":"success"}   ---   419234115380851510
2024-10-12T14:43:03.392+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:590193878413888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1940beaa433b11e3bb22e6be9c97503e&deviceId=510829083148431683
2024-10-12T14:43:03.393+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3e3ef135bd25d8a69bd7958e7c4e17d1\\\",\\\"dbIdValue\\\":\\\"3e3ef135bd25d8a69bd7958e7c4e17d1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GAOJING\\\",\\\"targetValue\\\":\\\"GAOJING\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510829083148431683
2024-10-12T14:43:04.756+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:478645417009728 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c4898d5180bf21e597c88dbdd8d7468d&deviceId=510718298124464949
2024-10-12T14:43:04.757+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"caa3143e1344314137c2db5220c42846\\\",\\\"dbIdValue\\\":\\\"caa3143e1344314137c2db5220c42846\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHWData\\\",\\\"targetValue\\\":\\\"SHWData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510718298124464949
2024-10-12T14:43:06.246+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41317915910720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b9ca0e7ce373b51db37c71543c82fffa&deviceId=510732237122057520
2024-10-12T14:43:06.248+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"53b72d3aefb7ab63e702728174d27221\\\",\\\"dbIdValue\\\":\\\"53b72d3aefb7ab63e702728174d27221\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MJXC\\\",\\\"targetValue\\\":\\\"MJXC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510732237122057520
2024-10-12T14:43:07.584+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:331141344563776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2ebefc8d1a86aa85f09b98594302b0da&deviceId=510726890357539142
2024-10-12T14:43:07.584+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"794535ac494da9ed2fb5c859fda0e77a\\\",\\\"dbIdValue\\\":\\\"794535ac494da9ed2fb5c859fda0e77a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KC2024\\\",\\\"targetValue\\\":\\\"KC2024\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510726890357539142
2024-10-12T14:43:08.904+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:203891727045184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=614fd739a3d62853a34190067964a2e2&deviceId=510726908829253956
2024-10-12T14:43:08.904+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7f35d55ae1682195389ec3d4671e81fe\\\",\\\"dbIdValue\\\":\\\"7f35d55ae1682195389ec3d4671e81fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ISTK\\\",\\\"targetValue\\\":\\\"ISTK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510726908829253956
2024-10-12T14:43:10.269+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:41321592558144 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d7b5ad4dede9039373eb11cf79364807&deviceId=510717532043228227
2024-10-12T14:43:10.270+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9f6df35e980cae6960de1831d51c2f30\\\",\\\"dbIdValue\\\":\\\"9f6df35e980cae6960de1831d51c2f30\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"hapy\\\",\\\"targetValue\\\":\\\"hapy\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510717532043228227
2024-10-12T14:43:11.609+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : eid:519413053198913 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dc1e78d861647010e9d70937e9747b3b&deviceId=510715906867868739
2024-10-12T14:43:11.610+08:00  INFO 33556 --- [nio-8081-exec-2] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7181a7193ebb5bf5b6f2846e3c702fff\\\",\\\"dbIdValue\\\":\\\"7181a7193ebb5bf5b6f2846e3c702fff\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SHXX\\\",\\\"targetValue\\\":\\\"SHXX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510715906867868739
deviceIds:,'517977836867302467','517829560436273475','517824208856695601','425595448552600643','517684048504566851','517645381450872114','517098949052478531','422156995479486768','502890545073370932','516967696965846580','516924682130175799','516778632136376633','516655651351441714','516529171191575603','516510421897720887','420403484316152899','516486483192857397','516076303816864835','516062592368652852','437782144232731715','515937228480852016','515932743226373187','515918104115557429','515904787988887619','515805773238907971','515800104502834243','515800047309304899','515758622014191666','515654987389355585','515637683637208134','515622538928207922','515619438582576691','515619540520939568','515618179351851844','515497245722817337','515493212597531715','515475817594106934','515068654660890678','514937675707335222','514936230132073268','514639507769799747','514745382840841283','514490947535386166','514052532188754744','514046157198996547','513917646324904006','513881787609395778','513876769577645380','513620699080831032','513622414433728068','513592734313886787','513587852479575602','513478949121049399','513468910138308934','513465312700543299','513183938269689156','507386931017954371','513178599776334915','513175586420572484','513154713131167792','512902773134604340','512893257819109441','512863822965392441','512756341996009538','465056219183199299','512718210387162179','512718357456238647','512609936191009859','512607596725679942','512595941274960197','512570257487968050','512467938213639235','511451176043034929','511425322755831861','511418022318061633','511411393237365827','511410215292253251','511267788841567287','511271553934045766','511267124027601220','511012433070932803','511010328989938755','510987650035888961','510984463321151280','510982357830873911','510976950215389253','510879660414874691','510874271136887875','510871730596622640','510869845592524353','510857236944271161','496652078333047875','419234115380851510','510829083148431683','510718298124464949','510732237122057520','510726890357539142','510726908829253956','510717532043228227','510715906867868739'