{"runner_name": "", "name": "", "mode": "script_v2", "script_exec_interprepter": "/bin/bash", "script_params": "-c", "script_params_spliter": "", "script_content": "#!/bin/bash\n\n# 使用free -h命令获取内存和swap的使用情况\noutput=$(free | grep Swap)\n\n# 使用awk从输出中提取swap总量、已使用和空闲的数据\nswap_total=$(echo $output | awk '{print $2}')\nswap_used=$(echo $output | awk '{print $3}')\nswap_free=$(echo $output | awk '{print $4}')\n\n# 创建JSON格式的数据\njson_data=$(cat <<EOF\n{\n    \"swapTotal\": \"$swap_total\",\n    \"swapUsed\": \"$swap_used\",\n    \"swapFree\": \"$swap_free\"\n}\nEOF\n)\n\n# 打印JSON数据\necho \"$json_data\"", "script_timeout": "1m", "script_cron": "0 0/5 * * * ?", "script_exec_onstart": "false", "script_check_command": "", "script_fix_result_encoding": "false", "script_detect_options": "", "script_detect_error_options": "", "script_fix_result_by_os_encoding": "false", "script_command_not_exist_result_key": "cmdNotExist", "execute_error_return": "true", "execute_error_return_key": "execError", "script_env_sources": "", "parser_type": "raw", "node_name": "", "executor_type": "reader"}