package com.example.demo.model;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DataExaminationComponentBaseParams {
    @ExcelProperty("ID")
    private String id;

    @ExcelProperty("组件参数ID")
    private String componentParamId;

    @ExcelProperty("字段Code")
    private String fieldCode;

    @ExcelProperty("字段名称")
    private String fieldName;

    @ExcelProperty("字段值")
    private String value;
}
