package com.digiwin.escloud.aiocdp.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 智能体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-11
 */
@TableName("chat_assistant")
@ApiModel(value = "ChatAssistant对象", description = "智能体")
public class ChatAssistant implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("智能体GUID")
    private String id;

    @ApiModelProperty("AI智能体唯一标识 英文特殊标识可以标识唯一得一个智能体 例如 SESSION_SUMMARY(会话总结)、SMART_CONVERSATIONS(智能对话) ")
    private String aiAgentId;

    @ApiModelProperty("AI智能体编号 例如 qxrom8881ad")
    private String code;

    @ApiModelProperty("AI智能体名称")
    private String name;

    @ApiModelProperty("技能id")
    private String appSkillId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAiAgentId() {
        return aiAgentId;
    }

    public void setAiAgentId(String aiAgentId) {
        this.aiAgentId = aiAgentId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppSkillId() {
        return appSkillId;
    }

    public void setAppSkillId(String appSkillId) {
        this.appSkillId = appSkillId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ChatAssistant{" +
            "id = " + id +
            ", aiAgentId = " + aiAgentId +
            ", code = " + code +
            ", name = " + name +
            ", appSkillId = " + appSkillId +
            ", createTime = " + createTime +
            ", updateTime = " + updateTime +
        "}";
    }
}
