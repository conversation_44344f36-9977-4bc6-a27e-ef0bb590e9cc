主机3
规范
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',1,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',5,80,'2024-11-11 11:11:11','2024-11-11');


稳定
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',6,90.6,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',7,100,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',8,60,'2024-11-11 11:11:11','2024-11-11');


性能
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',9,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',10,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('516496010621501746',11,80,'2024-11-11 11:11:11','2024-11-11');


终端1
规范
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',1,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',5,90,'2024-11-11 11:11:11','2024-11-11');


稳定
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',6,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',7,75,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',8,90,'2024-11-11 11:11:11','2024-11-11');


性能
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',9,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',10,95,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('414052247303894084',11,80,'2024-11-11 11:11:11','2024-11-11');


终端2
规范
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',1,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',4,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',5,90,'2024-11-11 11:11:11','2024-11-11');


稳定
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',6,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',7,75,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',8,90,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',9,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',10,95,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('436356330685477702',11,80,'2024-11-11 11:11:11','2024-11-11');


终端3
规范
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',1,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',4,85,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',5,90,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',6,95,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',7,100,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',8,80,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',9,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',10,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('524188330082973254',11,70,'2024-11-11 11:11:11','2024-11-11');


数据库2
规范
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',1,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',4,90,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',5,100,'2024-11-11 11:11:11','2024-11-11');

稳定
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',6,70,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',7,80,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',8,90,'2024-11-11 11:11:11','2024-11-11');

性能
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',9,60,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',10,50,'2024-11-11 11:11:11','2024-11-11');
insert into AIEOM.device_tag_string (id,tagId,tagValue,updateTime,tagDate) values ('0525f5fb0992b9a9bfc663f9f0b1612a',11,50,'2024-11-11 11:11:11','2024-11-11');
863408527925824