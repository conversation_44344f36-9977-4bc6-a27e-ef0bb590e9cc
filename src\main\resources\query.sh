#!/bin/bash
tokenGet() {
#       1.透過深信服地址({{sxfAddress}})取的公鑰
#       2.透過地端ras實用接口，取得密碼 <-需開發地端
#       3.透過深信服地址({{sxfAddress}})及用戶名({{sxfUserName}})及第2步取得的密碼，獲取Token({{sxfToken}})
#       發送器邏輯
#       4.透過發送器儲存Token({{sxfToken}})到文件參數
#       5.刷新地端執行參數緩存
     curl -k -s -X POST -H "Content-Type: application/json" -d "$payload" https://{{sxfIp}}/janus/authenticate

     payload='{
           "auth": {
               "passwordCredentials": {
                   "username": "{{sxfUser}}",
                   "password": "{{sxfPassword}}"
               }
           }
       }'
       response=$(curl -k -s -X POST -H "Content-Type: application/json" -d "$payload" https://{{sxfIp}}/janus/authenticate)
       code=$(echo "$response" |  awk -F '[:,}"]' '{for(i=1;i<=NF;i++){if($i == "code") print $(i+2)}}' |head -1)
       if [[ "$code" == "0" ]]; then
            access_token_id=$(echo "$response" |  awk -F '[:,}"]' '{for(i=1;i<=NF;i++){if($i == "id") print $(i+3)}}' |head -1)
           echo "$access_token_id"
       else
           echo "请求失败"
       fi
}


queryAzs(){
    token=$(tokenGet)
    res=$(curl -k -s -X GET -H "Content-Type: application/json" -H "Authorization: Token ${token}"  https://*************/janus/20180725/azs)
    echo $res
}

result=$(queryAzs)
echo "$result"

curl -k -s -X POST -H "Content-Type: application/json" -d '{"auth": {"passwordCredentials": {"username": "openlab","password": "7962ca4ff7f46298cf0695426ed9dfb3664345b1bf17dbb37c4bed632ed7eae59abaa13ba2f3740a78fb7b9b1460cdb00f75bb1659be989af56971a75fdbd042e8289767f21df1a091cda01161551ce6910bfa6a3120af38b3dff06e53361aad8a6cdc7c65b6ff195f63df30bb60a2446c83b93e77f5db8c2f8980a4dce6852137dcbf8c6e6e074767beee18618a7ebb6714e558f03d6806e1826e7ba2f4090a8b7bf08bfa4b802c3b7c53859535f4310673dfb1e9918c93bd8318af0f09bef0a6bfc4b26cdcfa5465d34e124297fc19ac8a25646aa5b7c81395f9f3acf51a91cc691840d8d9197feb360217abf346c9c7e043b9d5c89991fbe00b45f729d8dd"}}}' https://*************/janus/authenticate

curl -k -s -X GET -H "Content-Type: application/json" -H "Authorization: Token bad8d56f6a1179ba7a024dd27c760183"  https://*************/janus/20180725/azs


