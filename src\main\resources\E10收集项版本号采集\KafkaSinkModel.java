package com.digiwin.escloud.model.warning;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class KafkaSinkModel {


    private String eid;
    private String deviceId;
    private String collectedTime;
    private String collectConfigId;
    private String uploadDataModelCode;
    private String deviceCollectDetailId;
    private String aiId;
    private String aiopsItem;
    private String flumeTimestamp;
    private BasicInfo BasicInfo;
    private ModelJson modelJson;



    @Data
    public static class BasicInfo{
        private String eid;
        private String deviceId;
        private String collectedTime;
        private String collectConfigId;
        private String uploadDataModelCode;
        private String deviceCollectDetailId;
        private String aiId;
        private String aiopsItem;
        private String flumeTimestamp;
    }

    @Data
    public static class ModelJson{
        private Map<String, Object> DataContent;
    }

}
