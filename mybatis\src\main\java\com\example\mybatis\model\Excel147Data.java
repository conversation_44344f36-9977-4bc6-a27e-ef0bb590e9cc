package com.example.mybatis.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class Excel147Data {
    @ExcelProperty(index = 0)
    private String companyName;

    @ExcelProperty(index = 2)
    private String serviceCode;

    @ExcelProperty(index = 4)

    private String startDate;

    @ExcelProperty(index = 5)

    private String endDate;

    @ExcelProperty(index = 6)
    private String moduleCode;

    @ExcelProperty(index = 7)
    private String status;

    @ExcelProperty(index = 9)
    private String responsiblePerson;
}
