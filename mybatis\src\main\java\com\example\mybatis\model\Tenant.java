package com.example.mybatis.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Tenant {
    //("主键")
    private long sid;
    //("租户id")
    private String id;
    //("租户名称")
    private String name;
    //("租户中文全称")
    private String customerFullNameCH;
    //("租户英文全称")
    private String customerFullNameEN;
    //("鼎捷客户客代")
    private String customer_id;
    //("税号")
    private String taxCode;
    //("租户状态")
    private int status;
}
