package com.digiwin.escloud.aiocdp.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 进阶规则分析规则交叉分组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@TableName("aio_cdp_group_advance_analysis_rule_crosstab")
@ApiModel(value = "AioCdpGroupAdvanceAnalysisRuleCrosstab对象", description = "进阶规则分析规则交叉分组")
public class AioCdpGroupAdvanceAnalysisRuleCrosstab implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("进阶分析规则id")
    private Long gaarId;

    private String analysisType;

    private String analysisTypeObjId;

    private String analysisCondition;

    private String analysisRowValue;

    private String analysisColValue;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGaarId() {
        return gaarId;
    }

    public void setGaarId(Long gaarId) {
        this.gaarId = gaarId;
    }

    public String getAnalysisType() {
        return analysisType;
    }

    public void setAnalysisType(String analysisType) {
        this.analysisType = analysisType;
    }

    public String getAnalysisTypeObjId() {
        return analysisTypeObjId;
    }

    public void setAnalysisTypeObjId(String analysisTypeObjId) {
        this.analysisTypeObjId = analysisTypeObjId;
    }

    public String getAnalysisCondition() {
        return analysisCondition;
    }

    public void setAnalysisCondition(String analysisCondition) {
        this.analysisCondition = analysisCondition;
    }

    public String getAnalysisRowValue() {
        return analysisRowValue;
    }

    public void setAnalysisRowValue(String analysisRowValue) {
        this.analysisRowValue = analysisRowValue;
    }

    public String getAnalysisColValue() {
        return analysisColValue;
    }

    public void setAnalysisColValue(String analysisColValue) {
        this.analysisColValue = analysisColValue;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AioCdpGroupAdvanceAnalysisRuleCrosstab{" +
            "id = " + id +
            ", gaarId = " + gaarId +
            ", analysisType = " + analysisType +
            ", analysisTypeObjId = " + analysisTypeObjId +
            ", analysisCondition = " + analysisCondition +
            ", analysisRowValue = " + analysisRowValue +
            ", analysisColValue = " + analysisColValue +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
