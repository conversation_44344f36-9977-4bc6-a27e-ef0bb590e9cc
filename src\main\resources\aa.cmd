$ErrorActionPreference = "stop"
[Threading.Thread]::CurrentThread.CurrentUICulture = 'en-US'
# $PSDefaultParameterValues['Out-File:Encoding'] = 'utf8'
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = [Text.UTF8Encoding]::UTF8
$errMsg=""
$nl = [Environment]::NewLine

function CheckCVE20200796{
 $verisenable = CheckWindowsVersion

 if ($verisenable -eq $true) {
 CheckIfUpdateIsInstalled
 } else {
 CheckRegSmbv3Compression
 }
}

function CheckWindowsVersion {
 try {
 $WindowsVersion = Get-ComputerInfo | Select-Object -ExpandProperty WindowsVersion
 if ($WindowsVersion -eq 1903) {
 return $true
 } elseif ($WindowsVersion -eq 1909) {
 return $true
 } else {
 return $false
 }
 } catch {
		$err = "CheckWindowsVersion error:" + $_.Exception.Message
		$errMsg = $errMsg + $err.replace('\','\\').replace('"','\"') + $nl
 return $false
 }
 return $false
}

function CheckIfUpdateIsInstalled {

 $fix = Get-HotFix -Id KB4551762 -ErrorAction SilentlyContinue

 if ($fix) {
	 $Res = "{""code"": ""0"" ,""message"" : ""Windows Update $($fix.HotFixID) is installed."", ""ps_code"": ""CVE-2020-0796"", ""data"": """", ""error_message"" : """+$errMsg+"""}"
		return $Res
 } else {
	 $Res = "{""code"": ""1"" ,""message"" : ""Windows Update $($kb) is not installed."", ""ps_code"": ""CVE-2020-0796"", ""data"": """", ""error_message"" : """+$errMsg+"""}"
		return $Res
 }
}

function SetkRegSmbv3Compression {
 param (
 [string]$reg = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters",
 [string]$value
 )
 try {
 Set-ItemProperty -Path $reg DisableCompression -Type DWORD -Value $value -Force
 } catch {
 $err = "SetkRegSmbv3Compression error:" + $_.Exception.Message
		$errMsg = $errMsg + $err.replace('\','\\').replace('"','\"') + $nl
 }
 CheckRegSmbv3Compression
}

function CheckRegSmbv3Compression {
 param (
 [string]$reg = "HKLM:\SYSTEM\CurrentControlSet\Services\LanmanServer\Parameters"
 )
	$check = -1
	try {
 $check = Get-ItemProperty -Path $reg -Name "DisableCompression" -ErrorAction SilentlyContinue
 } catch {
 $err = "CheckRegSmbv3Compression error:" + $_.Exception.Message
		$errMsg = $errMsg + $err.replace('\','\\').replace('"','\"') + $nl
 }

 if ($check -eq $null) {
		$Res = "{""code"": ""1"" ,""message"" : ""SMBv3 Compression is not configued."", ""ps_code"": ""CVE-2020-0796"", ""data"": """", ""error_message"" : """+$errMsg+"""}"
		return $Res
 } elseif ($check.DisableCompression -eq 0) {
	 $Res = "{""code"": ""1"" ,""message"" : ""SMBv3 Compression is set to enabled."", ""ps_code"": ""CVE-2020-0796"", ""data"": """", ""error_message"" : """+$errMsg+"""}"
		return $Res
 } elseif ($check.DisableCompression -eq 1) {
	 $Res = "{""code"": ""0"" ,""message"" : ""SMBv3 Compression is disabled."", ""ps_code"": ""CVE-2020-0796"", ""data"": """", ""error_message"" : """+$errMsg+"""}"
		return $Res
 }
}

CheckCVE20200796