package com.example.mybatis.model;

import com.example.mybatis.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;

@Data
public class EsclientContractSetting {
   //("客服代号")
    private String serviceCode;
   //("产品别")
    private String productCode;
   //("合约起-取至旧库mars_customerservice")
    private String contractStartDate;
   //("合约迄-取至旧库mars_customerservice")
    private String contractExpiryDate;
   //("是否是试用")
    private Boolean isTrial;
   //("客服工号")
    private String staffCode;
   //("主机可授权台数")
    private int hostAuth;
   //("终端可授权台数")
    private int clientAuth;
   //("SNMP可授权台数")
    private int snmpAuth;
   //("起始日-取至旧库esclient_auth")
    private String startDate;
   //("终止日-取至旧库esclient_auth")
    private String endDate;

   //(value = "获取模组代号(前端不关注)", hidden = true)
    @JsonIgnore()
    private String moduleCode;

    @JsonIgnore()
    public String getModuleCode() {
        return StringUtils.isBlank(moduleCode) ? this.productCode : this.moduleCode;
    }

   //(value = "获取实际起始日(前端不关注)", hidden = true)
    @JsonIgnore()
    public Optional<Date> getCurrentStartDate() {
        return getCurrentDateCore(this.startDate, this.contractStartDate);
    }

    private Optional<Date> getCurrentDateCore(String firstDateString, String secondDateString) {
        //firstDateString取的是esclient_auth
        //secondDateString取的是mars_customerservice
        //考虑同步逻辑，将优先取mars_customerservice(源头)，避免同步异常
        //firstDate的格式为yyyy-MM-dd HH:mm:ss
        //secondDate的格式为yyyyMMdd
        Optional<Date> optFirstDate = getDataByString(secondDateString, DateUtil.UNSIGNED_DATE_FORMATTER);
        if (optFirstDate.isPresent()) {
            return optFirstDate;
        }
        return getDataByString(firstDateString, DateUtil.DATE_TIME_FORMATTER);
    }

    private Optional<Date> getDataByString(String sourceString, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(sourceString)) {
            return Optional.empty();
        }
        return DateUtil.tryParseDate(sourceString, formatter);
    }

   //(value = "获取实际结束日(前端不关注)", hidden = true)
    @JsonIgnore()
    public Optional<Date> getCurrentEndDate() {
        return getCurrentDateCore(this.endDate, this.contractExpiryDate);
    }
}
