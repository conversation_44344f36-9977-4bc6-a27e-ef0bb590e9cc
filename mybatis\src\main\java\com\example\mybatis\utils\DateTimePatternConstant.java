package com.example.mybatis.utils;

public final class DateTimePatternConstant {
    /**
     * 大时区日期T时间格式(时区为+08:00等格式)
     */
    public final static String ZONED_ALL_DATE_T_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSSZZZZZ";
    /**
     * 时区日期T时间格式
     */
    public final static String ZONED_DATE_T_TIME = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
    /**
     * 日期T时间毫秒格式
     */
    public final static String DATE_T_TIME_MIL = "yyyy-MM-dd'T'HH:mm:ss.SSS";
    /**
     * 日期T时间格式
     */
    public final static String DATE_T_TIME = "yyyy-MM-dd'T'HH:mm:ss";

    /**
     * 时区日期时间毫秒格式(时区为+08:00等格式)
     */
    public final static String ZONED_ALL_DATE_TIME = "yyyy-MM-dd HH:mm:ss.SSSZZZZZ";
    /**
     * 时区日期时间毫秒格式
     */
    public final static String ZONED_DATE_TIME = "yyyy-MM-dd HH:mm:ss.SSSZ";
    /**
     * 日期时间毫秒格式
     */
    public final static String DATE_TIME_MIL = "yyyy-MM-dd HH:mm:ss.SSS";
    /**
     * 日期时间格式
     */
    public final static String DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式
     */
    public final static String ONLY_DATE = "yyyy-MM-dd";
    /**
     * 时间毫秒格式
     */
    public final static String ONLY_TIME_MIL = "HH:mm:ss.SSS";
    /**
     * 时间格式
     */
    public final static String ONLY_TIME = "HH:mm:ss";

    /**
     * 时区名称
     */
    public final static String ZONED_NAME = "Asia/Shanghai";

    /**
     * 无符号日期时间毫秒格式
     */
    public final static String UNSIGNED_DATE_TIME_MIL = "yyyyMMddHHmmssSSS";

    /**DATE_TIME
     * 无符号日期时间格式
     */
    public final static String UNSIGNED_DATE_TIME = "yyyyMMddHHmmss";

    /**
     * 无符号日期格式
     */
    public final static String UNSIGNED_DATE = "yyyyMMdd";

    /**
     * 无符号时间时分格式
     */
    public final static String UNSIGNED_TIME_HOUR_MINUTE = "HHmm";

    /**
     * 日期时分格式
     */
    public final static String DATE_TIME_HOUR_MINUTE = "yyyy-MM-dd HH:mm";
}
