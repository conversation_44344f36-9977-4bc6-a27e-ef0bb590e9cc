package com.example.mybatis.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONPath;
import com.example.mybatis.config.BaseService;
import com.example.mybatis.config.JdbcSqlInfo;
import com.example.mybatis.config.MybatisService;
import com.example.mybatis.model.*;
import com.example.mybatis.utils.LongUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;

import net.sf.jsqlparser.statement.select.*;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@RestController
@RequestMapping("mybatis")
@Slf4j
public class MybatisController {
    @Autowired
    private MybatisService mybatisService;
    @Autowired
    private BaseService baseService;


    @Autowired
    private RestTemplate restTemplate;
    private String testBigDataUrl = "https://hw-test-ddp-dataapi.digiwincloud.com.cn";

    private String bigDataUrl = "https://hw-ddp-dataapi.digiwincloud.com.cn";

    public List<Map<String, Object>> jdbcQueryTest(JdbcSqlInfo jdbcSqlInfo) {
        String url = testBigDataUrl + "/jdbc/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JdbcSqlInfo> request = new HttpEntity<>(jdbcSqlInfo, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public List<Map<String, Object>> jdbcQuery(JdbcSqlInfo jdbcSqlInfo) {
        String url = bigDataUrl + "/jdbc/sql/query";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JdbcSqlInfo> request = new HttpEntity<>(jdbcSqlInfo, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    public Integer srSaveTest(String sql) {
        String url = testBigDataUrl + "/sr/sql/save";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            return restTemplate.postForObject(url, request, Integer.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public Integer srSave(String sql) {
        String url = bigDataUrl + "/sr/sql/save";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            return restTemplate.postForObject(url, request, Integer.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    public Integer srSaveDelete(String sql) {
        String url = testBigDataUrl + "/sr/sql/save";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(sql, headers);
            return restTemplate.postForObject(url, request, Integer.class);
        } catch (Exception ex) {
            ex.printStackTrace();
            return 0;
        }
    }

    @GetMapping(value = "/findAll")
    public Object findAll() {
//        baseService.saveV3();
        baseService.saveV4();
        System.out.println();
        return null;
    }

    @GetMapping(value = "/addSwapCollect2LinuxDevice")
    public Object addSwapCollect2LinuxDevice(
            @RequestParam Long accId,
            @RequestParam Boolean isEnable,
            @RequestParam String token,
            @RequestParam String collectName
                                             ) {
        mybatisService.addSwapCollect2LinuxDevice(accId, isEnable, collectName,token);
        return "ok";
    }

    /**
     * update servicecloud.IndustryPlanQuickScreening set f_CPOTotalQuantity = 9 where  fs_CPOSurvey IS NOT NULL;
     * @return
     */
    @GetMapping(value = "/test")
    public Object test() {
        List<Map<String, Object>> maps = jdbcQuery(new JdbcSqlInfo("starrocks", " SELECT\n" +
                "   eid,\n" +
                "    (\n" +
                "        (CASE WHEN f_CPOHighStockLevel0101 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPORollingDemand0104 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPODailyPlanScheduling0105 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOToolForScheduling0106 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOCapacityScheduling0107 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOScheduleWithERP0110 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOSupplierDelivery0111 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOOrderDeliveryIssueOne0112 IS NOT NULL THEN 1 ELSE 0 END) + \n" +
                "        (CASE WHEN f_CPOOrderDeliveryIssueTwo0113 IS NOT NULL THEN 1 ELSE 0 END)\n" +
                "    ) AS f_CPOCompletedCount\n" +
                "FROM\n" +
                "    servicecloud.IndustryPlanQuickScreening\n" +
                "WHERE\n" +
                "    fs_CPOSurvey IS NOT NULL and f_CPOCompletedCount is not null\n"));
        AtomicInteger i = new AtomicInteger(1);
        maps.forEach(map -> {
            Long eid = Long.parseLong(map.get("eid").toString());
            Integer f_CPOCompletedCount = Integer.parseInt(map.get("f_CPOCompletedCount").toString());
            String updateSql = "update servicecloud.IndustryPlanQuickScreening set f_CPOCompletedCount = " + f_CPOCompletedCount + " where eid = " + eid + " AND fs_CPOSurvey IS NOT NULL;";
//            System.out.println(i + ":" + updateSql);
            System.out.println(updateSql);
            i.getAndIncrement();
//            srSave(updateSql);
        });
//        Map<Object, Map<String, Object>> oldMap = maps.stream().collect(Collectors.toMap(m -> m.get("SalesContact"), Function.identity()));
//        List<Map<String, Object>> maps1 = jdbcQueryTest(new JdbcSqlInfo("starrocks", "SELECT\n" +
//                "    escv2.SalesContact,\n" +
//                "    escv2.Sales,\n" +
//                "    GROUP_CONCAT(escv2.CustomerCode) AS CustomerCodeString,\n" +
//                "    GROUP_CONCAT(CAST(escv2.eId AS STRING)) AS eIdString,\n" +
//                "    GROUP_CONCAT(escv2.CustomerServiceCode) AS CustomerServiceCodeString,\n" +
//                "    GROUP_CONCAT(escv2.CustomerName) AS CustomerNameString\n" +
//                "FROM\n" +
//                "    servicecloud.es_customerservice_v2 escv2\n" +
//                " INNER JOIN (\n" +
//                "        SELECT\n" +
//                "            eId,\n" +
//                "            SalesContact,\n" +
//                "            Sales,\n" +
//                "            MAX(updateDate) AS MaxUpdateDate\n" +
//                "        FROM\n" +
//                "            servicecloud.es_customerservice_v2\n" +
//                "        GROUP BY\n" +
//                "            eId,\n" +
//                "            SalesContact,\n" +
//                "            Sales\n" +
//                "    ) AS max_dates\n" +
//                "                   ON escv2.eId = max_dates.eId AND escv2.updateDate = max_dates.MaxUpdateDate AND escv2.SalesContact = max_dates.SalesContact AND escv2.Sales = max_dates.Sales" +
//                " GROUP BY\n" +
//                "    escv2.SalesContact,\n" +
//                "    escv2.Sales;"));
//
//        Map<Object, Map<String, Object>> newMap = maps1.stream().collect(Collectors.toMap(m -> m.get("SalesContact"), Function.identity()));
//        oldMap.forEach((k, v) -> {
//            Map<String, Object> map = newMap.get(k);
//            if (Objects.nonNull(map) ) {
//                String[] eIdString = map.get("eIdString").toString().split(",");
//                for (int i = 0; i < eIdString.length; i++) {
//                    eIdString[i] = eIdString[i].trim();
//                }
//                Arrays.sort(eIdString);
//                String[] eIdString1 = v.get("eIdString").toString().split(",");
//                for (int i = 0; i < eIdString1.length; i++) {
//                    eIdString1[i] = eIdString1[i].trim();
//                }
//                Arrays.sort(eIdString1);
//                boolean equals = Arrays.equals(eIdString, eIdString1);
//                if (!equals){
//                    System.out.println("email : " + k + " \n, oldeIdString : " + Arrays.toString(eIdString1) + " \n, neweIdString : " + Arrays.toString(eIdString));
//
//                }
//
//            }
//
//
//        });
        return null;
    }

    @GetMapping(value = "/updateChatMessageRecord")
    public Object updateChatMessageRecord() {
        List<Map<String, Object>> maps = jdbcQuery(new JdbcSqlInfo("starrocks", "select distinct cmr2.eid,cmr2.sessionId from servicecloud.ChatMessageRecord cmr inner join\n" +
                "    (SELECT sessionId,get_json_string(REPLACE(get_json_string(question, '$.headerInfo'), '\\\\\"', '\"'),'$.eid') as eid\n" +
                " FROM servicecloud.ChatMessageRecord where get_json_string(REPLACE(get_json_string(question, '$.headerInfo'), '\\\\\"', '\"'),'$.eid') is not null) cmr2 on cmr.sessionId = cmr2.sessionId" +
                " where cmr.eid is null or cmr.eid = ''"));
        AtomicInteger i = new AtomicInteger(1);
        maps.forEach(map -> {
            Long eid = Long.parseLong(map.get("eid").toString());
            String sessionId = map.get("sessionId").toString();
            String updateSql = "update servicecloud.ChatMessageRecord set eid = " + eid + " where sessionId = '" + sessionId + "';";
            System.out.println(i + ":" + updateSql);
            System.out.println(updateSql);
            srSave(updateSql);
            i.getAndIncrement();
        });
        return null;
    }

    @GetMapping(value = "/deleteTest")
    public Object syncDataToTest() {
//        List<Map<String, Object>> maps = jdbcQuery(new JdbcSqlInfo("starrocks", "SELECT\n" +
//                "   * " +
//                "FROM\n" +
//                "    AIEOM.tenant_tag_string WHERE tagId IN ( 729636528575040,729646154027584,729520260567616,731424753066560,732102633046592 ) \n"));
//        int i = 0;
//        List<String> sList = buildInsertStatements("AIEOM.tenant_tag_string", maps);
//        for (String s:sList) {
//            srSaveTest(s);
//
//        }

        //同步hbase
//        for (Map<String, Object> m : maps) {
//            Long eid = Long.parseLong(m.get("eid").toString());
//            String SalesContact = m.get("SalesContact").toString();
//            String modelCode = "IndustryPlanQuickScreening";
//            String rowKey = eid + MR_ROW_KEY + SalesContact;
//            Object mrDetailByKeyPro = getMrDetailByKeyPro(modelCode, rowKey);
//            saveMrDetailTest(modelCode, SalesContact, mrDetailByKeyPro, eid);
//            i++;
//            System.out.println(i);
//        }
        String sql = " DELETE FROM servicecloud.Clue_Customer_Info  WHERE clueFootprintsId = '739937926992449' and customerId = '739937926992448' and productCode = '02' ";
        JdbcSqlInfo jdbcSqlInfo = new JdbcSqlInfo("starrocks", sql);
        srTestDelete(jdbcSqlInfo);
        return null;
    }

    public List<Map<String, Object>> srTestDelete(JdbcSqlInfo jdbcSqlInfo) {
        String url = testBigDataUrl + "/jdbc/sql/execute";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JdbcSqlInfo> request = new HttpEntity<>(jdbcSqlInfo, headers);
            List<Map<String, Object>> result = restTemplate.postForObject(url, request, List.class);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }

    @GetMapping(value = "/updateRowKey")
    public Object updateRowKey() {
        List<Map<String, Object>> maps = jdbcQuery(new JdbcSqlInfo("starrocks", "SELECT\n" +
                "   eid ,sqCfoCompletedCount , fscfo,sqCfoTotalQuantity " +
                "FROM\n" +
                "    servicecloud.IndustryPlanQuickScreening  \n"));
//        List<String> eidList = getEidList();
        int i = 0;
        for (Map<String, Object> m : maps) {
            Long eid = Long.parseLong(m.get("eid").toString());
            Integer sqCfoCompletedCount = Integer.parseInt(m.get("sqCfoCompletedCount").toString());
            if (Objects.isNull(m.get("sqCfoTotalQuantity"))) {
                continue;
            }
            Integer sqCfoTotalQuantity = Integer.parseInt(m.get("sqCfoTotalQuantity").toString());

            if (sqCfoTotalQuantity != 11){
                continue;
            }
            if (sqCfoCompletedCount == 11) {
                continue;
            }
            if (Objects.isNull(m.get("fscfo"))) {
                continue;
            }
            String fscfo = m.get("fscfo").toString();

            int retSqCfoCompletedCount = sqCfoCompletedCount + 1;
//            String SalesContact = m.get("SalesContact").toString();
            String modelCode = "IndustryPlanQuickScreening";
            String rowKey = eid + MR_ROW_KEY + eid ;
            Object mrDetailByKeyPro = getMrDetailByKeyPro(modelCode, rowKey);
//            saveMrDetailTest(modelCode, Long.toString(eid), mrDetailByKeyPro, eid);
            i++;
            if (mrDetailByKeyPro == null) {
                continue;
            }
            Object json = JSON.parse(mrDetailByKeyPro.toString());

//
            Object sqIPODisclosureDeadline = JSONPath.eval(json, "$.field.DataContent.fscfo.sqIPODisclosureDeadline");
//            Object sqCfoCompletedCount = JSONPath.eval(json, "$.field.DataContent.fscfo.sqCfoCompletedCount");
//            Object sqScraningDesc = JSONPath.eval(json, "$.field.DataContent.sqScraningDesc");

//            JSONPath.set(json, "$.field.DataContent.sqScraningDesc", "");
//
            if (Objects.nonNull(sqIPODisclosureDeadline)) {
                if (StringUtils.isEmpty(sqIPODisclosureDeadline.toString())) {
                    sqIPODisclosureDeadline = "0";
                }
                if (retSqCfoCompletedCount == 11) {
                    JSONPath.set(json, "$.field.DataContent.fscfo.sqIPODisclosureDeadline", sqIPODisclosureDeadline);
//                saveMrDetailPro(modelCode, eid+"", json.toString(), eid);
                    String replace = fscfo.replace("\"sqIPODisclosureDeadline\":\"\"", "\"sqIPODisclosureDeadline\":\"" + sqIPODisclosureDeadline.toString() + "\"");
                    String replace1 = replace.replace("\"sqCfoCompletedCount\":" + sqCfoCompletedCount, "\"sqCfoCompletedCount\":" + retSqCfoCompletedCount);
                    System.out.println("sqIPODisclosureDeadline-:" + sqIPODisclosureDeadline);
                    System.out.println("update servicecloud.IndustryPlanQuickScreening set sqIPODisclosureDeadline = " + sqIPODisclosureDeadline + " where eid = " + eid + " ;");
                    System.out.println("update servicecloud.IndustryPlanQuickScreening set sqCfoCompletedCount = " + retSqCfoCompletedCount + " where eid = " + eid + " ;");
                    System.out.println("update servicecloud.IndustryPlanQuickScreening set fscfo = '" + replace1 + "' where eid = " + eid + " ;");
                }

            }


        }


        return null;
    }
    @GetMapping(value = "/selectTenant")
    public void selectTenant(){
        List<String> deviceIds = mybatisService.selectMaintainDevice(null);
        List<String> deviceIdList = new ArrayList<>();
        List<Map<String, Object>> maps = jdbcQuery(new JdbcSqlInfo("starrocks", "select distinct eid from servicecloud.AccountSetInformation where collectedTime >='2025-04-01 00:00:00' and collectedTime<='2025-04-30 23:59:59'   and  eid in " +
                "('"+String.join("','",deviceIds)+"')"));

        for (Map<String, Object> map : maps) {
            deviceIdList.add(map.get("eid").toString());
        }

        List<String> deviceId = mybatisService.selectMaintainDevice(deviceIdList);
        List<Tenant> tenants = mybatisService.selectTenant(deviceId);
        for (Tenant tenant : tenants) {
            System.out.println(tenant.getSid()+","+tenant.getId()+","+tenant.getName());
        }

    }

    public static List<String> buildInsertStatements(String tableName, List<Map<String, Object>> maps) {
        if (maps == null || maps.isEmpty()) {
            throw new IllegalArgumentException("List of maps cannot be null or empty");
        }

        // 假设所有map都有相同的keys，所以我们只取第一个元素来确定列名
        Map<String, Object> firstMap = maps.get(0);
        List<String> columnNames = new ArrayList<>(firstMap.keySet());

        // 构建列名部分
        String columnsPart = String.join(", ", columnNames);

        // 分批构建VALUES部分，每批最多100条记录
        List<String> insertStatements = new ArrayList<>();
        final int batchSize = 100;
        for (int i = 0; i < maps.size(); i += batchSize) {
            // 计算当前批次的结束索引，不能超过列表大小
            int end = Math.min(i + batchSize, maps.size());
            List<String> valuesParts = maps.subList(i, end).stream().map(map -> {
                return map.values().stream()
                        .map(value -> formatValue(value))
                        .collect(Collectors.joining(", ", "(", ")"));
            }).collect(Collectors.toList());

            String valuesPart = String.join(", ", valuesParts);

            // 为当前批次构建完整的INSERT语句
            insertStatements.add("INSERT INTO " + tableName + " (" + columnsPart + ") VALUES " + valuesPart + ";");
        }

        return insertStatements;
    }

    private static String formatValue(Object value) {
        // 这里简单的处理，实际情况可能需要根据值的类型进行更复杂的转换
        if (value == null) {
            return "NULL";
        } else if (value instanceof String) {
            return "'" + value.toString().replace("'", "''") + "'";
        } else if (value instanceof Number) {
            return value.toString();
        } else {
            // 对于其他类型，我们可以根据需要进行转换
            return "'" + value.toString() + "'";
        }
    }


    public Object getMrDetailByKeyPro(String modelCode, String rowKey) {
        HashMap hashMap = restTemplate.getForObject(bigDataUrl + "/hbase/getByRowKey?tableName=" +
                modelCode + "&rowKey=" + rowKey, HashMap.class);
        if (!ObjectUtils.isEmpty(hashMap)) {
            return hashMap.get("model");
        } else {
            return null;
        }
    }

    public Object getMrDetailByKeyTest(String modelCode, String rowKey) {
        HashMap hashMap = restTemplate.getForObject(testBigDataUrl + "/hbase/getByRowKey?tableName=" +
                modelCode + "&rowKey=" + rowKey, HashMap.class);
        if (!ObjectUtils.isEmpty(hashMap)) {
            return hashMap.get("model");
        } else {
            return null;
        }
    }

    public static final String MR_ROW_KEY = "maintenanceRecord";

    public HashMap saveMrDetailPro(String modelCode, String id, Object targetValue, Long eid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tableName", modelCode);
        map.put("rowKey", eid + MR_ROW_KEY + id);
        map.put("cf", "info");
        map.put("cn", "model");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("model", targetValue);
        map.put("data", dataMap);
        return restTemplate.postForObject(bigDataUrl + "/hbase/save", map, HashMap.class);
    }

    public HashMap saveMrDetailTest(String modelCode, String id, Object targetValue, Long eid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("tableName", modelCode);
        map.put("rowKey", eid + MR_ROW_KEY + id);
        map.put("cf", "info");
        map.put("cn", "model");
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("model", targetValue);
        map.put("data", dataMap);
        return restTemplate.postForObject(testBigDataUrl + "/hbase/save", map, HashMap.class);
    }

    @GetMapping
    public void mainTest() {
        // 初始JSON结构字符串
        String initialJson = "[{\"fieldCode\":\"eid\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.eid\"},{\"fieldCode\":\"uploadDataModelCode\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.uploadDataModelCode\"},{\"fieldCode\":\"collectedTime\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"BasicInfo.collectedTime\"},{\"fieldCode\":\"f_riskCtrlRecruit0101\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_highEmploymentRisk.f_riskCtrlRecruit0101\"},{\"fieldCode\":\"f_contractDisputeAvoid0102\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_highEmploymentRisk.f_contractDisputeAvoid0102\"},{\"fieldCode\":\"f_hrAudit0103\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highEmploymentRisk.f_hrAudit0103\"},{\"fieldCode\":\"f_hrAuditResp0104\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_highEmploymentRisk.f_hrAuditResp0104\"},{\"fieldCode\":\"f_highEmploymentRisk\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highEmploymentRisk.f_highEmploymentRisk\"},{\"fieldCode\":\"f_lbrCostsChg0105\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highLaborCost.f_lbrCostsChg0105\"},{\"fieldCode\":\"f_talentMgmtCore0106\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_highLaborCost.f_talentMgmtCore0106\"},{\"fieldCode\":\"f_emplSatisfaction0107\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highLaborCost.f_emplSatisfaction0107\"},{\"fieldCode\":\"f_turnoverRate0108\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highLaborCost.f_turnoverRate0108\"},{\"fieldCode\":\"f_highLaborCost\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_highLaborCost.f_highLaborCost\"},{\"fieldCode\":\"f_emplCount0109\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_lowEfficiencyHR.f_emplCount0109\"},{\"fieldCode\":\"f_hrStaffNum0110\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_lowEfficiencyHR.f_hrStaffNum0110\"},{\"fieldCode\":\"f_hrDivision0111\",\"fieldType\":\"TEXT\",\"valuePath\":\"CHO.fs_lowEfficiencyHR.f_hrDivision0111\"},{\"fieldCode\":\"f_hrNumTrend0112\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_lowEfficiencyHR.f_hrNumTrend0112\"},{\"fieldCode\":\"f_lowEfficiencyHR\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"CHO.fs_lowEfficiencyHR.f_lowEfficiencyHR\"},{\"fieldCode\":\"f_paperContract0201\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_contractRiskControlDifficult.f_paperContract0201\"},{\"fieldCode\":\"f_contractRiskCtrl0202\",\"fieldType\":\"TEXT\",\"valuePath\":\"HumanResourcesManager.fs_contractRiskControlDifficult.f_contractRiskCtrl0202\"},{\"fieldCode\":\"f_contractRiskControlDifficult\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_contractRiskControlDifficult.f_contractRiskControlDifficult\"},{\"fieldCode\":\"f_hrAudit0203\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_multipleInspectionInstitutionsLowPassRate.f_hrAudit0203\"},{\"fieldCode\":\"f_auditOrgs0204\",\"fieldType\":\"TEXT\",\"valuePath\":\"HumanResourcesManager.fs_multipleInspectionInstitutionsLowPassRate.f_auditOrgs0204\"},{\"fieldCode\":\"f_auditPassRate0205\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_multipleInspectionInstitutionsLowPassRate.f_auditPassRate0205\"},{\"fieldCode\":\"f_auditDataMethod0206\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_multipleInspectionInstitutionsLowPassRate.f_auditDataMethod0206\"},{\"fieldCode\":\"f_multipleInspectionInstitutionsLowPassRate\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_multipleInspectionInstitutionsLowPassRate.f_multipleInspectionInstitutionsLowPassRate\"},{\"fieldCode\":\"f_annualWrkforcePlan0207\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_staffControlDiff.f_annualWrkforcePlan0207\"},{\"fieldCode\":\"f_wrkforcePlanCtrl0208\",\"fieldType\":\"TEXT\",\"valuePath\":\"HumanResourcesManager.fs_staffControlDiff.f_wrkforcePlanCtrl0208\"},{\"fieldCode\":\"f_staffControlDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_staffControlDiff.f_staffControlDiff\"},{\"fieldCode\":\"f_annualBudget0209\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_budgetControlDiff.f_annualBudget0209\"},{\"fieldCode\":\"f_budgetCtrl0210\",\"fieldType\":\"TEXT\",\"valuePath\":\"HumanResourcesManager.fs_budgetControlDiff.f_budgetCtrl0210\"},{\"fieldCode\":\"f_budgetControlDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_budgetControlDiff.f_budgetControlDiff\"},{\"fieldCode\":\"f_deptOvrtmCtrl0211\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_overtimeCtrlDiff.f_deptOvrtmCtrl0211\"},{\"fieldCode\":\"f_ovrtmPreReport0212\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_overtimeCtrlDiff.f_ovrtmPreReport0212\"},{\"fieldCode\":\"f_overtimeCtrlDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_overtimeCtrlDiff.f_overtimeCtrlDiff\"},{\"fieldCode\":\"f_paperAttendance0213\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_payrollPeriodLong.f_paperAttendance0213\"},{\"fieldCode\":\"f_manualAttendTrack0214\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_payrollPeriodLong.f_manualAttendTrack0214\"},{\"fieldCode\":\"f_payrollPeriodLong\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_payrollPeriodLong.f_payrollPeriodLong\"},{\"fieldCode\":\"f_overseasPay0215\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_overseasCompRisk.f_overseasPay0215\"},{\"fieldCode\":\"f_overseasCompRisk\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_overseasCompRisk.f_overseasCompRisk\"},{\"fieldCode\":\"f_paperPaySlip0216\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_HRSalaryCycleLong.f_paperPaySlip0216\"},{\"fieldCode\":\"f_pyrlCalcTime0217\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_HRSalaryCycleLong.f_pyrlCalcTime0217\"},{\"fieldCode\":\"f_hrSalaryCycleLong\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"HumanResourcesManager.fs_HRSalaryCycleLong.f_hrSalaryCycleLong\"},{\"fieldCode\":\"f_paperHandbook0301\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_paperDocCtrlDiff.f_paperHandbook0301\"},{\"fieldCode\":\"f_paperDocCtrlDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_paperDocCtrlDiff.f_paperDocCtrlDiff\"},{\"fieldCode\":\"f_infoCollectOnboard0302\",\"fieldType\":\"TEXT\",\"valuePath\":\"Specialist.fs_HRInfoCollectDiff.f_infoCollectOnboard0302\"},{\"fieldCode\":\"f_infoChgProcess0303\",\"fieldType\":\"TEXT\",\"valuePath\":\"Specialist.fs_HRInfoCollectDiff.f_infoChgProcess0303\"},{\"fieldCode\":\"f_hrInfoCollectDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_HRInfoCollectDiff.f_hrInfoCollectDiff\"},{\"fieldCode\":\"f_statusChgOffline0304\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_HRChangeProcComplex.f_statusChgOffline0304\"},{\"fieldCode\":\"f_hrChangeProcComplex\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_HRChangeProcComplex.f_hrChangeProcComplex\"},{\"fieldCode\":\"f_manualReportAnalys0305\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_reportDiff.f_manualReportAnalys0305\"},{\"fieldCode\":\"f_reportDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_reportDiff.f_reportDiff\"},{\"fieldCode\":\"f_clockingMethods0306\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_attDataCollectDiff.f_clockingMethods0306\"},{\"fieldCode\":\"f_accessCtrlBrand0307\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_attDataCollectDiff.f_accessCtrlBrand0307\"},{\"fieldCode\":\"f_attDataCollectDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_attDataCollectDiff.f_attDataCollectDiff\"},{\"fieldCode\":\"f_shiftTypeNum0308\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_shiftChangeFreq.f_shiftTypeNum0308\"},{\"fieldCode\":\"f_flexiShiftAvailable0309\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_shiftChangeFreq.f_flexiShiftAvailable0309\"},{\"fieldCode\":\"f_shiftChg0310\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_shiftChangeFreq.f_shiftChg0310\"},{\"fieldCode\":\"f_shiftChangeFreq\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_shiftChangeFreq.f_shiftChangeFreq\"},{\"fieldCode\":\"f_leaveQuotaMgmt0311\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_leaveCtrlDiff.f_leaveQuotaMgmt0311\"},{\"fieldCode\":\"f_leaveCtrlDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_leaveCtrlDiff.f_leaveCtrlDiff\"},{\"fieldCode\":\"f_attendSumConf0312\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_attConfirmSlow.f_attendSumConf0312\"},{\"fieldCode\":\"f_attConfirmSlow\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_attConfirmSlow.f_attConfirmSlow\"},{\"fieldCode\":\"f_overtimeVerif0313\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_OTHoursVerifyDiff.f_overtimeVerif0313\"},{\"fieldCode\":\"f_otHoursVerifyDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_OTHoursVerifyDiff.f_otHoursVerifyDiff\"},{\"fieldCode\":\"f_attendanceCompliance0314\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_factoryAuditLow.f_attendanceCompliance0314\"},{\"fieldCode\":\"f_auditDataPrepTime0315\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_factoryAuditLow.f_auditDataPrepTime0315\"},{\"fieldCode\":\"f_auditPassRate0316\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_factoryAuditLow.f_auditPassRate0316\"},{\"fieldCode\":\"f_factoryAuditLow\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_factoryAuditLow.f_factoryAuditLow\"},{\"fieldCode\":\"f_excelSalaryCalc0317\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_excelPRLinkDiff.f_excelSalaryCalc0317\"},{\"fieldCode\":\"f_payrollAdjustPost0318\",\"fieldType\":\"TEXT\",\"valuePath\":\"Specialist.fs_excelPRLinkDiff.f_payrollAdjustPost0318\"},{\"fieldCode\":\"f_excelPRLinkDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_excelPRLinkDiff.f_excelPRLinkDiff\"},{\"fieldCode\":\"f_pieceRateAvailable0319\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_pieceworkOutputDiff.f_pieceRateAvailable0319\"},{\"fieldCode\":\"f_pieceWorkReporting0320\",\"fieldType\":\"TEXT\",\"valuePath\":\"Specialist.fs_pieceworkOutputDiff.f_pieceWorkReporting0320\"},{\"fieldCode\":\"f_excelPieceRateCalc0321\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_pieceworkOutputDiff.f_excelPieceRateCalc0321\"},{\"fieldCode\":\"f_pieceworkOutputDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_pieceworkOutputDiff.f_pieceworkOutputDiff\"},{\"fieldCode\":\"f_taxCalcMethod0322\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_taxCalcDiff.f_taxCalcMethod0322\"},{\"fieldCode\":\"f_taxCalcDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_taxCalcDiff.f_taxCalcDiff\"},{\"fieldCode\":\"f_salarySettlTime0323\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_salaryCycleLong.f_salarySettlTime0323\"},{\"fieldCode\":\"f_salaryCycleLong\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_salaryCycleLong.f_salaryCycleLong\"},{\"fieldCode\":\"f_paySlipSignConf0324\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_paySlipConfidLow.f_paySlipSignConf0324\"},{\"fieldCode\":\"f_paySlipConfidLow\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_paySlipConfidLow.f_paySlipConfidLow\"},{\"fieldCode\":\"f_ptCostShare0325\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_partTimeCostAllocDiff.f_ptCostShare0325\"},{\"fieldCode\":\"f_partTimeCostAllocDiff\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"Specialist.fs_partTimeCostAllocDiff.f_partTimeCostAllocDiff\"},{\"fieldCode\":\"ThemeApplayStatus\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.ThemeApplayStatus\"},{\"fieldCode\":\"ThemeLastUpdateTime\",\"fieldType\":\"DATETIME\",\"valuePath\":\"DataContent.ThemeLastUpdateTime\"},{\"fieldCode\":\"ThemeApplySourceCode\",\"fieldType\":\"VARCHAR\",\"valuePath\":\"DataContent.ThemeApplySourceCode\"},{\"fieldCode\":\"f_surveyCompletedCount\",\"fieldType\":\"INT\",\"valuePath\":\"CountGroup.f_surveyCompletedCount\"},{\"fieldCode\":\"f_surveyTotalQuantity\",\"fieldType\":\"INT\",\"valuePath\":\"CountGroup.f_surveyTotalQuantity\"}]";

        // 将字符串转换为JSON对象
        JSONObject transformedJson = transformJson(initialJson);

        // 打印转换后的JSON字符串
        System.out.println(transformedJson.toString(2)); // 使用2个空格缩进来格式化
    }

    public static JSONObject transformJson(String jsonStr) {
        // 解析初始JSON结构
        JSONArray jsonArray = new JSONArray(jsonStr);

        // 创建一个新的JSONObject用于存放转换后的结果
        JSONObject result = new JSONObject();

        // 遍历JSON数组中的每一个元素
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject field = jsonArray.getJSONObject(i);
            String fieldCode = field.getString("fieldCode"); // 字段名
            String valuePath = field.getString("valuePath"); // 值路径

            // 分割值路径以构建嵌套的JSONObject
            String[] pathParts = valuePath.split("\\.");
            JSONObject current = result;
            for (int j = 0; j < pathParts.length - 1; j++) {
                String part = pathParts[j];
                if (!current.has(part)) {
                    current.put(part, new JSONObject());
                }
                current = current.getJSONObject(part);
            }

            // 在最终的位置上放置字段值（这里假设值为"XXX"）
            current.put(pathParts[pathParts.length - 1], "XXX");
        }

        return result;
    }

    private List<String> getEidList(){
        String aa = "41320794042944";
        return Arrays.asList(aa.split("\n"));
    }

    public static void main(String[] args) {
        try {
            // 已经解析好的 SQL 语句
            Statement parse = CCJSqlParserUtil.parse("SELECT adpm.id AS adpmId, " +
                    "sp.productCode, sp.productCategory, sp.productName, " +
                    "apa.modelCode, " +
                    "IFNULL(cm.modelName, '') AS modelName " +
                    "FROM aiops_device_product_mapping adpm " +
                    "INNER JOIN (SELECT * FROM aiops_product_app where id = 11 AND code='2333') apa ON apa.id = adpm.apaId " +
                    "LEFT JOIN cmdb_model cm ON cm.modelCode = apa.modelCode " +
                    "AND (apa.modelRelateCode IS NOT NULL AND apa.modelRelateCode != '') " +
                    "LEFT JOIN supplier_product sp ON sp.id = apa.spId " +
                    "WHERE adId = 11 AND sp.id = 113 AND apa.modelCode = 'CCC' " +
                    "ORDER BY apa.modelCode LIMIT 10,10");

            PlainSelect plainSelect = (PlainSelect) ((Select) parse).getSelectBody();

            // 获取列别名
            getAliases(plainSelect.getSelectItems());

            // 获取表别名
            getTableAliases(plainSelect.getFromItem());
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
    }

    private static void getAliases(List<SelectItem<?>> selectItems) {
        for (SelectItem<?> item : selectItems) {

            Expression expression = item.getExpression();
            Alias alias = item.getAlias();
            System.out.println("Expression: " + expression + ", Alias: " + (alias != null ? alias.getName() : "None"));
        }
    }

    private static void getTableAliases(FromItem fromItem) {
        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            System.out.println("Table Name: " + table.getName() + ", Alias: " + (table.getAlias() != null ? table.getAlias().getName() : "None"));
        } else if (fromItem instanceof Join) {
            Join join = (Join) fromItem;
//            getTableAliases(join.get());
            getTableAliases(join.getRightItem());
        }
    }


    @GetMapping(value = "/addSwapCollect2LinuxDeviceV2")
    public Object addSwapCollect2LinuxDeviceV2(
            @RequestParam Long accId,
            @RequestParam Boolean isEnable,
            @RequestParam String token,
            @RequestParam int num,
            @RequestParam int size,
            @RequestParam String collectName,
            @RequestParam String execModel
    ) throws Exception {
        mybatisService.addSwapCollect2LinuxDeviceV2(accId, isEnable, collectName, token, num, size,execModel);
        return "ok";
    }

    @GetMapping(value = "/deviceAccEnable")
    public Object deviceAccEnable(
            @RequestParam Boolean isEnable,
            @RequestParam String token
    ) throws Exception {
        mybatisService.deviceAccEnable(isEnable, token);
        return "ok";
    }

    @DeleteMapping(value = "/deleteConfigDevice")
    public Object deleteConfigDevice(
            @RequestParam String token,
            @RequestBody List<String> deviceList
    ) throws Exception {
        mybatisService.deleteConfigDevice(token,deviceList);
        return "ok";
    }

    @GetMapping(value = "/sr/historyDataFix")
    public Map<String, Object> historyDataFix(String tableName) {
        Map<String, Object> result = new HashMap<>();
        try {
            // todo 先备份数据
            String sql = "select bak.orderId bId,at.orderId aId from AIEOM.AiopsTenant at INNER JOIN AIEOM.AiopsTenant_bak_20241204 bak ON bak.eid = at.eid";
            List<TagHistoryData> thdList = jdbcQueryTest(new JdbcSqlInfo("starrocks",sql))
                    .stream().map(e -> {
                        long bid = LongUtil.objectToLong(e.get("bId"));
                        long aid = LongUtil.objectToLong(e.get("aId"));
                        TagHistoryData thd = new TagHistoryData();
                        thd.setAId(aid);
                        thd.setBId(bid);
                        return thd;
                    }).collect(Collectors.toList());
            String bIdList = thdList.stream().map(i -> String.valueOf(i.getBId())).collect(Collectors.joining(","));
            Map<String, String> thdMap = thdList.stream()
                    .collect(Collectors.toMap(i->String.valueOf(i.getBId()), k->String.valueOf(k.getAId()),(o,n)->n));

            int pageSize = 100; // 每页查询的记录数
            int pageNumber = 0; // 当前页码

            while (true) {
                String sql2 = "select tagId,tagValue,tagDate,BITMAP_TO_STRING(uv) as bitmapString,updateTime from AIEOM." + tableName +
                        " where tagId = 100000000000033 and updateTime = '2024-12-08 21:32:38' and bitmap_has_any(uv,bitmap_from_string('" + bIdList + "')) ORDER BY tagId,tagValue,tagDate" +
                        " limit " + pageSize + " offset " + (pageNumber * pageSize);
                List<TagHistoryData.TagHistoryDataDetail> currentBatch =jdbcQueryTest(new JdbcSqlInfo("starrocks",sql2))
                        .stream().map(e -> {
                            TagHistoryData.TagHistoryDataDetail thdd = new TagHistoryData.TagHistoryDataDetail();
                            thdd.setTagId(LongUtil.objectToLong(e.get("tagId")));
                            thdd.setTagValue(e.get("tagValue").toString());
                            thdd.setBitmapString(e.get("bitmapString").toString());
                            thdd.setTagDate(e.get("tagDate").toString());
                            thdd.setUpdateTime(e.get("updateTime").toString());
                            return thdd;
                        }).collect(Collectors.toList());

                if (currentBatch.isEmpty()) {
                    break; // 如果当前批次为空，说明没有更多数据，退出循环
                }


                for (TagHistoryData.TagHistoryDataDetail thdd : currentBatch) {
                    String insertSql = "insert into AIEOM." + tableName + "(tagId,tagValue,tagDate,uv,updateTime) values";
                    String[] split = thdd.getBitmapString().split(",");
                    List<String> memoBitmap = new ArrayList<>();
                    for (String s : split) {
                        if (thdMap.containsKey(s)) {
                            memoBitmap.add(thdMap.get(s));
                        }
                    }
                    String bitMapString = String.join(",", memoBitmap);
                    thdd.setBitmapString(bitMapString);
                    String deleteSql = "delete from AIEOM." + tableName + " WHERE tagId=" + thdd.getTagId()
                            + " and tagValue = '" + thdd.getTagValue() + "'" + " and tagDate = '" + thdd.getTagDate() + "'";
                    srSaveDelete(deleteSql);
                    insertSql += " (" + thdd.getTagId() + ",'" + thdd.getTagValue() + "','" + thdd.getTagDate() + "', bitmap_from_string('" + thdd.getBitmapString() + "'),'" + thdd.getUpdateTime() + "')";
                    log.info("thdd  ,tagValue={},tagDate={},tagId={}", thdd.getTagValue(), thdd.getTagDate(), thdd.getTagId());
                    StopWatch one = new StopWatch();
                    one.start();
                    srSaveTest(insertSql);
                    one.stop();
                    log.info("thdd cost:{}s", one.getTotalTimeSeconds());
                }

                pageNumber++; // 处理完当前页，继续下一页
            }
//
            result.put("code","0");
            result.put("data",true);
            return result;
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("code","1");
            result.put("data",ex.getMessage());
            return result;
        }
    }

    @GetMapping(value = "/read147ProductLineTmc")
    public Object read147ProductLineTmc(
    ) throws Exception {
        mybatisService.read147ProductLineTmc();
        return "ok";
    }


}
