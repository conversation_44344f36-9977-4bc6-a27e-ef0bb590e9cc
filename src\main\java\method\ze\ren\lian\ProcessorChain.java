package method.ze.ren.lian;

import java.util.ArrayList;
import java.util.List;

public class Processor<PERSON>hain {

    private List<Processor> processorList = new ArrayList<>();

    private int index;

    public ProcessorChain addProcessor(Processor processor){
        processorList.add(processor);
        return this;
    }

    public boolean process(Product product, ProcessorChain processorChain) {
        if (index == processorList.size()) {
            return true;
        }
        Processor processor = processorList.get(index);
        index++;
        return processor.process(product, processorChain);
    }
}
