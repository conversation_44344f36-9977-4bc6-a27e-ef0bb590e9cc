package com.example.mybatis.model;

import lombok.Data;

import java.util.List;

@Data
public class ModuleCollectLayered {
    List<ModuleCollectMapping> moduleCollectMappingList;

    @Data
    public static class ModuleCollectMapping {
        public ModuleCollectMapping(Long accId, Boolean isEnable, String collectName,String execParamsModelCode, String execParamsContent) {
            this.accId = accId;
            this.isEnable = isEnable;
            this.collectName = collectName;
            this.execParamsModelCode = execParamsModelCode;
            this.execParamsContent = execParamsContent;
        }

        public ModuleCollectMapping(Long accId, Boolean isEnable, String collectName) {
            this.accId = accId;
            this.isEnable = isEnable;
            this.collectName = collectName;
        }

        private Long accId;
        private Boolean isEnable;


        private String collectName;
        private String execParamsModelCode;
        private String execParamsContent;
    }
}
