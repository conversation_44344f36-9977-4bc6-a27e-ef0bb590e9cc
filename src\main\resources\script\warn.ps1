
function TokenGet {
    $payload = @{
        auth = @{
            passwordCredentials = @{
                username = 'openlab'
                password = '7962ca4ff7f46298cf0695426ed9dfb3664345b1bf17dbb37c4bed632ed7eae59abaa13ba2f3740a78fb7b9b1460cdb00f75bb1659be989af56971a75fdbd042e8289767f21df1a091cda01161551ce6910bfa6a3120af38b3dff06e53361aad8a6cdc7c65b6ff195f63df30bb60a2446c83b93e77f5db8c2f8980a4dce6852137dcbf8c6e6e074767beee18618a7ebb6714e558f03d6806e1826e7ba2f4090a8b7bf08bfa4b802c3b7c53859535f4310673dfb1e9918c93bd8318af0f09bef0a6bfc4b26cdcfa5465d34e124297fc19ac8a25646aa5b7c81395f9f3acf51a91cc691840d8d9197feb360217abf346c9c7e043b9d5c89991fbe00b45f729d8dd'
            }
        }
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri https://*************/janus/authenticate -Method POST -ContentType "application/json" -Body $jsonBody -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.access.token.id
    } else {
        Write-Output $response
    }
}

function QueryWarn {
    # 调用函数并获取返回值
    $token = TokenGet
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $myArray = New-Object System.Collections.ArrayList
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "https://*************/janus/20180725/alarms?page_num=$next_page_num&page_size=1000" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            $myArray.AddRange($jsonObject.data.data)
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}
$aa=QueryWarn
$result = Write-Output $aa | ConvertTo-Json
Write-Output $result
