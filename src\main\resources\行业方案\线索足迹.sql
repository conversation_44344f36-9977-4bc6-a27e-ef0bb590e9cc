CREATE TABLE `CdpClueFootprintsData` (
                                         `phoneNumber` varchar(65533) NOT NULL COMMENT "",
                                         `courseDate` date NOT NULL COMMENT "",
                                         `courseName` varchar(65533) NOT NULL COMMENT "",
                                         `eid` varchar(65533) NOT NULL COMMENT "",
                                         `deleteFlag` int(11) NOT NULL COMMENT "",
                                         `deviceId` varchar(65533) NULL COMMENT "",
                                         `ID` varchar(65533) NULL COMMENT "",
                                         `collectedTime` varchar(65533) NULL COMMENT "",
                                         `collectConfigId` varchar(65533) NULL COMMENT "",
                                         `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                         `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
                                         `aiId` varchar(65533) NULL COMMENT "",
                                         `aiopsItem` varchar(65533) NULL COMMENT "",
                                         `flumeTimestamp` varchar(65533) NULL COMMENT "",
                                         `lecturer` varchar(65533) NULL COMMENT "",
                                         `ip` varchar(65533) NULL COMMENT "",
                                         `wechatNickname` varchar(65533) NULL COMMENT "",
                                         `realName` varchar(65533) NULL COMMENT "",
                                         `company` varchar(65533) NULL COMMENT "",
                                         `role` varchar(65533) NULL COMMENT "",
                                         `registrationDate` datetime NULL COMMENT "",
                                         `registeredStatus` int(11) NULL COMMENT "",
                                         `attendedStatus` int(11) NULL COMMENT "",
                                         `registrationTime` datetime NULL COMMENT "",
                                         `totalViewDuration` varchar(65533) NULL COMMENT "",
                                         `liveViewDuration` varchar(65533) NULL COMMENT "",
                                         `lastLiveClient` varchar(65533) NULL COMMENT "",
                                         `lastLoginCity` varchar(65533) NULL COMMENT "",
                                         `replayViewDuration` varchar(65533) NULL COMMENT "",
                                         `replayViewCount` int(11) NULL COMMENT "",
                                         `registrationChannel` varchar(65533) NULL COMMENT "",
                                         `registrationMethod` varchar(65533) NULL COMMENT "",
                                         `clickedLive` int(11) NULL COMMENT "",
                                         `clickedReplay` int(11) NULL COMMENT "",
                                         `browseCount` int(11) NULL COMMENT "",
                                         `shareCount` int(11) NULL COMMENT "",
                                         `isFavorite` int(11) NULL COMMENT "",
                                         `documentViewCount` int(11) NULL COMMENT "",
                                         `documentDownloadCount` int(11) NULL COMMENT "",
                                         `updateTime` datetime NULL COMMENT "",
                                         `cdpOpenId` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`phoneNumber`, `courseDate`, `courseName`, `eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`phoneNumber`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);