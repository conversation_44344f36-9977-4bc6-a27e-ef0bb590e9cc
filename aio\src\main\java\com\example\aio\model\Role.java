package com.example.aio.model;




public class Role {
    private long sid;

    private String id;

    private String name;

    private boolean visible; //是否可在租戶可以看到這個角色

    public Role(){}
    public Role(String id, String name, boolean visible) {
        this.id = id;
        this.name = name;
        this.visible = visible;
    }

    public long getSid() {
        return sid;
    }

    public void setSid(long sid) {
        this.sid = sid;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }
}
