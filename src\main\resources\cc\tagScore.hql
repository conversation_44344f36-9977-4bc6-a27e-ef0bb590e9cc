CREATE TABLE `tenant_tag_string` (
                                     `id` varchar(255) NOT NULL COMMENT "",
                                     `tagId` bigint(20) NOT NULL COMMENT "",
                                     `tagValue` varchar(255) NOT NULL COMMENT "",
                                     `updateTime` datetime NULL COMMENT "",
                                     `tagDate` date NOT NULL COMMENT ""
) ENGINE=OLAP
    UNIQUE KEY(`id`, `tagId`, `tagValue`, `updateTime`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "false",
"replication_num" = "1"
);

CREATE TABLE `tenant_tag_score` (
                                     `id` varchar(255) NOT NULL COMMENT "",
                                     `tagId` bigint(20) NOT NULL COMMENT "",
                                     `score` int NOT NULL COMMENT "",
                                     `updateTime` datetime NULL COMMENT "",
                                     `tagDate` date NOT NULL COMMENT ""
) ENGINE=OLAP
    UNIQUE KEY(`id`, `tagId`, `score`, `updateTime`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`tagId`) BUCKETS 8
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "false",
"replication_num" = "1"
);


