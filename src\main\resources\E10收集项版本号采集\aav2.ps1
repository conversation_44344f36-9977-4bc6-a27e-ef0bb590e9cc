
$result = @{
    e10_version = ""
    e10_pfversion = ""
    errmsg = ""
}
$serviceName = "Digiwin.E10.APService"
$service = Get-CimInstance -Class Win32_Service -Filter "Name='$serviceName'"
$service = Get-CimInstance -Class Win32_Service -Filter "Name=Digiwin.E10.APService"
$service = Get-WmiObject -Class Win32_Service -Filter "Name=Digiwin.E10.APService"
if ($service) {
    $exePath = $service.PathName.Trim('"')
    $directory = Split-Path -Path $exePath -Parent
    $pathParts = $directory -split '\\'
    $e10Index = [array]::IndexOf($pathParts, "E10")
    if ($e10Index -ne -1) {
        $rootDirParts = $pathParts[0..$e10Index]
        $rootDir = $rootDirParts -join '\'
        $newPath = Join-Path -Path $rootDir -ChildPath "Server"
        $newPath = Join-Path -Path $newPath -ChildPath "Application"
        $versionFilePath = Join-Path -Path $newPath -ChildPath "Version.xml"
        if (Test-Path $versionFilePath) {
            [xml]$xmlContent = Get-Content -Path $versionFilePath
            $versionValue = $xmlContent.SelectSingleNode("//Version").InnerText
            $pfVersionValue = $xmlContent.SelectSingleNode("//PFVersion").InnerText
            $result.e10_version = $versionValue
            $result.e10_pfversion = $pfVersionValue
        } else {
            $result.errmsg = "Version file not found: $versionFilePath"
        }
    } else {
        $result.errmsg = "Unable to resolve service path."
    }
} else {
    $result.errmsg = "No service found with service name: $serviceName"
}

$result | ConvertTo-Json -Depth 3