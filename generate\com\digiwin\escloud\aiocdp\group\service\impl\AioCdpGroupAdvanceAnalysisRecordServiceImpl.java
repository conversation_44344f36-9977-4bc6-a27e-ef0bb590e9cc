package com.digiwin.escloud.aiocdp.group.service.impl;

import com.digiwin.escloud.aiocdp.group.entity.AioCdpGroupAdvanceAnalysisRecord;
import com.digiwin.escloud.aiocdp.group.mapper.AioCdpGroupAdvanceAnalysisRecordMapper;
import com.digiwin.escloud.aiocdp.group.service.IAioCdpGroupAdvanceAnalysisRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 进阶分析记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Service
public class AioCdpGroupAdvanceAnalysisRecordServiceImpl extends ServiceImpl<AioCdpGroupAdvanceAnalysisRecordMapper, AioCdpGroupAdvanceAnalysisRecord> implements IAioCdpGroupAdvanceAnalysisRecordService {

}
