1.修改一下api的sql查询 支持注解设置操作权限
    1.) /aiouser/api/iamuser/getSupplierAiopsModuleInfo  没有关联tenant表 没有关联mc表，但是有tmc表 是否可以用tmc中的eid
    2.) /userv2/cdp/customer/stats 关联mc 可以
    3.) /aiouser/cdp/security/eid/cnt 没有关联tenant表 没有关联mc表 ，是否可以用 aiops_db_report_record eid
    4.) /aiouser/cdp/health/eid/cnt  没有关联tenant表 没有关联mc表 ，是否可以用 data_examination_report_record eid
    5.) /userv2/cdp/risk/customer/cnt 关联mc 可以
    6.) /aiobasic/vulnerability/project/selectVulnerabilityProject(aieom sp) 是否可以用vulnerability_project eid
    7.) /aiouser/api/tenant/searchTenantList(aieom sp) 关联tenant ，但是1.)这个api有其他权限（是否关注）2.)会根据当前isv查询对应eid
    8.) /aiobasic/vulnerability/project/scan/scanDataSelectGet(aieom sp) 查询专案的执行时间，是根据当前isv对应的eid查询的
    9.) /aiobasic/vulnerability/project/scan/scanDataCount(aieom sp) 是根据当前isv对应的eid查询的
    10.) /aiobasic/vulnerability/asset/assetGet(aieom sp) vulnerability_asset中的eid
    11.) /aiobasic/vulnerability/report/reportGet(aieom sp) 是根据当前isv对应的eid查询的
    12.) /aiouser/api/tenant/module/contracts(aieom sp)  Sql很复杂，（需要改动可以支持）
    13.) /userv2/cdp/customer/list Sql很复杂，（需要改动可以支持）
    14.) /userv2/cdp/cdpClueFootprintsGet 关联mc 可以
    15.) /userv2/cdp/cdpContactsGet 关联mc 可以
    16.) /userv2/customer/getCustomerSurvey 关联mc 可以
    17.) /userv2/cdp/getCustomersByUserSid 关联mc 可以
    18.) /aioitms/data/examination/report/record/reportRecordGet(aieom sp)  data_examination_report_record eid
2.删除曾经设置的数据权限
3.修改gateway代码 支持不同平台设置
4.遇到FROM是复杂查询的 将条件第一个where