import cn.hutool.core.collection.ListUtil;
import com.yomahub.liteflow.slot.DefaultContext;
import com.example.liteflow.service.vo.Person;
import com.yomahub.liteflow.script.ScriptExecuteWrap;
import com.yomahub.liteflow.script.body.JaninoCommonScriptBody;

import java.util.List;

public class Demo implements JaninoCommonScriptBody {
    public Void body(ScriptExecuteWrap wrap) {
        DefaultContext ctx = (DefaultContext) (wrap.getCmp().getFirstContextBean());
        List<Person> personList = ListUtil.toList(new Person("jack", 15000), new Person("tom", 23500), new Person("peter", 18500));
        int totalSalary = 0;
        for (Person person : personList) {
            totalSalary += person.getSalary();
        }
        System.out.println(totalSalary);
        ctx.setData("salary", totalSalary);
        return null;
    }
}