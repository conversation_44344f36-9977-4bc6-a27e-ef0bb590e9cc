# nginx docker安装

### 1. 首先运行带有标签的目标容器 `docker run --rm -it -d  --label=sh.acme.autoload.domain=www.520128.xyz   nginx:latest`
### 2. 在容器中运行  `docker run --rm  -itd  -v ./acmeout:/acme.sh --net=host --name=acme.sh -v c:/nginx/var/run/docker.sock:/var/run/docker.sock neilpang/acme.sh daemon`
### 3. 我们先颁发一个证书  `docker  exec -e CF_Email=<EMAIL> -e CF_Key=1234566 acme.sh --issue -d www.520128.xyz  --dns dns_cf`
### 4. 我们先颁发一个证书  `docker  exec -e DEPLOY_DOCKER_CONTAINER_LABEL=sh.acme.autoload.domain=www.520128.xyz   -e DEPLOY_DOCKER_CONTAINER_KEY_FILE=/etc/nginx/ssl/www.520128.xyz/key.pem   -e DEPLOY_DOCKER_CONTAINER_CERT_FILE="/etc/nginx/ssl/www.520128.xyz/cert.pem"   -e DEPLOY_DOCKER_CONTAINER_CA_FILE="/etc/nginx/ssl/www.520128.xyz/ca.pem"   -e DEPLOY_DOCKER_CONTAINER_FULLCHAIN_FILE="/etc/nginx/ssl/www.520128.xyz/full.pem"   -e DEPLOY_DOCKER_CONTAINER_RELOAD_CMD="service nginx force-reload"   acme.sh --deploy -d www.520128.xyz  --deploy-hook docker`

### 5. 安装 nginx 和 acme.sh（无限续期https证书），准备 docker-compose.yml 文件
```yaml
services:
  web:
    image: nginx
    container_name: nginx
    volumes:
      - ngconf:/etc/nginx
    ports:
      - "8000:80"
    labels:
      - sh.acme.autoload.domain=www.520128.xyz

  acme.sh:
    image: neilpang/acme.sh
    container_name: acme.sh
    command: daemon
    volumes:
      - ./acmeout:/acme.sh
      - ./var/run/docker.sock:/var/run/docker.sock
    environment:
      - DEPLOY_DOCKER_CONTAINER_LABEL=sh.acme.autoload.domain=www.520128.xyz
      - DEPLOY_DOCKER_CONTAINER_KEY_FILE=/etc/nginx/ssl/example.com/key.pem
      - DEPLOY_DOCKER_CONTAINER_CERT_FILE="/etc/nginx/ssl/example.com/cert.pem"
      - DEPLOY_DOCKER_CONTAINER_CA_FILE="/etc/nginx/ssl/example.com/ca.pem"
      - DEPLOY_DOCKER_CONTAINER_FULLCHAIN_FILE="/etc/nginx/ssl/example.com/full.pem"
      - DEPLOY_DOCKER_CONTAINER_RELOAD_CMD="service nginx force-reload"


volumes:
  ngconf:
 
```
### 2. 执行以下命令启动服务：`docker compose up -d`

### 3. http验证正式 `docker exec acme.sh --issue -d www.520128.xyz --standalone --debug`
### 4. 给nginx安装证书 `docker exec acme.sh --install-cert -d www.520128.xyz --key-file C:/nginx/key.pem --fullchain-file C:/nginx/cert.pem --reloadcmd "service nginx reload"`  




