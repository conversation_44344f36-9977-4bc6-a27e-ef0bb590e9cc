
CREATE EXTERNAL TABLE `es_customerservice_v2_external` (
  `SalesContact` varchar(100) NOT NULL COMMENT "",
  `eId` bigint(20) NOT NULL COMMENT "",
  `CustomerCode` varchar(100) NOT NULL COMMENT "",
  `grp_dpt_id` varchar(255) NULL COMMENT "",
  `grp_dpt_name` varchar(255) NULL COMMENT "",
  `grp_dpt_manager` varchar(255) NULL COMMENT "",
  `bus_dpt_id` varchar(255) NULL COMMENT "",
  `bus_dpt_name` varchar(255) NULL COMMENT "",
  `bus_dpt_manager` varchar(255) NULL COMMENT "",
  `dpt_id` varchar(255) NULL COMMENT "",
  `dpt_name` varchar(255) NULL COMMENT "",
  `dpt_manager` varchar(255) NULL COMMENT "",
  `Sales` varchar(255) NULL COMMENT "",
  `CustomerServiceCode` varchar(255) NULL COMMENT "",
  `CustomerName` varchar(255) NULL COMMENT "",
  `CustomerFullNameCH` varchar(255) NULL COMMENT "",
  `CustomerFullNameEN` varchar(255) NULL COMMENT "",
  `another_name` varchar(255) NULL COMMENT "",
  `current_valid_status` char(1) NULL COMMENT "",
  `t100_cust_id` varchar(255) NULL COMMENT "",
  `taxNo` varchar(255) NULL COMMENT "",
  `contacts` varchar(255) NULL COMMENT "",
  `tenantTelephone` varchar(255) NULL COMMENT "",
  `tenantEmail` varchar(255) NULL COMMENT "",
  `address` varchar(255) NULL COMMENT "",
  `__version__` datetime NULL COMMENT "",
  `grp_dpt_manager_contact` varchar(255) NULL COMMENT "",
  `bus_dpt_manager_contact` varchar(255) NULL COMMENT "",
  `bus_dpt_win_name` varchar(255) NULL COMMENT "",
  `bus_dpt_win_contact` varchar(255) NULL COMMENT "",
  `dpt_manager_contact` varchar(255) NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "es_customerservice_v4_1"
);



CREATE TABLE `es_customerservice_v2` (
                                         `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY( `eId`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eId`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);

-- aiodb表 es_customerservice_v4_1 对应sr es_customerservice_v2_external 然后 insert into  es_customerservice_v2
select count(1) from es_customerservice_v4_1
truncate es_customerservice_v2
insert into  es_customerservice_v2 select * from  es_customerservice_v2_external;


 CREATE EXTERNAL TABLE `es_customerservice_v3_external` (
                                         `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=MYSQL
    COMMENT "MYSQL"
    PROPERTIES (
                   "host" = "***************",
                   "port" = "4306",
                   "user" = "digiwin",
                   "password" = "gitlab123",
                   "database" = "aio-db",
                   "table" = "es_customerservice_v3"
               );

CREATE TABLE `es_customerservice_v3` (
                                         `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=OLAP
    DUPLICATE KEY(`grp_dpt_id`, `dpt_id`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`grp_dpt_id`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_manager_contact`, `dpt_id`, `dpt_manager_contact`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "true"
);
INSERT INTO `es_customerservice_v3_test` (
    `grp_dpt_id`,
    `dpt_id`,
    `grp_dpt_manager_contact`,
    `bus_dpt_id`,
    `bus_dpt_manager_contact`,
    `dpt_manager_contact`,
    `grp_dpt_name`,
    `grp_dpt_manager`,
    `bus_dpt_name`,
    `bus_dpt_manager`,
    `bus_dpt_win_name`,
    `bus_dpt_win_contact`,
    `dpt_name`,
    `dpt_manager`
)
SELECT
    `grp_dpt_id`,
    `dpt_id`,
    `grp_dpt_manager_contact`,
    `bus_dpt_id`,
    `bus_dpt_manager_contact`,
    `dpt_manager_contact`,
    `grp_dpt_name`,
    `grp_dpt_manager`,
    `bus_dpt_name`,
    `bus_dpt_manager`,
    `bus_dpt_win_name`,
    `bus_dpt_win_contact`,
    `dpt_name`,
    `dpt_manager`
FROM `es_customerservice_v3`
alter table es_customerservice_v3 rename es_customerservice_v3_bak;
alter table es_customerservice_v3_test rename es_customerservice_v3;
INSERT INTO es_customerservice_v3_test(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM5200', '福建战略大客事业部', '缪奇', '<EMAIL>', '张阳', '<EMAIL>', 'BM5230', '大客增值二部', '戴月香', '<EMAIL>');
INSERT INTO es_customerservice_v3_test(`grp_dpt_id`, `grp_dpt_name`, `grp_dpt_manager`, `grp_dpt_manager_contact`, `bus_dpt_id`, `bus_dpt_name`, `bus_dpt_manager`, `bus_dpt_manager_contact`, `bus_dpt_win_name`, `bus_dpt_win_contact`, `dpt_id`, `dpt_name`, `dpt_manager`, `dpt_manager_contact`) VALUES ('BM0000', '制造事业群（大陆）', '孔加余', '<EMAIL>', 'BM6400', '山东增值事业部', '张晓平', '<EMAIL>', '张阳', '<EMAIL>', 'BM6410', '增值经营部', '王芳', '<EMAIL>');


ALTER TABLE servicecloud.es_customerservice_v2
    ADD COLUMN valueAddedConsultant string,
    ADD COLUMN valueAddedConsultantEmail string;

select *
from es_customerservice_v2 where CustomerServiceCode =  '72006557';

select * from IndustryPlanQuickScreening where eid = '399031588229696'
CREATE TABLE `IndustryPlanQuickScreening_bak_20240614` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);
INSERT INTO `IndustryPlanQuickScreening` (
    `eid`,
    `SalesContact`,
    `userSid`,
    `uploadDataModelCode`,
    `collectedTime`,
    `sqSummitFreq`,
    `sqCollateRate`,
    `sqFirmCount`,
    `sqOverseasFirmCount`,
    `sqERPSysCount`,
    `sqConsolCycle`,
    `sqIPODisclosure`,
    `sqCfoCompletedCount`,
    `sqCfoTotalQuantity`,
    `sqIndustryAffil`,
    `sqBizModel`,
    `sqBizTeamSize`,
    `sqProdType`,
    `sqProdTypeRatio`,
    `sqProdModelCount`,
    `sqQuoteEfficiency`,
    `sqQuoteConfirmation`,
    `sqOrderConfirmation`,
    `sqOrderEfficiency`,
    `sqOrderingOperationMode`,
    `sqOrderProgressTracking`,
    `sqCsoCompletedCount`,
    `sqCsoTotalQuantity`,
    `sqBranchCount`,
    `sqOverseasBranches`,
    `sqEmployeeCount`,
    `sqHumanResourcesCount`,
    `sqPaperContract`,
    `sqPaperDummy`,
    `sqAttendanceSettlementDays`,
    `sqSalarySettlementDays`,
    `sqHumanRightsVerification`,
    `sqStaffingBlueprint`,
    `sqBudgetManagement`,
    `sqTalentManagement`,
    `sqEnterpriseResourceSharing`,
    `sqChoCompletedCount`,
    `sqChoTotalQuantity`,
    `serviceCode`,
    `customerName`,
    `CustomerCode`,
    `businessDepartmentCode`,
    `businessDepartmentCodeACP`,
    `businessDepartmentName`,
    `url`,
    `createTime`,
    `SalesName`,
    `userid`,
    `ThemeApplySourceCode`,
    `ThemeApplayStatus`,
    `ThemeLastUpdateTime`,
    `sqScraningState`,
    `sqScraningDesc`,
    `fscfo`,
    `fscso`,
    `fscho`
)
SELECT
    `eid`,
    `SalesContact`,
    `userSid`,
    `uploadDataModelCode`,
    `collectedTime`,
    `sqSummitFreq`,
    `sqCollateRate`,
    `sqFirmCount`,
    `sqOverseasFirmCount`,
    `sqERPSysCount`,
    `sqConsolCycle`,
    `sqIPODisclosure`,
    `sqCfoCompletedCount`,
    `sqCfoTotalQuantity`,
    `sqIndustryAffil`,
    `sqBizModel`,
    `sqBizTeamSize`,
    `sqProdType`,
    `sqProdTypeRatio`,
    `sqProdModelCount`,
    `sqQuoteEfficiency`,
    `sqQuoteConfirmation`,
    `sqOrderConfirmation`,
    `sqOrderEfficiency`,
    `sqOrderingOperationMode`,
    `sqOrderProgressTracking`,
    `sqCsoCompletedCount`,
    `sqCsoTotalQuantity`,
    `sqBranchCount`,
    `sqOverseasBranches`,
    `sqEmployeeCount`,
    `sqHumanResourcesCount`,
    `sqPaperContract`,
    `sqPaperDummy`,
    `sqAttendanceSettlementDays`,
    `sqSalarySettlementDays`,
    `sqHumanRightsVerification`,
    `sqStaffingBlueprint`,
    `sqBudgetManagement`,
    `sqTalentManagement`,
    `sqEnterpriseResourceSharing`,
    `sqChoCompletedCount`,
    `sqChoTotalQuantity`,
    `serviceCode`,
    `customerName`,
    `CustomerCode`,
    `businessDepartmentCode`,
    `businessDepartmentCodeACP`,
    `businessDepartmentName`,
    `url`,
    `createTime`,
    `SalesName`,
    `userid`,
    `ThemeApplySourceCode`,
    `ThemeApplayStatus`,
    `ThemeLastUpdateTime`,
    `sqScraningState`,
    `sqScraningDesc`,
    `fscfo`,
    `fscso`,
    `fscho`
FROM `IndustryPlanQuickScreening_bak_20240613`
where eid = 325015457477184 and collectedTime = '2024-06-07 10:18:19'
  select * from IndustryPlanQuickScreening where SalesContact = '<EMAIL>';

select a.bus_dpt_name,
       c.bus_dpt_manager,
       c.bus_dpt_win_name,
       a.grp_dpt_name,
       c.grp_dpt_manager,
       count(distinct a.eid)                                      as cus_count,
       sum(case
               when (b.sqCfoCompletedCount is null or b.sqCfoCompletedCount < b.sqCfoTotalQuantity or
                     b.sqCsoCompletedCount is null or b.sqCsoCompletedCount < b.sqCsoTotalQuantity or
                     b.sqChoCompletedCount is null or b.sqChoCompletedCount < b.sqChoTotalQuantity) then 0
               else 1 end)                                        as cus_compeleted_count,
       sum(case
               when ((b.sqCfoCompletedCount > 0 and b.sqCfoCompletedCount < b.sqCfoTotalQuantity) or
                     (b.sqCsoCompletedCount > 0 and b.sqCsoCompletedCount < b.sqCsoTotalQuantity) or
                     (b.sqChoCompletedCount > 0 and b.sqChoCompletedCount < b.sqChoTotalQuantity)) then 1
               else 0 end)                                        as cus_screening_count,
       sum(case when (b.sqScraningState = 'D') then 1 else 0 end) as no_screening_count
from servicecloud.es_customerservice_v2 a
         left join servicecloud.es_customerservice_v3 c on c.dpt_id = a.dpt_id
         left join servicecloud.IndustryPlanQuickScreening b on b.eid = a.eid and a.SalesContact = b.SalesContact
where a.grp_dpt_id = 'CD0000'
group by a.bus_dpt_name, c.bus_dpt_manager, c.bus_dpt_win_name, a.grp_dpt_name, c.grp_dpt_manager
order by a.grp_dpt_name, c.grp_dpt_manager, a.bus_dpt_name, c.bus_dpt_manager, c.bus_dpt_win_name
limit 1000;

SELECT eId                        eid,
       CustomerCode               customerCode,
       CustomerServiceCode        customerServiceCode,
       CustomerName               customerName,
       CustomerFullNameCH         customerFullNameCH,
       valueAddedConsultant,
       valueAddedConsultantEmail,
       bus_dpt_win_name           busDptWinName,
       bus_dpt_win_contact        busDptWinContact,
       grp_dpt_name               grpDptName,
       grp_dpt_manager            grpDptManager,
       bus_dpt_name               busDptName,
       bus_dpt_manager            busDptManager,
       Sales                      sales,
       SalesContact               salesContact,
       dpt_name                   dptName,
       dpt_manager                dptManager,
       group_concat(tts.tagId) as tagIdString
FROM servicecloud.es_customerservice_v2 escv2
         LEFT JOIN AIEOM.tenant_tag_string tts on escv2.eId = tts.id AND tts.tagId IN (729636528575040, 729646154027584,
                                                                                       729520260567616, 731424753066560)
WHERE 1 = 1
GROUP BY eid, customerCode, customerServiceCode, customerName, customerFullNameCH, valueAddedConsultant,
         valueAddedConsultantEmail, busDptWinName, busDptWinContact, grpDptName, busDptName, sales, salesContact,
         grpDptManager, busDptManager, dptName, dptManager
HAVING array_contains_all(CAST(concat('[',tagIdString,']')AS ARRAY<BIGINT>),CAST(concat('[',731424753066560,']') AS  ARRAY<BIGINT>))
ORDER BY eid

-- hbase shell
snapshot 'IndustryPlanQuickScreening', 'IndustryPlanQuickScreening_snapshot_20240813_1'
clone_snapshot  'IndustryPlanQuickScreening_snapshot_20240813_1' ,'IndustryPlanQuickScreening_bak_20240813_1'
scan 'IndustryPlanQuickScreening_bak_20240813_1'
-- beifen

snapshot 'IndustryPlanQuickScreening', 'IndustryPlanQuickScreening_snapshot_20240930_1'
clone_snapshot  'IndustryPlanQuickScreening_snapshot_20240930_1' ,'IndustryPlanQuickScreening_bak_20240930_1'
scan 'IndustryPlanQuickScreening_bak_20240930_1'
-- 恢复hbase数据

disable 'IndustryPlanQuickScreening'
drop 'IndustryPlanQuickScreening'
clone_snapshot  'IndustryPlanQuickScreening_snapshot_20240614' ,'IndustryPlanQuickScreening'
scan 'IndustryPlanQuickScreening'


truncate 'IndustryPlanQuickScreening';


