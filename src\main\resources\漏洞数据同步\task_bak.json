{"job": {"setting": {"speed": {"byte": 10485760}, "errorLimit": {"record": 0, "percentage": 0.02}}, "content": [{"reader": {"name": "hbase20xsqlreader", "parameter": {"hbaseConfig": {"hbase.zookeeper.quorum": "ddp5:2181"}, "schema": "", "table": "WindowsSecurityCEV20201472Collected", "column": ["DEVICEID", "EID", "COLLECTEDTIME", "COLLECTCONFIGID", "UPLOADDATAMODELCODE", "DEVICECOLLECTDETAILID", "CODE"]}}, "writer": {"name": "mysqlwriter", "parameter": {"writeMode": "truncate", "username": "servicecloud", "password": "servicecloud@123", "column": ["deviceId", "eid", "collectedTime", "collectConfigId", "uploadDataModelCode", "deviceCollectDetailId", "code"], "connection": [{"table": ["CVE20201472EventModel_test_sync"], "jdbcUrl": "*************************************************************************************************"}]}}}]}}