package com.example.mybatis.model;

import com.example.mybatis.utils.SnowFlake;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2022-01-06 18:05
 * @Description
 */
@Data
public class TenantModuleContract extends ModuleContractBase {
    //("当前日期-合约截止日")
    private int days;
    /**
     * 构造器
     */
    public TenantModuleContract() {
    }

    /**
     * 构造器
     * @param sid 运维商Id
     * @param eid 租户Id
     * @param moduleId 模组Id
     * @param status 状态
     * @param startDate 起始日
     * @param endDate 结束日
     */
    public TenantModuleContract(Long sid, Long eid, Long moduleId, int status, Date startDate, Date endDate) {
        this();
        setId(SnowFlake.getInstance().newId());
        setSid(sid);
        this.eid = eid;
        this.status = status;
        setModuleId(moduleId);
        setStartDate(startDate);
        setEndDate(endDate);
    }

    //("租户Id")
    private Long eid;
    //("状态(1:试用中2:试用到期3:已订阅4:订阅到期)")
    private int status;
    //("订购方式(1:开通订阅2:开通试用3:试用转正4:变更授权5:年维服务6:年维到期)")
    private int orderMethod;
    //("服务人员Id")
    private long staffId;
    //("开通试用id")
    private long openId;
    //("用户姓名")
    private String userName;
    //("用户邮箱")
    private String noticeEmail;

    //("是否已到期")
    private Boolean expired;
    //("服务人员名称")
    private String staffName;
    //("服务人员信箱")
    private String staffEmail;

    @Data
    public static class Detail{
        private Long eid;
        private String serviceCode;
    }


}
