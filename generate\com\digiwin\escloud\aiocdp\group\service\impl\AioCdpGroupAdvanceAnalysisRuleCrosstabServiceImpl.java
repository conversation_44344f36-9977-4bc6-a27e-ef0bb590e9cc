package com.digiwin.escloud.aiocdp.group.service.impl;

import com.digiwin.escloud.aiocdp.group.entity.AioCdpGroupAdvanceAnalysisRuleCrosstab;
import com.digiwin.escloud.aiocdp.group.mapper.AioCdpGroupAdvanceAnalysisRuleCrosstabMapper;
import com.digiwin.escloud.aiocdp.group.service.IAioCdpGroupAdvanceAnalysisRuleCrosstabService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 进阶规则分析规则交叉分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@Service
public class AioCdpGroupAdvanceAnalysisRuleCrosstabServiceImpl extends ServiceImpl<AioCdpGroupAdvanceAnalysisRuleCrosstabMapper, AioCdpGroupAdvanceAnalysisRuleCrosstab> implements IAioCdpGroupAdvanceAnalysisRuleCrosstabService {

}
