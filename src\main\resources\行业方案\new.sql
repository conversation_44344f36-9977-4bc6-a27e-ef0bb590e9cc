CREATE EXTERNAL TABLE `cdp_customer_contacts_external` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT "",
  `eid` bigint(20) NOT NULL COMMENT "租户sid",
  `customerCode` varchar(50) NOT NULL COMMENT "客户编号",
  `department` varchar(20) NOT NULL COMMENT "部门",
  `lastContactTime` date NULL COMMENT "最后联络时间",
  `contactCnt` int(11) NOT NULL COMMENT "联系人数量",
  `createTime` datetime NULL COMMENT "",
  `updateTime` datetime NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "rm-bp1k25v6j475s4ai7jo.mysql.rds.aliyuncs.com",
"port" = "3306",
"user" = "readniss",
"password" = "Escread001",
"database" = "escloud-db",
"table" = "cdp_customer_contacts"
);

CREATE TABLE `cdp_customer_contacts_external` (
                                                  `id` bigint(20) NOT NULL COMMENT "",
                                                  `eid` bigint(20) NOT NULL COMMENT "",
                                                  `customerCode` varchar(65535) NOT NULL COMMENT "",
                                                  `department` varchar(65535) NOT NULL COMMENT "",
                                                  `lastContactTime` date NULL COMMENT "",
                                                  `contactCnt` int(11) NULL COMMENT "",
                                                  `createTime` datetime NULL COMMENT "",
                                                  `updateTime` datetime NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`id`, `eid`, `customerCode`, `department`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE EXTERNAL TABLE `mars_customerservice_external` (
  `CustomerServiceCode` varchar(20) NOT NULL COMMENT "",
  `ProductCode` varchar(4) NOT NULL COMMENT "",
  `ProductCategory` varchar(20) NULL COMMENT "",
  `ProductVersion` varchar(30) NULL COMMENT "",
  `Sales` varchar(20) NULL COMMENT "",
  `SalesContact` varchar(100) NULL COMMENT "",
  `HasOwnerService` tinyint(4) NULL COMMENT "文字客服是否走专属",
  `HasTextService` tinyint(4) NULL COMMENT "文字客服是否走团队(当文字专属为1时没有作用)",
  `HasOwnerIssueService` tinyint(4) NULL COMMENT "案件是否走专属",
  `ServiceStaffCode` varchar(20) NULL COMMENT "",
  `ServiceStaff` varchar(20) NULL COMMENT "",
  `ServiceStaffContact` varchar(100) NULL COMMENT "",
  `ServiceStaffQQ` varchar(20) NULL COMMENT "",
  `Consultant` varchar(20) NULL COMMENT "",
  `ConsultantContact` varchar(100) NULL COMMENT "",
  `ContractStartDate` varchar(20) NULL COMMENT "",
  `ContractExprityDate` varchar(20) NULL COMMENT "",
  `ContractState` varchar(30) NULL COMMENT "",
  `IndustryCode` varchar(20) NULL COMMENT "",
  `AreaCode` varchar(20) NULL COMMENT "",
  `HostAuthNum` int(11) NULL COMMENT "",
  `ClientAuthNum` int(11) NULL COMMENT "",
  `SnmpAuthNum` int(11) NULL COMMENT "",
  `pmWorkno` varchar(30) NULL COMMENT "PM人员工号",
  `cust_level` varchar(5) NULL COMMENT "客户等级",
  `trial_expired` varchar(30) NULL COMMENT "试用期到期日",
  `service_department` varchar(10) NULL COMMENT "所属服务部门",
  `cust_borrowing_due_date` varchar(30) NULL COMMENT "客户借货到期日期",
  `status` varchar(2) NULL COMMENT "客户对应产品的当前项目状态，取值：0-未上线,1-已上线未结案,2-已结案未转TSC,3-转TSC,4-失效,5-服务云暂停支持。此字段只从控制台手动设置。",
  `service_cc_staff_emails` varchar(2000) NULL COMMENT "T产品群抄送人",
  `formal` tinyint(4) NULL COMMENT "是否正式数据",
  `IsTrial` tinyint(4) NULL COMMENT "是否試用",
  `window_department` varchar(10) NULL COMMENT "服务云客户服务部门",
  `businessDepartmentCode` varchar(10) NULL COMMENT "事业部编号",
  `businessDepartmentCodeACP` varchar(10) NULL COMMENT "事业部编号(ACP系统)",
  `serviceUnitType` varchar(10) NULL COMMENT "服务单位类别",
  `deliveryMode` varchar(10) NULL COMMENT "1 实施交付 | 2 售后服务",
  `contractSource` int(11) NULL COMMENT "合约来源 1:crm 2:iam",
  `salesCode` varchar(20) NULL COMMENT "业务人员工号",
  `agent_limit_issueCount` int(11) NULL COMMENT "限定案件量，同步与crm的SERAI.AI011",
  `__version__` datetime NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "rm-bp1k25v6j475s4ai7jo.mysql.rds.aliyuncs.com",
"port" = "3306",
"user" = "readniss",
"password" = "Escread001",
"database" = "aio-db",
"table" = "mars_customerservice"
);

CREATE EXTERNAL TABLE `mars_customerservice_external` (
  `CustomerServiceCode` varchar(20) NOT NULL COMMENT "",
  `ProductCode` varchar(4) NOT NULL COMMENT "",
  `ProductCategory` varchar(20) NULL COMMENT "",
  `ProductVersion` varchar(30) NULL COMMENT "",
  `Sales` varchar(20) NULL COMMENT "",
  `SalesContact` varchar(100) NULL COMMENT "",
  `HasOwnerService` tinyint(4) NULL COMMENT "文字客服是否走专属",
  `HasTextService` tinyint(4) NULL COMMENT "文字客服是否走团队(当文字专属为1时没有作用)",
  `HasOwnerIssueService` tinyint(4) NULL COMMENT "案件是否走专属",
  `ServiceStaffCode` varchar(20) NULL COMMENT "",
  `ServiceStaff` varchar(20) NULL COMMENT "",
  `ServiceStaffContact` varchar(100) NULL COMMENT "",
  `ServiceStaffQQ` varchar(20) NULL COMMENT "",
  `Consultant` varchar(20) NULL COMMENT "",
  `ConsultantContact` varchar(100) NULL COMMENT "",
  `ContractStartDate` varchar(20) NULL COMMENT "",
  `ContractExprityDate` varchar(20) NULL COMMENT "",
  `ContractState` varchar(30) NULL COMMENT "",
  `IndustryCode` varchar(20) NULL COMMENT "",
  `AreaCode` varchar(20) NULL COMMENT "",
  `HostAuthNum` int(11) NULL COMMENT "",
  `ClientAuthNum` int(11) NULL COMMENT "",
  `SnmpAuthNum` int(11) NULL COMMENT "",
  `pmWorkno` varchar(30) NULL COMMENT "PM人员工号",
  `cust_level` varchar(5) NULL COMMENT "客户等级",
  `trial_expired` varchar(30) NULL COMMENT "试用期到期日",
  `service_department` varchar(10) NULL COMMENT "所属服务部门",
  `cust_borrowing_due_date` varchar(30) NULL COMMENT "客户借货到期日期",
  `status` varchar(2) NULL COMMENT "客户对应产品的当前项目状态，取值：0-未上线,1-已上线未结案,2-已结案未转TSC,3-转TSC,4-失效,5-服务云暂停支持。此字段只从控制台手动设置。",
  `service_cc_staff_emails` varchar(2000) NULL COMMENT "T产品群抄送人",
  `formal` tinyint(4) NULL COMMENT "是否正式数据",
  `IsTrial` tinyint(4) NULL COMMENT "是否試用",
  `window_department` varchar(10) NULL COMMENT "服务云客户服务部门",
  `businessDepartmentCode` varchar(10) NULL COMMENT "事业部编号",
  `businessDepartmentCodeACP` varchar(10) NULL COMMENT "事业部编号(ACP系统)",
  `serviceUnitType` varchar(10) NULL COMMENT "服务单位类别",
  `deliveryMode` varchar(10) NULL COMMENT "1 实施交付 | 2 售后服务",
  `contractSource` int(11) NULL COMMENT "合约来源 1:crm 2:iam",
  `salesCode` varchar(20) NULL COMMENT "业务人员工号",
  `agent_limit_issueCount` int(11) NULL COMMENT "限定案件量，同步与crm的SERAI.AI011",
  `__version__` datetime NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "",
"database" = "aio-db",
"table" = "mars_customerservice"
);