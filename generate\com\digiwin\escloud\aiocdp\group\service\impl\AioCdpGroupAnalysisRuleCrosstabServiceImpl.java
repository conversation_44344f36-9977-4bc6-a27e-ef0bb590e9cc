package com.digiwin.escloud.aiocdp.group.service.impl;

import com.digiwin.escloud.aiocdp.group.entity.AioCdpGroupAnalysisRuleCrosstab;
import com.digiwin.escloud.aiocdp.group.mapper.AioCdpGroupAnalysisRuleCrosstabMapper;
import com.digiwin.escloud.aiocdp.group.service.IAioCdpGroupAnalysisRuleCrosstabService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 群画像分析规则交叉分组 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@Service
public class AioCdpGroupAnalysisRuleCrosstabServiceImpl extends ServiceImpl<AioCdpGroupAnalysisRuleCrosstabMapper, AioCdpGroupAnalysisRuleCrosstab> implements IAioCdpGroupAnalysisRuleCrosstabService {

}
