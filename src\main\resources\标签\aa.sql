CREATE TABLE `tenant_tag_score` (
  `id` varchar(255) NOT NULL COMMENT "",
  `tagId` bigint(20) NOT NULL COMMENT "",
  `score` decimal(10, 0) NOT NULL COMMENT "",
  `updateTime` datetime NULL COMMENT "",
  `tagDate` date NOT NULL COMMENT ""
) ENGINE = OLAP UNIQUE KEY(`id`, `tagId`, `score`, `updateTime`) DISTRIBUTED BY HASH(`tagId`) BUCKETS 8 PROPERTIES (
  "compression" = "LZ4",
  "fast_schema_evolution" = "true",
  "replicated_storage" = "true",
  "replication_num" = "2"
);

DEVICE CLIENT USER Total_Device MSSQL TENANT SCPUSER
select
  max(base.tenantId) as tenantId,
  max(base.tenantName) as tenantName,
  max(base.serviceCode) as serviceCode,
  group_concat(concat(tag.tagValue, '')) tagValue,
  date_format(max(tag.updateTime), '%Y-%m-%d %H:%i:%s') updateTime,
  MAX(if(tag_score.score IS NULL, 0, tag_score.score)) as score
from
  AIEOM.tenant_tag_string tag
  inner join AIEOM.AiopsTenant base on tag.id = base.eid
  LEFT JOIN AIEOM.tenant_tag_score tag_score ON tag_score.id = tag.id
  AND tag.tagId = tag_score.tagId
where
  tag.tagId = 747209582092864
  and tag.tagValue in (
    'CPO滚动需求计划领域高潜客户',
    'CPO生产日排程领域高潜客户',
    'CPO供应商交料排程领域高潜客户'
  )
group by
  tag.id
order by
  tag.id
limit
  0, 10;

select
  max(base.tenantId) as tenantId,
  max(base.tenantName) as tenantName,
  max(base.serviceCode) as serviceCode,
  group_concat(tag.tagValue) as tagValue,
  date_format(max(tag.updateTime), '%Y-%m-%d %H:%i:%s') as updateTime
from
  AIEOM.tenant_tag_string tag
  inner join AIEOM.AiopsTenant base on tag.id = base.eid
where
  1 = 1
group by
  tag.id
order by
  tag.id
limit
  0, 10;

select
  max(base.deviceId) as deviceId,
  max(base.deviceName) as deviceName,
  max(base.deviceType) as deviceType,
  group_concat(tag.tagValue, '') tagValue,
  date_format(max(tag.updateTime), '%Y-%m-%d %H:%i:%s') updateTime,
  MAX(if(tag_score.score IS NULL, 0, tag_score.score)) as score
from
  AIEOM.device_tag_string tag
  inner join AIEOM.AiopsDevice base on tag.id = base.deviceId
  LEFT JOIN AIEOM.device_tag_score tag_score ON tag_score.id = tag.id
  AND tag.tagId = tag_score.tagId
where
  tag.tagId = 491102278992448
  and tag.tagValue in ('MS17-010', 'CVE-2020-1472', 'CVE-2020-0796')
group by
  tag.id
order by
  tag.id
limit
  0, 10;

select
  max(base.tenantId) as tenantId,
  max(base.tenantName) as tenantName,
  max(base.serviceCode) as serviceCode,
  group_concat(cast (tag.tagValue as string)) tagValue,
  date_format(max(tag.updateTime), '%Y-%m-%d %H:%i:%s') updateTime,
  MAX(if(tag_score.score IS NULL, 0, tag_score.score)) as score
from
  AIEOM.tenant_tag_double tag
  inner join AIEOM.AiopsTenant base on tag.id = base.eid
  LEFT JOIN AIEOM.tenant_tag_score tag_score ON tag_score.id = tag.id
  AND tag.tagId = tag_score.tagId
where
  tag.tagId = 744789063471680
group by
  tag.id
order by
  tag.id
limit
  0, 10;

select
  id eid,
  tagId,
  score
from
  AIEOM.tenant_tag_score tag
where
  tag.tagId = '749081098658368'
select
  JSON_REPLACE()
UPDATE
  TransactionsOfNotesReceive
SET
  IdentifyYear = STR_TO_DATE(
    CONCAT(Year, '-', Month, '-01 00:00:00'),
    '%Y-%m-%d %H:%i:%s'
  )
where
  IdentifyYear is NULL
  or IdentifyYear = '';

select
  STR_TO_DATE(
    CONCAT(Year, '-', Month, '-01 00:00:00'),
    '%Y-%m-%d %H:%i:%s'
  )
from
  BillsReceiveNum;

show create table tag_device_score;

alter table
  tag_device_score rename tag_device_score_bak_20240802;

CREATE TABLE `tag_device_score` (
  `tagId` bigint(20) NULL COMMENT "id",
  `score` decimal64(12, 2) NULL COMMENT "分数值",
  `tagDate` date NULL COMMENT "生成日期",
  `uv` bitmap BITMAP_UNION NULL COMMENT "bitmap并聚合",
  `updateTime` datetime REPLACE NULL COMMENT "生成时间"
) ENGINE = OLAP AGGREGATE KEY(`tagId`, `score`, `tagDate`) COMMENT "OLAP" DISTRIBUTED BY HASH(`tagId`) BUCKETS 8 PROPERTIES (
  "replication_num" = "2",
  "in_memory" = "false",
  "storage_format" = "DEFAULT",
  "enable_persistent_index" = "false"
);

insert into
  tag_device_score
select
  *
from
  tag_device_score_bak_20240802;

CREATE TABLE `AccountSetInformationCount` (
  `eid` varchar(65533) NOT NULL COMMENT "",
  `Product_Line` varchar(65533) NOT NULL COMMENT "",
  `aiopsItem` varchar(65533) NOT NULL COMMENT "",
  `IndicatorNumber` varchar(65533) NOT NULL COMMENT "",
  `Account_Set_Count` int(11) NULL COMMENT "",
  `collectedTime` varchar(65533) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(
  `eid`,
  `Product_Line`,
  `aiopsItem`,
  `IndicatorNumber`
) COMMENT "OLAP" DISTRIBUTED BY HASH(`eid`) BUCKETS 10 PROPERTIES (
  "replication_num" = "2",
  "in_memory" = "false",
  "storage_format" = "DEFAULT",
  "enable_persistent_index" = "false"
);

insert into
  AccountSetInformationCount
select
  eid,
  Product_Line,
  aiopsItem,
  IndicatorNumber,
  count(Account_Set) as Account_Set_Count,
  max(collectedTime) as collectedTime
from
  servicecloud.AccountSetInformation
group by
  eid,
  Product_Line,
  aiopsItem,
  IndicatorNumber


  
select
  obj.eid as id,
  *************** as tagId,
  ifnull(my_afv, 0) as tagValue,
  '2024-11-05 13:32:19' as updateTime,
  '2024-11-05' as tagDate
from
  servicecloud.AiopsTenant as obj
  left join (
    select
      eid,
      count(eid) as my_afv
    from
      servicecloud.AiopsOperateLog as A1
    where
      A1.startTime BETWEEN '2023-11-11 00:00:00'
      and '2024-11-04 23:59:59'
    group by
      eid
  ) as A1 on obj.eid = A1.eid
where
  (A1.eid is not null)


WITH CitySummary AS (
    SELECT city, COUNT(*) AS total_people
    FROM people
    GROUP BY city
),
GenderSummary AS (
    SELECT 
        city, 
        gender, 
        COUNT(*) AS gender_count
    FROM 
        people
    GROUP BY 
        city, 
        gender
)
SELECT 
    cs.city, 
    cs.total_people, 
    gs.gender, 
    gs.gender_count
FROM 
    CitySummary cs
JOIN 
    GenderSummary gs ON cs.city = gs.city
ORDER BY 
    cs.city, 
    gs.gender;