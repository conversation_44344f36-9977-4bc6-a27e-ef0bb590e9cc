package com.digiwin.escloud.aiocdp.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 群画像分析规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-12
 */
@TableName("aio_cdp_group_analysis_rule")
@ApiModel(value = "AioCdpGroupAnalysisRule对象", description = "群画像分析规则")
public class AioCdpGroupAnalysisRule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String ruleScore;

    private Long groupInfoId;

    private String analysisType;

    private String analysisTypeObjId;

    private String analysisCondition;

    @ApiModelProperty("图表类型")
    private String diagramsType;

    @ApiModelProperty("TopN")
    private Integer diagramsTopN;

    @ApiModelProperty("排序类型")
    private String diagramsOrderType;

    @ApiModelProperty("排序")
    private Integer diagramsOrder;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleScore() {
        return ruleScore;
    }

    public void setRuleScore(String ruleScore) {
        this.ruleScore = ruleScore;
    }

    public Long getGroupInfoId() {
        return groupInfoId;
    }

    public void setGroupInfoId(Long groupInfoId) {
        this.groupInfoId = groupInfoId;
    }

    public String getAnalysisType() {
        return analysisType;
    }

    public void setAnalysisType(String analysisType) {
        this.analysisType = analysisType;
    }

    public String getAnalysisTypeObjId() {
        return analysisTypeObjId;
    }

    public void setAnalysisTypeObjId(String analysisTypeObjId) {
        this.analysisTypeObjId = analysisTypeObjId;
    }

    public String getAnalysisCondition() {
        return analysisCondition;
    }

    public void setAnalysisCondition(String analysisCondition) {
        this.analysisCondition = analysisCondition;
    }

    public String getDiagramsType() {
        return diagramsType;
    }

    public void setDiagramsType(String diagramsType) {
        this.diagramsType = diagramsType;
    }

    public Integer getDiagramsTopN() {
        return diagramsTopN;
    }

    public void setDiagramsTopN(Integer diagramsTopN) {
        this.diagramsTopN = diagramsTopN;
    }

    public String getDiagramsOrderType() {
        return diagramsOrderType;
    }

    public void setDiagramsOrderType(String diagramsOrderType) {
        this.diagramsOrderType = diagramsOrderType;
    }

    public Integer getDiagramsOrder() {
        return diagramsOrder;
    }

    public void setDiagramsOrder(Integer diagramsOrder) {
        this.diagramsOrder = diagramsOrder;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AioCdpGroupAnalysisRule{" +
            "id = " + id +
            ", ruleScore = " + ruleScore +
            ", groupInfoId = " + groupInfoId +
            ", analysisType = " + analysisType +
            ", analysisTypeObjId = " + analysisTypeObjId +
            ", analysisCondition = " + analysisCondition +
            ", diagramsType = " + diagramsType +
            ", diagramsTopN = " + diagramsTopN +
            ", diagramsOrderType = " + diagramsOrderType +
            ", diagramsOrder = " + diagramsOrder +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
