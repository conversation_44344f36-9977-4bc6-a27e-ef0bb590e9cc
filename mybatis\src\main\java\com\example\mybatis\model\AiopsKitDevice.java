package com.example.mybatis.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

//@ApiModel
@Data
public class AiopsKitDevice {
    //@ApiModelProperty("主键")
    private Long id;
    //@ApiModelProperty("租户Id")
    private Long eid;
    //@ApiModelProperty("设备Id")
    private String deviceId;
    //@ApiModelProperty("设备名称")
    private String deviceName;
    //@ApiModelProperty("设备平台")
    private DevicePlatform platform;
    //@ApiModelProperty("IP地址")
    private String ipAddress;
    //@ApiModelProperty("放置点")
    private String placementPoint;
    //@ApiModelProperty("备注")
    private String remark;
    //@ApiModelProperty("客户注记")
    private String customerNotes;
    //@ApiModelProperty("地端aiopskit版本")
    private String aiopskitVersion;
    //@ApiModelProperty("地端escli版本")
    private String escliVersion;
    //@ApiModelProperty("首次注册时间")
    private LocalDateTime registerTime;
    //@ApiModelProperty("最后报到时间")
    private LocalDateTime lastCheckInTime;
    //@ApiModelProperty("是否作废")
    private Boolean isDeleted;
    //@ApiModelProperty("是否中继站")
    private Boolean isZJZ;



}
