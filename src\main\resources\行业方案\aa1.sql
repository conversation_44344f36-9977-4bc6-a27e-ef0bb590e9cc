select *
from servicecloud.IndustryPlanQuickScreening
where (`f_COOGroundTugCount0101` is null
    or `f_COOStackerCount0102` is null
    or `f_COOStackerWorkers0103` is null
    or `f_COODailyStackerRuns0104` is null
    or `f_COOManualFeeding0105` is null
    or `f_COOWarehouseCount0106` is null
    or `f_COOSingleWarehouseStaff0107` is null
    or `f_COOWarehouseShelvingStaff0108` is null
    or `f_COOMaterialTurnoverTime0109` is null
    or `f_COOSlowMovingInventory0110` is null
    or `f_COOWarehouseWorkload0111` is null
    )
  and;


update IndustryPlanQuickScreening ipqs
inner join  IndustryPlanQuickScreening_count_table as t on t.eid = ipqs.eid
set ipqs.f_COOTotalQuantity =  t.f_COOCompletedCount where ipqs.fs_COOSurvey IS NOT NULL

CREATE TABLE IndustryPlanQuickScreening_count_table (
                                            eid bigint ,
                                            f_COOCompletedCount INT)ENGINE=OLAP
                                                PRIMARY KEY(`eid`)
                                                COMMENT "OLAP"
                                                DISTRIBUTED BY HASH(`eid`) BUCKETS 10
                                                PROPERTIES (
                                                "replication_num" = "2",
                                                "in_memory" = "false",
                                                "storage_format" = "DEFAULT",
                                                "enable_persistent_index" = "false"
                                                );
insert into IndustryPlanQuickScreening_count_table
SELECT eid,
    (
        (CASE WHEN f_COOGroundTugCount0101 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOStackerCount0102 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOStackerWorkers0103 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COODailyStackerRuns0104 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOManualFeeding0105 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOWarehouseCount0106 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOSingleWarehouseStaff0107 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOWarehouseShelvingStaff0108 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOMaterialTurnoverTime0109 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOSlowMovingInventory0110 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOWarehouseWorkload0111 IS NOT NULL THEN 1 ELSE 0 END) +
        (CASE WHEN f_COOIntelligenceWarehousing IS NOT NULL THEN 1 ELSE 0 END)
        ) AS f_COOCompletedCount

FROM servicecloud.IndustryPlanQuickScreening
WHERE fs_COOSurvey IS NOT NULL;

UPDATE IndustryPlanQuickScreening AS ipqs
SET ipqs.f_COOTotalQuantity = (
    SELECT t.f_COOCompletedCount
    FROM IndustryPlanQuickScreening_count_table AS t
    WHERE t.eid = ipqs.eid
)
WHERE ipqs.fs_COOSurvey IS NOT NULL;


CREATE TABLE `IndustryPlanQuickScreening` (
                                              `eid` varchar(65533) NOT NULL COMMENT "",
                                              `SalesContact` varchar(65533) NOT NULL COMMENT "",
                                              `userSid` bigint(20) NULL COMMENT "",
                                              `uploadDataModelCode` varchar(65533) NULL COMMENT "",
                                              `collectedTime` varchar(65533) NULL COMMENT "",
                                              `sqSummitFreq` varchar(65533) NULL COMMENT "",
                                              `sqCollateRate` varchar(65533) NULL COMMENT "",
                                              `sqFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqOverseasFirmCount` varchar(65533) NULL COMMENT "",
                                              `sqERPSysCount` varchar(65533) NULL COMMENT "",
                                              `sqConsolCycle` varchar(65533) NULL COMMENT "",
                                              `sqIPODisclosure` varchar(65533) NULL COMMENT "",
                                              `sqCfoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCfoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqIndustryAffil` varchar(65533) NULL COMMENT "",
                                              `sqBizModel` varchar(65533) NULL COMMENT "",
                                              `sqBizTeamSize` int(11) NULL COMMENT "",
                                              `sqProdType` varchar(65533) NULL COMMENT "",
                                              `sqProdTypeRatio` int(11) NULL COMMENT "",
                                              `sqProdModelCount` int(11) NULL COMMENT "",
                                              `sqQuoteEfficiency` int(11) NULL COMMENT "",
                                              `sqQuoteConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderConfirmation` varchar(65533) NULL COMMENT "",
                                              `sqOrderEfficiency` int(11) NULL COMMENT "",
                                              `sqOrderingOperationMode` varchar(65533) NULL COMMENT "",
                                              `sqOrderProgressTracking` varchar(65533) NULL COMMENT "",
                                              `sqCsoCompletedCount` int(11) NULL COMMENT "",
                                              `sqCsoTotalQuantity` int(11) NULL COMMENT "",
                                              `sqBranchCount` int(11) NULL COMMENT "",
                                              `sqOverseasBranches` varchar(65533) NULL COMMENT "",
                                              `sqEmployeeCount` varchar(65533) NULL COMMENT "",
                                              `sqHumanResourcesCount` varchar(65533) NULL COMMENT "",
                                              `sqPaperContract` varchar(65533) NULL COMMENT "",
                                              `sqPaperDummy` varchar(65533) NULL COMMENT "",
                                              `sqAttendanceSettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqSalarySettlementDays` varchar(65533) NULL COMMENT "",
                                              `sqHumanRightsVerification` varchar(65533) NULL COMMENT "",
                                              `sqStaffingBlueprint` varchar(65533) NULL COMMENT "",
                                              `sqBudgetManagement` varchar(65533) NULL COMMENT "",
                                              `sqTalentManagement` varchar(65533) NULL COMMENT "",
                                              `sqEnterpriseResourceSharing` varchar(65533) NULL COMMENT "",
                                              `sqChoCompletedCount` int(11) NULL COMMENT "",
                                              `sqChoTotalQuantity` int(11) NULL COMMENT "",
                                              `serviceCode` varchar(65533) NULL COMMENT "",
                                              `customerName` varchar(65533) NULL COMMENT "",
                                              `CustomerCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCode` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentCodeACP` varchar(65533) NULL COMMENT "",
                                              `businessDepartmentName` varchar(65533) NULL COMMENT "",
                                              `url` varchar(65533) NULL COMMENT "",
                                              `createTime` datetime NULL COMMENT "",
                                              `SalesName` varchar(65533) NULL COMMENT "",
                                              `userid` int(11) NULL COMMENT "",
                                              `ThemeApplySourceCode` varchar(65533) NULL COMMENT "",
                                              `ThemeApplayStatus` varchar(65533) NULL COMMENT "",
                                              `ThemeLastUpdateTime` datetime NULL COMMENT "",
                                              `sqScraningState` varchar(65533) NULL COMMENT "",
                                              `sqScraningDesc` varchar(65533) NULL COMMENT "",
                                              `fscfo` varchar(65533) NULL COMMENT "",
                                              `fscso` varchar(65533) NULL COMMENT "",
                                              `fscho` varchar(65533) NULL COMMENT "",
                                              `sqSalesP` int(11) NULL COMMENT "",
                                              `sqLackInfoTools` varchar(65535) NULL COMMENT "",
                                              `f_CPOHighStockLevel0101` varchar(65533) NULL COMMENT "",
                                              `f_CPORollingDemand0104` varchar(65533) NULL COMMENT "",
                                              `f_CPODailyPlanScheduling0105` varchar(65533) NULL COMMENT "",
                                              `f_CPOToolForScheduling0106` varchar(65533) NULL COMMENT "",
                                              `f_CPOCapacityScheduling0107` varchar(65533) NULL COMMENT "",
                                              `f_CPOScheduleWithERP0110` varchar(65533) NULL COMMENT "",
                                              `f_CPOSupplierDelivery0111` varchar(65533) NULL COMMENT "",
                                              `f_CPOCompletedCount` int(11) NULL COMMENT "",
                                              `f_CPOTotalQuantity` int(11) NULL COMMENT "",
                                              `f_COOGroundTugCount0101` int(11) NULL COMMENT "",
                                              `f_COOStackerCount0102` int(11) NULL COMMENT "",
                                              `f_COOStackerWorkers0103` int(11) NULL COMMENT "",
                                              `f_COODailyStackerRuns0104` int(11) NULL COMMENT "",
                                              `f_COOManualFeeding0105` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseCount0106` int(11) NULL COMMENT "",
                                              `f_COOSingleWarehouseStaff0107` int(11) NULL COMMENT "",
                                              `f_COOWarehouseShelvingStaff0108` int(11) NULL COMMENT "",
                                              `f_COOMaterialTurnoverTime0109` varchar(65533) NULL COMMENT "",
                                              `f_COOSlowMovingInventory0110` varchar(65533) NULL COMMENT "",
                                              `f_COOWarehouseWorkload0111` varchar(65533) NULL COMMENT "",
                                              `f_COOCompletedCount` int(11) NULL COMMENT "",
                                              `f_COOTotalQuantity` int(11) NULL COMMENT "",
                                              `fs_CPOSurvey` varchar(65533) NULL COMMENT "",
                                              `fs_COOSurvey` varchar(65533) NULL COMMENT "",
                                              `f_COOStackerCountWorkers` decimal64(10, 0) NULL COMMENT "",
                                              `f_COOIntelligenceWarehousing` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueOne0112` varchar(65533) NULL COMMENT "",
                                              `f_CPOOrderDeliveryIssueTwo0113` varchar(65533) NULL COMMENT "",
                                              `f_CPOUseErp0114` varchar(65533) NULL COMMENT "",
                                              `f_CPOIndustry0115` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);


INSERT INTO servicecloud.IndustryPlanQuickScreening(uploadDataModelCode, collectedTime, CustomerCode, serviceCode,
                                                    businessDepartmentCode, customerName, businessDepartmentName,
                                                    SalesName, SalesContact, fscfo, fscso, fscho, eid,
                                                    businessDepartmentCodeACP, createTime, userid, ThemeApplySourceCode,
                                                    ThemeApplayStatus, ThemeLastUpdateTime, userSid, url,
                                                    sqScraningState, sqScraningDesc, fs_CPOSurvey, fs_COOSurvey,
                                                    sqSummitFreq, sqCollateRate, sqFirmCount, sqOverseasFirmCount,
                                                    sqERPSysCount, sqConsolCycle, sqIPODisclosure, sqCfoCompletedCount,
                                                    sqCfoTotalQuantity, sqIndustryAffil, sqBizModel, sqSalesP,
                                                    sqLackInfoTools, sqBizTeamSize, sqProdType, sqProdTypeRatio,
                                                    sqProdModelCount, sqQuoteEfficiency, sqQuoteConfirmation,
                                                    sqOrderConfirmation, sqOrderEfficiency, sqOrderingOperationMode,
                                                    sqOrderProgressTracking, sqCsoCompletedCount, sqCsoTotalQuantity,
                                                    sqBranchCount, sqOverseasBranches, sqEmployeeCount,
                                                    sqHumanResourcesCount, sqPaperContract, sqPaperDummy,
                                                    sqAttendanceSettlementDays, sqSalarySettlementDays,
                                                    sqHumanRightsVerification, sqStaffingBlueprint, sqBudgetManagement,
                                                    sqTalentManagement, sqEnterpriseResourceSharing,
                                                    sqChoCompletedCount, sqChoTotalQuantity, f_CPOHighStockLevel0101,
                                                    f_CPOComponentAssembly0102, f_CPOSupportAttributes0103,
                                                    f_CPOExpertiseScheduling0108, f_CPORollingDemand0104,
                                                    f_CPORepetitiveWork0109, f_CPODailyPlanScheduling0105,
                                                    f_CPOToolForScheduling0106, f_CPOCapacityScheduling0107,
                                                    f_CPOScheduleWithERP0110, f_CPOTotalQuantity,
                                                    f_CPOSupplierDelivery0111, f_CPOCompletedCount,
                                                    f_COOGroundTugCount0101, f_COOStackerCount0102,
                                                    f_COOStackerWorkers0103, f_COODailyStackerRuns0104,
                                                    f_COOManualFeeding0105, f_COOWarehouseCount0106,
                                                    f_COOSingleWarehouseStaff0107, f_COOWarehouseShelvingStaff0108,
                                                    f_COOMaterialTurnoverTime0109, f_COOSlowMovingInventory0110,
                                                    f_COOWarehouseWorkload0111, f_COOCompletedCount, f_COOTotalQuantity,
                                                    f_COOStackerCountWorkers, f_COOIntelligenceWarehousing)
values ('IndustryPlanQuickScreening', '2024-08-05 11:40:02', '0000063845', null, null, null, null,
        '魏茂娟                 ', '<EMAIL>',
        '{"sqAnnualRevenue":"1","sqSummitFreq":"1","sqCollateRate":"1","sqFirmCount":"","sqOverseasFirmCount":"","sqERPSysCount":"","sqJointReportOutputMethod":"","sqConsolCycle":"","sqCombinedOutputDays":"","sqIPODisclosure":"","sqIPODisclosureDeadline":"","sqCfoCompletedCount":3,"sqCfoTotalQuantity":11}',
        '{"sqIndustryAffil":"B","sqBizModel":"A","sqSalesP":"","sqLackInfoTools":"","sqBizTeamSize":"","sqProdType":"","sqProdTypeRatio":"","sqProdModelCount":"","sqQuoteEfficiency":"","sqQuoteConfirmation":"","sqOrderConfirmation":"","sqOrderEfficiency":"","sqOrderingOperationMode":"","sqOrderProgressTracking":"","sqCsoCompletedCount":2,"sqCsoTotalQuantity":14}',
        '{"sqBranchCount":"","sqOverseasBranches":"","sqEmployeeCount":"","sqHumanResourcesCount":"","sqPaperContract":"","sqPaperDummy":"","sqAttendanceSettlementDays":"","sqSalarySettlementDays":"","sqHumanRightsVerification":"","sqStaffingBlueprint":"","sqBudgetManagement":"","sqTalentManagement":"","sqEnterpriseResourceSharing":"","sqChoCompletedCount":0,"sqChoTotalQuantity":13}',
        '241958519460428', null, null, 0, null, 'Applayed', '2024-08-05 11:40:02', 0, null, 'D', '11111',
        '{"f_CPOHighStockLevel0101":"","f_CPOComponentAssembly0102":"","f_CPOSupportAttributes0103":"","f_CPORollingDemand0104":"","f_CPODailyPlanScheduling0105":"","f_CPOToolForScheduling0106":"","f_CPOCapacityScheduling0107":"","f_CPOExpertiseScheduling0108":"","f_CPORepetitiveWork0109":"","f_CPOScheduleWithERP0110":"","f_CPOSupplierDelivery0111":"","f_CPOCompletedCount":0,"f_CPOTotalQuantity":15,"f_CPOUseErp0114":"","f_CPOIndustry0115":"","f_CPOOrderDeliveryIssueOne0112":"","f_CPOOrderDeliveryIssueTwo0113":""}',
        '{"f_COOGroundTugCount0101":"","f_COOStackerCount0102":"","f_COOStackerWorkers0103":"","f_COODailyStackerRuns0104":"","f_COOManualFeeding0105":"","f_COOWarehouseCount0106":"","f_COOSingleWarehouseStaff0107":"","f_COOWarehouseShelvingStaff0108":"","f_COOMaterialTurnoverTime0109":"","f_COOSlowMovingInventory0110":"","f_COOWarehouseWorkload0111":"","f_COOCompletedCount":0,"f_COOTotalQuantity":12,"f_COOStackerCountWorkers":0,"f_COOIntelligenceWarehousing":""}',
        '1', '1', null, null, null, null, null, 3, 11, 'B', 'A', null, null, null, null, null, null, null, null, null,
        null, null, null, 2, 14, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 13,
        null, null, null, null, null, null, null, null, null, null, 15, null, 0, null, null, null, null, null, null,
        null, null, null, null, null, 0, 12, 0, null);

INSERT INTO servicecloud.IndustryPlanQuickScreening(uploadDataModelCode, collectedTime, CustomerCode, serviceCode,
                                                    businessDepartmentCode, customerName, businessDepartmentName,
                                                    SalesName, SalesContact, fscfo, fscso, fscho, eid,
                                                    businessDepartmentCodeACP, createTime, userid, ThemeApplySourceCode,
                                                    ThemeApplayStatus, ThemeLastUpdateTime, userSid, url, sqSummitFreq,
                                                    sqCollateRate, sqFirmCount, sqOverseasFirmCount, sqERPSysCount,
                                                    sqConsolCycle, sqIPODisclosure, sqCfoCompletedCount,
                                                    sqCfoTotalQuantity, sqIndustryAffil, sqBizModel, sqBizTeamSize,
                                                    sqProdType, sqProdTypeRatio, sqProdModelCount, sqQuoteEfficiency,
                                                    sqQuoteConfirmation, sqOrderConfirmation, sqOrderEfficiency,
                                                    sqOrderingOperationMode, sqOrderProgressTracking,
                                                    sqCsoCompletedCount, sqCsoTotalQuantity, sqBranchCount,
                                                    sqOverseasBranches, sqEmployeeCount, sqHumanResourcesCount,
                                                    sqPaperContract, sqPaperDummy, sqAttendanceSettlementDays,
                                                    sqSalarySettlementDays, sqHumanRightsVerification,
                                                    sqStaffingBlueprint, sqBudgetManagement, sqTalentManagement,
                                                    sqEnterpriseResourceSharing, sqChoCompletedCount,
                                                    sqChoTotalQuantity)
values ('IndustryPlanQuickScreening', '2024-08-05 11:34:20', '0000063845', null, null, null, null,
        '魏茂娟                 ', '<EMAIL>',
        '{"sqAnnualRevenue":"1","sqSummitFreq":"1","sqCollateRate":"1","sqFirmCount":"","sqOverseasFirmCount":"","sqERPSysCount":"","sqJointReportOutputMethod":"","sqConsolCycle":"","sqCombinedOutputDays":"","sqIPODisclosure":"","sqIPODisclosureDeadline":"","sqCfoCompletedCount":3,"sqCfoTotalQuantity":11}',
        '{"sqIndustryAffil":"B","sqBizModel":"A","sqSalesP":"","sqLackInfoTools":"","sqBizTeamSize":"","sqProdType":"","sqProdTypeRatio":"","sqProdModelCount":"","sqQuoteEfficiency":"","sqQuoteConfirmation":"","sqOrderConfirmation":"","sqOrderEfficiency":"","sqOrderingOperationMode":"","sqOrderProgressTracking":"","sqCsoCompletedCount":2,"sqCsoTotalQuantity":14}',
        '{"sqBranchCount":"","sqOverseasBranches":"","sqEmployeeCount":"","sqHumanResourcesCount":"","sqPaperContract":"","sqPaperDummy":"","sqAttendanceSettlementDays":"","sqSalarySettlementDays":"","sqHumanRightsVerification":"","sqStaffingBlueprint":"","sqBudgetManagement":"","sqTalentManagement":"","sqEnterpriseResourceSharing":"","sqChoCompletedCount":0,"sqChoTotalQuantity":13}',
        '241958519460428', null, null, 0, null, 'Applayed', '2024-08-05 11:34:20', 0, null, '1', '1', null, null, null,
        null, null, 3, 11, 'B', 'A', null, null, null, null, null, null, null, null, null, null, 2, 14, null, null,
        null, null, null, null, null, null, null, null, null, null, null, 0, 13);



SELECT a.grp_dpt_id,
       a.grp_dpt_name,
       a.grp_dpt_manager,
       a.bus_dpt_win_name,
       a.grp_dpt_manager_contact,
       a.bus_dpt_win_contact,
       a.progrp,
       a.cus_count,
       a.cus_compeleted_count,
       a.cus_nocompeleted_count,
       a.no_screening_count,
       (case when (b.allcount is NULL) THEN 0 ELSE b.allcount end) AS gqCount
FROM (SELECT a.grp_dpt_id,
             a.grp_dpt_name,
             c.grp_dpt_manager,
             c.bus_dpt_win_name,
             pp.progrp,
             c.grp_dpt_manager_contact,
             c.bus_dpt_win_contact,
             count(distinct a.eid) AS cus_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) THEN 0
                     ELSE 1 end)   AS cus_compeleted_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) AND
                          (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
                     ELSE 0 end)   AS cus_nocompeleted_count,
             sum(case
                     WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) and
                          (b.sqScraningState = 'D') THEN 1
                     ELSE 0 end)   AS no_screening_count
      FROM servicecloud.es_customerservice_v2 a
               inner join (select distinct CustomerServiceCode
                           from servicecloud.mars_customerservice_external
                           where (ContractState like 'B%' or ContractState like 'H%' or
                                  (ContractState like 'G%' and ContractState != 'G6'))
                             and ProductCode in
                                 ('37', '152', '08', '165', '100', '06', '164', '176', '178', 'MES', 'PLM',
                                  '137')) as cc on cc.CustomerServiceCode = a.CustomerServiceCode
               left join (select CustomerServiceCode,
                                 case
                                     when proList like '#37' or proList like '#165' or proList like '#152' then 'E10'
                                     when proList like '#100' or proList like '#06' then 'T'
                                     else '异构' end as progrp
                          from (select CustomerServiceCode, GROUP_CONCAT(concat('#', ProductCode), ', ') as proList
                                from servicecloud.mars_customerservice_external
                                where (ContractState like 'B%' or ContractState like 'H%' or
                                       (ContractState like 'G%' and ContractState != 'G6'))
                                group by CustomerServiceCode) pg) as pp
                         on pp.CustomerServiceCode = a.CustomerServiceCode
               LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
               LEFT JOIN servicecloud.IndustryPlanQuickScreening b ON b.eid = a.eid
      WHERE a.grp_dpt_name is NOT null
      GROUP BY a.grp_dpt_id, a.grp_dpt_name, c.grp_dpt_manager, c.bus_dpt_win_name, pp.progrp,
               c.grp_dpt_manager_contact, c.bus_dpt_win_contact) a
         LEFT JOIN (SELECT grp_dpt_id, progrp, count(distinct CustomerServiceCode) AS allcount
                    FROM (SELECT b.CustomerServiceCode, pp.progrp, b.grp_dpt_id
                          FROM AIEOM.tenant_tag_string a
                                   LEFT JOIN servicecloud.es_customerservice_v2 b ON b.eid = a.id
                                   inner join (select distinct CustomerServiceCode
                                               from servicecloud.mars_customerservice_external
                                               where (ContractState like 'B%' or ContractState like 'H%' or
                                                      (ContractState like 'G%' and ContractState != 'G6'))
                                                 and ProductCode in
                                                     ('37', '152', '08', '165', '100', '06', '164', '176', '178', 'MES',
                                                      'PLM', '137')) as cc
                                              on cc.CustomerServiceCode = b.CustomerServiceCode
                                   left join (select CustomerServiceCode,
                                                     case
                                                         when proList like '#37' or proList like '#165' or proList like '#152'
                                                             then 'E10'
                                                         when proList like '#100' or proList like '#06' then 'T'
                                                         else '异构' end as progrp
                                              from (select CustomerServiceCode,
                                                           GROUP_CONCAT(concat('#', ProductCode), ', ') as proList
                                                    from servicecloud.mars_customerservice_external
                                                    where (ContractState like 'B%' or ContractState like 'H%' or
                                                           (ContractState like 'G%' and ContractState != 'G6'))
                                                    group by CustomerServiceCode) pg) as pp
                                             on pp.CustomerServiceCode = b.CustomerServiceCode

                          WHERE tagId = '747209582092864'
                          GROUP BY b.CustomerServiceCode, b.grp_dpt_id, pp.progrp) AS aa
                    WHERE CustomerServiceCode is NOT null
                    GROUP BY grp_dpt_id, progrp) b ON a.grp_dpt_id = b.grp_dpt_id AND a.progrp = b.progrp
ORDER BY a.grp_dpt_id
limit 1000;


SELECT a.grp_dpt_id,
       a.grp_dpt_name,
       c.grp_dpt_manager,
       c.bus_dpt_win_name,
       pp.progrp,
       c.grp_dpt_manager_contact,
       c.bus_dpt_win_contact,
       count(distinct a.eid) AS cus_count,
       sum(case
               WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) THEN 0
               ELSE 1 end)   AS cus_compeleted_count,
       sum(case
               WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) AND
                    (b.sqScraningState is null OR b.sqScraningState <> 'D') THEN 1
               ELSE 0 end)   AS cus_nocompeleted_count,
       sum(case
               WHEN (b.f_CPOCompletedCount is null OR b.f_CPOCompletedCount < b.f_CPOTotalQuantity) and
                    (b.sqScraningState = 'D') THEN 1
               ELSE 0 end)   AS no_screening_count
FROM servicecloud.es_customerservice_v2 a
         inner join (select distinct CustomerServiceCode
                     from servicecloud.mars_customerservice_external
                     where (ContractState like 'B%' or ContractState like 'H%' or
                            (ContractState like 'G%' and ContractState != 'G6'))
                       and ProductCode in
                           ('37', '152', '08', '165', '100', '06', '164', '176', '178', 'MES', 'PLM',
                            '137')) as cc on cc.CustomerServiceCode = a.CustomerServiceCode
         left join (select CustomerServiceCode,
                           case
                               when proList like '#37' or proList like '#165' or proList like '#152' then 'E10'
                               when proList like '#100' or proList like '#06' then 'T'
                               else '异构' end as progrp
                    from (select CustomerServiceCode, GROUP_CONCAT(concat('#', ProductCode), ', ') as proList
                          from servicecloud.mars_customerservice_external
                          where (ContractState like 'B%' or ContractState like 'H%' or
                                 (ContractState like 'G%' and ContractState != 'G6'))
                          group by CustomerServiceCode) pg) as pp
                   on pp.CustomerServiceCode = a.CustomerServiceCode
         LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
         LEFT JOIN servicecloud.IndustryPlanQuickScreening b ON b.eid = a.eid
WHERE a.grp_dpt_name is NOT null
GROUP BY a.grp_dpt_id, a.grp_dpt_name, c.grp_dpt_manager, c.bus_dpt_win_name, pp.progrp,
         c.grp_dpt_manager_contact, c.bus_dpt_win_contact;

select a.grp_dpt_id,a.dpt_id,c.dpt_id,a.eid,a.Sales from  servicecloud.es_customerservice_v2 a   LEFT JOIN servicecloud.es_customerservice_v3 c ON c.dpt_id = a.dpt_id
WHERE c.grp_dpt_manager is null or c.grp_dpt_manager = '' ORDER BY a.dpt_id;






CREATE TABLE `es_customerservice_v2` (
                                         `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultant` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultantEmail` varchar(65533) NULL COMMENT "",
                                         `IndustryCode` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eId`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eId`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `es_customerservice_v2` (
                                         `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultant` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultantEmail` varchar(65533) NULL COMMENT "",
                                         `IndustryCode` varchar(65533) NULL COMMENT ""
) ENGINE=OLAP
    PRIMARY KEY(`eId`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eId`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE external TABLE `es_customerservice_v5` (
                                         `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultant` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultantEmail` varchar(65533) NULL COMMENT "",
                                         `IndustryCode` varchar(65533) NULL COMMENT ""
) ENGINE=MYSQL
    COMMENT "MYSQL"
    PROPERTIES (
                   "host" = "***************",
                   "port" = "4306",
                   "user" = "digiwin",
                   "password" = "gitlab123",
                   "database" = "aio-db",
                   "table" = "es_customerservice_v5"
               );




insert into es_customerservice_v2  select * from servicecloud.es_customerservice_v2_external