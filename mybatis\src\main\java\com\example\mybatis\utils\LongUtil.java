package com.example.mybatis.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 长整数实用类
 */
public final class LongUtil {
    /**
     * 检查长整数是否为空或小于等于0
     * @param value 数值
     * @return 是否为空或小于等于0
     */
    public static final boolean isEmpty(Long value) {
        return value == null || value <= 0;
    }

    /**
     * 尝试将字串转换为长整数
     * @param value 字串
     * @return 长整数
     */
    public static final Optional<Long> tryParseLongByString(String value) {
        if (StringUtils.isBlank(value)) {
            return Optional.empty();
        }
        try {
            return Optional.of(Long.parseLong(value));
        } catch (Exception ex) {
            return Optional.empty();
        }
    }

    /**
     * 将对象转换为长整数
     * @param obj 对象
     * @return 整数值
     */
    public static final Long objectToLong(Object obj) {
        if (obj == null) {
            return 0L;
        }

        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }
        if (obj instanceof Long) {
            return (Long) obj;
        }
        if (obj instanceof Double) {
            return ((Double) obj).longValue();
        }
        if (obj instanceof Float) {
            return ((Float) obj).longValue();
        }
        String str = Objects.toString(obj);
        Optional<Long> optResult = tryParseLongByString(str);
        if (optResult.isPresent()) {
            return optResult.get();
        }
        return 0L;
    }

    /**
     * 安全将Long输出为字串
     * @param value 值
     * @return 字串值(当为null时输出空字串)
     */
    public static final String safeToString(Long value) {
        return safeToString(value, "");
    }

    /**
     * 安全将Long输出为字串
     * @param value 值
     * @param nullDefaultValue null时的默认值
     * @return 字串值
     */
    public static final String safeToString(Long value, String nullDefaultValue) {
        if (value == null) {
            return nullDefaultValue;
        }
        return Long.toString(value);
    }
}
