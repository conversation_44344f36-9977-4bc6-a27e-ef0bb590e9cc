package com.digiwin.escloud.aiocdp.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 进阶规则分析规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@TableName("aio_cdp_group_advance_analysis_rule")
@ApiModel(value = "AioCdpGroupAdvanceAnalysisRule对象", description = "进阶规则分析规则")
public class AioCdpGroupAdvanceAnalysisRule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String garId;

    private String analysisType;

    private String analysisTypeObjId;

    private String analysisCondition;

    private String analysisValue;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGarId() {
        return garId;
    }

    public void setGarId(String garId) {
        this.garId = garId;
    }

    public String getAnalysisType() {
        return analysisType;
    }

    public void setAnalysisType(String analysisType) {
        this.analysisType = analysisType;
    }

    public String getAnalysisTypeObjId() {
        return analysisTypeObjId;
    }

    public void setAnalysisTypeObjId(String analysisTypeObjId) {
        this.analysisTypeObjId = analysisTypeObjId;
    }

    public String getAnalysisCondition() {
        return analysisCondition;
    }

    public void setAnalysisCondition(String analysisCondition) {
        this.analysisCondition = analysisCondition;
    }

    public String getAnalysisValue() {
        return analysisValue;
    }

    public void setAnalysisValue(String analysisValue) {
        this.analysisValue = analysisValue;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AioCdpGroupAdvanceAnalysisRule{" +
            "id = " + id +
            ", garId = " + garId +
            ", analysisType = " + analysisType +
            ", analysisTypeObjId = " + analysisTypeObjId +
            ", analysisCondition = " + analysisCondition +
            ", analysisValue = " + analysisValue +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
