package com.example.debezium.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "timely")
public class DebeziumConfigProperties {
    private Boolean offsetFileClean = true;

    public Boolean getOffsetFileClean() {
        return offsetFileClean;
    }

    public void setOffsetFileClean(Boolean offsetFileClean) {
        this.offsetFileClean = offsetFileClean;
    }
}