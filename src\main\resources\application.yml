# DataSource Config
spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: **************************************************************************
    username: readniss
    password: Escread001
#  sql:
#    init:
#      schema-locations: classpath:db/schema-h2.sql
#      data-locations: classpath:db/data-h2.sql
#      mode: always

# Logger Config
logging:
  level:
    com.baomidou.mybatisplus.samples.quickstart: debug


datasource:
  password11: ENC(4sacmQET4tFPrTNv1QwwkIqTsA0eCrhaasGOA4EBIQ8Z7eJTbSxig8IBx0ITy5xY)

jasypt:
  encryptor:
    password: 11111