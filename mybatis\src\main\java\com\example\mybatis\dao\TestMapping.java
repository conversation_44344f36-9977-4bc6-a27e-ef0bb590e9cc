package com.example.mybatis.dao;

import com.example.mybatis.model.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

//@Mapper
public interface TestMapping {
    @Select(" select DISTINCT  g.* ,c.Sales,c.SalesContact, cc.*,c.IndustryCode  --  count(DISTINCT cc.eid)  --  \n" +
            "from   grp_bus_dpt_mapping g\n" +
            " INNER JOIN mars_customerservicesatff s  on s.departmentcode = g.dpt_id \n" +
            "INNER JOIN mars_customerservice c on c.SalesContact = s.email\n" +
            "INNER JOIN mars_customer cc on cc.CustomerServiceCode = c.CustomerServiceCode\n" +
            "where s.workStatus = 'work'  \n" +
            "-- and (c.ContractState like 'B%' or  c.ContractState like 'H%'  or  (c.ContractState like 'G%' and c.ContractState != 'G6')) \n" +
            " --  and c.ProductCode in ('37','152','08','165','100','06','164','176','178','MES','PLM','137','150','147'  )")
    List<EsCustomerServiceV41> selectEs();
    @Select(" select * from mars_customerservice limit 5")
    List<EsCustomerServiceV41> selectEsMaster();

    @Insert({
            "<script>",
            "INSERT INTO es_customerservice_v5 (",
            "eId, SalesContact, CustomerCode, grp_dpt_id, grp_dpt_name, grp_dpt_manager,",
            "bus_dpt_id, bus_dpt_name, bus_dpt_manager, dpt_id, dpt_name, dpt_manager,",
            "Sales, CustomerServiceCode, CustomerName, CustomerFullNameCH, CustomerFullNameEN, another_name,",
            "current_valid_status, t100_cust_id, taxNo, contacts, tenantTelephone, tenantEmail,",
            "address, __version__, grp_dpt_manager_contact, bus_dpt_manager_contact, bus_dpt_win_name,",
            "bus_dpt_win_contact, dpt_manager_contact, updateDate,IndustryCode",
            ") VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(",
            "#{item.eId}, #{item.SalesContact}, #{item.CustomerCode}, #{item.grp_dpt_id}, #{item.grp_dpt_name}, #{item.grp_dpt_manager},",
            "#{item.bus_dpt_id}, #{item.bus_dpt_name}, #{item.bus_dpt_manager}, #{item.dpt_id}, #{item.dpt_name}, #{item.dpt_manager},",
            "#{item.Sales}, #{item.CustomerServiceCode}, #{item.CustomerName}, #{item.CustomerFullNameCH}, #{item.CustomerFullNameEN}, #{item.another_name},",
            "#{item.current_valid_status}, #{item.t100_cust_id}, #{item.taxNo}, #{item.contacts}, #{item.tenantTelephone}, #{item.tenantEmail},",
            "#{item.address}, #{item.__version__}, #{item.grp_dpt_manager_contact}, #{item.bus_dpt_manager_contact}, #{item.bus_dpt_win_name},",
            "#{item.bus_dpt_win_contact}, #{item.dpt_manager_contact}, #{item.updateDate},#{item.IndustryCode}",
            ")",
            "</foreach>",
            "ON DUPLICATE KEY UPDATE",
            "updateDate = VALUES(updateDate),",
            "SalesContact = VALUES(SalesContact),",
            "CustomerCode = VALUES(CustomerCode),",
            "grp_dpt_id = VALUES(grp_dpt_id),",
            "grp_dpt_name = VALUES(grp_dpt_name),",
            "grp_dpt_manager = VALUES(grp_dpt_manager),",
            "bus_dpt_id = VALUES(bus_dpt_id),",
            "bus_dpt_name = VALUES(bus_dpt_name),",
            "bus_dpt_manager = VALUES(bus_dpt_manager),",
            "dpt_id = VALUES(dpt_id),",
            "dpt_name = VALUES(dpt_name),",
            "dpt_manager = VALUES(dpt_manager),",
            "Sales = VALUES(Sales),",
            "CustomerServiceCode = VALUES(CustomerServiceCode),",
            "CustomerName = VALUES(CustomerName),",
            "CustomerFullNameCH = VALUES(CustomerFullNameCH),",
            "CustomerFullNameEN = VALUES(CustomerFullNameEN),",
            "another_name = VALUES(another_name),",
            "current_valid_status = VALUES(current_valid_status),",
            "t100_cust_id = VALUES(t100_cust_id),",
            "taxNo = VALUES(taxNo),",
            "contacts = VALUES(contacts),",
            "tenantTelephone = VALUES(tenantTelephone),",
            "tenantEmail = VALUES(tenantEmail),",
            "address = VALUES(address),",
            "__version__ = VALUES(__version__),",
            "grp_dpt_manager_contact = VALUES(grp_dpt_manager_contact),",
            "bus_dpt_manager_contact = VALUES(bus_dpt_manager_contact),",
            "bus_dpt_win_name = VALUES(bus_dpt_win_name),",
            "bus_dpt_win_contact = VALUES(bus_dpt_win_contact),",
            "dpt_manager_contact = VALUES(dpt_manager_contact),",
            "IndustryCode = VALUES(IndustryCode)",
            "</script>"
    })
    int insertBatch(Set<EsCustomerServiceV41> list);



    @Insert({
            "<script>",
            "INSERT INTO es_customerservice_v3 (grp_dpt_id, grp_dpt_name, grp_dpt_manager, grp_dpt_manager_contact, ",
            "bus_dpt_id, bus_dpt_name, bus_dpt_manager, bus_dpt_manager_contact, bus_dpt_win_name, bus_dpt_win_contact, ",
            "dpt_id, dpt_name, dpt_manager, dpt_manager_contact) ",
            "VALUES ",
            "<foreach collection='list' item='item' index='index' separator=','>",
            "(#{item.grp_dpt_id}, #{item.grp_dpt_name}, #{item.grp_dpt_manager}, #{item.grp_dpt_manager_contact}, ",
            "#{item.bus_dpt_id}, #{item.bus_dpt_name}, #{item.bus_dpt_manager}, #{item.bus_dpt_manager_contact}, ",
            "#{item.bus_dpt_win_name}, #{item.bus_dpt_win_contact}, #{item.dpt_id}, #{item.dpt_name}, #{item.dpt_manager}, #{item.dpt_manager_contact})",
            "</foreach>",
            "</script>"
    })
    int insertV3Batch(@Param("list") Set<GrpBusDptMapping> list);

    @Select(" select * from aiops_device where platform = 'LINUX'")
    List<AiopsKitDevice> selectDevice();

    @Select("SELECT * FROM grp_bus_dpt_mapping")
    List<GrpBusDptMapping> selectV3Es();
    @Select("  select  ad.eid,ad.deviceId,ai.aiopsItemId,ai.aiopsAuthStatus,adcd.execParamsContent from aiops_device_collect_detail adcd\n" +
            "                                 left join aiops_device_collect adc on adcd.adcId = adc.id\n" +
            "                                 left join aiops_device ad on ad.deviceId = adc.deviceId\n" +
            "                                 left join aiops_instance ai on ai.id = adcd.aiId\n" +
            "      where accId = '652032207934016'\n" +
            "        and ad.deviceId not IN ('543301626392227895','543048365357675587','543479602153010243','455746214583877956','543323122787436102','543312797115630132')    And (ai.aiopsAuthStatus = 'AUTHED' or ai.aiopsAuthStatus = 'NONE') and execParamsContent is not null and execParamsContent !=''\n" +
            "            order by ai.__version__ desc")
    List<E10Model> selectE10(@Param("num")int num,@Param("size")int size);

    @Select("select  count(1) from aiops_device_collect_detail adcd\n" +
            "                     left join aiops_device_collect adc on adcd.adcId = adc.id\n" +
            "                     left join aiops_device ad on ad.deviceId = adc.deviceId\n" +
            "                     left join aiops_instance ai on ai.id = adcd.aiId\n" +
            "  where accId = '652032207934016'\n" +
            "        and ad.deviceId not IN ('543301626392227895','543048365357675587','543479602153010243','455746214583877956','543323122787436102','543312797115630132')    And (ai.aiopsAuthStatus = 'AUTHED' or ai.aiopsAuthStatus = 'NONE') and execParamsContent is not null and execParamsContent !=''\n" +
            "            order by ai.__version__ desc")
    int selectE10Count();

    @Select("\n" +
            "select adcd.id adcdId,adc.deviceId  from aiops_device_collect_detail adcd\n" +
            "                                        left join aiops_device_collect adc on adcd.adcId = adc.id\n" +
            "where  adcd.accId =779544910123584  and adc.deviceId in (\n" +
            "                       '466618914378363971',\n" +
            "                       '468794953586193202',\n" +
            "                       '505335755703338052',\n" +
            "                       '467934635666457913',\n" +
            "                       '448668501620568880',\n" +
            "                       '472705999929624134',\n" +
            "                       '465755298628383801',\n" +
            "                       '521129101466022706',\n" +
            "                       '485494740821750851',\n" +
            "                       '507386931017954371',\n" +
            "                       '508393089115636272',\n" +
            "                       '454015059429963073',\n" +
            "                       '470106627236705347',\n" +
            "                       '475316713903109187',\n" +
            "                       '479128310899556661',\n" +
            "                       '491417398415471683',\n" +
            "                       '526237970517210179',\n" +
            "                       '487211785967907632',\n" +
            "                       '455921677621015620',\n" +
            "                       '525933420358939715',\n" +
            "                       '447512815628988980',\n" +
            "                       '508677031383281989',\n" +
            "                       '526808186749989955',\n" +
            "                       '512893257819109441',\n" +
            "                       '508400877988495681',\n" +
            "                       '475940644490064963',\n" +
            "                       '525202584194007860',\n" +
            "                       '521121625555354675',\n" +
            "                       '527071971813371956',\n" +
            "                       '478214680540624433',\n" +
            "                       '457934657321711670',\n" +
            "                       '465198001070028853',\n" +
            "                       '482297779582940227',\n" +
            "                       '528963243968837428',\n" +
            "                       '504746961740510275',\n" +
            "                       '473879242958975554',\n" +
            "                       '469124003429103683',\n" +
            "                       '455746214583877956',\n" +
            "                       '456799204434523203',\n" +
            "                       '511267788841567287',\n" +
            "                       '452169917286855479',\n" +
            "                       '523005549709635632',\n" +
            "                       '472299007838794819',\n" +
            "                       '504315209247110211',\n" +
            "                       '515800047309304899',\n" +
            "                       '509531956183054392',\n" +
            "                       '506364645401966384',\n" +
            "                       '526948711033485378',\n" +
            "                       '477356747116261424',\n" +
            "                       '528695933525832242',\n" +
            "                       '523741153305048388',\n" +
            "                       '508814708791324977',\n" +
            "                       '384025625507153459',\n" +
            "                       '474025426985961030',\n" +
            "                       '460973531820209465',\n" +
            "                       '479263625790375217',\n" +
            "                       '490394753364407093',\n" +
            "                       '528091567722869061',\n" +
            "                       '491289684543025458',\n" +
            "                       '478973243420590902',\n" +
            "                       '465341799125235014',\n" +
            "                       '477384012759838768',\n" +
            "                       '435914263559681091',\n" +
            "                       '496202195507627317',\n" +
            "                       '457202982061093444',\n" +
            "                       '504604295610512708',\n" +
            "                       '454038149828457780',\n" +
            "                       '465763606437311028',\n" +
            "                       '479983787468408130',\n" +
            "                       '467961577308440132',\n" +
            "                       '516076303816864835',\n" +
            "                       '513592734313886787',\n" +
            "                       '527211029566863414',\n" +
            "                       '527078832134439473',\n" +
            "                       '461703602222675011',\n" +
            "                       '522019467228627270',\n" +
            "                       '506377886064718644',\n" +
            "                       '487227426695295284',\n" +
            "                       '520858811490120771',\n" +
            "                       '520975815425406019',\n" +
            "                       '419514983492105283',\n" +
            "                       '465066933381771574',\n" +
            "                       '524022600918123586',\n" +
            "                       '477521360260707651',\n" +
            "                       '451728461237597251',\n" +
            "                       '522147187258308165',\n" +
            "                       '461721480510583875',\n" +
            "                       '526660081849218370',\n" +
            "                       '509862925574354229',\n" +
            "                       '521699045991789636',\n" +
            "                       '511267124027601220',\n" +
            "                       '457641298002719792',\n" +
            "                       '510718298124464949',\n" +
            "                       '523035276050707522',\n" +
            "                       '455791644868883523',\n" +
            "                       '505771241328624184',\n" +
            "                       '473868791575491651',\n" +
            "                       '482590251622085953',\n" +
            "                       '463758910285689923',\n" +
            "                       '527690140382937923',\n" +
            "                       '510404600105283890',\n" +
            "                       '515918104115557429',\n" +
            "                       '475484340436878136',\n" +
            "                       '480142391550949443',\n" +
            "                       '482177022014010167',\n" +
            "                       '475937856385201219',\n" +
            "                       '482566160647206966',\n" +
            "                       '452739352271795267',\n" +
            "                       '437782144232731715',\n" +
            "                       '473032836958401603',\n" +
            "                       '511012433070932803',\n" +
            "                       '497669897300489283',\n" +
            "                       '484187946723853364',\n" +
            "                       '456064190960906561',\n" +
            "                       '515493212597531715',\n" +
            "                       '432292048800855107',\n" +
            "                       '463734806761451568',\n" +
            "                       '524781911256609859',\n" +
            "                       '511271553934045766',\n" +
            "                       '518516571275015235',\n" +
            "                       '474446081736848945',\n" +
            "                       '461691477160899651',\n" +
            "                       '424619881275600965',\n" +
            "                       '479982478325463362',\n" +
            "                       '464911243350651971',\n" +
            "                       '478080602180956485',\n" +
            "                       '462855865662189623',\n" +
            "                       '524751873698182211',\n" +
            "                       '510848891554378546',\n" +
            "                       '469256851062536248',\n" +
            "                       '478513331611649604',\n" +
            "                       '400286962633687609',\n" +
            "                       '526207057624314947',\n" +
            "                       '465178163001963332',\n" +
            "                       '456932285908795961',\n" +
            "                       '510871730596622640',\n" +
            "                       '396051271791948598',\n" +
            "                       '510714916794020931',\n" +
            "                       '533332692977071430',\n" +
            "                       '489122840121984048',\n" +
            "                       '490139351070553155',\n" +
            "                       '469961079653417521',\n" +
            "                       '505359239762425155',\n" +
            "                       '455755585481093443',\n" +
            "                       '452126151855322179',\n" +
            "                       '521014424362824770',\n" +
            "                       '527832622517204035',\n" +
            "                       '474327498159567939',\n" +
            "                       '513178599776334915',\n" +
            "                       '473005130963235654',\n" +
            "                       '435673153289598264',\n" +
            "                       '515068654660890678',\n" +
            "                       '526662749963433027',\n" +
            "                       '478940512515012147',\n" +
            "                       '486668115569030211',\n" +
            "                       '479120133684478263',\n" +
            "                       '464908671436993603',\n" +
            "                       '449837968383815737',\n" +
            "                       '519528289123971394',\n" +
            "                       '435811649660662851',\n" +
            "                       '382135512288212803',\n" +
            "                       '478540781636826179',\n" +
            "                       '478374399016186947',\n" +
            "                       '509855168175551555',\n" +
            "                       '496515776438613059',\n" +
            "                       '475928028812162115',\n" +
            "                       '452573065046410550',\n" +
            "                       '466628426338415683',\n" +
            "                       '497794353423467312',\n" +
            "                       '455750660848108610',\n" +
            "                       '487376444612162627',\n" +
            "                       '475490661756318787',\n" +
            "                       '506367309707753523',\n" +
            "                       '515904787988887619',\n" +
            "                       '525072049702450230',\n" +
            "                       '510542161364660272',\n" +
            "                       '510440770524689475',\n" +
            "                       '475504458936103489',\n" +
            "                       '469256648125329730',\n" +
            "                       '452009088562246962',\n" +
            "                       '475507198991021123',\n" +
            "                       '470996703961429829',\n" +
            "                       '496652078333047875',\n" +
            "                       '464886528884094019',\n" +
            "                       '510829083148431683',\n" +
            "                       '485326966128391732',\n" +
            "                       '504916714064983107',\n" +
            "                       '484462065646321976',\n" +
            "                       '481996653503465011',\n" +
            "                       '508256658640618307',\n" +
            "                       '528836642845242435',\n" +
            "                       '478983030208218433',\n" +
            "                       '489545836116586550',\n" +
            "                       '510585985633891651',\n" +
            "                       '510540822307943476',\n" +
            "                       '477352846581314369',\n" +
            "                       '456791594104796998',\n" +
            "                       '483616688307188803',\n" +
            "                       '461699186912408386',\n" +
            "                       '524198674411045189',\n" +
            "                       '510136401593906243',\n" +
            "                       '475896890265711683',\n" +
            "                       '461260476035118147',\n" +
            "                       '519562497682847288',\n" +
            "                       '522176337251681076',\n" +
            "                       '476482407252374595',\n" +
            "                       '528700509696046147',\n" +
            "                       '478978499957241392',\n" +
            "                       '524165121757430073',\n" +
            "                       '485207744345814081',\n" +
            "                       '512607596725679942',\n" +
            "                       '525763967490998585',\n" +
            "                       '508812690022483526',\n" +
            "                       '487493168569267267',\n" +
            "                       '512467938213639235',\n" +
            "                       '520842491017048626',\n" +
            "                       '521893155830772803',\n" +
            "                       '455035416957958449',\n" +
            "                       '434741637629887288',\n" +
            "                       '514745382840841283',\n" +
            "                       '469810678555751491',\n" +
            "                       '513917646324904006',\n" +
            "                       '521120433936811587',\n" +
            "                       '470246314739774531',\n" +
            "                       '482009894535313217',\n" +
            "                       '521564985164313667',\n" +
            "                       '477226839320835139',\n" +
            "                       '468806764159055929',\n" +
            "                       '442035683163386947',\n" +
            "                       '452735839609304386',\n" +
            "                       '476334245879493700',\n" +
            "                       '525191454323131188',\n" +
            "                       '514046157198996547',\n" +
            "                       '480000722037913909',\n" +
            "                       '408968408516801603',\n" +
            "                       '478533883046937394',\n" +
            "                       '515475817594106934',\n" +
            "                       '523050262852547652',\n" +
            "                       '519828583875625528',\n" +
            "                       '477493391232350273',\n" +
            "                       '521012132544786996',\n" +
            "                       '488109070050407491',\n" +
            "                       '521855337502880835',\n" +
            "                       '476053686351574083',\n" +
            "                       '465345370289877047',\n" +
            "                       '510595932761376312',\n" +
            "                       '521159347984675908',\n" +
            "                       '477960810023371573',\n" +
            "                       '482450389082325296',\n" +
            "                       '475042155014928178',\n" +
            "                       '477089627111437363',\n" +
            "                       '493768354209743416',\n" +
            "                       '459744571673298481',\n" +
            "                       '519723042721513776',\n" +
            "                       '484597082607337798',\n" +
            "                       '515618179351851844',\n" +
            "                       '510732237122057520',\n" +
            "                       '471985996813055043',\n" +
            "                       '533041539945869367',\n" +
            "                       '523768958201902147',\n" +
            "                       '524784636983784505',\n" +
            "                       '521736449855337539',\n" +
            "                       '461120118315824449',\n" +
            "                       '449553448862495541',\n" +
            "                       '451113715790529861',\n" +
            "                       '512570257487968050',\n" +
            "                       '475469309527733315',\n" +
            "                       '452892555818844995',\n" +
            "                       '474456369928353845',\n" +
            "                       '472444964266066742',\n" +
            "                       '528704618906010937',\n" +
            "                       '510435507361038660',\n" +
            "                       '484462191643210305',\n" +
            "                       '526224438031561795',\n" +
            "                       '474459430864303684',\n" +
            "                       '469841132390465843',\n" +
            "                       '477346765494829872',\n" +
            "                       '464746511742288692',\n" +
            "                       '477213060730204208',\n" +
            "                       '475318758408205379',\n" +
            "                       '475921518648375366',\n" +
            "                       '515937228480852016',\n" +
            "                       '528236118840194115',\n" +
            "                       '526961102970434617',\n" +
            "                       '514639507769799747',\n" +
            "                       '491265931394167875',\n" +
            "                       '510879660414874691',\n" +
            "                       '509867286627107124',\n" +
            "                       '511010328989938755',\n" +
            "                       '469377426649003075',\n" +
            "                       '472898532861294404',\n" +
            "                       '483468864642888771',\n" +
            "                       '510874271136887875',\n" +
            "                       '434637941080340036',\n" +
            "                       '510717532043228227',\n" +
            "                       '523188584287581251',\n" +
            "                       '513465312700543299',\n" +
            "                       '484605263177856067',\n" +
            "                       '517822763801523267',\n" +
            "                       '482279551641076787',\n" +
            "                       '465056117244834883',\n" +
            "                       '515805773238907971',\n" +
            "                       '523631834777530932',\n" +
            "                       '478507153720620099',\n" +
            "                       '479964613627098179',\n" +
            "                       '489669949766252345',\n" +
            "                       '477519181319779906',\n" +
            "                       '491696240141025857',\n" +
            "                       '472890954525717317',\n" +
            "                       '437651800129025091',\n" +
            "                       '523890661250839619',\n" +
            "                       '523915623869202756',\n" +
            "                       '472298839496209968',\n" +
            "                       '532169500993271107',\n" +
            "                       '515619540520939568',\n" +
            "                       '476938623627838520',\n" +
            "                       '515654987389355585',\n" +
            "                       '475946992132965441',\n" +
            "                       '515934592629224515',\n" +
            "                       '474190285128090689',\n" +
            "                       '510984463321151280',\n" +
            "                       '515932743226373187',\n" +
            "                       '527101823832568899',\n" +
            "                       '524784995009575222',\n" +
            "                       '479275659449873475',\n" +
            "                       '452857525646144579',\n" +
            "                       '497523297550874163',\n" +
            "                       '525936810497422390',\n" +
            "                       '475496243485750339',\n" +
            "                       '485322213646415153',\n" +
            "                       '472457409118352451',\n" +
            "                       '516336381602055746',\n" +
            "                       '509850095550739523',\n" +
            "                       '509531053585609795',\n" +
            "                       '520693830253560889',\n" +
            "                       '463025207011980355',\n" +
            "                       '521557177568080963',\n" +
            "                       '504898733536719939',\n" +
            "                       '455915183546582326',\n" +
            "                       '510575467695194931',\n" +
            "                       '405038473695736377',\n" +
            "                       '462898555758065473',\n" +
            "                       '517684048504566851',\n" +
            "                       '510715906867868739',\n" +
            "                       '450999743800161347',\n" +
            "                       '510869845592524353',\n" +
            "                       '452168124809098307',\n" +
            "                       '525940235331191605',\n" +
            "                       '506384983800164663',\n" +
            "                       '476045289875058743',\n" +
            "                       '418649857692025656',\n" +
            "                       '525650150371112003',\n" +
            "                       '508232656484316227',\n" +
            "                       '511411393237365827',\n" +
            "                       '480290018233955889',\n" +
            "                       '492428908411503671',\n" +
            "                       '479401383309620277',\n" +
            "                       '451573999500013878',\n" +
            "                       '496195688833495347',\n" +
            "                       '461875906529473603',\n" +
            "                       '493191291933309234',\n" +
            "                       '526801462760584259',\n" +
            "                       '475489148048782403',\n" +
            "                       '524632623092347203',\n" +
            "                       '456207384969229379',\n" +
            "                       '526814187993707587',\n" +
            "                       '435479882227529538'\n" +
            "    )")
    List<AdcdModel> selectAdcd();
    @Select({
            "<script>",
            "SELECT ea.serviceCode, ea.productCode, IFNULL(ea.snmpAuth, 0) AS snmpAuth, IFNULL(ea.hostAuth, 0) AS hostAuth,\n" +
                    "               IFNULL(ea.clientAuth, 0) AS clientAuth,\n" +
                    "               CAST(ea.startDate AS CHAR) AS startDate, CAST(ea.endDate AS CHAR) AS endDate,\n" +
                    "               IFNULL(mcs.ServiceStaffCode, '') AS staffCode, IF(IFNULL(mcs.IsTrial, 0) = 0, FALSE, TRUE) AS isTrial,\n" +
                    "               mcs.ContractStartDate AS contractStartDate, mcs.ContractExprityDate AS contractExpiryDate\n" +
                    "        FROM esclient_auth ea\n" +
                    "        INNER JOIN (\n" +
                    "            SELECT serviceCode\n" +
                    "            FROM esclient_auth\n" +
                    "            WHERE IFNULL(serviceCode, '') != '' AND IFNULL(productCode, '') != ''\n" +
                    "            <if test=\"filterProdcutList != null\">\n" +
                    "                <foreach collection=\"filterProdcutList\" item=\"item\"\n" +
                    "                         open=\" AND productCode IN (\" separator=\", \" close=\")\">\n" +
                    "                    #{item}\n" +
                    "                </foreach>\n" +
                    "            </if>\n" +
                    "            <if test=\"ecServiceCodeList != null\">\n" +
                    "                <foreach collection=\"ecServiceCodeList\" item=\"item\"\n" +
                    "                         open=\" AND serviceCode IN (\" separator=\", \" close=\")\">\n" +
                    "                    #{item}\n" +
                    "                </foreach>\n" +
                    "            </if>\n" +
                    "            GROUP BY serviceCode\n" +
                    "            ORDER BY serviceCode\n" +
                    "            LIMIT #{start}, ${size}\n" +
                    "        ) ea2 ON ea.serviceCode = ea2.serviceCode\n" +
                    "        LEFT JOIN mars_customerservice mcs ON ea.productCode = mcs.ProductCode\n" +
                    "                                              AND ea.serviceCode = mcs.CustomerServiceCode\n" +
                    "        WHERE IFNULL(ea.productCode, '') != ''\n" +
                    "        <if test=\"filterProdcutList != null\">\n" +
                    "            <foreach collection=\"filterProdcutList\" item=\"item\"\n" +
                    "                     open=\" AND ea.productCode IN (\" separator=\", \" close=\")\">\n" +
                    "                #{item}\n" +
                    "            </foreach>\n" +
                    "        </if>\n" +
                    "        ORDER BY ea.serviceCode, ea.productCode",
            "</script>"
    })

    List<EsclientContractSetting> selectEsclientTransferContractSetting(@Param("ecServiceCodeList") List<String> ecServiceCodeList,
                                                               @Param("filterProdcutList") List<String> filterProdcutList,
                                                               @Param("start") int start,@Param("size")int size);

    @Select("<script>SELECT COUNT(DISTINCT serviceCode)\n" +
            "        FROM esclient_auth ea\n" +
            "        WHERE 1 = 1 AND IFNULL(ea.serviceCode, '') != '' AND IFNULL(ea.productCode, '') != ''\n" +
            "        <if test=\"filterProdcutList != null\">\n" +
            "            <foreach collection=\"filterProdcutList\" item=\"item\"\n" +
            "                     open=\" AND ea.productCode IN (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if>\n" +
            "        <if test=\"ecServiceCodeList != null\">\n" +
            "            <foreach collection=\"ecServiceCodeList\" item=\"item\"\n" +
            "                     open=\" AND ea.serviceCode IN (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if></script>")
    int selectEsclientTransferContractSettingCount(@Param("ecServiceCodeList") List<String> ecServiceCodeList,
                                                                    @Param("filterProdcutList") List<String> filterProdcutList);

    @Select("<script>SELECT sid eid,id serviceCode \n" +
            "        FROM `aio-db`.tenant ea\n" +
            "        WHERE 1 = 1 \n" +
            "        <if test=\"serviceCodeList != null\">\n" +
            "            <foreach collection=\"serviceCodeList\" item=\"item\"\n" +
            "                     open=\" AND ea.id IN (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if></script>")
    List<TenantModuleContract.Detail> selectEid(@Param("serviceCodeList") Set<String> serviceCodeList);

    @Select("<script>SELECT *\n" +
            "        FROM `aio-db`.tenant_module_contract ea\n" +
            "        WHERE 1 = 1 AND (moduleId=1 OR moduleId=2001 OR moduleId=3001) \n" +
            "        <if test=\"eidList != null\">\n" +
            "            <foreach collection=\"eidList\" item=\"item\"\n" +
            "                     open=\" AND ea.eid IN (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if></script>")
    List<TenantModuleContract> selectTmc(@Param("eidList") List<Long> eidList);
    @Select("SELECT userSid\n" +
            "        FROM `aio-db`.supplier_employee ea\n" +
            "        WHERE 1 = 1 AND name = #{name} order by updateTime desc limit 1 \n")
    Long selectEmp(String name);
    @Select("<script>select ad.eid from aiops_device_collect_detail adcd\n" +
            "         left join aiops_device_collect adc on adcd.adcId = adc.id\n" +
            "         left join aiops_device ad on ad.deviceId = adc.deviceId\n" +
            "         where accId in (select id from aiops_collect_config where collectCode in( 'D5.42.0','A5.42.0','B5.42.0','C5.42.0')) and isEnable = true\n" +
            "         and ad.lastCheckInTime >= DATE_SUB(now(), INTERVAL 3 DAY)  = true" +
            "        <if test=\"eidList != null\">\n" +
            "            <foreach collection=\"eidList\" item=\"item\"\n" +
            "                     open=\" and ad.eid not in  (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if></script>")
    List<String> selectMaintainDevice(@Param("eidList") List<String> eidList);

    @Select("<script>select distinct t.sid, t.id, t.name, t.customerFullNameCH from aiops_device ad\n" +
            "     inner join tenant t on ad.eid = t.sid   where 1=1\n" +
            "        <if test=\"eidList != null\">\n" +
            "            <foreach collection=\"eidList\" item=\"item\"\n" +
            "                     open=\" and ad.eid in  (\" separator=\", \" close=\")\">\n" +
            "                #{item}\n" +
            "            </foreach>\n" +
            "        </if></script>")
    List<Tenant> selectTenant(@Param("eidList") List<String> eidList);


}
