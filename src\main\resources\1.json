{"code": "0", "errMsg": "success", "data": [{"id": 100, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000000101, "samcId": 105000000000101, "aiopsItemType": "DEVICE", "aiopsItem": "HOST", "holdAuth": true, "isAuthByInstance": false, "allowDuplicateMapping": false, "aiopsItemName": "主机", "aiopsItemName_CN": "主机", "aiopsItemName_TW": "主機", "aiopsItemTypeName": "设备基础", "aiopsItemTypeName_CN": "设备基础", "aiopsItemTypeName_TW": "設備基礎"}, {"id": 105000000000102, "samcId": 105000000000102, "aiopsItemType": "DEVICE", "aiopsItem": "CLIENT", "holdAuth": true, "isAuthByInstance": false, "allowDuplicateMapping": false, "aiopsItemName": "终端", "aiopsItemName_CN": "终端", "aiopsItemName_TW": "終端", "aiopsItemTypeName": "设备基础", "aiopsItemTypeName_CN": "设备基础", "aiopsItemTypeName_TW": "設備基礎"}], "aiopsItemGroupCode": "BASIC", "aiopsItemGroupName": "设备基础", "aiopsItemGroupName_CN": "设备基础", "aiopsItemGroupName_TW": "設備基礎"}, {"id": 200, "showInLocalWeb": true, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000000103, "samcId": 105000000000103, "aiopsItemType": "SNMP", "aiopsItem": "NAS", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "NAS", "aiopsItemName_CN": "NAS", "aiopsItemName_TW": "NAS", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000108, "samcId": 105000000000108, "aiopsItemType": "SNMP", "aiopsItem": "RAID", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "RAID", "aiopsItemName_CN": "RAID", "aiopsItemName_TW": "RAID", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000104, "samcId": 105000000000104, "aiopsItemType": "SNMP", "aiopsItem": "FIREWALL", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "防火墙", "aiopsItemName_CN": "防火墙", "aiopsItemName_TW": "防火牆", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000105, "samcId": 105000000000105, "aiopsItemType": "SNMP", "aiopsItem": "ESXI", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "ESXI", "aiopsItemName_CN": "ESXI", "aiopsItemName_TW": "ESXI", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000106, "samcId": 105000000000106, "aiopsItemType": "SNMP", "aiopsItem": "SWITCH", "holdAuth": false, "isAuthByInstance": false, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "交换器", "aiopsItemName_CN": "交换器", "aiopsItemName_TW": "交換器", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000107, "samcId": 105000000000107, "aiopsItemType": "SNMP", "aiopsItem": "UPS", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "不断电系统", "aiopsItemName_CN": "不断电系统", "aiopsItemName_TW": "不斷電系統", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000199, "samcId": 105000000000199, "aiopsItemType": "SNMP", "aiopsItem": "SNMP", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "其他的SNMP", "aiopsItemName_CN": "其他的SNMP", "aiopsItemName_TW": "其他的SNMP", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000111, "samcId": 583304110764473, "aiopsItemType": "HTTP", "aiopsItem": "iLO", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "HttpBasicAuthExecParams", "allowDuplicateMapping": true, "aiopsItemName": "IPMI/iLO", "aiopsItemName_CN": "IPMI/iLO", "aiopsItemName_TW": "IPMI/iLO", "aiopsItemTypeName": "http(s)协议设备", "aiopsItemTypeName_CN": "http(s)协议设备", "aiopsItemTypeName_TW": "http(s)協議設備"}, {"id": 105000000000112, "samcId": 583304110764473, "aiopsItemType": "HTTP", "aiopsItem": "iDRAC", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "HttpBasicAuthExecParams", "allowDuplicateMapping": true, "aiopsItemName": "IPMI/iDRAC", "aiopsItemName_CN": "IPMI/iDRAC", "aiopsItemName_TW": "IPMI/iDRAC", "aiopsItemTypeName": "http(s)协议设备", "aiopsItemTypeName_CN": "http(s)协议设备", "aiopsItemTypeName_TW": "http(s)協議設備"}, {"id": 583315297855174, "samcId": 583304110764473, "aiopsItemType": "SNMP", "aiopsItem": "iMM", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "IPMI/iMM", "aiopsItemName_CN": "IPMI/iMM", "aiopsItemName_TW": "IPMI/iMM", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 583426704211062, "samcId": 583304110764473, "aiopsItemType": "HTTP", "aiopsItem": "XCC", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "HttpBasicAuthExecParams", "allowDuplicateMapping": false, "aiopsItemName": "IPMI/XCC", "aiopsItemName_CN": "IPMI/XCC", "aiopsItemName_TW": "IPMI/XCC", "aiopsItemTypeName": "http(s)协议设备", "aiopsItemTypeName_CN": "http(s)协议设备", "aiopsItemTypeName_TW": "http(s)協議設備"}, {"id": 583426704211063, "samcId": 105000000000115, "aiopsItemType": "HTTP", "aiopsItem": "CRH", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "HttpBasicAuthExecParams", "allowDuplicateMapping": false, "aiopsItemName": "深信服超融合", "aiopsItemName_CN": "深信服超融合", "aiopsItemName_TW": "深信服超融合", "aiopsItemTypeName": "http(s)协议设备", "aiopsItemTypeName_CN": "http(s)协议设备", "aiopsItemTypeName_TW": "http(s)協議設備"}], "aiopsItemGroupCode": "EQUIPMENT", "aiopsItemGroupName": "机房设备", "aiopsItemGroupName_CN": "机房设备", "aiopsItemGroupName_TW": "機房設備"}, {"id": 300, "showInLocalWeb": true, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000000110, "samcId": 105000000000110, "aiopsItemType": "SNMP", "aiopsItem": "LINUX", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "LINUX主机", "aiopsItemName_CN": "LINUX主机", "aiopsItemName_TW": "LINUX主機", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000109, "samcId": 105000000000109, "aiopsItemType": "SNMP", "aiopsItem": "MAC", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "MAC终端", "aiopsItemName_CN": "MAC终端", "aiopsItemName_TW": "MAC終端", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}, {"id": 105000000000114, "samcId": 105000000000114, "aiopsItemType": "SNMP", "aiopsItem": "WINDOWS", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "SNMPExecParams", "allowDuplicateMapping": false, "aiopsItemName": "WINDOWS", "aiopsItemName_CN": "WINDOWS", "aiopsItemName_TW": "WINDOWS", "aiopsItemTypeName": "SNMP协议设备", "aiopsItemTypeName_CN": "SNMP协议设备", "aiopsItemTypeName_TW": "SNMP協議設備"}], "aiopsItemGroupCode": "OTHER", "aiopsItemGroupName": "其他设备", "aiopsItemGroupName_CN": "其他设备", "aiopsItemGroupName_TW": "其他設備"}, {"id": 400, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000002001, "samcId": 105000000002001, "aiopsItemType": "DATABASE", "aiopsItem": "MSSQL", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "DbExecParams", "allowDuplicateMapping": true, "aiopsItemName": "SQL Server数据库", "aiopsItemName_CN": "SQL Server数据库", "aiopsItemName_TW": "SQL Server資料庫", "aiopsItemTypeName": "数据库运维", "aiopsItemTypeName_CN": "数据库运维", "aiopsItemTypeName_TW": "資料庫運維"}, {"id": 105000000003001, "samcId": 105000000003001, "aiopsItemType": "DATABASE", "aiopsItem": "ORACLE", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "DbExecParams", "allowDuplicateMapping": true, "aiopsItemName": "ORACLE数据库", "aiopsItemName_CN": "ORACLE数据库", "aiopsItemName_TW": "ORACLE資料庫", "aiopsItemTypeName": "数据库运维", "aiopsItemTypeName_CN": "数据库运维", "aiopsItemTypeName_TW": "資料庫運維"}, {"id": 105000000004001, "samcId": 105000000004001, "aiopsItemType": "DATABASE", "aiopsItem": "MYSQL", "holdAuth": true, "isAuthByInstance": true, "execParamsModelCode": "DbExecParams", "allowDuplicateMapping": true, "aiopsItemName": "MYSQL数据库", "aiopsItemName_CN": "MYSQL数据库", "aiopsItemName_TW": "MYSQL資料庫", "aiopsItemTypeName": "数据库运维", "aiopsItemTypeName_CN": "数据库运维", "aiopsItemTypeName_TW": "資料庫運維"}], "aiopsItemGroupCode": "DATABASE", "aiopsItemGroupName": "数据库运维", "aiopsItemGroupName_CN": "数据库运维", "aiopsItemGroupName_TW": "資料庫運維"}, {"id": 500, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000111001, "samcId": 105000000011101, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "137", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "", "allowDuplicateMapping": true, "aiopsItemName": "sMES", "aiopsItemName_CN": "sMES", "aiopsItemName_TW": "sMES", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000000601, "samcId": 107000000000601, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "08", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "易飞", "aiopsItemName_CN": "易飞", "aiopsItemName_TW": "易飛", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 105000000100201, "samcId": 105000000010201, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "37", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "E10", "aiopsItemName_CN": "E10", "aiopsItemName_TW": "E10", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 105000000100301, "samcId": 105000000010301, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "Athena", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "雅典娜", "aiopsItemName_CN": "雅典娜", "aiopsItemName_TW": "雅典娜", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 105000000100401, "samcId": 105000000010401, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "148", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "鼎捷设备云", "aiopsItemName_CN": "鼎捷设备云", "aiopsItemName_TW": "鼎新設備雲", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 105000000100101, "samcId": 105000000010101, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "100", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "T100", "aiopsItemName_CN": "T100", "aiopsItemName_TW": "T100", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000200001, "samcId": 107000000200001, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "CRM", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "服管CRM", "aiopsItemName_CN": "服管CRM", "aiopsItemName_TW": "服管CRM", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 99999900000101, "samcId": 99999900000001, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "DMP", "holdAuth": false, "isAuthByInstance": false, "allowDuplicateMapping": false, "aiopsItemName": "标签中心", "aiopsItemName_CN": "标签中心", "aiopsItemName_TW": "標籤中心", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000300001, "samcId": 107000000300001, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "179", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "", "allowDuplicateMapping": true, "aiopsItemName": "雅典娜财务云", "aiopsItemName_CN": "雅典娜财务云", "aiopsItemName_TW": "雅典娜財務雲", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000000101, "samcId": 107000000000101, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "02", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "Workflow ERP", "aiopsItemName_CN": "Workflow ERP", "aiopsItemName_TW": "Workflow ERP", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000000201, "samcId": 107000000000201, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "10", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "Smart ERP", "aiopsItemName_CN": "Smart ERP", "aiopsItemName_TW": "Smart ERP", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000000401, "samcId": 107000000000401, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "HR", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "HR", "aiopsItemName_CN": "HR", "aiopsItemName_TW": "HR", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}, {"id": 107000000000501, "samcId": 107000000000501, "aiopsItemType": "PRODUCT_APP", "aiopsItem": "HRESS", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "HRESS", "aiopsItemName_CN": "HRESS", "aiopsItemName_TW": "HRESS", "aiopsItemTypeName": "应用产品/服务", "aiopsItemTypeName_CN": "应用产品/服务", "aiopsItemTypeName_TW": "應用產品/服務"}], "aiopsItemGroupCode": "PRODUCT_APP", "aiopsItemGroupName": "产品/应用服务运维", "aiopsItemGroupName_CN": "产品/应用服务运维", "aiopsItemGroupName_TW": "產品/應用服務運維"}, {"id": 600, "showInLocalWeb": true, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 110000000010002, "samcId": 105000000000101, "aiopsItemType": "HTTP", "aiopsItem": "VEEAM", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "HttpBasicAuthExecParams", "allowDuplicateMapping": false, "aiopsItemName": "<PERSON><PERSON><PERSON>", "aiopsItemName_CN": "<PERSON><PERSON><PERSON>", "aiopsItemName_TW": "<PERSON><PERSON><PERSON>", "aiopsItemTypeName": "http(s)协议设备", "aiopsItemTypeName_CN": "http(s)协议设备", "aiopsItemTypeName_TW": "http(s)協議設備"}, {"id": 105000000010001, "samcId": 105000000000101, "aiopsItemType": "BACKUP", "aiopsItem": "COBIAN", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "BackupSoftwareExecParams", "allowDuplicateMapping": false, "aiopsItemName": "cobian", "aiopsItemName_CN": "cobian", "aiopsItemName_TW": "cobian", "aiopsItemTypeName": "备份软件", "aiopsItemTypeName_CN": "备份软件", "aiopsItemTypeName_TW": "備份軟體"}, {"id": 105000000010002, "samcId": 105000000000101, "aiopsItemType": "BACKUP", "aiopsItem": "ACRONIS", "holdAuth": false, "isAuthByInstance": true, "execParamsModelCode": "BackupSoftwareExecParams", "allowDuplicateMapping": false, "aiopsItemName": "acronis", "aiopsItemName_CN": "acronis", "aiopsItemName_TW": "acronis", "aiopsItemTypeName": "备份软件", "aiopsItemTypeName_CN": "备份软件", "aiopsItemTypeName_TW": "備份軟體"}], "aiopsItemGroupCode": "BACKUP", "aiopsItemGroupName": "备份软件", "aiopsItemGroupName_CN": "备份软件", "aiopsItemGroupName_TW": "備份軟件"}, {"id": 700, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 105000000000301, "samcId": 105000000000301, "aiopsItemType": "EDR", "aiopsItem": "EDR_HOST", "holdAuth": true, "isAuthByInstance": false, "allowDuplicateMapping": false, "aiopsItemName": "EDR主机", "aiopsItemName_CN": "EDR主机", "aiopsItemName_TW": "EDR主機", "aiopsItemTypeName": "端点防护", "aiopsItemTypeName_CN": "端点防护", "aiopsItemTypeName_TW": "端點防護"}], "aiopsItemGroupCode": "EDR", "aiopsItemGroupName": "EDR运维", "aiopsItemGroupName_CN": "EDR运维", "aiopsItemGroupName_TW": "EDR運維"}, {"id": 800, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 107000000100001, "samcId": 107000000100001, "aiopsItemType": "SMART_METER", "aiopsItem": "SMART_METER", "holdAuth": false, "isAuthByInstance": true, "allowDuplicateMapping": true, "aiopsItemName": "智能电表", "aiopsItemName_CN": "智能电表", "aiopsItemName_TW": "智能電表"}], "aiopsItemGroupCode": "SMART_METER", "aiopsItemGroupName": "智能电表运维", "aiopsItemGroupName_CN": "智能电表运维", "aiopsItemGroupName_TW": "智能電表運維"}, {"id": 1100, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 147000000060001, "samcId": 147000000060001, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_E10", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "E10应用", "aiopsItemName_CN": "E10应用", "aiopsItemName_TW": "E10應用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}, {"id": 147000000060011, "samcId": 147000000060011, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_WF", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "WF应用", "aiopsItemName_CN": "WF应用", "aiopsItemName_TW": "WF應用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}, {"id": 147000000060021, "samcId": 147000000060021, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_YIFEI", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "易飞应用", "aiopsItemName_CN": "易飞应用", "aiopsItemName_TW": "易飛應用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}, {"id": 147000000060031, "samcId": 147000000060031, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_KSC", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "KSC应用", "aiopsItemName_CN": "KSC应用", "aiopsItemName_TW": "KSC应用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}, {"id": 147000000060051, "samcId": 147000000060051, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_PLM", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "PLM应用", "aiopsItemName_CN": "PLM应用", "aiopsItemName_TW": "PLM应用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}, {"id": 147000000060061, "samcId": 147000000060061, "aiopsItemType": "APP_AUTO_UPDATE", "aiopsItem": "APP_SCS", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "SCS应用", "aiopsItemName_CN": "SCS应用", "aiopsItemName_TW": "SCS应用", "aiopsItemTypeName": "稳态自动更新", "aiopsItemTypeName_CN": "稳态自动更新", "aiopsItemTypeName_TW": "穩態自動更新"}], "aiopsItemGroupCode": "APP_AUTO_UPDATE", "aiopsItemGroupName": "稳态自动更新", "aiopsItemGroupName_CN": "稳态自动更新", "aiopsItemGroupName_TW": "穩態自動更新"}, {"id": 1200, "showInLocalWeb": false, "parentId": null, "children": null, "supplierAiopsModuleClassDetailList": [{"id": 147000000070001, "samcId": 147000000070001, "aiopsItemType": "DATA_SRV", "aiopsItem": "DATA_SRV_ELECTRONIC", "holdAuth": true, "isAuthByInstance": true, "allowDuplicateMapping": false, "aiopsItemName": "电子行业", "aiopsItemName_CN": "电子行业", "aiopsItemName_TW": "電子行業", "aiopsItemTypeName": "数据服务", "aiopsItemTypeName_CN": "数据服务", "aiopsItemTypeName_TW": "數據服務"}], "aiopsItemGroupCode": "DATA_SRV", "aiopsItemGroupName": "数据服务", "aiopsItemGroupName_CN": "数据服务", "aiopsItemGroupName_TW": "數據服務"}]}