package com.example.aio.model;

import com.alibaba.fastjson2.JSONObject;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class NoticeModuleRequestTest {

    @Test
    public void testBooleanSerialization() {
        // 创建请求对象
        NoticeModuleRequest request = new NoticeModuleRequest(
            1,                                    // nmId
            true,                                // enabled (boolean类型)
            "1",                                 // nwIds
            "1,2,3,4",                          // nrlIds
            "<EMAIL>",                 // email
            "testuser",                         // authUserId
            123456789L,                         // authUserSid
            99990000L                           // eid
        );

        // 测试getter方法
        assertTrue(request.isEnabled());
        assertEquals(Integer.valueOf(1), request.getNmId());
        assertEquals("1", request.getNwIds());
        assertEquals("1,2,3,4", request.getNrlIds());
        assertEquals("<EMAIL>", request.getEmail());
        assertEquals("testuser", request.getAuthUserId());
        assertEquals(Long.valueOf(123456789L), request.getAuthUserSid());
        assertEquals(Long.valueOf(99990000L), request.getEid());

        // 测试JSON序列化
        String json = JSONObject.toJSONString(request);
        System.out.println("序列化后的JSON: " + json);
        
        // 验证JSON中包含正确的boolean值
        assertTrue(json.contains("\"enabled\":true"));
        assertTrue(json.contains("\"nmId\":1"));
        assertTrue(json.contains("\"nwIds\":\"1\""));
        assertTrue(json.contains("\"nrlIds\":\"1,2,3,4\""));
        
        // 测试反序列化
        NoticeModuleRequest deserializedRequest = JSONObject.parseObject(json, NoticeModuleRequest.class);
        assertNotNull(deserializedRequest);
        assertTrue(deserializedRequest.isEnabled());
        assertEquals(request.getNmId(), deserializedRequest.getNmId());
        assertEquals(request.getEmail(), deserializedRequest.getEmail());
    }

    @Test
    public void testSetterMethods() {
        NoticeModuleRequest request = new NoticeModuleRequest();
        
        // 测试setter方法
        request.setNmId(2);
        request.setEnabled(false);
        request.setNwIds("2");
        request.setNrlIds("5,6,7,8");
        request.setEmail("<EMAIL>");
        request.setAuthUserId("setteruser");
        request.setAuthUserSid(987654321L);
        request.setEid(88880000L);
        
        // 验证setter方法
        assertEquals(Integer.valueOf(2), request.getNmId());
        assertFalse(request.isEnabled());
        assertEquals("2", request.getNwIds());
        assertEquals("5,6,7,8", request.getNrlIds());
        assertEquals("<EMAIL>", request.getEmail());
        assertEquals("setteruser", request.getAuthUserId());
        assertEquals(Long.valueOf(987654321L), request.getAuthUserSid());
        assertEquals(Long.valueOf(88880000L), request.getEid());
    }
}
