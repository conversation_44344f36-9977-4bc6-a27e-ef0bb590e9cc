CREATE TABLE `AiopsTenant` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"compression" = "LZ4",
"fast_schema_evolution" = "true",
"replicated_storage" = "false",
"replication_num" = "1"
);