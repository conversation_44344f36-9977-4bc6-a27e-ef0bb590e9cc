CREATE EXTERNAL TABLE `es_customerservice_v2_external` (
       `eId` bigint(20) NOT NULL COMMENT "",
                                         `updateDate` datetime NOT NULL COMMENT "",
                                         `SalesContact` varchar(65535) NOT NULL COMMENT "",
                                         `CustomerCode` varchar(65535) NOT NULL COMMENT "",
                                         `grp_dpt_id` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_name` varchar(65535) NULL COMMENT "",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_id` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_name` varchar(65535) NULL COMMENT "",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "",
                                         `dpt_id` varchar(65535) NULL COMMENT "",
                                         `dpt_name` varchar(65535) NULL COMMENT "",
                                         `dpt_manager` varchar(65535) NULL COMMENT "",
                                         `Sales` varchar(65535) NULL COMMENT "",
                                         `CustomerServiceCode` varchar(65535) NULL COMMENT "",
                                         `CustomerName` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameCH` varchar(65535) NULL COMMENT "",
                                         `CustomerFullNameEN` varchar(65535) NULL COMMENT "",
                                         `another_name` varchar(65535) NULL COMMENT "",
                                         `current_valid_status` char(1) NULL COMMENT "",
                                         `t100_cust_id` varchar(65535) NULL COMMENT "",
                                         `taxNo` varchar(65535) NULL COMMENT "",
                                         `contacts` varchar(65535) NULL COMMENT "",
                                         `tenantTelephone` varchar(65535) NULL COMMENT "",
                                         `tenantEmail` varchar(65535) NULL COMMENT "",
                                         `address` varchar(65535) NULL COMMENT "",
                                         `__version__` datetime NULL COMMENT "",
                                         `grp_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_name` varchar(65533) NULL COMMENT "",
                                         `bus_dpt_win_contact` varchar(65533) NULL COMMENT "",
                                         `dpt_manager_contact` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultant` varchar(65533) NULL COMMENT "",
                                         `valueAddedConsultantEmail` varchar(65533) NULL COMMENT "",
                                         `IndustryCode` varchar(65533) NULL COMMENT ""
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "es_customerservice_v4_1"
);


CREATE EXTERNAL TABLE `es_customerservice_v3_external` (
        `grp_dpt_id` varchar(64) NOT NULL COMMENT "事业群ID",
                                         `dpt_id` varchar(64) NOT NULL COMMENT "部门ID",
                                         `grp_dpt_manager_contact` varchar(64) NOT NULL COMMENT "事业群经理联系方式",
                                         `bus_dpt_id` varchar(64) NOT NULL COMMENT "业务部门ID",
                                         `bus_dpt_manager_contact` varchar(64) NOT NULL COMMENT "业务部门经理联系方式",
                                         `dpt_manager_contact` varchar(64) NOT NULL COMMENT "部门经理联系方式",
                                         `grp_dpt_name` varchar(65535) NOT NULL COMMENT "事业群名称",
                                         `grp_dpt_manager` varchar(65535) NULL COMMENT "事业群经理",
                                         `bus_dpt_name` varchar(65535) NOT NULL COMMENT "业务部门名称",
                                         `bus_dpt_manager` varchar(65535) NULL COMMENT "业务部门经理",
                                         `bus_dpt_win_name` varchar(65535) NULL COMMENT "业务部门负责人",
                                         `bus_dpt_win_contact` varchar(65535) NULL COMMENT "业务部门负责人联系方式",
                                         `dpt_name` varchar(65535) NOT NULL COMMENT "部门名称",
                                         `dpt_manager` varchar(65535) NULL COMMENT "部门经理"
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "es_customerservice_v3"
);


CREATE EXTERNAL TABLE `mars_customerservice_external` (
        `CustomerServiceCode` varchar(20) NOT NULL,
                                        `ProductCode` varchar(4) NOT NULL,
                                        `ProductCategory` varchar(20) DEFAULT NULL,
                                        `ProductVersion` varchar(30) DEFAULT NULL,
                                        `Sales` varchar(20) DEFAULT NULL,
                                        `SalesContact` varchar(100) DEFAULT NULL,
                                        `HasOwnerService` tinyint(1) DEFAULT '1' COMMENT '文字客服是否走专属',
                                        `HasTextService` tinyint(1) DEFAULT '0' COMMENT '文字客服是否走团队(当文字专属为1时没有作用)',
                                        `HasOwnerIssueService` tinyint(1) DEFAULT '1' COMMENT '案件是否走专属',
                                        `ServiceStaffCode` varchar(20) DEFAULT NULL,
                                        `ServiceStaff` varchar(20) DEFAULT NULL,
                                        `ServiceStaffContact` varchar(100) DEFAULT NULL,
                                        `ServiceStaffQQ` varchar(20) DEFAULT NULL,
                                        `Consultant` varchar(20) DEFAULT NULL,
                                        `ConsultantContact` varchar(100) DEFAULT NULL,
                                        `ContractStartDate` varchar(20) DEFAULT NULL,
                                        `ContractExprityDate` varchar(20) DEFAULT NULL,
                                        `ContractState` varchar(30) DEFAULT NULL,
                                        `IndustryCode` varchar(20) DEFAULT NULL,
                                        `AreaCode` varchar(20) DEFAULT NULL,
                                        `HostAuthNum` int(11) DEFAULT NULL,
                                        `ClientAuthNum` int(11) DEFAULT NULL,
                                        `SnmpAuthNum` int(11) DEFAULT NULL,
                                        `pmWorkno` varchar(30) DEFAULT NULL COMMENT 'PM人员工号',
                                        `cust_level` varchar(5) DEFAULT NULL COMMENT '客户等级',
                                        `trial_expired` varchar(30) DEFAULT NULL COMMENT '试用期到期日',
                                        `service_department` varchar(10) DEFAULT NULL COMMENT '所属服务部门',
                                        `cust_borrowing_due_date` varchar(30) DEFAULT NULL COMMENT '客户借货到期日期',
                                        `status` varchar(2) DEFAULT NULL COMMENT '客户对应产品的当前项目状态，取值：0-未上线,1-已上线未结案,2-已结案未转TSC,3-转TSC,4-失效,5-服务云暂停支持。此字段只从控制台手动设置。',
                                        `service_cc_staff_emails` varchar(2000) DEFAULT NULL COMMENT 'T产品群抄送人',
                                        `formal` tinyint(1) DEFAULT b'0' COMMENT '是否正式数据',
                                        `IsTrial` tinyint(1) DEFAULT b'0' COMMENT '是否試用',
                                        `window_department` varchar(10) DEFAULT NULL COMMENT '服务云客户服务部门',
                                        `businessDepartmentCode` varchar(10) DEFAULT NULL COMMENT '事业部编号',
                                        `businessDepartmentCodeACP` varchar(10) DEFAULT NULL COMMENT '事业部编号(ACP系统)',
                                        `serviceUnitType` varchar(10) DEFAULT NULL COMMENT '服务单位类别',
                                        `deliveryMode` varchar(10) DEFAULT '2' COMMENT '1 实施交付 | 2 售后服务',
                                        `contractSource` int(1) DEFAULT '1' COMMENT '合约来源 1:crm 2:iam',
                                        `salesCode` varchar(20) DEFAULT NULL COMMENT '业务人员工号',
                                        `agent_limit_issueCount` int(10) DEFAULT '0' COMMENT '限定案件量，同步与crm的SERAI.AI011',
                                        `__version__` timestamp NULL,
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "aio-db",
"table" = "mars_customerservice"
);

show create table `escloud-db`.cdp_customer_contacts_external;


CREATE EXTERNAL TABLE `cdp_customer_contacts_external_mysql` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                                  `eid` bigint(20) NOT NULL COMMENT '租户sid',
                                                  `customerCode` varchar(50) NOT NULL COMMENT '客户编号',
                                                  `department` varchar(20) NOT NULL COMMENT '部门',
                                                  `lastContactTime` date DEFAULT NULL COMMENT '最后联络时间',
                                                  `contactCnt` int(11) NOT NULL COMMENT '联系人数量',
                                                  `createTime` datetime,
                                                  `updateTime` datetime
) ENGINE=MYSQL
COMMENT "MYSQL"
PROPERTIES (
"host" = "***************",
"port" = "4306",
"user" = "digiwin",
"password" = "gitlab123",
"database" = "escloud-db",
"table" = "cdp_customer_contacts_external"
);

CREATE TABLE `cdp_customer_contacts_external`
(
    `id`              bigint(20)    NOT NULL COMMENT "",
    `eid`             bigint(20)     NOT NULL COMMENT "",
    `customerCode`    varchar(65535) NOT NULL COMMENT "",
    `department`      varchar(65535) NOT NULL COMMENT "",
    `lastContactTime` date       NULL COMMENT "",
    `contactCnt`      int(10),
    `createTime`      datetime       NULL COMMENT "",
    `updateTime`      datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY( `id`,`eid`,`customerCode`,`department`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);
insert into cdp_customer_contacts_external  select * from cdp_customer_contacts_external_mysql