CREATE EXTERNAL TABLE if not exists `servicecloud`.`Tenant_List_XinGang`(eid STRING,CustomerCode STRING,taxNo STRING,product STRING,customerFullNameCH STRING,registeredCapital STRING,EstablishmentDate STRING,telephone STRING,email STRING,officialWebsite STRING,businessType STRING,enterpriseSize STRING,employeesNumber STRING,branchesNumber STRING,income2021 STRING,income2022 STRING,income2023 STRING)ENGINE=MYSQL
    COMMENT "MYSQL"
    PROPERTIES (
                   "host" = "***************",
                   "port" = "4306",
                   "user" = "digiwin",
                   "password" = "gitlab123",
                   "database" = "aio-db",
                   "table" = "tenant_xingang"
               );

CREATE EXTERNAL TABLE if not exists `servicecloud`.`Tenant_List_XinGang_v2`(eid STRING,CustomerCode STRING,taxNo STRING,product STRING,customerFullNameCH STRING,registeredCapital STRING,EstablishmentDate STRING,telephone STRING,email STRING,officialWebsite STRING,businessType STRING,enterpriseSize STRING,employeesNumber STRING,branchesNumber STRING,income2021 STRING,income2022 STRING,income2023 STRING)ENGINE=MYSQL
    COMMENT "MYSQL"
    PROPERTIES (
                   "host" = "rm-bp1k25v6j475s4ai7jo.mysql.rds.aliyuncs.com",
                   "port" = "3306",
                   "user" = "readniss",
                   "password" = "Escread001",
                   "database" = "aio-db",
                   "table" = "tenant_xingang"
               );