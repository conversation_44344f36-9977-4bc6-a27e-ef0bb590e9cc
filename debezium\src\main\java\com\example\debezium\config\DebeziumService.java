package com.example.debezium.config;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.example.debezium.service.ChangeEventHandler;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.debezium.embedded.Connect;
import io.debezium.engine.DebeziumEngine;
import io.debezium.engine.RecordChangeEvent;
import io.debezium.engine.format.ChangeEventFormat;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.connect.source.SourceRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.Properties;
import java.util.concurrent.*;

@Configuration
public class DebeziumService {
    private final ChangeEventHandler changeEventHandler;
    private final DebeziumConfigProperties debeziumConfigProperties;




    @Autowired
    public DebeziumService(ChangeEventHandler changeEventHandler,DebeziumConfigProperties debeziumConfigProperties) {
        this.changeEventHandler = changeEventHandler;
        this.debeziumConfigProperties = debeziumConfigProperties;
    }


    @Bean
    public String cleanFile() {
        if (debeziumConfigProperties.getOffsetFileClean() && FileUtil.exist(offsetFileName)) {
            FileUtil.del(offsetFileName);
        }
        return Void.TYPE.getName();
    }

    @Value("${timely.offset-file-name}")
    private String offsetFileName;

    @Value("${timely.offset-time}")
    private String offsetTime;
    @Value("${timely.history-file-name}")
    private String historyFileName;
    @Value("${timely.offline.instance-name}")
    private String instanceName;
    @Value("${timely.offline.logic-name}")
    private String logicName;
    @Value("${timely.offline.ip}")
    private String ip;
    @Value("${timely.offline.port}")
    private String port;
    @Value("${timely.offline.username}")
    private String username;
    @Value("${timely.offline.password}")
    private String password;
    @Value("${timely.offline.include-table}")
    private String includeTable;
    @Value("${timely.offline.include-db}")
    private String includeDb;

    @Bean
    public MysqlExecutor debeziumConfig() {
        // Define the configuration for the Debezium Engine with MySQL connector...
        final Properties props = new Properties();
        props.setProperty("name", "engine");
        props.setProperty("connector.class", "io.debezium.connector.mysql.MySqlConnector");
        props.setProperty("offset.storage", "org.apache.kafka.connect.storage.FileOffsetBackingStore");
        props.setProperty("offset.storage.file.filename", offsetFileName);
        props.setProperty("offset.flush.interval.ms", offsetTime);
        /* begin connector properties */
        props.setProperty("database.hostname", ip);
        props.setProperty("database.port", port);
        props.setProperty("database.user", username);
        props.setProperty("database.password", password);
        props.setProperty("database.server.id", "1");

        props.setProperty("database.include.list", includeDb);
        props.setProperty("topic.prefix", "my-app-connector");
        props.setProperty("schema.history.internal", "io.debezium.storage.file.history.FileSchemaHistory");
        props.setProperty("schema.history.internal.file.filename", historyFileName);

        MysqlExecutor mysqlExecutor = new MysqlExecutor();
        // Create the engine with this configuration ...
        try (DebeziumEngine<RecordChangeEvent<SourceRecord>> engine = DebeziumEngine.create(ChangeEventFormat.of(Connect.class)).using(props)
                .notifying(changeEventHandler::handlePayload).build()) {
            // Run the engine asynchronously ...

            mysqlExecutor.setDebeziumEngine(engine);
//            ExecutorService executor = Executors.newSingleThreadExecutor();
//            executor.execute(engine);

            // Do something else or wait for a signal or an event
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // Engine is stopped when the main code is finished
        return mysqlExecutor;
    }





    @Data
    @Slf4j
    public static class MysqlExecutor implements InitializingBean, SmartLifecycle {
        private final ExecutorService executor = ThreadPoolEnum.INSTANCE.getInstance();
        private DebeziumEngine<?> debeziumEngine;

        @Override
        public void start() {
            log.warn(ThreadPoolEnum.SQL_SERVER_LISTENER_POOL + "线程池开始执行 debeziumEngine 实时监听任务!");
            executor.execute(debeziumEngine);
        }

        @SneakyThrows
        @Override
        public void stop() {
            log.warn("debeziumEngine 监听实例关闭!");
            debeziumEngine.close();
            Thread.sleep(2000);
            log.warn(ThreadPoolEnum.SQL_SERVER_LISTENER_POOL + "线程池关闭!");
            executor.shutdown();
        }

        @Override
        public boolean isRunning() {
            return false;
        }

        @Override
        public void afterPropertiesSet() {
            Assert.notNull(debeziumEngine, "DebeZiumEngine 不能为空!");
        }

        public enum ThreadPoolEnum {
            INSTANCE;

            public static final String SQL_SERVER_LISTENER_POOL = "mysql-listener-pool";
            private final ExecutorService es;

            ThreadPoolEnum() {
                final ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat(SQL_SERVER_LISTENER_POOL + "-%d").build();
                es = new ThreadPoolExecutor(8, 16, 60,
                        TimeUnit.SECONDS, new ArrayBlockingQueue<>(256),
                        threadFactory, new ThreadPoolExecutor.DiscardPolicy());
            }

            public ExecutorService getInstance() {
                return es;
            }
        }
    }
}
