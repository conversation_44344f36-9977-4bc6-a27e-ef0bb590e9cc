eid:41324326138432 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8e529b1fd30a446aa321177e884ef3f0&deviceId=529098241334064195
2024-10-11T18:54:19.803+08:00  INFO 29712 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f002c911396cbb17508590428f74e89c\\\",\\\"dbIdValue\\\":\\\"f002c911396cbb17508590428f74e89c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FL\\\",\\\"targetValue\\\":\\\"FL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]

2024-10-12T10:04:25.040+08:00  INFO 6712 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:495727855137344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=a2dcdcee6e2779449341c032ff769b9a&deviceId=533332692977071430
2024-10-12T10:04:26.903+08:00  INFO 6712 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"63c3a48cd3f5a3d5b43756c81e30d40c\\\",\\\"dbIdValue\\\":\\\"63c3a48cd3f5a3d5b43756c81e30d40c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LHCC\\\",\\\"targetValue\\\":\\\"LHCC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]

2024-10-12T11:03:39.773+08:00  INFO 3216 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : eid:221232517599808 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d822e71eaea7ef9c5b7a724764aeb3ba&deviceId=533190535951692867
2024-10-12T11:03:42.758+08:00  INFO 3216 --- [nio-8081-exec-3] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0311b676f923bbdba4ba4ceb3dadf0db\\\",\\\"dbIdValue\\\":\\\"0311b676f923bbdba4ba4ceb3dadf0db\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]






2024-10-12T11:10:48.033+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:348505635279424 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3a134d1dd414cc2cff7a3275b4073ccb&deviceId=533041539945869367
2024-10-12T11:10:53.869+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fe2e54a75d937e4d324ef9bcde2247b6\\\",\\\"dbIdValue\\\":\\\"fe2e54a75d937e4d324ef9bcde2247b6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JW2021\\\",\\\"targetValue\\\":\\\"JW2021\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   533041539945869367
2024-10-12T11:10:54.801+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321782743616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dfa2c8581fcd173ea50d9d2bb8780ca5&deviceId=532169500993271107
2024-10-12T11:10:54.801+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bdf2ca49c2f4ea384e5dc9249378873b\\\",\\\"dbIdValue\\\":\\\"bdf2ca49c2f4ea384e5dc9249378873b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YSBL\\\",\\\"targetValue\\\":\\\"YSBL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   532169500993271107
2024-10-12T11:10:55.165+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:293968354452032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f1f66e537f4a807f612475f2b69e202d&deviceId=519723042721513776
2024-10-12T11:10:55.165+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"74ea3b8b26ec20e5410c8faa53f5a04c\\\",\\\"dbIdValue\\\":\\\"74ea3b8b26ec20e5410c8faa53f5a04c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JXQC\\\",\\\"targetValue\\\":\\\"JXQC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519723042721513776
2024-10-12T11:10:55.518+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:895728590244864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7b32f8b9f14388e9f5117bb37ca10874&deviceId=531442060771735617
2024-10-12T11:10:55.518+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fbb17d93e3376ceb20ccbba907d182fe\\\",\\\"dbIdValue\\\":\\\"fbb17d93e3376ceb20ccbba907d182fe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KLS_PRODECT\\\",\\\"targetValue\\\":\\\"KLS_PRODECT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   531442060771735617
2024-10-12T11:10:55.872+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:306259822981696 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2e30a3650d64e7e8fccb4fcd598d5458&deviceId=528857179835346992
2024-10-12T11:10:55.872+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"96e1c7fea328b87b609289afcd6898e7\\\",\\\"dbIdValue\\\":\\\"96e1c7fea328b87b609289afcd6898e7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"scpd\\\",\\\"targetValue\\\":\\\"scpd\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85039ee33d801718b67cf8bbea9fca1495c7fa9517ed53ea331f1dc7a3dbd8e180e48f44c2f07d41e224d189fd19b7710424bf3e7090c31b129d86bab3c14dc868c66e813b28edd2f377debabb60a12a43667a9f422afd63daf78a4b3894fb849248cc02735002a8e0abe80814be0007d0b2e0d240114d5ed592a5b3f9728bcf98f3a9b3f2f50b337b1c75d5f56accbdf69a9b9c87849515ebad86c0f07348f7c4d949864fb476e83563a0b50d6434143afc6bdba80bef47e442f97a92bb20b24ad1be2990663ea46aa53bb9d45cd0326eebe245165ae66e190cc2e544a2bc3c783235e48d7368c6482b3f8e37903f0bdf5a","collectName":"E10附件数据采集","accId":779544910123584,"adimId":765972867641920,"id":779794257023552,"adcId":763617031303744,"execParamsVersion":"2f2eda1723a80228e963128fab0ed565","aiId":765972867478082,"isEnable":1},"paramsMap":{"deviceId":"528857179835346992","eid":306259822981696,"aiId":765972867478082,"execParamsDbAiId":763617030873664,"execParamsDbId":"96e1c7fea328b87b609289afcd6898e7"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794257023552 acc incomplete","batchId":779794258240064,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528857179835346992
2024-10-12T11:10:56.343+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:136555735216704 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2baed2895c7ecbcc03a6ed8325b47774&deviceId=528963243968837428
2024-10-12T11:10:56.343+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fe97a9ee59bae025e610f992f253239b\\\",\\\"dbIdValue\\\":\\\"fe97a9ee59bae025e610f992f253239b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BLG_DB\\\",\\\"targetValue\\\":\\\"BLG_DB\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85034a3f30c5b07cda1707eab1c5e4225fbada9f0a5bd5059f3ed4ca2b5b62065bc5d74d1c283573eec38ab38b4ec119d348600ea74782963acb6861fd02463a3c54d58629a00a1c6de3da53ebfc5968a79fc6bd273df1e5aaf72d55120b465ac86673c90c9aa90210800a32cf6f2bc4489acd0d43533d3040868f84202d73f53ba9229c9c8294327b6d6329e3c4198e980ad3bbc080e46f2f4e550f42a8c476310502b5f772fbac09a677e4432be4888f8f6fb305980fa3abac86e2bbcb099f96455672dfb3c01a1cb7d352b3a9182f5379c5b92622380a44e602d54b009429e74bdb4f7b4b0d3a4be7f3ffc6f9d64dd2b4df3e623069894d93","collectName":"E10附件数据采集","accId":779544910123584,"adimId":763881347969601,"id":779794259014208,"adcId":763880326033984,"execParamsVersion":"42331151a3da222f505c0ec052554e0c","aiId":763881347797572,"isEnable":1},"paramsMap":{"deviceId":"528963243968837428","eid":136555735216704,"aiId":763881347797572,"execParamsDbAiId":763874877272640,"execParamsDbId":"fe97a9ee59bae025e610f992f253239b"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794259014208 acc incomplete","batchId":779794260050496,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528963243968837428
2024-10-12T11:10:56.722+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:466059209773632 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=173674dcf486e696af413f8fbdebb698&deviceId=528836642845242435
2024-10-12T11:10:56.723+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"10f4d9048bcd5466bd3d47962589669d\\\",\\\"dbIdValue\\\":\\\"10f4d9048bcd5466bd3d47962589669d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YHT-A99\\\",\\\"targetValue\\\":\\\"YHT-A99\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85036cd6687b209faa28d2c1d71858fc3443ca313222f6bb91f742ee304426ad658e9f2614c201d580d329e29d393d9159af0bf0de973042711d8b499d2dd4bd1e07b2ab0a273358022ee3a55d9dfcf902938b2732d048ad531f9f35c771038ca160bff10bc7c1965907b3f0a8b224afdbb1bc7bf480d82a331c311512c6278ed42803de3c5f9ea7e31fb3f3ed3dbd01ab45f76474d84256de1c7adbd662a5367c9dfe57507edfbb9b8b55892c4e7f4d6d86a2aafa7dfa1dcc3d63963ff7d22484126a8bd04c54dbb84d94136ff8334fd99de84d725fb1299e4594b7b329fce682e4f74ca39efbb03771bf05214620bbc9fb4671b10821cec7e8","collectName":"E10附件数据采集","accId":779544910123584,"adimId":763570646483521,"id":779794260517440,"adcId":763568194277952,"execParamsVersion":"44cad0556091f0f0a933e92c308a4f7f","aiId":763570646323777,"isEnable":1},"paramsMap":{"deviceId":"528836642845242435","eid":466059209773632,"aiId":763570646323777,"execParamsDbAiId":763568193716800,"execParamsDbId":"10f4d9048bcd5466bd3d47962589669d"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794260517440 acc incomplete","batchId":779794261652032,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528836642845242435
2024-10-12T11:10:57.120+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:521978760274496 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61c6a17dbe1409489f99ca4e3a666bab&deviceId=528704618906010937
2024-10-12T11:10:57.120+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"788dad9ffd24c0b08ce81a50ba22cbad\\\",\\\"dbIdValue\\\":\\\"788dad9ffd24c0b08ce81a50ba22cbad\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_Data\\\",\\\"targetValue\\\":\\\"E10_Data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85038b66d2bbd671c99693821bca79abb324d5325680c47e8ebef71ee4ced202f7e6daa39896531ee7af7c50e4694bf1df831e2a8ec60d2aff4468d3a2537bc93ece9caf76b373fc63540928fb978caace8cf069e0d0e9d1a3a1a741dc22fd931f46015b4dcea2e6fe7f6e7cbf8691b3e6d1609b1de532d3e56cb0459bb5f52a3724936ac200de649d85b2558d7d6b43e83c004ed5fa0cf87c5dd88f3d08f68380a022577219ce5babe13f244bbc98596f07e7aec613babd3f0ced331cb09b37586e6debeb7a5da487cc6d1348e1ad1deed787a18c70ca36778056b975c18c3545e3364bf5b614ae9a0a5848928a32476a7288617246edef456b","collectName":"E10附件数据采集","accId":779544910123584,"adimId":763245643326017,"id":779794262172224,"adcId":763245582504512,"execParamsVersion":"74f670bab23acd96f90d71a14b41acb2","aiId":763245643199042,"isEnable":1},"paramsMap":{"deviceId":"528704618906010937","eid":521978760274496,"aiId":763245643199042,"execParamsDbAiId":719273638531648,"execParamsDbId":"788dad9ffd24c0b08ce81a50ba22cbad"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794262172224 acc incomplete","batchId":779794263220800,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528704618906010937
2024-10-12T11:10:57.506+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324256326208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bb4e2610ac963f1b1e9aa269fac19fe7&deviceId=528700509696046147
2024-10-12T11:10:57.506+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4a3ca2fcf3425d370219563a3e8e842d\\\",\\\"dbIdValue\\\":\\\"4a3ca2fcf3425d370219563a3e8e842d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GYTJT\\\",\\\"targetValue\\\":\\\"GYTJT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85037573397e22e8eb596fe03bc9668c3dfea6bc366538815015a26be64c4ccf628dac0837ce6af13467828bc3fdfb5c1f3fb2eb9c239cc3f971e916c7d74d2222f553aaad17d2d226b77dd8a647ee07cafd84f3cdf49b2cf43f119253fb731375039f6e106cbd9b2af70c2c6c8a0b123c83bc9232b343376266198f80bf80dba02c1c6f948e32f192531d121e599595678509de9cca353e61ed9579b650f25f65a58dc809084202acc15f597452479538e9659a44afbdda6c0672036d8f7d5b39bdd6ed909bebc16d67eafed8901cfd9890a3cb1d9acf19f738bf28c35ddb41500133eb1c535cff4ce016c86e21dc1e1fed76a6112c70208f57","collectName":"E10附件数据采集","accId":779544910123584,"adimId":763235498885697,"id":779794263679552,"adcId":763234517770816,"execParamsVersion":"b12d15273b147d200032a94716b2bf56","aiId":763235498709569,"isEnable":1},"paramsMap":{"deviceId":"528700509696046147","eid":41324256326208,"aiId":763235498709569,"execParamsDbAiId":763234517348928,"execParamsDbId":"4a3ca2fcf3425d370219563a3e8e842d"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794263679552 acc incomplete","batchId":779794264666688,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528700509696046147
2024-10-12T11:10:57.851+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:335648583275072 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b4eda03f4d3bbb7771c19a1879278aa2&deviceId=528695933525832242
2024-10-12T11:10:57.851+08:00  INFO 26168 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4701f27b1528b0046ecdaa6eab36d982\\\",\\\"dbIdValue\\\":\\\"4701f27b1528b0046ecdaa6eab36d982\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DB_SY_Formal\\\",\\\"targetValue\\\":\\\"DB_SY_Formal\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85036d11b374c1494609159dc98833d07f10eff04fd2259f70397c494d4338cca605dd40db12d896bfc904691b375cd56a431c568bae960e02968b709737cc5b63e100e94e9a82ed80fb3ab01cfbb575ccb1e1f5c56cd5c2814ffa6ef2795953979dfa636b33184297a3aec90718a58e664a70144a979bdb220351a2e20fff4aa270ef7490f1a583e4c47474c612efe5d290da8134c953eea7700f2f7265bcd6e6863baf97df9b4b3db539ad17a17e735ab6a1418d25f185b4f9e7a5f3f7c882788d28d5ab1071412eb212054f72fc804b06c6ca84bd0f1002d4572bd73646c45698689004d08f9c387bef9eca05d6b4d795b5c2e80c41c5ee28e1fd2175e6fac968","collectName":"E10附件数据采集","accId":779544910123584,"adimId":763225525449281,"id":779794265088576,"adcId":763225326658112,"execParamsVersion":"6b0952cbedb6277a051bc0d44644d4d4","aiId":763225525260866,"isEnable":1},"paramsMap":{"deviceId":"528695933525832242","eid":335648583275072,"aiId":763225525260866,"execParamsDbAiId":763225325969984,"execParamsDbId":"4701f27b1528b0046ecdaa6eab36d982"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779794265088576 acc incomplete","batchId":779794266231360,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   528695933525832242