{
  name: '营销管理',
  nameI18n: 'menu.mktgManage',
  menuCode: 'mktg-manage',
  children: [
    {
      name: '客户管理',
      nameI18n: 'menu.customerManage',
      menuCode: 'customer-manage',
      img: '/assets/img/menu/cxoThematicActivities.png',
      children: [
        {
          name: '客户列表',
          nameI18n: 'menu.customerList',
          path: '/mktg-manage/customer-manage/customer-list',
          menuCode: 'customer-list',
          isPage: true
        },
        {
          name: '客户线索足迹',
          nameI18n: 'menu.customerLeadFootprint',
          path: '/mktg-manage/customer-manage/customer-lead-footprint',
          menuCode: 'customer-lead-footprint',
          isPage: true
        },
        {
          name: '客户联系人',
          nameI18n: 'menu.customerContact',
          path: '/mktg-manage/customer-manage/customer-contact',
          menuCode: 'customer-contact',
          isPage: true
        }
      ]
    },
    {
      name: 'CXO专题活动',
      nameI18n: 'menu.cxoThematicActivities',
      menuCode: 'cxo-thematic-activity',
      img: '/assets/img/menu/cxoThematicActivities.png',
      children: [
        {
          name: '快筛问卷',
          nameI18n: 'menu.quickScreenQuestionnaire',
          path: '/mktg-manage/cxo-thematic-activity/quick-screen-questionnaire',
          menuCode: 'quick-screen-questionnaire',
          isPage: true
        },
        {
          name: '职能别调研列表',
          nameI18n: 'menu.jobCategorySurveyList',
          path: '/mktg-manage/cxo-thematic-activity/job-category-survey-list',
          menuCode: 'job-category-survey-list',
          isPage: true
        }
      ]
    }
  ]
}