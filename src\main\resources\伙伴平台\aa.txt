curl -X POST "https://aisom-pre.digiwincloud.com/aiogateway/aiouser/api/user/aio/identity/login?aioPlatformEnum=AIEOM_SERVICE" \
-H "Content-Type: application/json" \
-d '{
    "appId": "AIEOM",
    "changed": true,
    "comefrom": "ServiceCloud",
    "dept": "CG0300",
    "deviceType": "COMPUTER",
    "email": "<EMAIL>",
    "enterpriseTenantType": 1,
    "hash": "",
    "identityType": "service",
    "isActivated": true,
    "isConfirm": true,
    "isDealer": false,
    "isEnterprise": false,
    "isEoc": true,
    "isOwner": false,
    "jobCode": "08627",
    "kicked": false,
    "mapping": [
        {
            "tenantSid": 99990000,
            "tenantId": "99990000",
            "userSid": 43113339314752,
            "userId": "<EMAIL>",
            "providerId": "escloud",
            "verifyUserId": "34018be4b424452caeb81840329d559f"
        }
    ],
    "metadata": [
        {
            "sid": 840513855175680,
            "key": "email",
            "name": "信箱",
            "value": "<EMAIL>",
            "catalogId": "contact",
            "userSid": 43113339314752,
            "userId": "<EMAIL>",
            "tenantSid": 99990000,
            "tenantId": "99990000",
            "tenantName": "鼎新電腦（正式）",
            "updateProvider": "0",
            "createBy": "642139749990976"
        },
        {
            "sid": 423820785005120,
            "key": "wechat",
            "name": "微信",
            "value": "",
            "catalogId": "contact",
            "userSid": 43113339314752,
            "userId": "<EMAIL>",
            "tenantSid": 99990000,
            "tenantId": "99990000",
            "tenantName": "鼎新電腦（正式）",
            "updateProvider": "**********",
            "createBy": "43113025696320"
        }
    ],
    "shared": true,
    "sid": 43113339314752,
    "telephone": "18955533265854566",
    "tenantId": "99990000",
    "tenantName": "鼎新電腦（正式）",
    "tenantSid": 99990000,
    "token": "7bb472a3-b51c-42ea-81c5-c08ca2d0248b",
    "tokenExpiresIn": **********,
    "tokenType": "digiwin",
    "userId": "<EMAIL>",
    "userName": "樊丽香",
    "userType": 0
}'         --referer "你的引荐来源网址"  --resolve 'aisom-pre.digiwincloud.com:443:20.194.211.184'