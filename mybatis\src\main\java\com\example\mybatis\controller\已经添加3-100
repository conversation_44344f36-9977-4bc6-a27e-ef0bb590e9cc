2024-10-12T13:40:32.499+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : selectE10 count:721
2024-10-12T13:40:32.773+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:350498238710336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e050fe05bcfe2fb0e67e2449bcfbb1bd&deviceId=525657258575545410
2024-10-12T13:40:32.867+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"63f1c1cef14d0db25714a9707b94e338\\\",\\\"dbIdValue\\\":\\\"63f1c1cef14d0db25714a9707b94e338\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10demo\\\",\\\"targetValue\\\":\\\"E10demo\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525657258575545410
2024-10-12T13:40:34.758+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324280517184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0d07016c278f53e7059b9528a5dc0220&deviceId=525650150371112003
2024-10-12T13:40:34.758+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ef6d74b0210ba3a3381399d7969d2a9d\\\",\\\"dbIdValue\\\":\\\"ef6d74b0210ba3a3381399d7969d2a9d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MT\\\",\\\"targetValue\\\":\\\"MT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525650150371112003
2024-10-12T13:40:36.142+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:446126438838848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d051b38b25b08cd69f36af3d19691874&deviceId=435673153289598264
2024-10-12T13:40:36.143+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"20673407576546e0b7ec74c106022ad2\\\",\\\"dbIdValue\\\":\\\"20673407576546e0b7ec74c106022ad2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"KOSO_E10\\\",\\\"targetValue\\\":\\\"KOSO_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   435673153289598264
2024-10-12T13:40:37.604+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:861071148580864 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=32a46eeed6d22450bb90a63574697899&deviceId=525202584194007860
2024-10-12T13:40:37.605+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b081064db33082ee98048e1da6178513\\\",\\\"dbIdValue\\\":\\\"b081064db33082ee98048e1da6178513\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HZDZ\\\",\\\"targetValue\\\":\\\"HZDZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525202584194007860
2024-10-12T13:40:38.957+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321224741440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bac4a7976f84cd690caadcc5c02211a7&deviceId=525191454323131188
2024-10-12T13:40:38.958+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"97d0856acc9b605d74cefcaaeb0c940b\\\",\\\"dbIdValue\\\":\\\"97d0856acc9b605d74cefcaaeb0c940b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MRZN\\\",\\\"targetValue\\\":\\\"MRZN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525191454323131188
2024-10-12T13:40:40.312+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323540951616 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b12e8f68b6a448c0b85c92a527ab2f8b&deviceId=525085880419890243
2024-10-12T13:40:40.313+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3d5fe10ae7a8b62dd4dfeeb5cc812a69\\\",\\\"dbIdValue\\\":\\\"3d5fe10ae7a8b62dd4dfeeb5cc812a69\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RTJQ\\\",\\\"targetValue\\\":\\\"RTJQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525085880419890243
2024-10-12T13:40:41.669+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:381729226367552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9193cc4b0cf2c4986c46d89a7b827b2d&deviceId=525072049702450230
2024-10-12T13:40:41.670+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ba05297de0d982460b372cedc571c952\\\",\\\"dbIdValue\\\":\\\"ba05297de0d982460b372cedc571c952\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003_YY01\\\",\\\"targetValue\\\":\\\"E6003_YY01\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525072049702450230
2024-10-12T13:40:43.100+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:124522582524480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0715d8b661accf165b892a63b69de4e5&deviceId=525074098368296003
2024-10-12T13:40:43.100+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e5c9d3cdfc1dc283fdc70fe1f599ed14\\\",\\\"dbIdValue\\\":\\\"e5c9d3cdfc1dc283fdc70fe1f599ed14\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"PRD\\\",\\\"targetValue\\\":\\\"PRD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525074098368296003
2024-10-12T13:40:45.398+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323998949952 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4c46c4234f10c8f6d25b27a0b7628c0f&deviceId=525063473156080953
2024-10-12T13:40:45.398+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"995a71dd34024917c8ca4b72ab057874\\\",\\\"dbIdValue\\\":\\\"995a71dd34024917c8ca4b72ab057874\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LPS\\\",\\\"targetValue\\\":\\\"LPS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   525063473156080953
2024-10-12T13:40:46.900+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323303621184 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b64b5dc64d2b51d34f19cc68a9dc8331&deviceId=469821947560079942
2024-10-12T13:40:46.901+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a2021b1a32c4ab4835839e2a334e2e1d\\\",\\\"dbIdValue\\\":\\\"a2021b1a32c4ab4835839e2a334e2e1d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HHDATA\\\",\\\"targetValue\\\":\\\"HHDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   469821947560079942
2024-10-12T13:40:49.200+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322516001344 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6f4263548c6ef06f92cac9c9e3d437bb&deviceId=524917234099831860
2024-10-12T13:40:49.200+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9ccbb4bca97554cdbeb7842519a400cc\\\",\\\"dbIdValue\\\":\\\"9ccbb4bca97554cdbeb7842519a400cc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_CSF\\\",\\\"targetValue\\\":\\\"E10_CSF\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524917234099831860
2024-10-12T13:40:50.979+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322040336960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e79dc73a3099fce1ebf98e5fe5f510a5&deviceId=524903997631378499
2024-10-12T13:40:50.979+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9f589ec4ef47206ead1837134151c927\\\",\\\"dbIdValue\\\":\\\"9f589ec4ef47206ead1837134151c927\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"szmcs_new2\\\",\\\"targetValue\\\":\\\"szmcs_new2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524903997631378499
2024-10-12T13:40:52.640+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41317315736128 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=727d6860d31b9e09606334883cfd6139&deviceId=524903239552872770
2024-10-12T13:40:52.641+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7c8bdc4721b7c1a4bdc6cc237f82f8dd\\\",\\\"dbIdValue\\\":\\\"7c8bdc4721b7c1a4bdc6cc237f82f8dd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"reachdb\\\",\\\"targetValue\\\":\\\"reachdb\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524903239552872770
2024-10-12T13:40:54.989+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:233430812213824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ef6b48e16b9dc6221d1d4bb020712430&deviceId=524899183577019461
2024-10-12T13:40:54.991+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1db9aabc8beeb5cc7457abe262168ffc\\\",\\\"dbIdValue\\\":\\\"1db9aabc8beeb5cc7457abe262168ffc\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HXDQ\\\",\\\"targetValue\\\":\\\"HXDQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524899183577019461
2024-10-12T13:40:56.682+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:184944967295553 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b8029449424eeace47ec44dccab5ea1d&deviceId=524784995009575222
2024-10-12T13:40:56.683+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"205740b672952910003c20e2b5470640\\\",\\\"dbIdValue\\\":\\\"205740b672952910003c20e2b5470640\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GMDATA\\\",\\\"targetValue\\\":\\\"GMDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524784995009575222
2024-10-12T13:40:58.518+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323095949888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3e5521415ffce663b3b3db856a44fba2&deviceId=524784636983784505
2024-10-12T13:40:58.518+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"eeeaf19acbb0f441b755b6231ad94ea4\\\",\\\"dbIdValue\\\":\\\"eeeaf19acbb0f441b755b6231ad94ea4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JLDF_E10\\\",\\\"targetValue\\\":\\\"JLDF_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524784636983784505
2024-10-12T13:41:00.297+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324290028096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=8fb4a5a1587a7c0ee4980e143bcb6d0c&deviceId=524781911256609859
2024-10-12T13:41:00.297+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1325623ac9a94b00bddecdb6565cfdfe\\\",\\\"dbIdValue\\\":\\\"1325623ac9a94b00bddecdb6565cfdfe\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WH\\\",\\\"targetValue\\\":\\\"WH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524781911256609859
2024-10-12T13:41:01.788+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324681814592 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=61e52500941bbf2cc42dc0036848e4f7&deviceId=382149395132465985
2024-10-12T13:41:01.789+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a7dc9a4b16dfab7bb5785c61c7e688d4\\\",\\\"dbIdValue\\\":\\\"a7dc9a4b16dfab7bb5785c61c7e688d4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Tronxin\\\",\\\"targetValue\\\":\\\"Tronxin\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   382149395132465985
2024-10-12T13:41:03.234+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320121475648 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=553db1e76f7a705afc7c9665a124c9a7&deviceId=524751873698182211
2024-10-12T13:41:03.235+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"fcb2cf7e3c68ab3b02c872240ed2e273\\\",\\\"dbIdValue\\\":\\\"fcb2cf7e3c68ab3b02c872240ed2e273\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SZAT\\\",\\\"targetValue\\\":\\\"SZAT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524751873698182211
2024-10-12T13:41:05.152+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:642659639235136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=4a9da47730b9daa8f6c838c3750e2ce0&deviceId=524637153511093297
2024-10-12T13:41:05.153+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e723ed6aec3988584a3fda1a4b8e9160\\\",\\\"dbIdValue\\\":\\\"e723ed6aec3988584a3fda1a4b8e9160\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"koshin\\\",\\\"targetValue\\\":\\\"koshin\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524637153511093297
2024-10-12T13:41:06.662+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:231263544070720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=41400efd633cbf191b0506a8ffc8db85&deviceId=524632623092347203
2024-10-12T13:41:06.663+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"25eebbb47371172577b79797380d1bab\\\",\\\"dbIdValue\\\":\\\"25eebbb47371172577b79797380d1bab\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetValue\\\":\\\"E10_6.0.0.1BaseData.CHS_Single\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524632623092347203
2024-10-12T13:41:08.403+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:510565572899392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=c3f002038108edfac06f8d1b90b96477&deviceId=524629296069750837
2024-10-12T13:41:08.403+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"024e419cb80bf59498d2207386be7754\\\",\\\"dbIdValue\\\":\\\"024e419cb80bf59498d2207386be7754\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524629296069750837
2024-10-12T13:41:10.258+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320117850688 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b16124c8f31ea13f17371db04d0407cf&deviceId=524606644512896067
2024-10-12T13:41:10.259+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a15a15068344e5b737b0e5ed94149fbd\\\",\\\"dbIdValue\\\":\\\"a15a15068344e5b737b0e5ed94149fbd\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"[E10_5.0.0.1BaseData.CHS_Single]\\\",\\\"targetValue\\\":\\\"[E10_5.0.0.1BaseData.CHS_Single]\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524606644512896067
2024-10-12T13:41:11.814+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324603888192 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=20e40d49b55f16589aa8b84d82ab4f31&deviceId=524198674411045189
2024-10-12T13:41:11.815+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bb9f920cf1bf98cffe5a2665a7e82027\\\",\\\"dbIdValue\\\":\\\"bb9f920cf1bf98cffe5a2665a7e82027\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Hansong\\\",\\\"targetValue\\\":\\\"Hansong\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524198674411045189
2024-10-12T13:41:13.462+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:639828144009792 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6a6bd299ffa45c033a1c7a6b3adfb884&deviceId=524165121757430073
2024-10-12T13:41:13.462+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e99fc8f0ddfadbec4de525c9c4602039\\\",\\\"dbIdValue\\\":\\\"e99fc8f0ddfadbec4de525c9c4602039\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ALS_ZS\\\",\\\"targetValue\\\":\\\"ALS_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524165121757430073
2024-10-12T13:41:14.975+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:317575518921280 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2d294b5a93d8fac195a7c5df31583cfb&deviceId=524022600918123586
2024-10-12T13:41:14.976+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"89ff5919fce66007b6cf3bf298ee5254\\\",\\\"dbIdValue\\\":\\\"89ff5919fce66007b6cf3bf298ee5254\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_ANQ_ZS\\\",\\\"targetValue\\\":\\\"E10_ANQ_ZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   524022600918123586
2024-10-12T13:41:16.647+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:131954512548416 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=797ed0be612661e4221db48c64416bb5&deviceId=523915623869202756
2024-10-12T13:41:16.647+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"52f9e6fd78a0f705473988e15e8a497a\\\",\\\"dbIdValue\\\":\\\"52f9e6fd78a0f705473988e15e8a497a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GH\\\",\\\"targetValue\\\":\\\"GH\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523915623869202756
2024-10-12T13:41:18.237+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:642659579781696 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3c66a8be091dbc5f166161b97c535820&deviceId=523890661250839619
2024-10-12T13:41:18.238+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c8670f2c09e57c08fcdf1c9521af190c\\\",\\\"dbIdValue\\\":\\\"c8670f2c09e57c08fcdf1c9521af190c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MJYData\\\",\\\"targetValue\\\":\\\"MJYData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523890661250839619
2024-10-12T13:41:19.968+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318257660480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ee47c86be76a8d1311251042cbdba9ad&deviceId=523884194540045360
2024-10-12T13:41:19.968+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b369372907f4c3ebe6406ec693470c35\\\",\\\"dbIdValue\\\":\\\"b369372907f4c3ebe6406ec693470c35\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BTZZ\\\",\\\"targetValue\\\":\\\"BTZZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523884194540045360
2024-10-12T13:41:21.430+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:486865749467712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7e3c132e1fed0c976cf4a578caaea54b&deviceId=523876061650367812
2024-10-12T13:41:21.430+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"5dbfa5085722bba9ca3f1816385fd5a4\\\",\\\"dbIdValue\\\":\\\"5dbfa5085722bba9ca3f1816385fd5a4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZHZZ\\\",\\\"targetValue\\\":\\\"ZHZZ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523876061650367812
2024-10-12T13:41:23.042+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320605258304 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d857f8c960ac0245bfc19c7671bd41b9&deviceId=523035276050707522
2024-10-12T13:41:23.043+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8850eaa3e40143e2cc0628cf95ee0413\\\",\\\"dbIdValue\\\":\\\"8850eaa3e40143e2cc0628cf95ee0413\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TOPTEK\\\",\\\"targetValue\\\":\\\"TOPTEK\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523035276050707522
2024-10-12T13:41:24.636+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324235670080 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e910b52c909312b91f48608b1a395a07&deviceId=523768958201902147
2024-10-12T13:41:24.636+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"9c0d9ae57219121a242fb63252e1eaff\\\",\\\"dbIdValue\\\":\\\"9c0d9ae57219121a242fb63252e1eaff\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSN\\\",\\\"targetValue\\\":\\\"XSN\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523768958201902147
2024-10-12T13:41:26.143+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:642659460481600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b1cc3d41b638370369d314f4d50cabf4&deviceId=523739243168674883
2024-10-12T13:41:26.144+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c830d06cdb2876c7672cd52d93635f92\\\",\\\"dbIdValue\\\":\\\"c830d06cdb2876c7672cd52d93635f92\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_6.3.0.0.NEW.CHS\\\",\\\"targetValue\\\":\\\"E10_6.3.0.0.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523739243168674883
2024-10-12T13:41:27.468+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:519088653308480 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=7eb22417deedb9de832c5f25c26fa360&deviceId=523743316005631043
2024-10-12T13:41:27.468+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"cc4ab989b01f2ae324f5831f1b5b134b\\\",\\\"dbIdValue\\\":\\\"cc4ab989b01f2ae324f5831f1b5b134b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZX\\\",\\\"targetValue\\\":\\\"ZX\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523743316005631043
2024-10-12T13:41:28.822+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324750455360 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=22d1d70ac15cf93c9d5a3a09074a7764&deviceId=523741153305048388
2024-10-12T13:41:28.823+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b6d31203f6464f9af34a92ab181442b2\\\",\\\"dbIdValue\\\":\\\"b6d31203f6464f9af34a92ab181442b2\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GY\\\",\\\"targetValue\\\":\\\"GY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523741153305048388
2024-10-12T13:41:30.244+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:284041950188096 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eb441578bbd5789595285f93d7f213f0&deviceId=523631834777530932
2024-10-12T13:41:30.245+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"efa1afde8f2efeea1624fdeb034d420e\\\",\\\"dbIdValue\\\":\\\"efa1afde8f2efeea1624fdeb034d420e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LBJM-202302\\\",\\\"targetValue\\\":\\\"LBJM-202302\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523631834777530932
2024-10-12T13:41:31.809+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:201605970743872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=618c6d1058ae31053873711fd58a6132&deviceId=523188584287581251
2024-10-12T13:41:31.810+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b3e8fd5698fdb7e46e5fd67e220c21e6\\\",\\\"dbIdValue\\\":\\\"b3e8fd5698fdb7e46e5fd67e220c21e6\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SBEP_6.0.0.1.NEW.CHS\\\",\\\"targetValue\\\":\\\"SBEP_6.0.0.1.NEW.CHS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523188584287581251
2024-10-12T13:41:33.347+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:348728859038272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5d72173146596617557083418a9df8e7&deviceId=522019264845067060
2024-10-12T13:41:33.347+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b63b3a24fc79e0aef43aaab546474066\\\",\\\"dbIdValue\\\":\\\"b63b3a24fc79e0aef43aaab546474066\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003\\\",\\\"targetValue\\\":\\\"E6003\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522019264845067060
2024-10-12T13:41:34.737+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:239300322370112 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=0371091cc6781da3cf8f7301f564159d&deviceId=523050262852547652
2024-10-12T13:41:34.737+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"00478b7a6af335bd5606102b2dabd4f1\\\",\\\"dbIdValue\\\":\\\"00478b7a6af335bd5606102b2dabd4f1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_LC\\\",\\\"targetValue\\\":\\\"E10_LC\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523050262852547652
2024-10-12T13:41:36.072+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:243886175658560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=290d332f362aed5e6c91affc3ac3d3fe&deviceId=523005549709635632
2024-10-12T13:41:36.073+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6a3937a459dcdf085b0be891809b373f\\\",\\\"dbIdValue\\\":\\\"6a3937a459dcdf085b0be891809b373f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YUNGKU1\\\",\\\"targetValue\\\":\\\"YUNGKU1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   523005549709635632
2024-10-12T13:41:37.449+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321719181888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=785bf5b84572ce39b601ceccb61a30f0&deviceId=522886063316812868
2024-10-12T13:41:37.449+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ec3b2a88e5eb71435ad34e82b4bb7d02\\\",\\\"dbIdValue\\\":\\\"ec3b2a88e5eb71435ad34e82b4bb7d02\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10\\\",\\\"targetValue\\\":\\\"E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522886063316812868
2024-10-12T13:41:38.877+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:393363351929408 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e51955c8faa55a16b95e5f003d3ee2c2&deviceId=522863360656556852
2024-10-12T13:41:38.877+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"20c2d03aa4187c652de442aebac5e938\\\",\\\"dbIdValue\\\":\\\"20c2d03aa4187c652de442aebac5e938\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"WBQ\\\",\\\"targetValue\\\":\\\"WBQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd850343f46eeec979f1f224cceae048f2a332e155cf06eddf743e592d7dbb3b317d00b93ca1a7446ce82e3c33347f66a142359106ce6060d39e023f7c29825b937ef2048b074d7f4038b742cd60df13f45417f47b097b13df85cd356482aab00bbb865e6ccb6f7d391060c61712a9cb8a9d33f00b88bc9ab9a2bbc92d285460503863d3fe22d58d4ecba77f8ebfffb991656415eb0b8749dd5b0f5c9fa12cb4beae7eba21ca66ddd7102f9444ae2e696a9e1763b4d752dde86374551774642f9fd4065258a8de99fa158e73a3654db58a65cc39d38c046da57f39224629e43535fb015a0a37466486a0e0d3b5f1d669e99ea1","collectName":"E10附件数据采集","accId":779544910123584,"adimId":748984307462721,"id":779831298490944,"adcId":748983644230208,"execParamsVersion":"f67ee721db6c49cd15a68d21e30c9fbf","aiId":748984307450433,"isEnable":1},"paramsMap":{"deviceId":"522863360656556852","eid":393363351929408,"aiId":748984307450433,"execParamsDbAiId":635400878518848,"execParamsDbId":"20c2d03aa4187c652de442aebac5e938"},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779831298490944 acc incomplete","batchId":779831299904064,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   522863360656556852
2024-10-12T13:41:40.380+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:364399986467392 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e0bbd63d6f7bc04cb774f15efecc48ad&deviceId=516336381602055746
2024-10-12T13:41:40.382+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"28e8bca07f153b51d3d9839f3d5dbe7d\\\",\\\"dbIdValue\\\":\\\"28e8bca07f153b51d3d9839f3d5dbe7d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MZTT\\\",\\\"targetValue\\\":\\\"MZTT\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516336381602055746
2024-10-12T13:41:41.719+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:603287887430208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=293839d5aa8905316a692a7f1ed24616&deviceId=485326966128391732
2024-10-12T13:41:41.719+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0c4ebc4d09c1dc8dea1543e17478090e\\\",\\\"dbIdValue\\\":\\\"0c4ebc4d09c1dc8dea1543e17478090e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"beist\\\",\\\"targetValue\\\":\\\"beist\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   485326966128391732
2024-10-12T13:41:43.078+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:239300343095872 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=84e9f7c67cb91811d6bb4d763fa54108&deviceId=522176337251681076
2024-10-12T13:41:43.078+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0ab4ff920fc2ebffe3aea270a3b5427f\\\",\\\"dbIdValue\\\":\\\"0ab4ff920fc2ebffe3aea270a3b5427f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LH_formal\\\",\\\"targetValue\\\":\\\"LH_formal\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522176337251681076
2024-10-12T13:41:44.474+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:340868545000000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=881441a91d0770a49a983bb2a471f264&deviceId=522147187258308165
2024-10-12T13:41:44.475+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0453801faf12cf676ac766458b84ff18\\\",\\\"dbIdValue\\\":\\\"0453801faf12cf676ac766458b84ff18\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MRX_data\\\",\\\"targetValue\\\":\\\"MRX_data\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522147187258308165
2024-10-12T13:41:45.781+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318669156928 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=63355d524faf164fc8952130e9ddbe0d&deviceId=522025839198483767
2024-10-12T13:41:45.782+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"964b2d6676920da028e7c70453e2ccb8\\\",\\\"dbIdValue\\\":\\\"964b2d6676920da028e7c70453e2ccb8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"EardaData\\\",\\\"targetValue\\\":\\\"EardaData\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522025839198483767
2024-10-12T13:41:47.182+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41317757993536 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3796368ed8aa48f424b84fd38c09d20e&deviceId=522021323996672821
2024-10-12T13:41:47.182+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"409a4ff108e4ea5871f145de5650e5b0\\\",\\\"dbIdValue\\\":\\\"409a4ff108e4ea5871f145de5650e5b0\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"dayange10\\\",\\\"targetValue\\\":\\\"dayange10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522021323996672821
2024-10-12T13:41:48.508+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321073431104 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fdb16338e89205eaf9ee7b473d0d8502&deviceId=522019467228627270
2024-10-12T13:41:48.509+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"89c306f6bdbf5c0195c872b72a3d451d\\\",\\\"dbIdValue\\\":\\\"89c306f6bdbf5c0195c872b72a3d451d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10-CKS\\\",\\\"targetValue\\\":\\\"E10-CKS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   522019467228627270
2024-10-12T13:41:49.958+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:494620987830848 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=26fd711fe3eb2ecd3f3d75e26c88b21e&deviceId=487500913200608323
2024-10-12T13:41:49.958+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b7667a87b153344e7f96d565045392d9\\\",\\\"dbIdValue\\\":\\\"b7667a87b153344e7f96d565045392d9\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"FXDATA\\\",\\\"targetValue\\\":\\\"FXDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   487500913200608323
2024-10-12T13:41:51.327+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323782079040 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cdd4860f528536daca5aea86d8715a13&deviceId=521893155830772803
2024-10-12T13:41:51.327+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d12fd84f714eb197ccac9975cbb50b55\\\",\\\"dbIdValue\\\":\\\"d12fd84f714eb197ccac9975cbb50b55\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"JINYI\\\",\\\"targetValue\\\":\\\"JINYI\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521893155830772803
2024-10-12T13:41:52.664+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321320858176 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1503731e82041f05aa2c46fe5fa9e5df&deviceId=521885128687890743
2024-10-12T13:41:52.664+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"76ca7e913a65f37e2b605d45c10f8850\\\",\\\"dbIdValue\\\":\\\"76ca7e913a65f37e2b605d45c10f8850\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CTDATA\\\",\\\"targetValue\\\":\\\"CTDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521885128687890743
2024-10-12T13:41:54.005+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:457241440973376 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=47b045718c67ac09f10634a04fba46dd&deviceId=521874460375008817
2024-10-12T13:41:54.006+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6c45f89fd86fcb43ad4bb2870a745743\\\",\\\"dbIdValue\\\":\\\"6c45f89fd86fcb43ad4bb2870a745743\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"RCDATA\\\",\\\"targetValue\\\":\\\"RCDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521874460375008817
2024-10-12T13:41:55.437+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:195398912782913 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bae7c160968f8254490986edf28e7e4f&deviceId=521859651898197062
2024-10-12T13:41:55.438+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"34c57002254bfe40a39a7de40ecddb78\\\",\\\"dbIdValue\\\":\\\"34c57002254bfe40a39a7de40ecddb78\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_MGNW\\\",\\\"targetValue\\\":\\\"E10_MGNW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521859651898197062
2024-10-12T13:41:56.805+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41325033984576 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=516dd4b8ac552f3badc86dba78d7f1e8&deviceId=521855337502880835
2024-10-12T13:41:56.806+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1b5431481ce56a12f3e3ac92d3ca56cb\\\",\\\"dbIdValue\\\":\\\"1b5431481ce56a12f3e3ac92d3ca56cb\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HJdata\\\",\\\"targetValue\\\":\\\"HJdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521855337502880835
2024-10-12T13:41:58.167+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:360167742964288 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=ddb1f9d8b403829fb99fe6090bf6226a&deviceId=521750240039024708
2024-10-12T13:41:58.167+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a8e76f764ec9d46369412fad5a609174\\\",\\\"dbIdValue\\\":\\\"a8e76f764ec9d46369412fad5a609174\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"HTtech\\\",\\\"targetValue\\\":\\\"HTtech\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521750240039024708
2024-10-12T13:41:59.496+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321194922560 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=259d00bfa013cc8485aa5768efa3ca91&deviceId=521736449855337539
2024-10-12T13:41:59.497+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"de0994f17c0e6be832e3125622787aed\\\",\\\"dbIdValue\\\":\\\"de0994f17c0e6be832e3125622787aed\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TianXin\\\",\\\"targetValue\\\":\\\"TianXin\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521736449855337539
2024-10-12T13:42:01.077+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:441580142301760 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=1d07ba1a0035ed1983e77d88179df485&deviceId=472705999929624134
2024-10-12T13:42:01.078+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"588aaff67beffaf994560b17cf001baa\\\",\\\"dbIdValue\\\":\\\"588aaff67beffaf994560b17cf001baa\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZW_DATA\\\",\\\"targetValue\\\":\\\"ZW_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   472705999929624134
2024-10-12T13:42:02.432+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324653830720 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eca1ad221bf271ee6a626d2d6b93c95c&deviceId=521699045991789636
2024-10-12T13:42:02.433+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"94a7ccd7f825743e6f1897a34a225708\\\",\\\"dbIdValue\\\":\\\"94a7ccd7f825743e6f1897a34a225708\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"TGDATA\\\",\\\"targetValue\\\":\\\"TGDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521699045991789636
2024-10-12T13:42:03.798+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41320155624000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=84cdcc7d1213187bb2060848774289e6&deviceId=521585082255815750
2024-10-12T13:42:03.799+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e1c227c7d42b78400b1a2d5d65c06ca8\\\",\\\"dbIdValue\\\":\\\"e1c227c7d42b78400b1a2d5d65c06ca8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"mingyouZS20180508\\\",\\\"targetValue\\\":\\\"mingyouZS20180508\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521585082255815750
2024-10-12T13:42:05.152+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321570259520 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=061136405c0c2b3348f89de3696d154b&deviceId=521569558146856003
2024-10-12T13:42:05.153+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a4e1ab94e97c8e0446d87f9ceecbb322\\\",\\\"dbIdValue\\\":\\\"a4e1ab94e97c8e0446d87f9ceecbb322\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DCNEW\\\",\\\"targetValue\\\":\\\"DCNEW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521569558146856003
2024-10-12T13:42:06.507+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:419676528308800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=18b513f3383ee3bbdfd7f21473caa859&deviceId=521564985164313667
2024-10-12T13:42:06.508+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bb13df04d2cb88ff594b237655c0e394\\\",\\\"dbIdValue\\\":\\\"bb13df04d2cb88ff594b237655c0e394\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BCDQ\\\",\\\"targetValue\\\":\\\"BCDQ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521564985164313667
2024-10-12T13:42:07.906+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:242116719120960 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e7f56287967880bca49f507c7698b80a&deviceId=521557177568080963
2024-10-12T13:42:07.907+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0693745d348c47656410a8ad0dd10903\\\",\\\"dbIdValue\\\":\\\"0693745d348c47656410a8ad0dd10903\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CTJXZS\\\",\\\"targetValue\\\":\\\"CTJXZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521557177568080963
2024-10-12T13:42:09.364+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:642659534275136 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=eb8691e577c6aae404a138f60afb1323&deviceId=521169892884363316
2024-10-12T13:42:09.364+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7b9a7ac9ac67a3efc19617bdb2b4ce07\\\",\\\"dbIdValue\\\":\\\"7b9a7ac9ac67a3efc19617bdb2b4ce07\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YQJM\\\",\\\"targetValue\\\":\\\"YQJM\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521169892884363316
2024-10-12T13:42:10.797+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41317450314304 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=40991d72413f278ab03f44d9d2b6c010&deviceId=521159347984675908
2024-10-12T13:42:10.798+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3e3185689e28bad426996aa46e6e8b25\\\",\\\"dbIdValue\\\":\\\"3e3185689e28bad426996aa46e6e8b25\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NQ_DATA\\\",\\\"targetValue\\\":\\\"NQ_DATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521159347984675908
2024-10-12T13:42:12.137+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323141268032 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fa92f30c73a506f7f829429d9f6ec00e&deviceId=521154424559645763
2024-10-12T13:42:12.138+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"de2c5c3a0e962e415b866a0e1357e6e4\\\",\\\"dbIdValue\\\":\\\"de2c5c3a0e962e415b866a0e1357e6e4\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"GCDATA\\\",\\\"targetValue\\\":\\\"GCDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521154424559645763
2024-10-12T13:42:13.633+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41319614788160 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=e74a37e4e079e4ce278526f9deb167cf&deviceId=521144261324849475
2024-10-12T13:42:13.635+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7a0d47fa099a726e5a9714a74e3aff0a\\\",\\\"dbIdValue\\\":\\\"7a0d47fa099a726e5a9714a74e3aff0a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSDMCW\\\",\\\"targetValue\\\":\\\"XSDMCW\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521144261324849475
2024-10-12T13:42:14.989+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41319839593024 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2daf024ad4ad9869e67ab8f895fb5886&deviceId=420115673574814787
2024-10-12T13:42:14.989+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"3bd545b3316a15e2ec65bc455d509cce\\\",\\\"dbIdValue\\\":\\\"3bd545b3316a15e2ec65bc455d509cce\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"nagaoka\\\",\\\"targetValue\\\":\\\"nagaoka\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   420115673574814787
2024-10-12T13:42:16.418+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:67540825760320 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=39b879dd6e07f93b67064f34f06c5278&deviceId=521129101466022706
2024-10-12T13:42:16.418+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"adb828266631acf774b36a12dfab3c3d\\\",\\\"dbIdValue\\\":\\\"adb828266631acf774b36a12dfab3c3d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"SanU\\\",\\\"targetValue\\\":\\\"SanU\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521129101466022706
2024-10-12T13:42:17.774+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:237176374104640 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fa61b7affab0b1b3b90c20fd28aecd53&deviceId=521121625555354675
2024-10-12T13:42:17.774+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"509c09ea2bcca770efdb8018181638b7\\\",\\\"dbIdValue\\\":\\\"509c09ea2bcca770efdb8018181638b7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"CRE\\\",\\\"targetValue\\\":\\\"CRE\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521121625555354675
2024-10-12T13:42:19.204+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:478446035087936 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6b807cf0a5f9ed9fe8d00e132994cf37&deviceId=521120433936811587
2024-10-12T13:42:19.205+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"00bd9b1e34cb5fac7b65d56822895281\\\",\\\"dbIdValue\\\":\\\"00bd9b1e34cb5fac7b65d56822895281\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NBzhanjing_E10\\\",\\\"targetValue\\\":\\\"NBzhanjing_E10\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521120433936811587
2024-10-12T13:42:20.543+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322102293056 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=d2148bd01a19223665ecc107a46ec8e9&deviceId=521015303019181123
2024-10-12T13:42:20.543+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"93e378bc6e03bddf52ff64c2ef73be64\\\",\\\"dbIdValue\\\":\\\"93e378bc6e03bddf52ff64c2ef73be64\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"NJHJ\\\",\\\"targetValue\\\":\\\"NJHJ\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521015303019181123
2024-10-12T13:42:21.899+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41318400123456 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6b7b98dd89fa8d4aabc6e0537242b8a9&deviceId=521014424362824770
2024-10-12T13:42:21.899+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"c5167cc6cbd3d54357aebff811c4d46a\\\",\\\"dbIdValue\\\":\\\"c5167cc6cbd3d54357aebff811c4d46a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"powerjet_r\\\",\\\"targetValue\\\":\\\"powerjet_r\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521014424362824770
2024-10-12T13:42:23.301+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:241615457137216 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=13542bf7bc42aa8c51b7bf8152ebf3d7&deviceId=521012132544786996
2024-10-12T13:42:23.302+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f6d8bde5444ebfe7c08a54a218ba4c88\\\",\\\"dbIdValue\\\":\\\"f6d8bde5444ebfe7c08a54a218ba4c88\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"lkdata-1\\\",\\\"targetValue\\\":\\\"lkdata-1\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   521012132544786996
2024-10-12T13:42:24.645+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:251317040927296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=321a47e2340d5583671107651749d610&deviceId=515934592629224515
2024-10-12T13:42:24.645+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"72eeb0e30098b7f5f7bdc3c90d0d8796\\\",\\\"dbIdValue\\\":\\\"72eeb0e30098b7f5f7bdc3c90d0d8796\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ATDATA\\\",\\\"targetValue\\\":\\\"ATDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   515934592629224515
2024-10-12T13:42:25.984+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:68690971624000 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=282ab14fc970cda92a7398541c3c13dc&deviceId=520975815425406019
2024-10-12T13:42:25.984+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a31f8132e43f924e69b1c8574dee13a1\\\",\\\"dbIdValue\\\":\\\"a31f8132e43f924e69b1c8574dee13a1\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"ZSBY\\\",\\\"targetValue\\\":\\\"ZSBY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520975815425406019
2024-10-12T13:42:27.350+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324336259648 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b4ce16075f07b8efd5bd4b866e468a57&deviceId=520858811490120771
2024-10-12T13:42:27.351+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"4100ba4d9383394dadd2991728995b1b\\\",\\\"dbIdValue\\\":\\\"4100ba4d9383394dadd2991728995b1b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10yimei\\\",\\\"targetValue\\\":\\\"E10yimei\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520858811490120771
2024-10-12T13:42:28.701+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:477384334389824 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=9cfd03701d83ec13e53c61ec201d778d&deviceId=520845632735295285
2024-10-12T13:42:28.702+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"d849d251d176b61cf5859e3fc50bcd80\\\",\\\"dbIdValue\\\":\\\"d849d251d176b61cf5859e3fc50bcd80\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E6003_23\\\",\\\"targetValue\\\":\\\"E6003_23\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520845632735295285
2024-10-12T13:42:30.100+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:254500381585984 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=131a5fd18bc58710ff5099a01d02cf7e&deviceId=520842491017048626
2024-10-12T13:42:30.101+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"8cd6446f44b4c22ed97736b8b232bd17\\\",\\\"dbIdValue\\\":\\\"8cd6446f44b4c22ed97736b8b232bd17\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"Grenergy\\\",\\\"targetValue\\\":\\\"Grenergy\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520842491017048626
2024-10-12T13:42:31.557+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:501262756381248 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=07908cb5288e14b4b920b8b2219de7d3&deviceId=520730041710030896
2024-10-12T13:42:31.557+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a0be58853e3f79cf7a1be3e9c0f86558\\\",\\\"dbIdValue\\\":\\\"a0be58853e3f79cf7a1be3e9c0f86558\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520730041710030896
2024-10-12T13:42:32.909+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323477541440 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=33a6e0392655b04eabaf91cb17b42e20&deviceId=520713744557294900
2024-10-12T13:42:32.910+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e1f0f50fde809e49a683ab0bd5800b7e\\\",\\\"dbIdValue\\\":\\\"e1f0f50fde809e49a683ab0bd5800b7e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"caiwu\\\",\\\"targetValue\\\":\\\"caiwu\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520713744557294900
2024-10-12T13:42:34.267+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:203729368351296 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6c30565e4073b4aff76f7403e6c157b5&deviceId=520715854157984569
2024-10-12T13:42:34.267+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"63ce62c5a24afdba2dc217a954e9e139\\\",\\\"dbIdValue\\\":\\\"63ce62c5a24afdba2dc217a954e9e139\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"caiwu\\\",\\\"targetValue\\\":\\\"caiwu\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   520715854157984569
2024-10-12T13:42:35.697+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:230909193376320 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=791450b3db4041ddec3d195d53f5ea5a&deviceId=517822763801523267
2024-10-12T13:42:35.698+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"b2edfc9026902250921c83852a10c644\\\",\\\"dbIdValue\\\":\\\"b2edfc9026902250921c83852a10c644\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"300237\\\",\\\"targetValue\\\":\\\"300237\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517822763801523267
2024-10-12T13:42:37.017+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324922126912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=84e1f17c8d9a3a8661474bbc1276ea33&deviceId=421130308797088580
2024-10-12T13:42:37.017+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"411fb220ca2e51b08e2dbffefbfe664d\\\",\\\"dbIdValue\\\":\\\"411fb220ca2e51b08e2dbffefbfe664d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"LYJLYY\\\",\\\"targetValue\\\":\\\"LYJLYY\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   421130308797088580
2024-10-12T13:42:38.463+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:358014793908800 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=14ef3df335c9fd9481808b2b8ab2fcba&deviceId=519862447327162435
2024-10-12T13:42:38.463+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"16a1c103fcff81b8ee59a8f3a701de2a\\\",\\\"dbIdValue\\\":\\\"16a1c103fcff81b8ee59a8f3a701de2a\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519862447327162435
2024-10-12T13:42:39.799+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324173070912 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=3991d72f0390e7684c3cf269692b9eaf&deviceId=519828583875625528
2024-10-12T13:42:39.799+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ccd7f4b431c6efef7d1374a786148ce7\\\",\\\"dbIdValue\\\":\\\"ccd7f4b431c6efef7d1374a786148ce7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"BD\\\",\\\"targetValue\\\":\\\"BD\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519828583875625528
2024-10-12T13:42:41.140+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:117444752458304 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=fab3ec2e5b53707ca26949b47beffc26&deviceId=519567727543726898
2024-10-12T13:42:41.141+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"ea28d952dbc6a541b38f694da12d229e\\\",\\\"dbIdValue\\\":\\\"ea28d952dbc6a541b38f694da12d229e\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"PKDATA\\\",\\\"targetValue\\\":\\\"PKDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519567727543726898
2024-10-12T13:42:42.483+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:332468448977472 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=96f654597896691baebc5bbed0231102&deviceId=519562497682847288
2024-10-12T13:42:42.483+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"1dfbc8b6645ddca71a70d09a302ed707\\\",\\\"dbIdValue\\\":\\\"1dfbc8b6645ddca71a70d09a302ed707\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YZDATA\\\",\\\"targetValue\\\":\\\"YZDATA\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519562497682847288
2024-10-12T13:42:43.803+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324430283328 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=dfa98993d7af3e79a0a6ffd3cc3f0fa8&deviceId=519528289123971394
2024-10-12T13:42:43.803+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"6ffb3b7c04fb95769b5a89b4fff7814d\\\",\\\"dbIdValue\\\":\\\"6ffb3b7c04fb95769b5a89b4fff7814d\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"XSTH_2022\\\",\\\"targetValue\\\":\\\"XSTH_2022\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   519528289123971394
2024-10-12T13:42:45.283+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41321874829888 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=cfb5c4b2e0b0c59fe70d6db31a64c3a5&deviceId=516781739109008705
2024-10-12T13:42:45.284+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a824d4f075bc353531ddd26c38383a9b\\\",\\\"dbIdValue\\\":\\\"a824d4f075bc353531ddd26c38383a9b\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   516781739109008705
2024-10-12T13:42:46.707+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:469290930094656 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=5b8579d7d44d35f0194eafdf851d88fe&deviceId=518960316860609603
2024-10-12T13:42:46.708+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"449809b64270be492c3a8fff85b33c57\\\",\\\"dbIdValue\\\":\\\"449809b64270be492c3a8fff85b33c57\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518960316860609603
2024-10-12T13:42:48.049+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:472002262684224 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bfc31b6de30ed4f53f0df39658d3dd29&deviceId=518804984385516611
2024-10-12T13:42:48.049+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"933fc31a6e6c897191c19f8469fbf350\\\",\\\"dbIdValue\\\":\\\"933fc31a6e6c897191c19f8469fbf350\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518804984385516611
2024-10-12T13:42:49.457+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41322677633600 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=2e55dacd83e0e5f1a69273f75579a069&deviceId=515635214182003779
2024-10-12T13:42:49.457+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"f5f0b1e4929d23d9214f34ad15117197\\\",\\\"dbIdValue\\\":\\\"f5f0b1e4929d23d9214f34ad15117197\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"DSL\\\",\\\"targetValue\\\":\\\"DSL\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"16101","data":[{"upgradeType":"INSERT_OR_UDPATE","upgradeTable":"aiops_device_collect_detail","upgradeParams":{"execParamsContent":"686e079fd345fc964fd2ef5ca9baef0a64cc34fed5c097c8877cae135cbd85030917ed8a921b7380087d30f2f090bd547267ca4531933284dc564b0fb2ff648f63fcefbcd00eaf844e782e8698acd8c2445fa9c579b8e83db28ea7efbecab8f4e847164b8200bff6d888efda490d0e46044ccad7e92a88e66943b682f0d45403497a6b8b44d4500bccb5cb4b7d3156a2aaad1d8503b1d2e9206ccb575b140dbf043bfb7440c826f09278df1ba689b23f7bab2151bcf2c471a0ab2f00f4c71a776f0d28a44d0a232d284c564168a1aab88285f70a8eafa9f64f619a38a7c848eef5ac4dd583789997068270fa1ed5f8fff8d2a34abd83862392ac3df803dd983c8a6ed3950c1d27254544c9851567995d","collectName":"E10附件数据采集","accId":779544910123584,"adimId":731339652330048,"id":779831587738176,"adcId":731339202548288,"execParamsVersion":"962c25c7f81a3a141eb448de3d0578bc","aiId":731339652317762,"isEnable":1},"paramsMap":{"deviceId":"515635214182003779","eid":41322677633600,"aiId":731339652317762},"targetColumnSet":[],"errorMsg":"upgrade adcd: 779831587738176 acc incomplete","batchId":779831588872768,"batchIndex":1}],"errMsg":"upgrade fail"}   ---   515635214182003779
2024-10-12T13:42:50.924+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323126878784 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=f441b1d4f568b58bfc3633939863b600&deviceId=385759172102205488
2024-10-12T13:42:50.924+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"aafd8d5b343b727cdcc161e69b249640\\\",\\\"dbIdValue\\\":\\\"aafd8d5b343b727cdcc161e69b249640\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   385759172102205488
2024-10-12T13:42:52.322+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41324550558272 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=aec8fdd0e2da272f62bfa9e2d4ef8d28&deviceId=518684139289854256
2024-10-12T13:42:52.322+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"a60efdbe89396d13734d0de7057d4002\\\",\\\"dbIdValue\\\":\\\"a60efdbe89396d13734d0de7057d4002\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YDATA2\\\",\\\"targetValue\\\":\\\"YDATA2\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518684139289854256
2024-10-12T13:42:53.733+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:233428520694336 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=782d17ba5c953e7babfd96da7fd2f269&deviceId=510734771370869556
2024-10-12T13:42:53.733+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"7b34b8b5b3ab2b1c05f2a753e9678f40\\\",\\\"dbIdValue\\\":\\\"7b34b8b5b3ab2b1c05f2a753e9678f40\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YOFCZS\\\",\\\"targetValue\\\":\\\"YOFCZS\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   510734771370869556
2024-10-12T13:42:55.086+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:209553994879552 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=bd5a66fb49008e9a0d48dba0098d7e6d&deviceId=517957728786723649
2024-10-12T13:42:55.087+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"bf264a7f5b7f76396a78d0ddf9e5dd4c\\\",\\\"dbIdValue\\\":\\\"bf264a7f5b7f76396a78d0ddf9e5dd4c\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"E10_C_6.0\\\",\\\"targetValue\\\":\\\"E10_C_6.0\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   517957728786723649
2024-10-12T13:42:56.488+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323227099712 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=b8080817070d09c1ddaac107212832eb&deviceId=518110321525666883
2024-10-12T13:42:56.488+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"085b14c16262996a2671cb5d1c5218f8\\\",\\\"dbIdValue\\\":\\\"085b14c16262996a2671cb5d1c5218f8\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"YWdata\\\",\\\"targetValue\\\":\\\"YWdata\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518110321525666883
2024-10-12T13:42:57.812+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:123107013939776 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=341c407c397c9fc7f39e72d80aaae96d&deviceId=518109176698778681
2024-10-12T13:42:57.813+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"e9fd8aafb26a68c745447fc46d2de19f\\\",\\\"dbIdValue\\\":\\\"e9fd8aafb26a68c745447fc46d2de19f\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"master\\\",\\\"targetValue\\\":\\\"master\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518109176698778681
2024-10-12T13:42:59.183+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : eid:41323427910208 ,url:https://aiops.digiwincloud.com.cn/aiogateway/aioitms/v2/device/collect/detail/by/mcl/list?aiopsItemId=6534b69af38d11da289914f0bfbcb417&deviceId=518108221185995843
2024-10-12T13:42:59.184+08:00  INFO 30240 --- [nio-8081-exec-1] c.example.mybatis.config.MybatisService  : data:[{"moduleCollectMappingList":[{"accId":779544910123584,"collectName":"E10附件数据采集","execParamsContent":"{\\\"ExecParams\\\":{\\\"usedToChooseDb\\\":{\\\"targetValue\\\":\\\"0c8d482c2bb5b63fc4003868f9d6e9e7\\\",\\\"dbIdValue\\\":\\\"0c8d482c2bb5b63fc4003868f9d6e9e7\\\",\\\"targetKey\\\":\\\"database.db_id\\\",\\\"usedType\\\":\\\"choose\\\"},\\\"usedToAttachDbName\\\":{\\\"dbNameValue\\\":\\\"MXGY001\\\",\\\"targetValue\\\":\\\"MXGY001\\\",\\\"targetKey\\\":\\\"dbName\\\",\\\"usedType\\\":\\\"attach\\\"}}}","execParamsModelCode":"DbExtExecParams","isEnable":true}]}]
{"code":"0","data":1,"errMsg":"success"}   ---   518108221185995843
deviceIds:,'525657258575545410','525650150371112003','435673153289598264','525202584194007860','525191454323131188','525085880419890243','525072049702450230','525074098368296003','525063473156080953','469821947560079942','524917234099831860','524903997631378499','524903239552872770','524899183577019461','524784995009575222','524784636983784505','524781911256609859','382149395132465985','524751873698182211','524637153511093297','524632623092347203','524629296069750837','524606644512896067','524198674411045189','524165121757430073','524022600918123586','523915623869202756','523890661250839619','523884194540045360','523876061650367812','523035276050707522','523768958201902147','523739243168674883','523743316005631043','523741153305048388','523631834777530932','523188584287581251','522019264845067060','523050262852547652','523005549709635632','522886063316812868','522863360656556852','516336381602055746','485326966128391732','522176337251681076','522147187258308165','522025839198483767','522021323996672821','522019467228627270','487500913200608323','521893155830772803','521885128687890743','521874460375008817','521859651898197062','521855337502880835','521750240039024708','521736449855337539','472705999929624134','521699045991789636','521585082255815750','521569558146856003','521564985164313667','521557177568080963','521169892884363316','521159347984675908','521154424559645763','521144261324849475','420115673574814787','521129101466022706','521121625555354675','521120433936811587','521015303019181123','521014424362824770','521012132544786996','515934592629224515','520975815425406019','520858811490120771','520845632735295285','520842491017048626','520730041710030896','520713744557294900','520715854157984569','517822763801523267','421130308797088580','519862447327162435','519828583875625528','519567727543726898','519562497682847288','519528289123971394','516781739109008705','518960316860609603','518804984385516611','515635214182003779','385759172102205488','518684139289854256','510734771370869556','517957728786723649','518110321525666883','518109176698778681','518108221185995843'