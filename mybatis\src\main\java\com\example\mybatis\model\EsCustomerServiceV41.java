package com.example.mybatis.model;

import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import java.util.Date;

@Data
public class EsCustomerServiceV41 {
    private Long eId;
    private Date updateDate;
    private String SalesContact;
    private String CustomerCode;
    private String grp_dpt_id;
    private String grp_dpt_name;
    private String grp_dpt_manager;
    private String bus_dpt_id;
    private String bus_dpt_name;
    private String bus_dpt_manager;
    private String dpt_id;
    private String dpt_name;
    private String dpt_manager;
    private String Sales;
    private String CustomerServiceCode;
    private String CustomerName;
    private String CustomerFullNameCH;
    private String CustomerFullNameEN;
    private String another_name;
    private String current_valid_status;
    private String t100_cust_id;
    private String taxNo;
    private String contacts;
    private String tenantTelephone;
    private String tenantEmail;
    private String address;
    private String __version__;
    private String grp_dpt_manager_contact;
    private String bus_dpt_manager_contact;
    private String bus_dpt_win_name;
    private String bus_dpt_win_contact;
    private String dpt_manager_contact;
    private String valueAddedConsultant;
    private String valueAddedConsultantEmail;
    private String IndustryCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        EsCustomerServiceV41 that = (EsCustomerServiceV41) o;

        return new EqualsBuilder().append(eId, that.eId).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(eId).toHashCode();
    }
}
