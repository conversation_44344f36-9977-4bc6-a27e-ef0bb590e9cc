{"job": {"content": [{"writer": {"parameter": {"partitionEnabled": false, "writeMode": "truncate", "fieldDelimiter": "\t", "column": [{"type": "STRING", "name": "eid"}, {"type": "STRING", "name": "type"}, {"type": "DOUBLE", "name": "score"}, {"type": "DOUBLE", "name": "totalScore"}], "path": "/user/hive/warehouse/tbb.db/aiops_ability_tag_score_v2", "fileType": "text", "defaultFS": "hdfs://ddp1:8020", "fileName": "aiops_ability_tag_score_v2"}, "name": "hdfswriter"}, "reader": {"parameter": {"username": "servicecloud", "password": "servicecloud@123", "connection": [{"querySql": ["SELECT id AS eid, CASE WHEN tagId = '754698248249920' THEN '基础应用能力' WHEN tagId = '754699437040192' THEN '订单交付管理能力' WHEN tagId = '758200593494592' THEN '生产制造管理能力' WHEN tagId = '758211379151424' THEN '物料准备管理能力' WHEN tagId = '758236385014336' THEN '月结核算管理能力' WHEN tagId = '758245165445696' THEN '存货周转管理能力' end as type, score, 100 as totalScore FROM AIEOM.tenant_tag_score WHERE tagId IN ( '754698248249920', '754699437040192', '758200593494592', '758211379151424', '758236385014336', '758245165445696' )"], "jdbcUrl": ["*************************************************************************************************"]}], "splitPk": ""}, "name": "mysqlreader"}}], "setting": {"speed": {"channel": 3}, "errorLimit": {"record": 0, "percentage": 0.02}}}}