package com.example.demo;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class Main {
    static String aa = "IncreaseInMaterials\n" +
            "ECNReplaceNumber\n" +
            "InventoryTurnoverProduct\n" +
            "RateOfPreparationLeadTime\n" +
            "Financialreplenishmentrate\n" +
            "ProcurementCycle\n" +
            "DeliveryRateForProcurement\n" +
            "CostCompositionRatio\n" +
            "ProductInventoryTurnover\n" +
            "ProductProductionCycle\n" +
            "InventoryTurnover\n" +
            "DocumentDeductionOnTime\n" +
            "DocumentDeductionTimely\n" +
            "OrderIncrease\n" +
            "OrderLeadTime\n" +
            "OrdertoWork\n" +
            "DeliveryRateOfOrders\n" +
            "ModuleMonthlyDays\n" +
            "TimelyCompletionOfWork\n" +
            "StartWorkRate\n" +
            "CompleteOnTimeRate\n" +
            "InvoicingVolume\n" +
            "ProcurementSourcesRate\n" +
            "MaterialIssuanceCycle\n" +
            "ProductionSourceRate\n" +
            "LeadTimesRate\n" +
            "ProductionCycle\n" +
            "DaysPayableOutstanding\n" +
            "ReceivableTurnoverRate\n" +
            "MISCAccountSubjects\n" +
            "MISCReasonCode\n" +
            "DocumentReplenishmentRate\n" +
            "DocumentQuantity\n" +
            "SemiFinishProductProductionCycle\n" +
            "MissingworkRate\n" +
            "MissingrateOfWorkHour\n" +
            "MonthlyIncreaseInPurchase\n" +
            "DeviationRatePurchasePrice\n" +
            "PurchaseZeroUnitPriceRatio\n" +
            "PurchaseQualityDefectRate\n" +
            "ProcurementTypeDeliveryRate\n" +
            "NumberOfOrderChanges\n" +
            "OrderChangeCoverageRate\n" +
            "DeviationRateBetweenOrderPrice\n" +
            "PaymentVerificationCycle\n" +
            "PaymentApprovalCycle\n" +
            "WorkOrderReportRateSingle\n" +
            "WorkOrderReportRateBatch\n" +
            "StartRateOfWork\n" +
            "CompleteRateOfWork\n" +
            "RateBetweenInvoiceAndPurchase\n" +
            "RateBetweenInvoiceOrder\n" +
            "NumOfBankPay\n" +
            "NumberOfBillsPay\n" +
            "TransactionOfBillsPay\n" +
            "ProductionYield\n" +
            "CollectVerificationRate\n" +
            "CollectVerificationCycle\n" +
            "BankReceiptsNum\n" +
            "BankIncomeRate\n" +
            "BankAccounts\n" +
            "BankExpenseRate\n" +
            "PayableReconciliationRate\n" +
            "AccountsPayableRate\n" +
            "AccountsPayCloseCycle\n" +
            "AccountsReceiveRate\n" +
            "BillsReceiveNum\n" +
            "TransactionsOfNotesReceive\n" +
            "AccountsReceivableRate\n" +
            "OverdueAccountsPayable\n" +
            "OverdueAccountsReceiveRate\n" +
            "AdvancePaymentPeriod\n" +
            "MaterialInventoryTurnoverDay\n" +
            "BOMRateOfaudit\n" +
            "BOMAuditcycle\n" +
            "ECNRateOfaudit\n" +
            "ECNAuditcycle\n";

    /**
     * INVALIDATE METADATA tbb.dws_aiops_pec_erpindex_smesavgontimerate
     * @param args
     */
    public static void main(String[] args) {
//        parseName();
        String fileName = "D:\\3.txt";
        Map<String, Map<String, Object>> ret = new LinkedHashMap<>();
        Map<String, Map<String, Object>> ret2 = new LinkedHashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(fileName))) {
            String line;

            while ((line = reader.readLine()) != null) {
                parseJson(line, ret,ret2);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }



//        System.out.println(createTable(ret));
        System.out.println(syncData(ret2));
    }

    private static StringBuilder createTable(Map<String, Map<String, Object>> ret){
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Map<String, Object>> entry : ret.entrySet()) {

            sb.append("beeline -d \"com.cloudera.impala.jdbc41.Driver\" -u \"************************\" -n service_cloud -e \"");
            sb.append("CREATE TABLE IF NOT EXISTS ");
            String tableName = "tbb.dws_aiops_pec_erpindex_" + entry.getKey();
            tableName = tableName.toLowerCase();
//            System.out.println(tableName);
            sb.append(tableName);
            sb.append("(\n" +
                    "  key STRING,");
            int i = 0;
            for (Map.Entry<String, Object> e : entry.getValue().entrySet()) {
                sb.append(e.getKey()).append(" ").append("string");
                if (i != entry.getValue().entrySet().size() - 1) {
                    sb.append(",");
                }
                i++;
            }
            sb.append(")");
            sb.append("STORED AS PARQUET;");
            sb.append("\"");
            sb.append("\n");
        }
        return sb;
    }



    private static StringBuilder syncData(Map<String, Map<String, Object>> ret){
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Map<String, Object>> entry : ret.entrySet()) {

            sb.append("beeline -d \"com.cloudera.impala.jdbc41.Driver\" -u \"************************\" -n service_cloud -e \"");
            sb.append("INSERT overwrite  table ");
            String tableName = "tbb.dws_aiops_pec_erpindex_" + entry.getKey();
            tableName = tableName.toLowerCase();
//            System.out.println(tableName);
            sb.append(tableName);
            sb.append(" SELECT key,");
            int i = 0;
            for (Map.Entry<String, Object> e : entry.getValue().entrySet()) {
                sb.append("get_json_object(model,'$.").append(e.getKey()).append("')");
                if (i != entry.getValue().entrySet().size() - 1) {
                    sb.append(",");
                }
                i++;
            }
            sb.append(" from ");
            sb.append(" ");
            sb.append(entry.getKey());
            sb.append("\"");
            sb.append("\n");
        }
        return sb;
    }


    private static void parseName(){
        String[] split = aa.split("\n");
        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            sb.append("select '{\"").append(s).append("\":'").append(" as name,").append("model,").append("'}' as last");
            sb.append(" from ").append(s).append(" limit 1 ;").append("\n");
        }
        System.out.println(sb.toString());
    }

    // Helper method to parse JSON data
    // {"DataContent":{"source_db_id":"d52535af9369779e50054b0f3e8966ce","Product_Line":"T100","IndicatorNumber":"B2.34.0","enterpriseCode":99,"Account_Set":"02","Account_set_name":"臺灣帳套","Year":"2023","Month":"06","Procurement_cycle":0,"Total_number_of_purchases":1,"Total_procurement_time":0},"BasicInfo":{"sort_minus_1_field":null,"deviceId":"442418894422945844","eid":"********","collectedTime":"2023-10-30 17:17:04","collectConfigId":"***************","uploadDataModelCode":"ProcurementCycle","deviceCollectDetailId":"***************","aiId":"***************","aiopsItem":"100","flumeTimestamp":"*************"}}
    private static void parseJson(String jsonData, Map<String, Map<String, Object>> ret,Map<String, Map<String, Object>> ret2) {


        // Parse JSON

        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(jsonData, JsonObject.class);
        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            Map<String, Object> map = new LinkedHashMap<>();
            Map<String, Object> map2 = new LinkedHashMap<>();
            JsonObject asJsonObject = entry.getValue().getAsJsonObject();
            JsonObject dataContent = asJsonObject.getAsJsonObject("DataContent");
            JsonObject basicInfo = asJsonObject.getAsJsonObject("BasicInfo");
            convertToMap(dataContent, map,map2,"DataContent");
            convertToMap(basicInfo, map,map2,"BasicInfo");
            ret.put(entry.getKey(), map);
            ret2.put(entry.getKey(), map2);


        }


    }

    private static void convertToMap(JsonObject jsonObject, Map<String, Object> map,Map<String, Object> map2,String type) {

        for (Map.Entry<String, JsonElement> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            map.put(key, value);
            map2.put(type + "." + key, value);
        }


    }



}
