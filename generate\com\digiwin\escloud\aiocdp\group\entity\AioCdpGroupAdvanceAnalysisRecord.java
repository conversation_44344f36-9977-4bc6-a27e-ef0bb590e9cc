package com.digiwin.escloud.aiocdp.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 * 进阶分析记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-14
 */
@TableName("aio_cdp_group_advance_analysis_record")
@ApiModel(value = "AioCdpGroupAdvanceAnalysisRecord对象", description = "进阶分析记录")
public class AioCdpGroupAdvanceAnalysisRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("进阶分析规则id")
    private Long gaarId;

    @ApiModelProperty("群画像id")
    private Long gpiId;

    private Long parentId;

    private String profilesNum;

    private String profilesName;

    private String createUser;

    private String createUserId;

    private Integer level;

    private LocalDateTime createDate;

    private LocalDateTime updateDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGaarId() {
        return gaarId;
    }

    public void setGaarId(Long gaarId) {
        this.gaarId = gaarId;
    }

    public Long getGpiId() {
        return gpiId;
    }

    public void setGpiId(Long gpiId) {
        this.gpiId = gpiId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getProfilesNum() {
        return profilesNum;
    }

    public void setProfilesNum(String profilesNum) {
        this.profilesNum = profilesNum;
    }

    public String getProfilesName() {
        return profilesName;
    }

    public void setProfilesName(String profilesName) {
        this.profilesName = profilesName;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(LocalDateTime updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        return "AioCdpGroupAdvanceAnalysisRecord{" +
            "id = " + id +
            ", gaarId = " + gaarId +
            ", gpiId = " + gpiId +
            ", parentId = " + parentId +
            ", profilesNum = " + profilesNum +
            ", profilesName = " + profilesName +
            ", createUser = " + createUser +
            ", createUserId = " + createUserId +
            ", level = " + level +
            ", createDate = " + createDate +
            ", updateDate = " + updateDate +
        "}";
    }
}
