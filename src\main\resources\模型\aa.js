// #script
// #let
// result = {};
// const DataContentData = [ * DataContent *
// ]
// ;result.value = 0;
// for (let key in DataContentData) {
//     if (key === 'fs_COOSurvey') {
//         for (let fieldSetItem in DataContentData[key]) {
//             if (fieldSetItem !== 'f_COOCompletedCount' && fieldSetItem !== 'f_COOTotalQuantity') {
//                 result.value++
//             }
//         }
//     }
// }
// result.style = {'display': 'none'};
// return result;