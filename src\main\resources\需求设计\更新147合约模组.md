# 更新147合约模组.sql
先备份数据

```sql
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656491803200,241199971893824,41317456650816,2001,241199971893824,3,1,'2024-12-02','2029-12-01',0,0,0,184293222941248,0,'韩磊');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656491815488,816656491803200,105000000002001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492257856,241199971893824,41317535846976,2001,241199971893824,3,1,'2024-11-21','2025-11-21',0,0,0,184293222941248,0,'韩磊');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492257857,816656492257856,105000000002001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492311104,241199971893824,882396229658624,1,241199971893824,3,1,'2024-07-15','2025-07-14',0,0,0,184293222941248,0,'韩磊');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492311105,816656492311104,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492368448,241199971893824,882396229658624,2001,241199971893824,3,1,'2024-07-15','2025-07-14',0,0,0,184293222941248,0,'韩磊');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492368449,816656492368448,105000000002001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492642880,241199971893824,204819591709248,1,241199971893824,3,1,'2024-03-01','2025-04-30',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492642881,816656492642880,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492700224,241199971893824,204819591709248,3001,241199971893824,3,1,'2024-03-01','2025-04-30',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492700225,816656492700224,105000000003001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656492999232,241199971893824,41317915910720,3001,241199971893824,3,1,'2024-10-18','2025-10-17',0,0,0,325106685227584,0,'石位剑');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656492999233,816656492999232,105000000003001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493224512,241199971893824,50606461723200,1,241199971893824,3,1,'2024-11-01','2025-10-31',0,0,0,325106685227584,0,'石位剑');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493224513,816656493224512,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493277760,241199971893824,70681612517952,1,241199971893824,3,1,'2024-12-01','2025-11-30',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493277761,816656493277760,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493335104,241199971893824,70681612517952,3001,241199971893824,3,1,'2024-12-01','2025-11-30',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493335105,816656493335104,105000000003001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493396544,241199971893824,219345114653248,3001,241199971893824,3,1,'2024-08-13','2026-08-12',0,0,0,325106685227584,0,'石位剑');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493396545,816656493396544,105000000003001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493605440,241199971893824,454779142607424,1,241199971893824,3,1,'2024-05-30','2025-05-29',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493605441,816656493605440,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493662784,241199971893824,454779142607424,3001,241199971893824,3,1,'2024-05-30','2025-05-29',0,0,0,70950584586816,0,'张智毅');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493662785,816656493662784,105000000003001,0,1);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493728320,241199971893824,41320939405888,1,241199971893824,3,1,'2024-05-31','2025-05-30',0,0,0,325106685227584,0,'石位剑');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493728321,816656493728320,105000000000101,0,3);
insert into tenant_module_contract (id, sid, eid, moduleId, serviceIsvSid, status, orderMethod, startDate, endDate, serverCount, workStationCount, iotCount, staffId, openId, userName)
values (816656493785664,241199971893824,41320939405888,3001,241199971893824,3,1,'2024-05-31','2025-05-30',0,0,0,325106685227584,0,'石位剑');
insert into tenant_module_contract_detail (id, tmcId, samcId, availableCount, usedCount)
values (816656493785665,816656493785664,105000000003001,0,1);


--
update tenant_module_contract set startDate = '2023-07-05' and endDate = '2024-07-04' and status = 4 where moduleId = 1 and  eid = 59755198186048;
update tenant_module_contract set startDate = '2023-07-05' and endDate = '2024-07-04' and status = 4 where moduleId = 2001 and  eid = 59755198186048;
update tenant_module_contract set startDate = '2022-07-11' and endDate = '2023-07-10' and status = 4 where moduleId = 1 and  eid = 41319378412096;
update tenant_module_contract set startDate = '2022-07-11' and endDate = '2023-07-10' and status = 4 where moduleId = 2001 and  eid = 41319378412096;
update tenant_module_contract set startDate = '2022-06-28' and endDate = '2023-06-27' and status = 4 where moduleId = 1 and  eid = 41317469078080;
update tenant_module_contract set startDate = '2022-06-28' and endDate = '2023-06-27' and status = 4 where moduleId = 2001 and  eid = 41317469078080;
update tenant_module_contract set startDate = '2024-11-29' and endDate = '2025-11-29' and status = 3 where moduleId = 1 and  eid = 41323418952256;
update tenant_module_contract set startDate = '2024-11-29' and endDate = '2025-11-29' and status = 3 where moduleId = 2001 and  eid = 41323418952256;
update tenant_module_contract set startDate = '2023-03-27' and endDate = '2024-03-26' and status = 4 where moduleId = 1 and  eid = 41323764019776;
update tenant_module_contract set startDate = '2023-03-27' and endDate = '2024-03-26' and status = 4 where moduleId = 2001 and  eid = 41323764019776;
update tenant_module_contract set startDate = '2022-04-01' and endDate = '2023-03-31' and status = 4 where moduleId = 1 and  eid = 41320752194112;
update tenant_module_contract set startDate = '2022-04-01' and endDate = '2023-03-31' and status = 4 where moduleId = 2001 and  eid = 41320752194112;
update tenant_module_contract set startDate = '2023-08-15' and endDate = '2024-08-14' and status = 4 where moduleId = 1 and  eid = 41323765645888;
update tenant_module_contract set startDate = '2023-08-15' and endDate = '2024-08-14' and status = 4 where moduleId = 2001 and  eid = 41323765645888;
update tenant_module_contract set startDate = '2022-05-15' and endDate = '2023-05-14' and status = 4 where moduleId = 1 and  eid = 41321359086144;
update tenant_module_contract set startDate = '2022-05-15' and endDate = '2023-05-14' and status = 4 where moduleId = 2001 and  eid = 41321359086144;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 1 and  eid = 41318785737280;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 2001 and  eid = 41318785737280;
update tenant_module_contract set startDate = '2022-06-08' and endDate = '2023-06-07' and status = 4 where moduleId = 1 and  eid = 41318789538368;
update tenant_module_contract set startDate = '2022-06-08' and endDate = '2023-06-07' and status = 4 where moduleId = 2001 and  eid = 41318789538368;
update tenant_module_contract set startDate = '2022-08-01' and endDate = '2023-07-31' and status = 4 where moduleId = 1 and  eid = 41324668248640;
update tenant_module_contract set startDate = '2022-08-01' and endDate = '2023-07-31' and status = 4 where moduleId = 2001 and  eid = 41324668248640;
update tenant_module_contract set startDate = '2024-10-08' and endDate = '2025-10-07' and status = 3 where moduleId = 1 and  eid = 41323788218944;
update tenant_module_contract set startDate = '2024-10-08' and endDate = '2025-10-07' and status = 3 where moduleId = 2001 and  eid = 41323788218944;
update tenant_module_contract set startDate = '2023-05-20' and endDate = '2024-05-19' and status = 4 where moduleId = 1 and  eid = 72097211044416;
update tenant_module_contract set startDate = '2023-05-20' and endDate = '2024-05-19' and status = 4 where moduleId = 2001 and  eid = 72097211044416;
update tenant_module_contract set startDate = '2022-10-12' and endDate = '2023-10-11' and status = 4 where moduleId = 1 and  eid = 41320186651200;
update tenant_module_contract set startDate = '2022-10-12' and endDate = '2023-10-11' and status = 4 where moduleId = 2001 and  eid = 41320186651200;
update tenant_module_contract set startDate = '2023-09-25' and endDate = '2024-09-24' and status = 4 where moduleId = 1 and  eid = 41323319730752;
update tenant_module_contract set startDate = '2023-09-25' and endDate = '2024-09-24' and status = 4 where moduleId = 2001 and  eid = 41323319730752;
update tenant_module_contract set startDate = '2023-12-07' and endDate = '2024-12-06' and status = 4 where moduleId = 1 and  eid = 41320360084032;
update tenant_module_contract set startDate = '2023-12-07' and endDate = '2024-12-06' and status = 4 where moduleId = 2001 and  eid = 41320360084032;
update tenant_module_contract set startDate = '2022-06-27' and endDate = '2023-06-26' and status = 4 where moduleId = 1 and  eid = 41320057586240;
update tenant_module_contract set startDate = '2022-06-27' and endDate = '2023-06-26' and status = 4 where moduleId = 2001 and  eid = 41320057586240;
update tenant_module_contract set startDate = '2022-01-14' and endDate = '2023-01-13' and status = 4 where moduleId = 1 and  eid = 41322982666816;
update tenant_module_contract set startDate = '2022-01-14' and endDate = '2023-01-13' and status = 4 where moduleId = 2001 and  eid = 41322982666816;
update tenant_module_contract set startDate = '2024-11-19' and endDate = '2025-11-18' and status = 3 where moduleId = 1 and  eid = 41318030893632;
update tenant_module_contract set startDate = '2024-11-19' and endDate = '2025-11-18' and status = 3 where moduleId = 2001 and  eid = 41318030893632;
update tenant_module_contract set startDate = '2022-06-01' and endDate = '2023-05-31' and status = 4 where moduleId = 1 and  eid = 41320913171008;
update tenant_module_contract set startDate = '2022-06-01' and endDate = '2023-05-31' and status = 4 where moduleId = 2001 and  eid = 41320913171008;
update tenant_module_contract set startDate = '2024-01-04' and endDate = '2025-01-03' and status = 3 where moduleId = 1 and  eid = 233427568456256;
update tenant_module_contract set startDate = '2024-01-04' and endDate = '2025-01-03' and status = 3 where moduleId = 2001 and  eid = 233427568456256;
update tenant_module_contract set startDate = '2024-12-01' and endDate = '2025-11-30' and status = 3 where moduleId = 1 and  eid = 41324466401856;
update tenant_module_contract set startDate = '2024-12-01' and endDate = '2025-11-30' and status = 3 where moduleId = 2001 and  eid = 41324466401856;
update tenant_module_contract set startDate = '2023-04-25' and endDate = '2024-04-24' and status = 4 where moduleId = 1 and  eid = 41320709861952;
update tenant_module_contract set startDate = '2023-04-25' and endDate = '2024-04-24' and status = 4 where moduleId = 2001 and  eid = 41320709861952;
update tenant_module_contract set startDate = '2023-01-06' and endDate = '2024-01-05' and status = 4 where moduleId = 1 and  eid = 41321156989504;
update tenant_module_contract set startDate = '2023-01-06' and endDate = '2024-01-05' and status = 4 where moduleId = 2001 and  eid = 41321156989504;
update tenant_module_contract set startDate = '2023-10-10' and endDate = '2024-10-09' and status = 4 where moduleId = 1 and  eid = 41323158536768;
update tenant_module_contract set startDate = '2023-10-10' and endDate = '2024-10-09' and status = 4 where moduleId = 2001 and  eid = 41323158536768;
update tenant_module_contract set startDate = '2022-01-01' and endDate = '2022-12-31' and status = 4 where moduleId = 1 and  eid = 233430182216256;
update tenant_module_contract set startDate = '2022-01-01' and endDate = '2022-12-31' and status = 4 where moduleId = 2001 and  eid = 233430182216256;
update tenant_module_contract set startDate = '2022-08-11' and endDate = '2023-08-10' and status = 4 where moduleId = 1 and  eid = 41320588763712;
update tenant_module_contract set startDate = '2022-08-11' and endDate = '2023-08-10' and status = 4 where moduleId = 2001 and  eid = 41320588763712;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 1 and  eid = 41320605258304;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 2001 and  eid = 41320605258304;
update tenant_module_contract set startDate = '2023-05-22' and endDate = '2024-05-21' and status = 4 where moduleId = 1 and  eid = 41321181426240;
update tenant_module_contract set startDate = '2023-05-22' and endDate = '2024-05-21' and status = 4 where moduleId = 2001 and  eid = 41321181426240;
update tenant_module_contract set startDate = '2022-11-16' and endDate = '2023-11-15' and status = 4 where moduleId = 1 and  eid = 41317282685504;
update tenant_module_contract set startDate = '2022-11-16' and endDate = '2023-11-15' and status = 4 where moduleId = 2001 and  eid = 41317282685504;
update tenant_module_contract set startDate = '2022-06-25' and endDate = '2023-06-24' and status = 4 where moduleId = 1 and  eid = 129757220815424;
update tenant_module_contract set startDate = '2022-06-25' and endDate = '2023-06-24' and status = 4 where moduleId = 2001 and  eid = 129757220815424;
update tenant_module_contract set startDate = '2024-12-03' and endDate = '2025-12-02' and status = 3 where moduleId = 1 and  eid = 365005571555904;
update tenant_module_contract set startDate = '2024-12-03' and endDate = '2025-12-02' and status = 3 where moduleId = 2001 and  eid = 365005571555904;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 1 and  eid = 50605811593792;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 3001 and  eid = 50605811593792;
update tenant_module_contract set startDate = '2024-11-01' and endDate = '2025-10-31' and status = 3 where moduleId = 1 and  eid = 50606676353600;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 1 and  eid = 41319988851264;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 2001 and  eid = 41319988851264;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 1 and  eid = 50606662427200;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 3001 and  eid = 50606662427200;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-30' and status = 3 where moduleId = 1 and  eid = 437692042146368;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-30' and status = 3 where moduleId = 2001 and  eid = 437692042146368;
update tenant_module_contract set startDate = '2022-09-30' and endDate = '2023-09-29' and status = 4 where moduleId = 1 and  eid = 41321213911616;
update tenant_module_contract set startDate = '2022-09-30' and endDate = '2023-09-29' and status = 4 where moduleId = 3001 and  eid = 41321213911616;
update tenant_module_contract set startDate = '2024-04-08' and endDate = '2025-04-07' and status = 3 where moduleId = 1 and  eid = 41320313942592;
update tenant_module_contract set startDate = '2024-04-08' and endDate = '2025-04-07' and status = 3 where moduleId = 2001 and  eid = 41320313942592;
update tenant_module_contract set startDate = '2023-07-12' and endDate = '2024-07-11' and status = 4 where moduleId = 1 and  eid = 41322201973312;
update tenant_module_contract set startDate = '2023-07-12' and endDate = '2024-07-11' and status = 4 where moduleId = 2001 and  eid = 41322201973312;
update tenant_module_contract set startDate = '2022-06-30' and endDate = '2023-06-29' and status = 4 where moduleId = 1 and  eid = 334719463490112;
update tenant_module_contract set startDate = '2022-06-30' and endDate = '2023-06-29' and status = 4 where moduleId = 2001 and  eid = 334719463490112;
update tenant_module_contract set startDate = '2022-11-30' and endDate = '2023-11-29' and status = 4 where moduleId = 1 and  eid = 41319473844800;
update tenant_module_contract set startDate = '2022-11-30' and endDate = '2023-11-29' and status = 4 where moduleId = 2001 and  eid = 41319473844800;
update tenant_module_contract set startDate = '2024-11-20' and endDate = '2026-05-19' and status = 3 where moduleId = 1 and  eid = 327861234983488;
update tenant_module_contract set startDate = '2024-11-20' and endDate = '2026-05-19' and status = 3 where moduleId = 2001 and  eid = 327861234983488;
update tenant_module_contract set startDate = '2023-11-15' and endDate = '2024-11-14' and status = 4 where moduleId = 1 and  eid = 286445314777664;
update tenant_module_contract set startDate = '2023-11-15' and endDate = '2024-11-14' and status = 4 where moduleId = 2001 and  eid = 286445314777664;
update tenant_module_contract set startDate = '2023-02-02' and endDate = '2024-02-01' and status = 4 where moduleId = 1 and  eid = 284042122666560;
update tenant_module_contract set startDate = '2023-02-02' and endDate = '2024-02-01' and status = 4 where moduleId = 2001 and  eid = 284042122666560;
update tenant_module_contract set startDate = '2024-11-21' and endDate = '2025-11-21' and status = 3 where moduleId = 1 and  eid = 41317535846976;
update tenant_module_contract set startDate = '2024-05-20' and endDate = '2025-05-20' and status = 3 where moduleId = 1 and  eid = 41319487783488;
update tenant_module_contract set startDate = '2024-05-20' and endDate = '2025-05-20' and status = 3 where moduleId = 3001 and  eid = 41319487783488;
update tenant_module_contract set startDate = '2024-11-25' and endDate = '2025-11-24' and status = 3 where moduleId = 1 and  eid = 390620716069440;
update tenant_module_contract set startDate = '2024-11-25' and endDate = '2025-11-24' and status = 3 where moduleId = 2001 and  eid = 390620716069440;
update tenant_module_contract set startDate = '2024-12-26' and endDate = '2025-12-25' and status = 3 where moduleId = 1 and  eid = 41324601721408;
update tenant_module_contract set startDate = '2024-12-26' and endDate = '2025-12-25' and status = 3 where moduleId = 3001 and  eid = 41324601721408;
update tenant_module_contract set startDate = '2023-07-15' and endDate = '2024-07-14' and status = 4 where moduleId = 1 and  eid = 41322902323776;
update tenant_module_contract set startDate = '2023-07-15' and endDate = '2024-07-14' and status = 4 where moduleId = 2001 and  eid = 41322902323776;
update tenant_module_contract set startDate = '2023-11-07' and endDate = '2024-11-06' and status = 4 where moduleId = 1 and  eid = 41322629554752;
update tenant_module_contract set startDate = '2023-11-07' and endDate = '2024-11-06' and status = 4 where moduleId = 2001 and  eid = 41322629554752;
update tenant_module_contract set startDate = '2024-10-01' and endDate = '2025-09-30' and status = 3 where moduleId = 1 and  eid = 41324555493952;
update tenant_module_contract set startDate = '2024-10-01' and endDate = '2025-09-30' and status = 3 where moduleId = 3001 and  eid = 41324555493952;
update tenant_module_contract set startDate = '2023-07-01' and endDate = '2024-06-30' and status = 4 where moduleId = 1 and  eid = 41319607173696;
update tenant_module_contract set startDate = '2023-07-01' and endDate = '2024-06-30' and status = 4 where moduleId = 2001 and  eid = 41319607173696;
update tenant_module_contract set startDate = '2023-09-01' and endDate = '2026-12-31' and status = 3 where moduleId = 3001 and  eid = 204825389392448;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 1 and  eid = 114618385830464;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 3001 and  eid = 114618385830464;
update tenant_module_contract set startDate = '2024-04-01' and endDate = '2025-03-31' and status = 3 where moduleId = 1 and  eid = 121288000463424;
update tenant_module_contract set startDate = '2024-04-01' and endDate = '2025-03-31' and status = 3 where moduleId = 3001 and  eid = 121288000463424;
update tenant_module_contract set startDate = '2022-08-31' and endDate = '2023-08-30' and status = 4 where moduleId = 1 and  eid = 41322922033728;
update tenant_module_contract set startDate = '2022-08-31' and endDate = '2023-08-30' and status = 4 where moduleId = 3001 and  eid = 41322922033728;
update tenant_module_contract set startDate = '2022-11-28' and endDate = '2024-11-27' and status = 4 where moduleId = 1 and  eid = 50606321189440;
update tenant_module_contract set startDate = '2022-11-28' and endDate = '2024-11-27' and status = 4 where moduleId = 3001 and  eid = 50606321189440;
update tenant_module_contract set startDate = '2023-07-31' and endDate = '2024-07-30' and status = 4 where moduleId = 1 and  eid = 114859928830528;
update tenant_module_contract set startDate = '2023-07-31' and endDate = '2024-07-30' and status = 4 where moduleId = 3001 and  eid = 114859928830528;
update tenant_module_contract set startDate = '2023-11-01' and endDate = '2024-11-30' and status = 4 where moduleId = 1 and  eid = 50605942358592;
update tenant_module_contract set startDate = '2023-11-01' and endDate = '2024-11-30' and status = 4 where moduleId = 3001 and  eid = 50605942358592;
update tenant_module_contract set startDate = '2024-07-24' and endDate = '2025-07-23' and status = 3 where moduleId = 1 and  eid = 114859271508544;
update tenant_module_contract set startDate = '2024-07-24' and endDate = '2025-07-23' and status = 3 where moduleId = 3001 and  eid = 114859271508544;
update tenant_module_contract set startDate = '2024-12-06' and endDate = '2025-12-05' and status = 3 where moduleId = 1 and  eid = 50606824370752;
update tenant_module_contract set startDate = '2024-12-06' and endDate = '2025-12-05' and status = 3 where moduleId = 3001 and  eid = 50606824370752;
update tenant_module_contract set startDate = '2025-12-31' and endDate = '2026-01-30' and status = 3 where moduleId = 1 and  eid = 50606029910592;
update tenant_module_contract set startDate = '2025-12-31' and endDate = '2026-01-30' and status = 3 where moduleId = 3001 and  eid = 50606029910592;
update tenant_module_contract set startDate = '2022-09-13' and endDate = '2023-09-12' and status = 4 where moduleId = 1 and  eid = 50605870867008;
update tenant_module_contract set startDate = '2022-09-13' and endDate = '2023-09-12' and status = 4 where moduleId = 3001 and  eid = 50605870867008;
update tenant_module_contract set startDate = '2025-01-10' and endDate = '2026-01-09' and status = 3 where moduleId = 1 and  eid = 50605876654656;
update tenant_module_contract set startDate = '2025-01-10' and endDate = '2026-01-09' and status = 3 where moduleId = 3001 and  eid = 50605876654656;
update tenant_module_contract set startDate = '2024-11-05' and endDate = '2025-11-04' and status = 3 where moduleId = 1 and  eid = 50606846165568;
update tenant_module_contract set startDate = '2024-11-05' and endDate = '2025-11-04' and status = 3 where moduleId = 3001 and  eid = 50606846165568;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2023-12-31' and status = 4 where moduleId = 1 and  eid = 192430400258624;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2023-12-31' and status = 4 where moduleId = 3001 and  eid = 192430400258624;
update tenant_module_contract set startDate = '2024-04-07' and endDate = '2025-04-06' and status = 3 where moduleId = 1 and  eid = 50606808642113;
update tenant_module_contract set startDate = '2024-04-07' and endDate = '2025-04-06' and status = 3 where moduleId = 3001 and  eid = 50606808642113;
update tenant_module_contract set startDate = '2023-07-25' and endDate = '2024-10-31' and status = 4 where moduleId = 1 and  eid = 50605896221248;
update tenant_module_contract set startDate = '2023-07-25' and endDate = '2024-10-31' and status = 4 where moduleId = 3001 and  eid = 50605896221248;
update tenant_module_contract set startDate = '2024-10-18' and endDate = '2025-10-17' and status = 3 where moduleId = 1 and  eid = 41317915910720;
update tenant_module_contract set startDate = '2024-02-24' and endDate = '2025-02-23' and status = 3 where moduleId = 1 and  eid = 50605953909312;
update tenant_module_contract set startDate = '2024-12-31' and endDate = '2025-12-30' and status = 3 where moduleId = 1 and  eid = 204826483311168;
update tenant_module_contract set startDate = '2024-12-31' and endDate = '2025-12-30' and status = 3 where moduleId = 3001 and  eid = 204826483311168;
update tenant_module_contract set startDate = '2022-11-28' and endDate = '2023-05-27' and status = 4 where moduleId = 1 and  eid = 50605935542848;
update tenant_module_contract set startDate = '2022-11-28' and endDate = '2023-05-27' and status = 4 where moduleId = 3001 and  eid = 50605935542848;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2027-12-31' and status = 3 where moduleId = 1 and  eid = 50606799479360;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2027-12-31' and status = 3 where moduleId = 3001 and  eid = 50606799479360;
update tenant_module_contract set startDate = '2022-12-27' and endDate = '2023-12-26' and status = 4 where moduleId = 1 and  eid = 50605912134208;
update tenant_module_contract set startDate = '2022-12-27' and endDate = '2023-12-26' and status = 4 where moduleId = 3001 and  eid = 50605912134208;
update tenant_module_contract set startDate = '2024-10-17' and endDate = '2025-10-16' and status = 3 where moduleId = 1 and  eid = 67452354056768;
update tenant_module_contract set startDate = '2024-10-17' and endDate = '2025-10-16' and status = 3 where moduleId = 3001 and  eid = 67452354056768;
update tenant_module_contract set startDate = '2024-11-01' and endDate = '2025-10-31' and status = 3 where moduleId = 3001 and  eid = 50606461723200;
update tenant_module_contract set startDate = '2023-07-07' and endDate = '2024-09-06' and status = 4 where moduleId = 1 and  eid = 375713087840832;
update tenant_module_contract set startDate = '2023-07-07' and endDate = '2024-09-06' and status = 4 where moduleId = 3001 and  eid = 375713087840832;
update tenant_module_contract set startDate = '2024-01-07' and endDate = '2025-01-06' and status = 3 where moduleId = 1 and  eid = 50607034094144;
update tenant_module_contract set startDate = '2024-01-07' and endDate = '2025-01-06' and status = 3 where moduleId = 3001 and  eid = 50607034094144;
update tenant_module_contract set startDate = '2024-11-10' and endDate = '2025-11-09' and status = 3 where moduleId = 1 and  eid = 58693517939264;
update tenant_module_contract set startDate = '2024-11-10' and endDate = '2025-11-09' and status = 3 where moduleId = 3001 and  eid = 58693517939264;
update tenant_module_contract set startDate = '2024-10-25' and endDate = '2025-10-24' and status = 3 where moduleId = 1 and  eid = 364650092290624;
update tenant_module_contract set startDate = '2024-10-25' and endDate = '2025-10-24' and status = 3 where moduleId = 3001 and  eid = 364650092290624;
update tenant_module_contract set startDate = '2024-05-07' and endDate = '2025-05-06' and status = 3 where moduleId = 1 and  eid = 50606428643904;
update tenant_module_contract set startDate = '2024-05-07' and endDate = '2025-05-06' and status = 3 where moduleId = 3001 and  eid = 50606428643904;
update tenant_module_contract set startDate = '2023-11-28' and endDate = '2024-11-27' and status = 4 where moduleId = 1 and  eid = 204828824683072;
update tenant_module_contract set startDate = '2023-11-28' and endDate = '2024-11-27' and status = 4 where moduleId = 3001 and  eid = 204828824683072;
update tenant_module_contract set startDate = '2023-11-01' and endDate = '2024-10-31' and status = 4 where moduleId = 1 and  eid = 335648580158016;
update tenant_module_contract set startDate = '2023-11-01' and endDate = '2024-10-31' and status = 4 where moduleId = 3001 and  eid = 335648580158016;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 1 and  eid = 233428062474816;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 3001 and  eid = 233428062474816;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 1 and  eid = 203980043735616;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 3001 and  eid = 203980043735616;
update tenant_module_contract set startDate = '2022-12-19' and endDate = '2023-12-18' and status = 4 where moduleId = 1 and  eid = 175115148141120;
update tenant_module_contract set startDate = '2022-12-19' and endDate = '2023-12-18' and status = 4 where moduleId = 3001 and  eid = 175115148141120;
update tenant_module_contract set startDate = '2024-10-12' and endDate = '2025-10-11' and status = 3 where moduleId = 1 and  eid = 204820977267264;
update tenant_module_contract set startDate = '2024-10-12' and endDate = '2025-10-11' and status = 3 where moduleId = 3001 and  eid = 204820977267264;
update tenant_module_contract set startDate = '2024-08-13' and endDate = '2026-08-12' and status = 3 where moduleId = 1 and  eid = 219345114653248;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 1 and  eid = 165235687203392;
update tenant_module_contract set startDate = '2022-07-01' and endDate = '2023-06-30' and status = 4 where moduleId = 3001 and  eid = 165235687203392;
update tenant_module_contract set startDate = '2024-11-21' and endDate = '2025-11-20' and status = 3 where moduleId = 1 and  eid = 201414412923456;
update tenant_module_contract set startDate = '2024-11-21' and endDate = '2025-11-20' and status = 3 where moduleId = 3001 and  eid = 201414412923456;
update tenant_module_contract set startDate = '2024-11-28' and endDate = '2025-11-27' and status = 3 where moduleId = 1 and  eid = 196814478242368;
update tenant_module_contract set startDate = '2024-11-28' and endDate = '2025-11-27' and status = 3 where moduleId = 3001 and  eid = 196814478242368;
update tenant_module_contract set startDate = '2022-08-01' and endDate = '2023-07-31' and status = 4 where moduleId = 1 and  eid = 379924160918080;
update tenant_module_contract set startDate = '2022-08-01' and endDate = '2023-07-31' and status = 4 where moduleId = 3001 and  eid = 379924160918080;
update tenant_module_contract set startDate = '2024-08-30' and endDate = '2025-08-29' and status = 3 where moduleId = 1 and  eid = 50606132265536;
update tenant_module_contract set startDate = '2024-08-30' and endDate = '2025-08-29' and status = 3 where moduleId = 3001 and  eid = 50606132265536;
update tenant_module_contract set startDate = '2023-11-06' and endDate = '2024-11-05' and status = 4 where moduleId = 1 and  eid = 204828348736064;
update tenant_module_contract set startDate = '2023-11-06' and endDate = '2024-11-05' and status = 4 where moduleId = 3001 and  eid = 204828348736064;
update tenant_module_contract set startDate = '2024-07-17' and endDate = '2025-07-16' and status = 3 where moduleId = 1 and  eid = 204820752904768;
update tenant_module_contract set startDate = '2024-07-17' and endDate = '2025-07-16' and status = 3 where moduleId = 3001 and  eid = 204820752904768;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 1 and  eid = 41322912428608;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 3001 and  eid = 41322912428608;
update tenant_module_contract set startDate = '2023-12-18' and endDate = '2024-12-17' and status = 4 where moduleId = 1 and  eid = 50606254637633;
update tenant_module_contract set startDate = '2023-12-18' and endDate = '2024-12-17' and status = 4 where moduleId = 3001 and  eid = 50606254637633;
update tenant_module_contract set startDate = '2024-01-02' and endDate = '2025-01-01' and status = 4 where moduleId = 1 and  eid = 50606283715136;
update tenant_module_contract set startDate = '2024-01-02' and endDate = '2025-01-01' and status = 4 where moduleId = 3001 and  eid = 50606283715136;
update tenant_module_contract set startDate = '2024-12-15' and endDate = '2025-12-14' and status = 3 where moduleId = 1 and  eid = 41318489813568;
update tenant_module_contract set startDate = '2024-12-15' and endDate = '2025-12-14' and status = 3 where moduleId = 3001 and  eid = 41318489813568;
update tenant_module_contract set startDate = '2024-02-29' and endDate = '2025-02-28' and status = 3 where moduleId = 1 and  eid = 50606220698176;
update tenant_module_contract set startDate = '2024-02-29' and endDate = '2025-02-28' and status = 3 where moduleId = 3001 and  eid = 50606220698176;
update tenant_module_contract set startDate = '2022-12-01' and endDate = '2025-11-30' and status = 3 where moduleId = 1 and  eid = 54724175880768;
update tenant_module_contract set startDate = '2022-12-01' and endDate = '2025-11-30' and status = 3 where moduleId = 3001 and  eid = 54724175880768;
update tenant_module_contract set startDate = '2023-07-03' and endDate = '2024-07-02' and status = 4 where moduleId = 1 and  eid = 50606540653120;
update tenant_module_contract set startDate = '2023-07-03' and endDate = '2024-07-02' and status = 4 where moduleId = 3001 and  eid = 50606540653120;
update tenant_module_contract set startDate = '2024-03-18' and endDate = '2025-03-17' and status = 3 where moduleId = 1 and  eid = 451948011786816;
update tenant_module_contract set startDate = '2024-03-18' and endDate = '2025-03-17' and status = 3 where moduleId = 3001 and  eid = 451948011786816;
update tenant_module_contract set startDate = '2024-04-01' and endDate = '2025-03-31' and status = 3 where moduleId = 1 and  eid = 50606512431680;
update tenant_module_contract set startDate = '2024-04-01' and endDate = '2025-03-31' and status = 3 where moduleId = 3001 and  eid = 50606512431680;
update tenant_module_contract set startDate = '2022-11-30' and endDate = '2024-11-29' and status = 4 where moduleId = 1 and  eid = 122738551022144;
update tenant_module_contract set startDate = '2022-11-30' and endDate = '2024-11-29' and status = 4 where moduleId = 3001 and  eid = 122738551022144;
update tenant_module_contract set startDate = '2024-12-16' and endDate = '2025-12-15' and status = 3 where moduleId = 1 and  eid = 204822348714560;
update tenant_module_contract set startDate = '2024-12-16' and endDate = '2025-12-15' and status = 3 where moduleId = 3001 and  eid = 204822348714560;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 1 and  eid = 498927615689280;
update tenant_module_contract set startDate = '2025-01-01' and endDate = '2025-12-31' and status = 3 where moduleId = 3001 and  eid = 498927615689280;
update tenant_module_contract set startDate = '2024-09-29' and endDate = '2025-09-28' and status = 3 where moduleId = 1 and  eid = 204822366847552;
update tenant_module_contract set startDate = '2024-09-29' and endDate = '2025-09-28' and status = 3 where moduleId = 3001 and  eid = 204822366847552;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2023-12-31' and status = 4 where moduleId = 1 and  eid = 114859134292544;
update tenant_module_contract set startDate = '2023-01-01' and endDate = '2023-12-31' and status = 4 where moduleId = 3001 and  eid = 114859134292544;
update tenant_module_contract set startDate = '2024-12-07' and endDate = '2025-12-06' and status = 3 where moduleId = 1 and  eid = 50605846024768;
update tenant_module_contract set startDate = '2024-12-07' and endDate = '2025-12-06' and status = 3 where moduleId = 3001 and  eid = 50605846024768;
update tenant_module_contract set startDate = '2024-09-10' and endDate = '2025-09-09' and status = 3 where moduleId = 1 and  eid = 41324544787008;
update tenant_module_contract set startDate = '2024-09-10' and endDate = '2025-09-09' and status = 3 where moduleId = 3001 and  eid = 41324544787008;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 1 and  eid = 41322429674048;
update tenant_module_contract set startDate = '2024-01-01' and endDate = '2024-12-31' and status = 4 where moduleId = 3001 and  eid = 41322429674048;
update tenant_module_contract set startDate = '2024-09-01' and endDate = '2025-08-31' and status = 3 where moduleId = 1 and  eid = 41323408364096;
update tenant_module_contract set startDate = '2024-09-01' and endDate = '2025-08-31' and status = 3 where moduleId = 3001 and  eid = 41323408364096;
update tenant_module_contract set startDate = '2024-09-01' and endDate = '2025-08-31' and status = 3 where moduleId = 1 and  eid = 41318453105216;
update tenant_module_contract set startDate = '2024-09-01' and endDate = '2025-08-31' and status = 3 where moduleId = 3001 and  eid = 41318453105216;
update tenant_module_contract set startDate = '2022-06-20' and endDate = '2024-07-21' and status = 4 where moduleId = 1 and  eid = 41318457918016;
update tenant_module_contract set startDate = '2022-06-20' and endDate = '2024-07-21' and status = 4 where moduleId = 3001 and  eid = 41318457918016;
update tenant_module_contract set startDate = '2023-11-09' and endDate = '2024-11-08' and status = 4 where moduleId = 1 and  eid = 41319154704960;
update tenant_module_contract set startDate = '2023-11-09' and endDate = '2024-11-08' and status = 4 where moduleId = 3001 and  eid = 41319154704960;
update tenant_module_contract set startDate = '2024-06-01' and endDate = '2025-05-31' and status = 3 where moduleId = 1 and  eid = 41318261473856;
update tenant_module_contract set startDate = '2024-06-01' and endDate = '2025-05-31' and status = 3 where moduleId = 3001 and  eid = 41318261473856;
```