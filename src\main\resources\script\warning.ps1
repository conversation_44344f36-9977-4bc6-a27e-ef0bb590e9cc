
function TokenGet {
    $payload = @{
        auth = @{
            passwordCredentials = @{
                username = 'admin'
                password = '6f66e437d3d7bdcab50e39698eac9c62a61fa576aa43c40e3a23ca029db1bc37f921fc22c6748ff123d91c311fa3e1fa15c235054f7784977068be4f1b409a4cbf231149de75a8045f70848ba277845b84c27b2bb94f9e346631a54e2413a2b933b6016525c7d5ff0ba4dea42a07206c951886ce158b4c5acee3992ef7cb93111c885cf3919d5c2a81c660354f24b4144cdfafcb67a8aa8e823a134c222ed320c4752a91fc6ed95bd81f334f3a5b4ac47cca6ef295eba49f8281f554b1b7118d2b00113dceac85bf8b39ede39dd0717afa4772fd71674baadb56949b4f7273a66d04086d10390cc4b19e9fb0b20f162e294bccbedd717ad5921f5c09b2ce806a'
            }
        }
    }
    $jsonBody = $payload | ConvertTo-Json

    $response = Invoke-WebRequest -Uri https://************:4435/janus/authenticate -Method POST -ContentType "application/json" -Body $jsonBody -SkipCertificateCheck
    if ($response.StatusCode -eq 200) {
        $jsonObject = $response.Content | ConvertFrom-Json
        return $jsonObject.data.access.token.id
    } else {
        Write-Output $response
    }
}

function QueryWarn {
    # 调用函数并获取返回值
    $token = TokenGet
    $InvalidHeaders = @{
        'ContentType' = "application/json"
        'Authorization' = "Token "+$token
    }
    $next_page_num="0"
    $myArray = New-Object System.Collections.ArrayList
    while ($next_page_num -ne "") {
        $response = Invoke-WebRequest -Uri "https://************:4435/janus/20180725/alarms?page_num=$next_page_num&page_size=1000&begin_time=2023-11-10" -Method GET -Headers $InvalidHeaders -SkipCertificateCheck


        if ($response.StatusCode -eq 200) {
            $jsonObject = $response.Content | ConvertFrom-Json
            $next_page_num = $jsonObject.data.next_page_num
            if ($next_page_num -eq "3")
            {
                $next_page_num = ""
            }
            $myArray.AddRange($jsonObject.data.data)
        }
        else
        {
            $next_page_num = ""
        }
    }
    return $myArray

}
$aa=QueryWarn
$result = Write-Output $aa | ConvertTo-Json
Write-Output $result
