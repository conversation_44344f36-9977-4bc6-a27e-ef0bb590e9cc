{"body": {"prefetch": -1, "scanAvailable": true}, "cookies": {}, "headers": {"Host": ["aiogateway:8081"], "Connection": ["close"], "sid": ["241199971893824"], "User-Agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "eid": ["99990000"], "Accept": ["application/json, text/plain, */*"], "funcAppId": ["AIEOM"], "application": ["om"], "token": ["9a6f8ee2-660f-4b00-a61a-9f6073ce3382"], "appToken": ["eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IkFJRU9NIiwic2lkIjozMTM4MTg3NDkxNDE1Njh9.18klpg6bYR-q_3BNFjXzfCM2QzmGYvQmLoFDbryrvSo"], "version": ["2.0"], "Referer": ["http://************:30010/"], "Accept-Encoding": ["gzip, deflate"], "Accept-Language": ["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"], "authUserId": ["<EMAIL>"], "authUserSid": ["41373889970752"], "data-permission": ["true"]}, "id": "e9e604a6-20976", "localAddress": {"address": "************", "port": 8081}, "method": "GET", "methodValue": "GET", "nativeRequest": {"body": {"prefetch": -1, "scanAvailable": true}, "cookies": {}, "headers": {"Host": ["aiogateway:8081"], "Connection": ["close"], "sid": ["241199971893824"], "User-Agent": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"], "eid": ["99990000"], "Accept": ["application/json, text/plain, */*"], "funcAppId": ["AIEOM"], "application": ["om"], "token": ["9a6f8ee2-660f-4b00-a61a-9f6073ce3382"], "appToken": ["eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IkFJRU9NIiwic2lkIjozMTM4MTg3NDkxNDE1Njh9.18klpg6bYR-q_3BNFjXzfCM2QzmGYvQmLoFDbryrvSo"], "version": ["2.0"], "Referer": ["http://************:30010/"], "Accept-Encoding": ["gzip, deflate"], "Accept-Language": ["zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"], "authUserId": ["<EMAIL>"], "authUserSid": ["41373889970752"], "data-permission": ["true"]}, "id": "e9e604a6-20976", "localAddress": {"address": "************", "port": 8081}, "method": "GET", "methodValue": "GET", "nativeRequest": {"disposed": false, "inboundCancelled": false, "inboundDisposed": false, "keepAlive": false, "persistent": true, "subscriptionDisposed": false, "websocket": false}, "path": {}, "queryParams": {"moduleId": [""], "status": [""], "serviceIsvSids": [""], "attention": ["true"], "page": ["1"], "size": ["10"], "content": [""]}, "remoteAddress": {"address": "*************", "port": 53108}, "uRI": "http://aiogateway:8081/aiouser/api/tenant/module/contracts?moduleId=&status=&serviceIsvSids=&attention=true&page=1&size=10&content="}, "path": {}, "queryParams": {"moduleId": [""], "status": [""], "serviceIsvSids": [""], "attention": ["true"], "page": ["1"], "size": ["10"], "content": [""]}, "remoteAddress": {"address": "*************", "port": 53108}, "uRI": "http://aiogateway:8081/aiouser/api/tenant/module/contracts?moduleId=&status=&serviceIsvSids=&attention=true&page=1&size=10&content="}