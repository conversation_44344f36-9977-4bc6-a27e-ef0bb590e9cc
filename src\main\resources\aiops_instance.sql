CREATE TABLE `AiopsTenant` (
                               `eid` bigint(20) NULL COMMENT "",
                               `deviceId` varchar(65533) REPLACE NULL COMMENT "",
                               `collectedTime` datetime REPLACE NULL COMMENT "",
                               `collectConfigId` varchar(65533) REPLACE NULL COMMENT "",
                               `uploadDataModelCode` varchar(65533) REPLACE NULL COMMENT "",
                               `deviceCollectDetailId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantId` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantName` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameCH` varchar(65533) REPLACE NULL COMMENT "",
                               `customerFullNameEN` varchar(65533) REPLACE NULL COMMENT "",
                               `serviceCode` varchar(65533) REPLACE NULL COMMENT "",
                               `taxCode` varchar(65533) REPLACE NULL COMMENT "",
                               `tenantStatus` int(11) REPLACE NULL COMMENT "",
                               `registerPhone` varchar(65533) REPLACE NULL COMMENT "",
                               `address` varchar(65533) REPLACE NULL COMMENT "",
                               `contacts` varchar(65533) REPLACE NULL COMMENT "",
                               `email` varchar(65533) REPLACE NULL COMMENT "",
                               `phone` varchar(65533) REPLACE NULL COMMENT "",
                               `cellphone_prefix` varchar(65533) REPLACE NULL COMMENT "",
                               `telephone` varchar(65533) REPLACE NULL COMMENT "",
                               `tenant_sync_source` varchar(65533) REPLACE NULL COMMENT "",
                               `updateTime` datetime REPLACE NULL COMMENT "",
                               `orderId` bigint(20) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT "",
                               `customerCode` varchar(65533) REPLACE_IF_NOT_NULL NULL DEFAULT "0" COMMENT ""
) ENGINE=OLAP
    AGGREGATE KEY(`eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"storage_format" = "DEFAULT",
"enable_persistent_index" = "false"
);

CREATE TABLE es_customerservice_v2 (
                                 SalesContact VARCHAR(65535),
                                 eId BIGINT,
                                 CustomerCode VARCHAR(65535),
                                 grp_dpt_id VARCHAR(65535),
                                 grp_dpt_name VARCHAR(65535),
                                 grp_dpt_manager VARCHAR(65535),
                                 bus_dpt_id VARCHAR(65535),
                                 bus_dpt_name VARCHAR(65535),
                                 bus_dpt_manager VARCHAR(65535),
                                 dpt_id VARCHAR(65535),
                                 dpt_name VARCHAR(65535),
                                 dpt_manager VARCHAR(65535),
                                 Sales VARCHAR(65535),
                                 CustomerServiceCode VARCHAR(65535),
                                 CustomerName VARCHAR(65535),
                                 CustomerFullNameCH VARCHAR(65535),
                                 CustomerFullNameEN VARCHAR(65535),
                                 another_name VARCHAR(65535),
                                 current_valid_status CHAR(1),
                                 t100_cust_id VARCHAR(65535),
                                 taxNo VARCHAR(65535),
                                 contacts VARCHAR(65535),
                                 tenantTelephone VARCHAR(65535),
                                 tenantEmail VARCHAR(65535),
                                 address VARCHAR(65535),
                                 __version__ DATETIME

) ENGINE=OLAP
    PRIMARY KEY(SalesContact, eId, CustomerCode)
DISTRIBUTED BY HASH(SalesContact, eId, CustomerCode)
BUCKETS 10
PROPERTIES(
    "replication_num" = "2"
);


PRIMARY KEY(userSid,eid)
DISTRIBUTED BY HASH(userSid,eid) BUCKETS 10
PROPERTIES(
    "replication_num" = "2"
);


CREATE TABLE es_customerservice_v4 (
                                       SalesContact VARCHAR(65535),
                                       eId BIGINT,
                                       CustomerCode VARCHAR(65535),
                                       grp_dpt_id VARCHAR(65535),
                                       grp_dpt_name VARCHAR(65535),
                                       grp_dpt_manager VARCHAR(65535),
                                       bus_dpt_id VARCHAR(65535),
                                       bus_dpt_name VARCHAR(65535),
                                       bus_dpt_manager VARCHAR(65535),
                                       dpt_id VARCHAR(65535),
                                       dpt_name VARCHAR(65535),
                                       dpt_manager VARCHAR(65535),
                                       Sales VARCHAR(65535),
                                       CustomerServiceCode VARCHAR(65535),
                                       CustomerName VARCHAR(65535),
                                       CustomerFullNameCH VARCHAR(65535),
                                       CustomerFullNameEN VARCHAR(65535),
                                       another_name VARCHAR(65535),
                                       current_valid_status CHAR(1),
                                       t100_cust_id VARCHAR(65535),
                                       taxNo VARCHAR(65535),
                                       contacts VARCHAR(65535),
                                       tenantTelephone VARCHAR(65535),
                                       tenantEmail VARCHAR(65535),
                                       address VARCHAR(65535),
                                       __version__ DATETIME

) ENGINE=OLAP
    PRIMARY KEY(SalesContact, eId, CustomerCode)
DISTRIBUTED BY HASH(SalesContact, eId, CustomerCode)
BUCKETS 10
PROPERTIES(
    "replication_num" = "2"
);

select DISTINCT g.* , c.Sales,c.SalesContact, cc.*
from grp_bus_dpt_mapping g
         INNER JOIN mars_customerservicesatff s on s.departmentcode = g.dpt_id
         INNER JOIN mars_customerservice c on c.SalesContact = s.email
         INNER JOIN mars_customer cc on cc.CustomerServiceCode = c.CustomerServiceCode

INSERT INTO es_customerservice_v2(grp_dpt_id, grp_dpt_name, grp_dpt_manager, bus_dpt_id, bus_dpt_name, bus_dpt_manager, dpt_id,
                     dpt_name, dpt_manager, Sales, SalesContact, eId, CustomerCode, CustomerServiceCode, CustomerName,
                     CustomerFullNameCH, CustomerFullNameEN, another_name, current_valid_status, t100_cust_id, taxNo,
                     contacts, tenantTelephone, tenantEmail, address, __version__)
VALUES ('CD0000', '广东事业处', '伍定一', 'CD1100', '深圳增值事业部', '陈亚欣', 'CD1120', '增值业务二部', '黄桂贤',
        '李宇琛', '<EMAIL>', 41319769805376, 'C002216693', '02220000', '惠州惠兰', '惠兰灯饰（惠东）有限公司',
        '', null, 'Y', null, '91441323617917767U', '李佩茹', '', '', '广东惠州惠東縣白花镇太阳坳工业城',
        '2023-12-13 16:28:18');

-- 找出a2表在a1表中的数据：
SELECT a1.*
FROM es_customerservice_v2 a1
 LEFT JOIN  es_customerservice_v4 as a2 ON a2.eId = a1.eId
WHERE a2.eId is null;

-- 找出a2表不在a1表中的数据：
SELECT a2.*
FROM es_customerservice_v4 a2
         LEFT JOIN es_customerservice_v2 as a1 ON a2.eId = a1.eId AND a2.SalesContact = a1.SalesContact AND a2.CustomerCode = a1.CustomerCode
WHERE a1.eId IS NULL









request:<{"alias":"ASIA_123456","email":"<EMAIL>","license":{"desktop":{"expiration_time":**********,"start_time":**********,"total":5,"vulnerability":true},"server":{"expiration_time":**********,"start_time":**********,"total":5,"vulnerability":true}},"tenant_name":"鼎捷123456"},[Authorization:"Access=ak-a00ff28a7f84426a8b9d712a322c4da6, ExpireTime=1716427227916, Signature=7b4be32a04829f3fd0d28df952c2b435bd06b277", Content-Type:"application/json; charset=utf-8"]>

CREATE TABLE servicecloud.es_customerservice_v3 (
                                                    grp_dpt_id VARCHAR(64) NOT NULL COMMENT '事业群ID',
                                                    grp_dpt_manager_contact  VARCHAR(64) NOT NULL COMMENT '事业群经理联系方式',
                                                    bus_dpt_id VARCHAR(64) NOT NULL COMMENT '业务部门ID',
                                                    bus_dpt_manager_contact VARCHAR(64) NOT NULL COMMENT '业务部门经理联系方式',
                                                    dpt_id VARCHAR(64) NOT NULL COMMENT '部门ID',
                                                    dpt_manager_contact VARCHAR(64) NOT NULL COMMENT '部门经理联系方式',
                                                    grp_dpt_name VARCHAR(65535) NOT NULL COMMENT '事业群名称',
                                                    grp_dpt_manager VARCHAR(65535) COMMENT '事业群经理',


                                                    bus_dpt_name VARCHAR(65535) NOT NULL COMMENT '业务部门名称',
                                                    bus_dpt_manager VARCHAR(65535) COMMENT '业务部门经理',

                                                    bus_dpt_win_name VARCHAR(65535) COMMENT '业务部门负责人',
                                                    bus_dpt_win_contact VARCHAR(65535) COMMENT '业务部门负责人联系方式',

                                                    dpt_name VARCHAR(65535) NOT NULL COMMENT '部门名称',
                                                    dpt_manager VARCHAR(65535) COMMENT '部门经理'

) ENGINE=OLAP

DISTRIBUTED BY HASH(grp_dpt_id, grp_dpt_manager_contact, bus_dpt_id,bus_dpt_manager_contact,dpt_id,dpt_manager_contact)
BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);


PRIMARY KEY(`SalesContact`, `eId`, `CustomerCode`)
DISTRIBUTED BY HASH(`SalesContact`, `eId`, `CustomerCode`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "true",
"replicated_storage" = "true",
"compression" = "LZ4"
);


INSERT INTO servicecloud.IndustryPlanQuickScreening(sqSummitFreq, sqCollateRate, sqFirmCount, sqOverseasFirmCount,
                                                    sqERPSysCount, sqConsolCycle, sqIPODisclosure, sqCfoCompletedCount,
                                                    sqCfoTotalQuantity, sqIndustryAffil, sqBizModel, sqBizTeamSize,
                                                    sqProdType, sqProdTypeRatio, sqProdModelCount, sqQuoteEfficiency,
                                                    sqQuoteConfirmation, sqOrderConfirmation, sqOrderEfficiency,
                                                    sqOrderingOperationMode, sqOrderProgressTracking,
                                                    sqCsoCompletedCount, sqCsoTotalQuantity, sqBranchCount,
                                                    sqOverseasBranches, sqEmployeeCount, sqHumanResourcesCount,
                                                    sqPaperContract, sqPaperDummy, sqAttendanceSettlementDays,
                                                    sqSalarySettlementDays, sqHumanRightsVerification,
                                                    sqStaffingBlueprint, sqBudgetManagement, sqTalentManagement,
                                                    sqEnterpriseResourceSharing, sqChoCompletedCount,
                                                    sqChoTotalQuantity, customerName, uploadDataModelCode,
                                                    collectedTime, CustomerCode, serviceCode, businessDepartmentCode,
                                                    businessDepartmentName, SalesName, SalesContact, fscfo, fscso,
                                                    fscho, eid, businessDepartmentCodeACP, createTime, userid,
                                                    ThemeApplySourceCode, ThemeApplayStatus, ThemeLastUpdateTime,
                                                    userSid, url)
values (null, null, null, null, null, null, null, 0, 7, null, null, null, null, null, null, null, null, null, null,
        null, null, 0, 12, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 13, null,
        'IndustryPlanQuickScreening', '2024-05-30 10:01:30', '99990000', 'c99990000', null, null, '陈超',
        '<EMAIL>',
        '{"sqAnnualRevenue":"","sqSummitFreq":"","sqCollateRate":"","sqFirmCount":"","sqOverseasFirmCount":"","sqERPSysCount":"","sqJointReportOutputMethod":"","sqConsolCycle":"","sqCombinedOutputDays":"","sqIPODisclosure":"","sqIPODisclosureDeadline":"","sqCfoCompletedCount":0,"sqCfoTotalQuantity":7}',
        '{"sqIndustryAffil":"","sqBizModel":"","sqBizTeamSize":"","sqProdType":"","sqProdTypeRatio":"","sqProdModelCount":"","sqQuoteEfficiency":"","sqQuoteConfirmation":"","sqOrderConfirmation":"","sqOrderEfficiency":"","sqOrderingOperationMode":"","sqOrderProgressTracking":"","sqCsoCompletedCount":0,"sqCsoTotalQuantity":12}',
        '{"sqBranchCount":"","sqOverseasBranches":"","sqEmployeeCount":"","sqHumanResourcesCount":"","sqPaperContract":"","sqPaperDummy":"","sqAttendanceSettlementDays":"","sqSalarySettlementDays":"","sqHumanRightsVerification":"","sqStaffingBlueprint":"","sqBudgetManagement":"","sqTalentManagement":"","sqEnterpriseResourceSharing":"","sqChoCompletedCount":0,"sqChoTotalQuantity":13}',
        '99990000', null, null, 0, null, 'Applayed', '2024-05-30 10:01:30', 0, null);






