package com.example.mybatis.plus.controller;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.mybatis.plus.mapper.V4Mapper;
import com.example.mybatis.plus.model.V4;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/cat")
@Slf4j
public class TestController {
    @Resource
    private V4Mapper v4Mapper;

    @GetMapping(value = "/test")
    public String test() {
//        List<V4> v4s = v4Mapper.selectAll();
        return "v4s";

    }
}

