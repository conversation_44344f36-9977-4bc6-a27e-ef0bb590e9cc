package com.example.aio.controller;

import cn.hutool.json.JSONUtil;
import com.example.aio.model.DataExaminationMenu;
import com.example.aio.service.TestService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/menu")
@Slf4j
public class TestController  {
    @Autowired
    private TestService testService;

    @Value("${esc.integration.iamAddress}")
    private String iamAddress;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private RestTemplate restTemplate;

    public TestController() {
        this.restTemplate = new RestTemplate();
    }

    public static final String MR_ROW_KEY = "maintenanceRecord";


    @Value("${esc.integration.datauri}")
    private String dataUri;

    @GetMapping(value = "/generate")
    public List<DataExaminationMenu> getTypeList(String parentCode,String menuCode,String menuName,String menuSource) {
        List<DataExaminationMenu> test = testService.test(parentCode, menuCode, menuName,menuSource);
        return test;

    }

    @GetMapping(value = "/reids")
    public void reids() {
        // 获取所有的key
        Set<String> keys = stringRedisTemplate.keys("*");

        // 检查keys是否为空
        if (keys != null && !keys.isEmpty()) {
            // 遍历所有的key
            for (String key : keys) {
                // 获取key的数据类型
                DataType dataType = stringRedisTemplate.type(key);

                // 根据数据类型获取value
                Object value = null;
                switch (dataType) {
                    case STRING:

                        value = stringRedisTemplate.opsForValue().get(key);
                        if (value.toString().contains("username")){
                            System.out.println("Key: " + key + ", Value: " + value);
                        }

                        break;
                    case LIST:
                        value = stringRedisTemplate.opsForList().range(key, 0, -1);
                        break;
                    case SET:
                        value = stringRedisTemplate.opsForSet().members(key);
                        break;
                    case ZSET:
                        value = stringRedisTemplate.opsForZSet().range(key, 0, -1);
                        break;
                    case HASH:
                        value = stringRedisTemplate.opsForHash().entries(key);
                        break;
                    // 其他类型可以继续添加处理分支
                    default:
                        // 其他类型不处理或者特殊处理
                        break;
                }

                // 打印出key和value

            }
        }

    }

    @GetMapping(value = "/detail")
    public Object gerMrDetail(@RequestParam(value = "modelCode") String modelCode,
                                    @RequestParam(value = "id") String id,
                                    @RequestParam(value = "eid", required = false) Long eid) {
        return getMrDetail(modelCode, id, eid);
    }


    @GetMapping(value = "/roleGet")
    public Object roleGet(@RequestParam(value = "token") String token,
                              @RequestParam(value = "userSid", required = false) Long userSid) {
//        String token = RequestUtil.getHeaderToken();
//        String userId = RequestUtil.getHeaderUserId();
        RoleDelete req = new RoleDelete();
        req.setSid(userSid);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("digi-middleware-auth-user", token);
        HttpEntity<RoleDelete> request = new HttpEntity<>(req, headers);
        Object o = restTemplate.postForObject(iamAddress+"/api/iam/v2/user/role", request, Object.class);

        return o;
    }
    @Data
    public static class RoleDelete{
        private Long sid;
    }

    private Object getMrDetail(String modelCode, String id, Long eid) {
        return getMrDetailByKey(modelCode, eid + MR_ROW_KEY + id);
    }

    private Object getMrDetailByKey(String modelCode, String rowKey) {
        HashMap hashMap = restTemplate.getForObject(dataUri + "/hbase/getByRowKey?tableName=" +
                modelCode + "&rowKey=" + rowKey, HashMap.class);
        if (!ObjectUtils.isEmpty(hashMap)) {
            return hashMap.get("model");
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        String jsonStr = "{\"alarmContent\":\"消火栓进户阀无压力\",\"operateState\":0,\"level\":3,\"alarmObjects\":[{\"code\":\"155986\",\"name\":\"消火栓进户阀无压力\",\"location\":\"普洛斯沈阳（沈西）工业物流园_9号库消火栓进户阀入口\",\"alarmTime\":1718640608000}],\"operateTime\":1724744266000,\"alarmTime\":1718640608000,\"uniqueKey\":\"166977\",\"source\":\"QC-千乘\",\"operator\":\"陈亮\",\"alarmType\":0,\"alarmState\":1,\"parkCode\":\"23017\"}";
        Map<String, Object> map = JSONUtil.toBean(jsonStr, Map.class);
        String data = new ArrayList<>(map.keySet())
                .stream()
                .sorted()
                .map(key -> key + "=" + map.get(key))
                .collect(Collectors.joining("&"));
        String signSecret = "F4E016525DAE7BE59F09A60DCAE5BC29"; // F4E016525DAE7BE59F09A60DCAE5BC29 DE075EA9B24D2CE975DDFB51F148E104
        String actualSign = DigestUtils.md5Hex(signSecret + data + signSecret);
        System.out.println(actualSign);
    }

}
